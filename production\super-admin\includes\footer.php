        </div> <!-- End content-wrapper -->
    </div> <!-- End main-content -->
    
    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- Custom JavaScript -->
    <script>
        // Toggle sidebar for mobile
        function toggleSidebar() {
            const sidebar = document.getElementById('sidebar');
            sidebar.classList.toggle('show');
        }
        
        // Close sidebar when clicking outside on mobile
        document.addEventListener('click', function(event) {
            const sidebar = document.getElementById('sidebar');
            const mobileMenuBtn = document.querySelector('.mobile-menu-btn');
            
            if (window.innerWidth <= 768 && 
                !sidebar.contains(event.target) && 
                !mobileMenuBtn.contains(event.target) &&
                sidebar.classList.contains('show')) {
                sidebar.classList.remove('show');
            }
        });
        
        // Auto-hide alerts after 5 seconds
        document.addEventListener('DOMContentLoaded', function() {
            const alerts = document.querySelectorAll('.alert:not(.alert-permanent)');
            alerts.forEach(function(alert) {
                setTimeout(function() {
                    if (alert.parentNode) {
                        alert.style.transition = 'opacity 0.5s ease';
                        alert.style.opacity = '0';
                        setTimeout(function() {
                            if (alert.parentNode) {
                                alert.remove();
                            }
                        }, 500);
                    }
                }, 5000);
            });
        });
        
        // Confirm dangerous actions
        document.addEventListener('click', function(event) {
            if (event.target.classList.contains('confirm-action')) {
                const message = event.target.getAttribute('data-confirm') || 'Are you sure you want to perform this action?';
                if (!confirm(message)) {
                    event.preventDefault();
                    return false;
                }
            }
        });
        
        // Form validation enhancement
        document.addEventListener('DOMContentLoaded', function() {
            const forms = document.querySelectorAll('.needs-validation');
            forms.forEach(function(form) {
                form.addEventListener('submit', function(event) {
                    if (!form.checkValidity()) {
                        event.preventDefault();
                        event.stopPropagation();
                    }
                    form.classList.add('was-validated');
                });
            });
        });
        
        // Session timeout warning
        let sessionWarningShown = false;
        function checkSessionTimeout() {
            const sessionDuration = <?php echo $session_info['session_duration'] ?? 0; ?>;
            const maxSession = 30 * 60; // 30 minutes
            const warningTime = 25 * 60; // 25 minutes
            
            if (sessionDuration > warningTime && !sessionWarningShown) {
                sessionWarningShown = true;
                if (confirm('Your session will expire in 5 minutes. Would you like to extend it?')) {
                    // Refresh the page to extend session
                    window.location.reload();
                }
            }
        }
        
        // Check session timeout every minute
        setInterval(checkSessionTimeout, 60000);
        
        // Tooltips initialization
        document.addEventListener('DOMContentLoaded', function() {
            var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
            var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
                return new bootstrap.Tooltip(tooltipTriggerEl);
            });
        });
        
        // Auto-refresh for real-time data
        function autoRefreshData() {
            const refreshElements = document.querySelectorAll('[data-auto-refresh]');
            refreshElements.forEach(function(element) {
                const url = element.getAttribute('data-refresh-url');
                const interval = parseInt(element.getAttribute('data-refresh-interval')) || 30000;
                
                setInterval(function() {
                    fetch(url)
                        .then(response => response.text())
                        .then(data => {
                            element.innerHTML = data;
                        })
                        .catch(error => {
                            console.error('Auto-refresh error:', error);
                        });
                }, interval);
            });
        }
        
        // Initialize auto-refresh
        document.addEventListener('DOMContentLoaded', autoRefreshData);
        
        // Copy to clipboard functionality
        function copyToClipboard(text, button) {
            navigator.clipboard.writeText(text).then(function() {
                const originalText = button.innerHTML;
                button.innerHTML = '<i class="fas fa-check"></i> Copied!';
                button.classList.add('btn-success');
                button.classList.remove('btn-outline-secondary');
                
                setTimeout(function() {
                    button.innerHTML = originalText;
                    button.classList.remove('btn-success');
                    button.classList.add('btn-outline-secondary');
                }, 2000);
            }).catch(function(err) {
                console.error('Failed to copy: ', err);
                alert('Failed to copy to clipboard');
            });
        }
        
        // Enhanced form submission with loading states
        document.addEventListener('submit', function(event) {
            const form = event.target;
            const submitBtn = form.querySelector('button[type="submit"]');
            
            if (submitBtn && !submitBtn.disabled) {
                const originalText = submitBtn.innerHTML;
                submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Processing...';
                submitBtn.disabled = true;
                
                // Re-enable after 10 seconds as fallback
                setTimeout(function() {
                    if (submitBtn.disabled) {
                        submitBtn.innerHTML = originalText;
                        submitBtn.disabled = false;
                    }
                }, 10000);
            }
        });
        
        // Real-time clock
        function updateClock() {
            const now = new Date();
            const timeString = now.toLocaleTimeString();
            const clockElements = document.querySelectorAll('.real-time-clock');
            clockElements.forEach(function(element) {
                element.textContent = timeString;
            });
        }
        
        // Update clock every second
        setInterval(updateClock, 1000);
        updateClock(); // Initial call
        
        // Keyboard shortcuts
        document.addEventListener('keydown', function(event) {
            // Ctrl/Cmd + / for help
            if ((event.ctrlKey || event.metaKey) && event.key === '/') {
                event.preventDefault();
                // Show help modal or navigate to help page
                console.log('Help shortcut triggered');
            }
            
            // Ctrl/Cmd + K for quick search
            if ((event.ctrlKey || event.metaKey) && event.key === 'k') {
                event.preventDefault();
                const searchInput = document.querySelector('#quick-search');
                if (searchInput) {
                    searchInput.focus();
                }
            }
        });
        
        // Enhanced table functionality
        document.addEventListener('DOMContentLoaded', function() {
            // Make tables responsive
            const tables = document.querySelectorAll('table:not(.no-responsive)');
            tables.forEach(function(table) {
                if (!table.parentElement.classList.contains('table-responsive')) {
                    const wrapper = document.createElement('div');
                    wrapper.className = 'table-responsive';
                    table.parentNode.insertBefore(wrapper, table);
                    wrapper.appendChild(table);
                }
            });
            
            // Add sorting functionality to sortable tables
            const sortableTables = document.querySelectorAll('.sortable-table');
            sortableTables.forEach(function(table) {
                const headers = table.querySelectorAll('th[data-sort]');
                headers.forEach(function(header) {
                    header.style.cursor = 'pointer';
                    header.addEventListener('click', function() {
                        sortTable(table, header.getAttribute('data-sort'));
                    });
                });
            });
        });
        
        // Simple table sorting function
        function sortTable(table, column) {
            // Basic implementation - can be enhanced
            console.log('Sorting table by column:', column);
        }
    </script>
    
    <!-- Page-specific JavaScript can be added here -->
    <?php if (isset($additional_js)): ?>
        <?php echo $additional_js; ?>
    <?php endif; ?>
    
</body>
</html>
