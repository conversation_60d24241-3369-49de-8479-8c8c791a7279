<?php
require_once '../config/config.php';
requireAdmin();

$page_title = 'Cheque Deposit Management';

// Handle form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['action']) && $_POST['action'] === 'add_cheque') {
        try {
            $db = getDB();
            
            $account_id = intval($_POST['account_id']);
            $cheque_number = trim($_POST['cheque_number']);
            $deposit_date = $_POST['deposit_date'];
            $sender_name = trim($_POST['sender_name']);
            $amount = floatval($_POST['amount']);
            $currency = $_POST['currency'];
            $account_type = trim($_POST['account_type']);
            $branch_code = trim($_POST['branch_code']);
            $id_passport_number = trim($_POST['id_passport_number']);
            $bank_name = trim($_POST['bank_name']);
            $clearance_status = $_POST['clearance_status'];
            
            // Validate inputs
            if (empty($account_id) || empty($cheque_number) || empty($deposit_date) || 
                empty($sender_name) || $amount <= 0 || empty($currency) || 
                empty($account_type) || empty($branch_code) || empty($id_passport_number) || 
                empty($bank_name)) {
                throw new Exception("All fields are required and amount must be positive.");
            }
            
            // Handle file upload
            if (!isset($_FILES['cheque_image']) || $_FILES['cheque_image']['error'] !== UPLOAD_ERR_OK) {
                throw new Exception("Cheque image is required.");
            }
            
            $upload_dir = '../uploads/cheques/';
            if (!file_exists($upload_dir)) {
                mkdir($upload_dir, 0755, true);
            }
            
            $file_extension = strtolower(pathinfo($_FILES['cheque_image']['name'], PATHINFO_EXTENSION));
            $allowed_extensions = ['jpg', 'jpeg', 'png', 'gif', 'pdf'];
            
            if (!in_array($file_extension, $allowed_extensions)) {
                throw new Exception("Invalid file type. Only JPG, PNG, GIF, and PDF files are allowed.");
            }
            
            $file_name = 'cheque_' . time() . '_' . uniqid() . '.' . $file_extension;
            $file_path = $upload_dir . $file_name;
            
            if (!move_uploaded_file($_FILES['cheque_image']['tmp_name'], $file_path)) {
                throw new Exception("Failed to upload cheque image.");
            }
            
            // Insert cheque deposit record
            $insert_cheque = "INSERT INTO cheque_deposits (
                account_id, cheque_number, deposit_date, sender_name, amount, currency,
                account_type, branch_code, id_passport_number, bank_name, cheque_image_path,
                clearance_status, created_by
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";
            
            $cheque_id = $db->insert($insert_cheque, [
                $account_id, $cheque_number, $deposit_date, $sender_name, $amount, $currency,
                $account_type, $branch_code, $id_passport_number, $bank_name, $file_path,
                $clearance_status, $_SESSION['user_id']
            ]);
            
            if ($cheque_id) {
                $success = "✅ Cheque deposit record added successfully! Cheque ID: {$cheque_id}";
            } else {
                throw new Exception("Failed to add cheque deposit record.");
            }
            
        } catch (Exception $e) {
            $error = $e->getMessage();
        }
    } elseif (isset($_POST['action']) && $_POST['action'] === 'update_status') {
        try {
            $db = getDB();
            
            $cheque_id = intval($_POST['cheque_id']);
            $new_status = $_POST['new_status'];
            $status_reason = trim($_POST['status_reason'] ?? '');
            
            // Update cheque status
            $update_status = "UPDATE cheque_deposits SET 
                             clearance_status = ?, status_reason = ?, updated_by = ? 
                             WHERE id = ?";
            $db->query($update_status, [$new_status, $status_reason, $_SESSION['user_id'], $cheque_id]);
            
            $success = "✅ Cheque clearance status updated successfully!";
            
        } catch (Exception $e) {
            $error = $e->getMessage();
        }
    }
}

// Pagination settings
$records_per_page = 25;
$current_page = intval($_GET['page'] ?? 1);
$offset = ($current_page - 1) * $records_per_page;

// Filter parameters
$filter_status = $_GET['status'] ?? '';
$filter_user = $_GET['user'] ?? '';
$filter_date_from = $_GET['date_from'] ?? '';
$filter_date_to = $_GET['date_to'] ?? '';
$filter_amount_min = floatval($_GET['amount_min'] ?? 0);
$filter_amount_max = floatval($_GET['amount_max'] ?? 0);

// Build WHERE clause for filters
$where_conditions = [];
$params = [];

if (!empty($filter_status)) {
    $where_conditions[] = "cd.clearance_status = ?";
    $params[] = $filter_status;
}

if (!empty($filter_user)) {
    $where_conditions[] = "(a.first_name LIKE ? OR a.last_name LIKE ? OR a.username LIKE ?)";
    $search_term = "%$filter_user%";
    $params[] = $search_term;
    $params[] = $search_term;
    $params[] = $search_term;
}

if (!empty($filter_date_from)) {
    $where_conditions[] = "cd.deposit_date >= ?";
    $params[] = $filter_date_from;
}

if (!empty($filter_date_to)) {
    $where_conditions[] = "cd.deposit_date <= ?";
    $params[] = $filter_date_to;
}

if ($filter_amount_min > 0) {
    $where_conditions[] = "cd.amount >= ?";
    $params[] = $filter_amount_min;
}

if ($filter_amount_max > 0) {
    $where_conditions[] = "cd.amount <= ?";
    $params[] = $filter_amount_max;
}

$where_clause = !empty($where_conditions) ? "WHERE " . implode(" AND ", $where_conditions) : "";

try {
    $db = getDB();
    
    // Get total count for pagination
    $count_query = "SELECT COUNT(*) as total 
                    FROM cheque_deposits cd 
                    LEFT JOIN accounts a ON cd.account_id = a.id 
                    $where_clause";
    $count_result = $db->query($count_query, $params);
    $total_records = $count_result->fetch_assoc()['total'];
    $total_pages = ceil($total_records / $records_per_page);
    
    // Get cheque deposits with pagination
    $cheques_query = "SELECT cd.*, 
                      a.first_name, a.last_name, a.username, a.account_number,
                      admin.first_name as admin_first_name, admin.last_name as admin_last_name
                      FROM cheque_deposits cd 
                      LEFT JOIN accounts a ON cd.account_id = a.id 
                      LEFT JOIN accounts admin ON cd.created_by = admin.id 
                      $where_clause
                      ORDER BY cd.created_at DESC 
                      LIMIT $records_per_page OFFSET $offset";
    
    $cheques_result = $db->query($cheques_query, $params);
    $cheques = [];
    while ($row = $cheques_result->fetch_assoc()) {
        $cheques[] = $row;
    }
    
    // Get all users for the dropdown
    $users_query = "SELECT id, first_name, last_name, username, account_number FROM accounts WHERE is_admin = 0 ORDER BY first_name, last_name";
    $users_result = $db->query($users_query);
    $users = [];
    while ($row = $users_result->fetch_assoc()) {
        $users[] = $row;
    }
    
    // Get summary statistics
    $stats_query = "SELECT 
                    COUNT(*) as total_cheques,
                    COUNT(CASE WHEN clearance_status = 'pending' THEN 1 END) as pending_cheques,
                    COUNT(CASE WHEN clearance_status = 'processing' THEN 1 END) as processing_cheques,
                    COUNT(CASE WHEN clearance_status = 'cleared' THEN 1 END) as cleared_cheques,
                    COUNT(CASE WHEN clearance_status = 'rejected' THEN 1 END) as rejected_cheques,
                    SUM(CASE WHEN clearance_status = 'cleared' THEN amount ELSE 0 END) as total_cleared_amount
                    FROM cheque_deposits";
    $stats_result = $db->query($stats_query);
    $stats = $stats_result->fetch_assoc();
    
} catch (Exception $e) {
    $error = "Failed to load cheque deposits: " . $e->getMessage();
    $cheques = [];
    $users = [];
    $total_records = 0;
    $total_pages = 0;
    $stats = ['total_cheques' => 0, 'pending_cheques' => 0, 'processing_cheques' => 0, 'cleared_cheques' => 0, 'rejected_cheques' => 0, 'total_cleared_amount' => 0];
}

include 'includes/admin-header.php';
?>

<!-- Breadcrumb -->
<nav aria-label="breadcrumb" style="margin-bottom: 1rem;">
    <ol class="breadcrumb">
        <li class="breadcrumb-item"><a href="index.php">Admin</a></li>
        <li class="breadcrumb-item active" aria-current="page">Cheque Deposits</li>
    </ol>
</nav>

<!-- Success Message -->
<?php if (isset($success)): ?>
<div class="alert alert-success alert-dismissible" role="alert">
    <div class="d-flex">
        <div><i class="fas fa-check-circle me-2"></i></div>
        <div>
            <h4 class="alert-title">Success!</h4>
            <div class="text-muted"><?php echo $success; ?></div>
        </div>
    </div>
    <a class="btn-close" data-bs-dismiss="alert" aria-label="close"></a>
</div>
<?php endif; ?>

<!-- Error Message -->
<?php if (isset($error)): ?>
<div class="alert alert-danger" role="alert">
    <div class="d-flex">
        <div><i class="fas fa-exclamation-circle me-2"></i></div>
        <div>
            <h4 class="alert-title">Error!</h4>
            <div class="text-muted"><?php echo htmlspecialchars($error); ?></div>
        </div>
    </div>
</div>
<?php endif; ?>

<!-- Statistics Cards -->
<div class="row row-cards mb-4">
    <div class="col-sm-6 col-lg-3">
        <div class="card card-sm">
            <div class="card-body">
                <div class="row align-items-center">
                    <div class="col-auto">
                        <span class="bg-primary text-white avatar">
                            <i class="fas fa-file-invoice"></i>
                        </span>
                    </div>
                    <div class="col">
                        <div class="font-weight-medium"><?php echo number_format($stats['total_cheques']); ?></div>
                        <div class="text-muted">Total Cheques</div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-sm-6 col-lg-3">
        <div class="card card-sm">
            <div class="card-body">
                <div class="row align-items-center">
                    <div class="col-auto">
                        <span class="bg-warning text-white avatar">
                            <i class="fas fa-clock"></i>
                        </span>
                    </div>
                    <div class="col">
                        <div class="font-weight-medium"><?php echo number_format($stats['pending_cheques']); ?></div>
                        <div class="text-muted">Pending</div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-sm-6 col-lg-3">
        <div class="card card-sm">
            <div class="card-body">
                <div class="row align-items-center">
                    <div class="col-auto">
                        <span class="bg-success text-white avatar">
                            <i class="fas fa-check-circle"></i>
                        </span>
                    </div>
                    <div class="col">
                        <div class="font-weight-medium"><?php echo number_format($stats['cleared_cheques']); ?></div>
                        <div class="text-muted">Cleared</div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-sm-6 col-lg-3">
        <div class="card card-sm">
            <div class="card-body">
                <div class="row align-items-center">
                    <div class="col-auto">
                        <span class="bg-info text-white avatar">
                            <i class="fas fa-dollar-sign"></i>
                        </span>
                    </div>
                    <div class="col">
                        <div class="font-weight-medium">$<?php echo number_format(floatval($stats['total_cleared_amount'] ?? 0), 2); ?></div>
                        <div class="text-muted">Cleared Amount</div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Filters Card -->
<div class="row row-cards mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h3 class="card-title">
                    <i class="fas fa-filter me-2"></i>
                    Cheque Filters
                </h3>
                <div class="card-actions">
                    <a href="cheque-deposits.php" class="btn btn-outline-secondary btn-sm">
                        <i class="fas fa-times me-2"></i>
                        Clear Filters
                    </a>
                    <button type="button" class="btn btn-success btn-sm" onclick="showAddChequeForm()">
                        <i class="fas fa-plus me-2"></i>
                        Add Cheque Deposit
                    </button>
                </div>
            </div>
            <div class="card-body">
                <form method="GET" action="" class="row g-2">
                    <div class="col-md-2">
                        <label class="form-label">Status</label>
                        <select name="status" class="form-select form-select-sm">
                            <option value="">All Status</option>
                            <option value="pending" <?php echo $filter_status === 'pending' ? 'selected' : ''; ?>>Pending</option>
                            <option value="processing" <?php echo $filter_status === 'processing' ? 'selected' : ''; ?>>Processing</option>
                            <option value="cleared" <?php echo $filter_status === 'cleared' ? 'selected' : ''; ?>>Cleared</option>
                            <option value="rejected" <?php echo $filter_status === 'rejected' ? 'selected' : ''; ?>>Rejected</option>
                        </select>
                    </div>

                    <div class="col-md-2">
                        <label class="form-label">User</label>
                        <input type="text" name="user" class="form-control form-control-sm" placeholder="Search user..." value="<?php echo htmlspecialchars($filter_user); ?>">
                    </div>

                    <div class="col-md-2">
                        <label class="form-label">Date From</label>
                        <input type="date" name="date_from" class="form-control form-control-sm" value="<?php echo htmlspecialchars($filter_date_from); ?>">
                    </div>

                    <div class="col-md-2">
                        <label class="form-label">Date To</label>
                        <input type="date" name="date_to" class="form-control form-control-sm" value="<?php echo htmlspecialchars($filter_date_to); ?>">
                    </div>

                    <div class="col-md-2">
                        <label class="form-label">Amount Range</label>
                        <div class="input-group input-group-sm">
                            <input type="number" name="amount_min" class="form-control" step="0.01" placeholder="Min" value="<?php echo $filter_amount_min > 0 ? $filter_amount_min : ''; ?>">
                            <span class="input-group-text">-</span>
                            <input type="number" name="amount_max" class="form-control" step="0.01" placeholder="Max" value="<?php echo $filter_amount_max > 0 ? $filter_amount_max : ''; ?>">
                        </div>
                    </div>

                    <div class="col-md-2">
                        <label class="form-label">&nbsp;</label>
                        <div class="btn-group d-block" role="group">
                            <button type="submit" class="btn btn-primary btn-sm">
                                <i class="fas fa-search me-1"></i>
                                Filter
                            </button>
                            <a href="cheque-deposits.php" class="btn btn-outline-secondary btn-sm">
                                <i class="fas fa-times me-1"></i>
                                Clear
                            </a>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- Cheque Deposits Table -->
<div class="row row-cards">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h3 class="card-title">
                    <i class="fas fa-file-invoice me-2"></i>
                    Cheque Deposits
                    <span class="badge bg-secondary ms-2"><?php echo number_format($total_records); ?> total</span>
                </h3>
                <div class="card-actions">
                    <span class="text-muted">
                        Showing <?php echo number_format(($current_page - 1) * $records_per_page + 1); ?> -
                        <?php echo number_format(min($current_page * $records_per_page, $total_records)); ?>
                        of <?php echo number_format($total_records); ?> cheques
                    </span>
                </div>
            </div>
            <div class="card-body p-0">
                <?php if (!empty($cheques)): ?>
                <div class="table-responsive">
                    <table class="table table-vcenter card-table table-sm">
                        <thead>
                            <tr>
                                <th class="w-1">#</th>
                                <th>Cheque Details</th>
                                <th>Account Holder</th>
                                <th>Amount</th>
                                <th>Sender</th>
                                <th>Status</th>
                                <th>Deposit Date</th>
                                <th class="w-1">Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php
                            $row_number = ($current_page - 1) * $records_per_page + 1;
                            foreach ($cheques as $cheque):
                            ?>
                            <tr>
                                <!-- Row Number -->
                                <td>
                                    <span class="text-muted"><?php echo $row_number++; ?></span>
                                </td>

                                <!-- Cheque Details -->
                                <td>
                                    <div class="d-flex flex-column">
                                        <div class="fw-bold">Cheque #<?php echo htmlspecialchars($cheque['cheque_number']); ?></div>
                                        <small class="text-muted"><?php echo htmlspecialchars($cheque['bank_name']); ?></small>
                                        <small class="text-muted">Branch: <?php echo htmlspecialchars($cheque['branch_code']); ?></small>
                                    </div>
                                </td>

                                <!-- Account Holder -->
                                <td>
                                    <div class="d-flex align-items-center">
                                        <div class="avatar avatar-xs me-2">
                                            <?php echo strtoupper(substr($cheque['first_name'] ?? 'U', 0, 1) . substr($cheque['last_name'] ?? 'U', 0, 1)); ?>
                                        </div>
                                        <div>
                                            <div class="fw-bold" style="font-size: 0.85rem;"><?php echo htmlspecialchars(($cheque['first_name'] ?? 'Unknown') . ' ' . ($cheque['last_name'] ?? 'User')); ?></div>
                                            <small class="text-muted">@<?php echo htmlspecialchars($cheque['username'] ?? 'unknown'); ?></small>
                                        </div>
                                    </div>
                                </td>

                                <!-- Amount -->
                                <td>
                                    <span class="fw-bold text-primary">
                                        <?php echo number_format($cheque['amount'], 2); ?> <?php echo $cheque['currency']; ?>
                                    </span>
                                </td>

                                <!-- Sender -->
                                <td>
                                    <div class="d-flex flex-column">
                                        <div class="fw-bold" style="font-size: 0.85rem;"><?php echo htmlspecialchars($cheque['sender_name']); ?></div>
                                        <small class="text-muted">ID: <?php echo htmlspecialchars($cheque['id_passport_number']); ?></small>
                                    </div>
                                </td>

                                <!-- Status -->
                                <td>
                                    <?php
                                    $status_colors = [
                                        'pending' => 'warning',
                                        'processing' => 'info',
                                        'cleared' => 'success',
                                        'rejected' => 'danger'
                                    ];
                                    $status_color = $status_colors[$cheque['clearance_status']] ?? 'secondary';
                                    ?>
                                    <span class="badge bg-<?php echo $status_color; ?> badge-sm">
                                        <?php echo ucfirst($cheque['clearance_status']); ?>
                                    </span>
                                </td>

                                <!-- Deposit Date -->
                                <td>
                                    <div class="d-flex flex-column">
                                        <div class="text-muted" style="font-size: 0.85rem;"><?php echo date('M j, Y', strtotime($cheque['deposit_date'])); ?></div>
                                        <small class="text-muted"><?php echo htmlspecialchars($cheque['account_type']); ?></small>
                                    </div>
                                </td>

                                <!-- Actions -->
                                <td>
                                    <div class="btn-list">
                                        <button type="button" class="btn btn-sm btn-outline-primary" onclick="viewChequeDetails('<?php echo $cheque['id']; ?>')" title="View Details">
                                            <i class="fas fa-eye"></i>
                                        </button>
                                                        <div class="dropdown">
                                            <button class="btn btn-sm btn-outline-secondary dropdown-toggle" type="button" data-bs-toggle="dropdown" aria-expanded="false">
                                                <i class="fas fa-edit"></i>
                                            </button>
                                            <ul class="dropdown-menu">
                                                <?php if ($cheque['clearance_status'] !== 'pending'): ?>
                                                <li><a class="dropdown-item" href="#" onclick="updateChequeStatus(<?php echo $cheque['id']; ?>, 'pending')">
                                                    <i class="fas fa-clock text-warning me-2"></i>Mark as Pending
                                                </a></li>
                                                <?php endif; ?>

                                                <?php if ($cheque['clearance_status'] !== 'processing'): ?>
                                                <li><a class="dropdown-item" href="#" onclick="updateChequeStatus(<?php echo $cheque['id']; ?>, 'processing')">
                                                    <i class="fas fa-spinner text-info me-2"></i>Mark as Processing
                                                </a></li>
                                                <?php endif; ?>

                                                <?php if ($cheque['clearance_status'] !== 'cleared'): ?>
                                                <li><a class="dropdown-item" href="#" onclick="updateChequeStatus(<?php echo $cheque['id']; ?>, 'cleared')">
                                                    <i class="fas fa-check text-success me-2"></i>Mark as Cleared
                                                </a></li>
                                                <?php endif; ?>

                                                <?php if ($cheque['clearance_status'] !== 'rejected'): ?>
                                                <li><a class="dropdown-item" href="#" onclick="updateChequeStatus(<?php echo $cheque['id']; ?>, 'rejected')">
                                                    <i class="fas fa-times text-danger me-2"></i>Mark as Rejected
                                                </a></li>
                                                <?php endif; ?>

                                                <?php if ($cheque['clearance_status'] !== 'returned'): ?>
                                                <li><a class="dropdown-item" href="#" onclick="updateChequeStatus(<?php echo $cheque['id']; ?>, 'returned')">
                                                    <i class="fas fa-undo text-warning me-2"></i>Mark as Returned
                                                </a></li>
                                                <?php endif; ?>

                                                <?php if ($cheque['clearance_status'] !== 'bounced'): ?>
                                                <li><a class="dropdown-item" href="#" onclick="updateChequeStatus(<?php echo $cheque['id']; ?>, 'bounced')">
                                                    <i class="fas fa-exclamation-triangle text-danger me-2"></i>Mark as Bounced
                                                </a></li>
                                                <?php endif; ?>
                                            </ul>
                                        </div>
                                    </div>
                                </td>
                            </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
                <?php else: ?>
                <div class="empty">
                    <div class="empty-img">
                        <i class="fas fa-file-invoice" style="font-size: 3rem; color: #6c757d;"></i>
                    </div>
                    <p class="empty-title">No cheque deposits found</p>
                    <p class="empty-subtitle text-muted">
                        <?php if (!empty(array_filter([$filter_status, $filter_user, $filter_date_from, $filter_date_to]))): ?>
                        Try adjusting your filters to see more results.
                        <?php else: ?>
                        No cheque deposits have been recorded yet.
                        <?php endif; ?>
                    </p>
                    <div class="empty-action">
                        <button type="button" class="btn btn-primary" onclick="showAddChequeForm()">
                            <i class="fas fa-plus me-2"></i>
                            Add First Cheque Deposit
                        </button>
                    </div>
                </div>
                <?php endif; ?>

                <!-- Pagination -->
                <?php if ($total_pages > 1): ?>
                <div class="card-footer d-flex align-items-center">
                    <p class="m-0 text-muted">
                        Showing <span><?php echo number_format(($current_page - 1) * $records_per_page + 1); ?></span>
                        to <span><?php echo number_format(min($current_page * $records_per_page, $total_records)); ?></span>
                        of <span><?php echo number_format($total_records); ?></span> entries
                    </p>
                    <ul class="pagination m-0 ms-auto">
                        <?php if ($current_page > 1): ?>
                        <li class="page-item">
                            <a class="page-link" href="?page=<?php echo $current_page - 1; ?><?php echo !empty($_GET) ? '&' . http_build_query(array_diff_key($_GET, ['page' => ''])) : ''; ?>">
                                <i class="fas fa-chevron-left"></i> prev
                            </a>
                        </li>
                        <?php endif; ?>

                        <?php
                        $start_page = max(1, $current_page - 2);
                        $end_page = min($total_pages, $current_page + 2);

                        for ($i = $start_page; $i <= $end_page; $i++):
                        ?>
                        <li class="page-item <?php echo $i == $current_page ? 'active' : ''; ?>">
                            <a class="page-link" href="?page=<?php echo $i; ?><?php echo !empty($_GET) ? '&' . http_build_query(array_diff_key($_GET, ['page' => ''])) : ''; ?>">
                                <?php echo $i; ?>
                            </a>
                        </li>
                        <?php endfor; ?>

                        <?php if ($current_page < $total_pages): ?>
                        <li class="page-item">
                            <a class="page-link" href="?page=<?php echo $current_page + 1; ?><?php echo !empty($_GET) ? '&' . http_build_query(array_diff_key($_GET, ['page' => ''])) : ''; ?>">
                                next <i class="fas fa-chevron-right"></i>
                            </a>
                        </li>
                        <?php endif; ?>
                    </ul>
                </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<!-- Add Cheque Modal -->
<div class="modal modal-blur fade" id="addChequeModal" tabindex="-1" role="dialog" aria-hidden="true">
    <div class="modal-dialog modal-lg modal-dialog-centered" role="document">
        <div class="modal-content">
            <form method="POST" action="" enctype="multipart/form-data">
                <div class="modal-header">
                    <h5 class="modal-title">Add Cheque Deposit</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <input type="hidden" name="action" value="add_cheque">

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">Select User <span class="text-danger">*</span></label>
                                <select name="account_id" class="form-select" required>
                                    <option value="">Choose a user...</option>
                                    <?php foreach ($users as $user): ?>
                                    <option value="<?php echo $user['id']; ?>">
                                        <?php echo htmlspecialchars($user['first_name'] . ' ' . $user['last_name']); ?>
                                        (@<?php echo htmlspecialchars($user['username']); ?>) -
                                        <?php echo htmlspecialchars($user['account_number']); ?>
                                    </option>
                                    <?php endforeach; ?>
                                </select>
                            </div>
                        </div>

                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">Cheque Number <span class="text-danger">*</span></label>
                                <input type="text" name="cheque_number" class="form-control"
                                       placeholder="e.g., CHQ001234" required>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">Deposit Date <span class="text-danger">*</span></label>
                                <input type="date" name="deposit_date" class="form-control"
                                       value="<?php echo date('Y-m-d'); ?>" required>
                            </div>
                        </div>

                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">Sender Name <span class="text-danger">*</span></label>
                                <input type="text" name="sender_name" class="form-control"
                                       placeholder="e.g., John Smith" required>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">Amount <span class="text-danger">*</span></label>
                                <div class="input-group">
                                    <span class="input-group-text">$</span>
                                    <input type="number" name="amount" class="form-control"
                                           step="0.01" min="0.01" placeholder="0.00" required>
                                </div>
                            </div>
                        </div>

                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">Currency <span class="text-danger">*</span></label>
                                <select name="currency" class="form-select" required>
                                    <option value="USD">USD - US Dollar</option>
                                    <option value="EUR">EUR - Euro</option>
                                    <option value="GBP">GBP - British Pound</option>
                                    <option value="CAD">CAD - Canadian Dollar</option>
                                    <option value="AUD">AUD - Australian Dollar</option>
                                    <option value="JPY">JPY - Japanese Yen</option>
                                </select>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">Account Type <span class="text-danger">*</span></label>
                                <select name="account_type" class="form-select" required>
                                    <option value="">Select account type...</option>
                                    <option value="Checking">Checking Account</option>
                                    <option value="Savings">Savings Account</option>
                                    <option value="Business">Business Account</option>
                                    <option value="Joint">Joint Account</option>
                                </select>
                            </div>
                        </div>

                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">Branch Code <span class="text-danger">*</span></label>
                                <input type="text" name="branch_code" class="form-control"
                                       placeholder="e.g., BR001" required>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">ID/Passport Number <span class="text-danger">*</span></label>
                                <input type="text" name="id_passport_number" class="form-control"
                                       placeholder="e.g., ID123456789" required>
                            </div>
                        </div>

                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">Bank Name <span class="text-danger">*</span></label>
                                <input type="text" name="bank_name" class="form-control"
                                       placeholder="e.g., First National Bank" required>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">Clearance Status</label>
                                <select name="clearance_status" class="form-select">
                                    <option value="pending">Pending</option>
                                    <option value="processing">Processing</option>
                                    <option value="cleared">Cleared</option>
                                    <option value="rejected">Rejected</option>
                                </select>
                            </div>
                        </div>

                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">Cheque Image <span class="text-danger">*</span></label>
                                <input type="file" name="cheque_image" class="form-control"
                                       accept=".jpg,.jpeg,.png,.gif,.pdf" required>
                                <small class="form-hint">Accepted formats: JPG, PNG, GIF, PDF (Max 5MB)</small>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save me-2"></i>
                        Add Cheque Deposit
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Update Status Modal -->
<div class="modal modal-blur fade" id="updateStatusModal" tabindex="-1" role="dialog" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered" role="document">
        <div class="modal-content">
            <form method="POST" action="">
                <div class="modal-header">
                    <h5 class="modal-title">Update Cheque Status</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <input type="hidden" name="action" value="update_status">
                    <input type="hidden" name="cheque_id" id="updateChequeId">
                    <input type="hidden" name="new_status" id="updateNewStatus">

                    <div class="mb-3">
                        <label class="form-label">New Status</label>
                        <div class="form-control-plaintext" id="updateStatusDisplay"></div>
                    </div>

                    <div class="mb-3">
                        <label class="form-label">Reason/Notes</label>
                        <textarea name="status_reason" class="form-control" rows="3"
                                  placeholder="Enter reason for status change (optional)..."></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn" id="updateStatusBtn">Update Status</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Cheque Details Modal -->
<div class="modal modal-blur fade" id="chequeDetailsModal" tabindex="-1" role="dialog" aria-hidden="true">
    <div class="modal-dialog modal-lg modal-dialog-centered" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Cheque Deposit Details</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body" id="chequeDetailsContent">
                <!-- Cheque details will be loaded here -->
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
            </div>
        </div>
    </div>
</div>

<script>
function showAddChequeForm() {
    $('#addChequeModal').modal('show');
}

function viewChequeDetails(chequeId) {
    // Load cheque details via AJAX
    fetch('ajax/get-cheque-details.php?id=' + chequeId)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                displayChequeDetails(data.cheque);
            } else {
                alert('Error loading cheque details: ' + data.message);
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('An error occurred while loading cheque details');
        });
}

function displayChequeDetails(cheque) {
    const modalContent = `
        <div class="row">
            <div class="col-md-6">
                <h5>Cheque Information</h5>
                <dl class="row">
                    <dt class="col-sm-5">Cheque Number:</dt>
                    <dd class="col-sm-7">${cheque.cheque_number}</dd>

                    <dt class="col-sm-5">Amount:</dt>
                    <dd class="col-sm-7"><strong>${parseFloat(cheque.amount).toLocaleString()} ${cheque.currency}</strong></dd>

                    <dt class="col-sm-5">Deposit Date:</dt>
                    <dd class="col-sm-7">${formatDate(cheque.deposit_date)}</dd>

                    <dt class="col-sm-5">Bank:</dt>
                    <dd class="col-sm-7">${cheque.bank_name}</dd>

                    <dt class="col-sm-5">Branch Code:</dt>
                    <dd class="col-sm-7">${cheque.branch_code}</dd>

                    <dt class="col-sm-5">Status:</dt>
                    <dd class="col-sm-7">
                        <span class="badge bg-${getChequeStatusColor(cheque.clearance_status)}">${cheque.clearance_status.charAt(0).toUpperCase() + cheque.clearance_status.slice(1)}</span>
                    </dd>
                </dl>
            </div>
            <div class="col-md-6">
                <h5>Sender Information</h5>
                <dl class="row">
                    <dt class="col-sm-5">Sender Name:</dt>
                    <dd class="col-sm-7">${cheque.sender_name}</dd>

                    <dt class="col-sm-5">ID/Passport:</dt>
                    <dd class="col-sm-7">${cheque.id_passport_number}</dd>

                    <dt class="col-sm-5">Account Type:</dt>
                    <dd class="col-sm-7">${cheque.account_type}</dd>
                </dl>

                <h5>Account Holder</h5>
                <dl class="row">
                    <dt class="col-sm-5">Name:</dt>
                    <dd class="col-sm-7">${cheque.first_name} ${cheque.last_name}</dd>

                    <dt class="col-sm-5">Username:</dt>
                    <dd class="col-sm-7">@${cheque.username}</dd>

                    <dt class="col-sm-5">Account:</dt>
                    <dd class="col-sm-7">${cheque.account_number}</dd>
                </dl>
            </div>
        </div>

        ${cheque.status_reason ? `
        <div class="mt-3">
            <h5>Status Notes</h5>
            <div class="alert alert-info">
                ${cheque.status_reason}
            </div>
        </div>
        ` : ''}

        <div class="mt-3">
            <h5>Cheque Image</h5>
            <div class="text-center">
                ${cheque.cheque_image_path ? `
                <img src="${cheque.cheque_image_path}" alt="Cheque Image" class="img-fluid" style="max-height: 400px; border: 1px solid #ddd; border-radius: 8px;">
                <br><br>
                <a href="${cheque.cheque_image_path}" target="_blank" class="btn btn-outline-primary btn-sm">
                    <i class="fas fa-external-link-alt me-1"></i>
                    View Full Size
                </a>
                ` : '<p class="text-muted">No image available</p>'}
            </div>
        </div>
    `;

    document.getElementById('chequeDetailsContent').innerHTML = modalContent;
    $('#chequeDetailsModal').modal('show');
}

function updateChequeStatus(chequeId, newStatus) {
    document.getElementById('updateChequeId').value = chequeId;
    document.getElementById('updateNewStatus').value = newStatus;

    const statusDisplay = newStatus.charAt(0).toUpperCase() + newStatus.slice(1);
    const statusColor = getChequeStatusColor(newStatus);

    document.getElementById('updateStatusDisplay').innerHTML = `<span class="badge bg-${statusColor}">${statusDisplay}</span>`;

    const updateBtn = document.getElementById('updateStatusBtn');
    updateBtn.className = `btn btn-${statusColor}`;
    updateBtn.textContent = `Mark as ${statusDisplay}`;

    $('#updateStatusModal').modal('show');
}

function getChequeStatusColor(status) {
    const colors = {
        'pending': 'warning',
        'processing': 'info',
        'cleared': 'success',
        'rejected': 'danger',
        'returned': 'warning',
        'bounced': 'danger'
    };
    return colors[status] || 'secondary';
}

function formatDate(dateString) {
    return new Date(dateString).toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'short',
        day: 'numeric'
    });
}

function formatTime(dateString) {
    return new Date(dateString).toLocaleTimeString('en-US', {
        hour: 'numeric',
        minute: '2-digit',
        hour12: true
    });
}
</script>

<?php include 'includes/admin-footer.php'; ?>
