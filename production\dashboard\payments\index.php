<?php
// Set page variables
$page_title = 'Bill Payments';
$additional_css = ['dashboard.css'];
$additional_js = ['dashboard.js'];

// Include header template
require_once '../../templates/user/header.php';

// Include database connection
require_once '../../config/config.php';
requireLogin();

// Handle payment form submission
$success_message = '';
$error_message = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    try {
        $db = getDB();
        $user_id = $_SESSION['user_id'];

        $biller_name = sanitizeInput($_POST['biller_name']);
        $account_number = sanitizeInput($_POST['account_number']);
        $amount = floatval($_POST['amount']);
        $payment_type = sanitizeInput($_POST['payment_type']);
        $description = sanitizeInput($_POST['description'] ?? '');

        // Validation
        if (empty($biller_name) || empty($account_number) || $amount <= 0) {
            throw new Exception('Please provide valid biller information and amount.');
        }

        // Check user balance
        $balance_sql = "SELECT balance FROM accounts WHERE id = ?";
        $balance_result = $db->query($balance_sql, [$user_id]);
        $user_balance = $balance_result->fetch_assoc()['balance'];

        if ($amount > $user_balance) {
            throw new Exception('Insufficient balance for this payment.');
        }

        // Create payment record
        $payment_sql = "INSERT INTO bill_payments (user_id, biller_name, account_number, amount, payment_type, description, status)
                       VALUES (?, ?, ?, ?, ?, ?, 'pending')";
        $result = $db->query($payment_sql, [$user_id, $biller_name, $account_number, $amount, $payment_type, $description]);

        if ($result) {
            $success_message = 'Bill payment scheduled successfully! It will be processed shortly.';
        } else {
            throw new Exception('Failed to schedule payment.');
        }

    } catch (Exception $e) {
        $error_message = $e->getMessage();
    }
}

// Get user balance
try {
    $db = getDB();
    $user_id = $_SESSION['user_id'];

    $balance_sql = "SELECT balance, currency FROM accounts WHERE id = ?";
    $balance_result = $db->query($balance_sql, [$user_id]);
    $account_info = $balance_result->fetch_assoc();

} catch (Exception $e) {
    error_log("Payments page error: " . $e->getMessage());
    $account_info = ['balance' => 0, 'currency' => 'USD'];
}

?>

<!-- Include Sidebar -->
<?php require_once '../../templates/user/sidebar.php'; ?>
?>

<div class="main-content">
    <div class="banking-container">
        <div class="banking-dashboard">
            <!-- Page Header -->
            <div class="page-header">
                <div class="page-title">
                    <h1>Bill Payments</h1>
                    <p>Pay your bills quickly and securely</p>
                </div>
                <div class="page-actions">
                    <a href="../transactions/" class="btn-outline">
                        <svg width="16" height="16" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M3 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1z" clip-rule="evenodd"/>
                        </svg>
                        Payment History
                    </a>
                    <a href="../transfers/" class="btn-primary">
                        <svg width="16" height="16" fill="currentColor" viewBox="0 0 20 20">
                            <path d="M8 5a1 1 0 100 2h5.586l-1.293 1.293a1 1 0 001.414 1.414l3-3a1 1 0 000-1.414l-3-3a1 1 0 10-1.414 1.414L13.586 5H8z"/>
                        </svg>
                        Transfer Money
                    </a>
                </div>
            </div>

            <?php if ($success_message): ?>
                <div class="alert alert-success">
                    <svg width="20" height="20" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"/>
                    </svg>
                    <?php echo htmlspecialchars($success_message); ?>
                </div>
            <?php endif; ?>

            <?php if ($error_message): ?>
                <div class="alert alert-error">
                    <svg width="20" height="20" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clip-rule="evenodd"/>
                    </svg>
                    <?php echo htmlspecialchars($error_message); ?>
                </div>
            <?php endif; ?>

            <!-- Payment Form -->
            <div class="dashboard-grid">
                <div class="main-section">
                    <div class="form-card">
                        <div class="form-header">
                            <h3>Pay Bills</h3>
                            <p>Schedule payments for utilities and services</p>
                        </div>
                        <form method="POST" style="padding: 2rem;">
                            <div class="form-group">
                                <label for="payment_type">Payment Category</label>
                                <select id="payment_type" name="payment_type" class="form-control" required>
                                    <option value="">Select payment type</option>
                                    <option value="utilities">Utilities</option>
                                    <option value="telecom">Telecom</option>
                                    <option value="insurance">Insurance</option>
                                    <option value="credit_card">Credit Card</option>
                                    <option value="loan">Loan Payment</option>
                                    <option value="other">Other</option>
                                </select>
                            </div>

                            <div class="form-group">
                                <label for="biller_name">Biller Name</label>
                                <input type="text" id="biller_name" name="biller_name" class="form-control"
                                       placeholder="Enter biller name" required>
                            </div>

                            <div class="form-group">
                                <label for="account_number">Account/Reference Number</label>
                                <input type="text" id="account_number" name="account_number" class="form-control"
                                       placeholder="Enter account or reference number" required>
                            </div>

                            <div class="form-group">
                                <label for="amount">Amount</label>
                                <div class="amount-input">
                                    <span class="currency-symbol"><?php echo $account_info['currency'] ?? 'USD'; ?></span>
                                    <input type="number" id="amount" name="amount" class="form-control"
                                           placeholder="0.00" step="0.01" min="0.01" required>
                                </div>
                                <small style="color: #6b7280; font-size: 0.875rem;">
                                    Available balance: <?php echo formatCurrency($account_info['balance'], $account_info['currency'] ?? 'USD'); ?>
                                </small>
                            </div>

                            <div class="form-group">
                                <label for="description">Description (Optional)</label>
                                <textarea id="description" name="description" class="form-control"
                                          placeholder="Enter payment description" rows="3"></textarea>
                            </div>

                            <div class="form-actions">
                                <button type="button" class="btn-outline" onclick="history.back()">Cancel</button>
                                <button type="submit" class="btn-primary">
                                    <svg width="16" height="16" fill="currentColor" viewBox="0 0 20 20">
                                        <path fill-rule="evenodd" d="M4 2a1 1 0 011 1v2.101a7.002 7.002 0 0111.601 2.566 1 1 0 11-1.885.666A5.002 5.002 0 005.999 7H9a1 1 0 010 2H4a1 1 0 01-1-1V3a1 1 0 011-1zm.008 9.057a1 1 0 011.276.61A5.002 5.002 0 0014.001 13H11a1 1 0 110-2h5a1 1 0 011 1v5a1 1 0 11-2 0v-2.101a7.002 7.002 0 01-11.601-2.566 1 1 0 01.61-1.276z" clip-rule="evenodd"/>
                                    </svg>
                                    Schedule Payment
                                </button>
                            </div>
                        </form>
                    </div>
                </div>

                <div class="sidebar-section">
                    <!-- Quick Bill Categories -->
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">Quick Categories</h3>
                        </div>
                        <div style="padding: 1.5rem;">
                            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 1rem;">
                                <button class="quick-category-btn" onclick="selectCategory('utilities', 'Utilities')">
                                    <svg width="24" height="24" fill="#f59e0b" viewBox="0 0 20 20">
                                        <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
                                    </svg>
                                    <span>Utilities</span>
                                </button>
                                <button class="quick-category-btn" onclick="selectCategory('telecom', 'Telecom')">
                                    <svg width="24" height="24" fill="#8b5cf6" viewBox="0 0 20 20">
                                        <path d="M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zM3 10a1 1 0 011-1h6a1 1 0 011 1v6a1 1 0 01-1 1H4a1 1 0 01-1-1v-6zM14 9a1 1 0 00-1 1v6a1 1 0 001 1h2a1 1 0 001-1v-6a1 1 0 00-1-1h-2z"/>
                                    </svg>
                                    <span>Telecom</span>
                                </button>
                                <button class="quick-category-btn" onclick="selectCategory('insurance', 'Insurance')">
                                    <svg width="24" height="24" fill="#10b981" viewBox="0 0 20 20">
                                        <path fill-rule="evenodd" d="M18 8a6 6 0 01-7.743 5.743L10 14l-1 1-1 1H6v2H2v-4l4.257-4.257A6 6 0 1118 8zm-6-4a1 1 0 100 2 2 2 0 012 2 1 1 0 102 0 4 4 0 00-4-4z" clip-rule="evenodd"/>
                                    </svg>
                                    <span>Insurance</span>
                                </button>
                                <button class="quick-category-btn" onclick="selectCategory('credit_card', 'Credit Card')">
                                    <svg width="24" height="24" fill="#ef4444" viewBox="0 0 20 20">
                                        <path fill-rule="evenodd" d="M4 4a2 2 0 00-2 2v8a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2H4zm0 2h12v2H4V6zm0 4h12v4H4v-4z" clip-rule="evenodd"/>
                                    </svg>
                                    <span>Credit Card</span>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.quick-category-btn {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 0.5rem;
    padding: 1rem;
    background: #f8fafc;
    border: 1px solid #e5e7eb;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 0.875rem;
    font-weight: 500;
}

.quick-category-btn:hover {
    background: white;
    border-color: #6366f1;
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}
</style>

<script>
function selectCategory(value, name) {
    document.getElementById('payment_type').value = value;
    document.getElementById('biller_name').focus();
}
</script>

<?php include '../../includes/dashboard/footer.php'; ?>