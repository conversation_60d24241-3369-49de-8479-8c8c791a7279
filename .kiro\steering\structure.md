# Project Structure

## Root Level Organization

### Entry Points
- `index.php` - Main application entry, redirects based on login status
- `login.php` - User login page
- `register.php` - User registration
- `logout.php` - Session cleanup and logout

### Core Directories

#### `/admin/` - Administrative Interface
- `login.php` - Admin authentication
- `index.php` - Admin dashboard
- `users.php` - User management
- `transactions.php` - Transaction oversight
- `settings/` - Admin configuration panels
- `ajax/` - AJAX endpoints for admin operations

#### `/super-admin/` - System Administration
- `login.php` - Super admin authentication
- `dashboard.php` - System overview
- `system-settings.php` - Global configuration
- `email-templates.php` - Email template management
- `security.php` - Security settings
- `2fa-setup.php` - Two-factor authentication setup

#### `/dashboard/` - User Interface
- `index.php` - User dashboard
- `cards.php` - Virtual card management
- `payments.php` - Payment processing
- `transfers/` - Money transfer functionality
- `statements/` - Account statements
- `security/` - User security settings

#### `/config/` - Configuration Layer
- `config.php` - Main application configuration
- `database.php` - Database connection and wrapper
- `email.php` - Email/SMTP configuration
- `*Manager.php` - Specialized service classes

#### `/auth/` - Authentication System
- `includes/` - Modular login components
- `verify-otp.php` - OTP verification
- `styles/` - Authentication-specific CSS

### Data Directories

#### `/database/` - Database Schema
- `schema.sql` - Main database structure
- `create_*.sql` - Individual table creation scripts
- `add_*.sql` - Database migration scripts

#### `/assets/` - Static Resources
- `css/` - Stylesheets organized by section
- `js/` - JavaScript files
- `img/` - Images and icons
- `uploads/` - User-uploaded files

#### `/uploads/` - File Storage
- `documents/` - KYC and verification documents
- `cheques/` - Cheque deposit images
- `temp/` - Temporary file storage

### Utility Directories

#### `/includes/` - Shared Components
- `header.php`, `footer.php` - Common page elements
- `sidebar.php` - Navigation components
- `components/` - Reusable UI components
- `dashboard/` - Dashboard-specific includes

#### `/logs/` - Application Logging
- `error.log` - PHP and application errors
- `audit.log` - Security and user activity
- `email_*.log` - Email delivery logs

#### `/vendor/` - Composer Dependencies
- Auto-generated by Composer
- Contains PHPMailer and Google2FA libraries

## File Naming Conventions

### PHP Files
- **Pages**: `kebab-case.php` (e.g., `user-management.php`)
- **Classes**: `PascalCase.php` (e.g., `EmailManager.php`)
- **Includes**: `snake_case.php` (e.g., `login_header.php`)

### Database Files
- **Schema**: `schema.sql`
- **Tables**: `create_table_name.sql`
- **Migrations**: `add_field_name.sql`

### Assets
- **CSS**: `section-name.css` or `component.css`
- **JS**: `functionality.js` or `page-script.js`
- **Images**: Descriptive names with proper extensions

## Module Organization

### Authentication Flow
1. `login.php` → `auth/includes/login_logic.php`
2. OTP verification → `auth/verify-otp.php`
3. Session management → `config/SessionManager.php`

### Admin Hierarchy
- **User Level**: `/dashboard/` - Personal banking features
- **Admin Level**: `/admin/` - User and transaction management
- **Super Admin**: `/super-admin/` - System configuration

### Security Layer
- Input validation: `config/InputValidator.php`
- Audit logging: `config/AuditLogger.php`
- Session handling: `config/SessionManager.php`
- Error handling: `config/ErrorHandler.php`

## Development vs Production

### Development Structure
- Includes `/test/` directory with testing utilities
- Debug files (`debug_*.php`) for troubleshooting
- Setup scripts (`setup_*.php`) for database initialization

### Production Structure
- `/production/` contains clean deployment-ready code
- Excludes test files, debug scripts, and development tools
- Optimized autoloader and compressed assets

## Key Architectural Patterns

### Configuration Centralization
All configuration in `/config/` with environment-specific overrides

### Modular Authentication
Authentication logic separated into reusable components in `/auth/includes/`

### Three-Tier Administration
Clear separation between user, admin, and super-admin functionality

### Security-First Design
Audit logging, input validation, and session management built into core architecture