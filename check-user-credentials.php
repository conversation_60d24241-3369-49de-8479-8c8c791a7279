<?php
require_once 'config/config.php';

echo "<h2>User Credentials Check</h2>";

try {
    $db = getDB();
    
    // Get user details for users with virtual cards
    $users_query = "SELECT DISTINCT a.id, a.username, a.first_name, a.last_name, a.email, a.balance
                    FROM accounts a 
                    INNER JOIN virtual_cards vc ON a.id = vc.account_id 
                    WHERE vc.card_balance > 0
                    ORDER BY a.id";
    $users_result = $db->query($users_query);
    
    if ($users_result && $users_result->num_rows > 0) {
        echo "<table border='1' style='border-collapse: collapse;'>";
        echo "<tr><th>ID</th><th>Username</th><th>Name</th><th>Email</th><th>Balance</th></tr>";
        while ($user = $users_result->fetch_assoc()) {
            echo "<tr>";
            echo "<td>" . $user['id'] . "</td>";
            echo "<td>" . $user['username'] . "</td>";
            echo "<td>" . $user['first_name'] . " " . $user['last_name'] . "</td>";
            echo "<td>" . $user['email'] . "</td>";
            echo "<td>$" . number_format($user['balance'], 2) . "</td>";
            echo "</tr>";
        }
        echo "</table>";
        
        echo "<p><strong>Note:</strong> You can try logging in with username 'testuser' and a common password like 'password', 'test123', or 'testuser'.</p>";
    } else {
        echo "No users found with virtual cards.";
    }
    
} catch (Exception $e) {
    echo "Error: " . $e->getMessage();
}
?>
