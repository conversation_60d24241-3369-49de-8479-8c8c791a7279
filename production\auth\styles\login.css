/**
 * Modern User Login Page Styles
 * Clean, professional design matching admin login quality
 */

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    background: #f8f9fa;
    min-height: 100vh;
    margin: 0;
    padding: 0;
}

.container-fluid {
    min-height: 100vh;
    padding: 0;
}

.row {
    min-height: 100vh;
    margin: 0;
}

/* Left Panel - Login Form */
.left-panel {
    background: white;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: flex-start;
    padding: 60px 80px;
    position: relative;
}

.back-link {
    position: absolute;
    top: 20px;
    left: 20px;
    color: #6b7280;
    text-decoration: none;
    font-size: 14px;
    font-weight: 500;
    display: flex;
    align-items: center;
    gap: 8px;
    transition: color 0.2s ease;
}

.back-link:hover {
    color: #4f46e5;
}

.logo {
    margin-bottom: 40px;
    text-align: left;
    width: 100%;
}

.logo img {
    max-width: 200px;
    height: auto;
    max-height: 80px;
    object-fit: contain;
    background: transparent;
    mix-blend-mode: multiply;
}

.logo-fallback {
    font-size: 24px;
    font-weight: 700;
    color: #4f46e5;
    margin: 0;
}

.welcome-text {
    text-align: left;
    margin-bottom: 40px;
    width: 100%;
}

.welcome-text h1 {
    font-size: 28px;
    font-weight: 600;
    color: #1a1a1a;
    margin-bottom: 10px;
}

.welcome-text p {
    color: #6b7280;
    font-size: 16px;
    margin: 0;
    line-height: 1.5;
}

.login-form {
    width: 100%;
    max-width: 400px;
    text-align: left;
}

.form-group {
    margin-bottom: 20px;
    width: 100%;
}

.form-label {
    font-weight: 500;
    color: #374151;
    margin-bottom: 8px;
    display: block;
    font-size: 14px;
}

.form-control {
    border: 1px solid #d1d5db;
    border-radius: 8px;
    padding: 12px 16px;
    font-size: 16px;
    transition: all 0.2s ease;
    background: white;
    width: 100%;
    box-sizing: border-box;
}

.form-control:focus {
    border-color: #4f46e5;
    box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.1);
    outline: none;
}

.input-group {
    position: relative;
}

.input-icon {
    position: absolute;
    left: 12px;
    top: 50%;
    transform: translateY(-50%);
    color: #9ca3af;
    font-size: 16px;
}

.input-group .form-control {
    padding-left: 40px;
}

.forgot-password {
    text-align: right;
    margin-top: 8px;
}

.forgot-password a {
    color: #4f46e5;
    text-decoration: none;
    font-size: 14px;
    font-weight: 500;
}

.forgot-password a:hover {
    text-decoration: underline;
}

.btn-login {
    background: #4f46e5;
    border: none;
    color: white;
    padding: 12px 24px;
    border-radius: 8px;
    font-weight: 500;
    font-size: 16px;
    width: 100%;
    transition: all 0.2s ease;
    cursor: pointer;
    box-sizing: border-box;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
}

.btn-login:hover {
    background: #4338ca;
    transform: translateY(-1px);
}

.btn-login:active {
    transform: translateY(0);
}

.signup-link {
    text-align: center;
    margin-top: 20px;
    color: #6b7280;
    font-size: 14px;
}

.signup-link a {
    color: #4f46e5;
    text-decoration: none;
    font-weight: 500;
}

.signup-link a:hover {
    text-decoration: underline;
}

/* Alert Styles */
.alert {
    border-radius: 8px;
    border: 1px solid #e5e7eb;
    padding: 12px 16px;
    margin-bottom: 20px;
    display: flex;
    align-items: flex-start;
    gap: 8px;
}

.alert-danger {
    background: #fef2f2;
    color: #dc2626;
    border-color: #fecaca;
}

.alert-warning {
    background: #fffbeb;
    color: #d97706;
    border-color: #fed7aa;
}

.alert-success {
    background: #f0fdf4;
    color: #16a34a;
    border-color: #bbf7d0;
}

.alert-info {
    background: #eff6ff;
    color: #2563eb;
    border-color: #bfdbfe;
}

.alert ul {
    margin: 8px 0 0 0;
    padding-left: 20px;
}

.alert li {
    margin-bottom: 4px;
}

/* Right Panel - Enhanced Visual Design */
.right-panel {
    background: linear-gradient(135deg, #4f46e5 0%, #3b82f6 50%, #2563eb 100%);
    position: relative;
    overflow: hidden;
    display: flex;
    flex-direction: column;
}

/* Layer 1: Background Image */
.right-panel::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-image: url('../../demo-images/use_this.jpg');
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
    opacity: 0.4;
    z-index: 1;
}

/* Layer 2: Gradient Overlay */
.right-panel::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg,
        rgba(79, 70, 229, 0.8) 0%,
        rgba(59, 130, 246, 0.7) 50%,
        rgba(37, 99, 235, 0.8) 100%);
    z-index: 2;
}

.right-panel-content {
    display: flex;
    flex-direction: column;
    height: 100%;
    z-index: 3;
    position: relative;
    padding: 40px;
}

.feature-illustration {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 40px;
    position: relative;
}

/* SVG Dashboard Container */
.dashboard-svg-container {
    width: 100%;
    max-width: 400px;
    height: auto;
    position: relative;
    z-index: 4;
    filter: drop-shadow(0 10px 30px rgba(0, 0, 0, 0.2));
}

/* SVG Dashboard Elements */
.dashboard-svg {
    width: 100%;
    height: auto;
    opacity: 0.9;
    filter: drop-shadow(0 4px 20px rgba(0, 0, 0, 0.1));
}

/* Simple fade animation for SVG elements */
@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.dashboard-svg-container {
    animation: fadeIn 1s ease-out;
}

.bottom-text {
    flex: 0 0 auto;
    text-align: center;
    color: white;
}

.bottom-text h3 {
    font-size: 20px;
    font-weight: 600;
    margin-bottom: 10px;
    opacity: 0.95;
}

.bottom-text p {
    font-size: 14px;
    opacity: 0.8;
    margin: 0;
    line-height: 1.5;
}

/* Security Notice */
.security-notice {
    background: #f9fafb;
    border-radius: 8px;
    padding: 16px;
    margin-top: 20px;
    text-align: left;
    border: 1px solid #e5e7eb;
}

.security-notice h6 {
    color: #374151;
    font-weight: 600;
    margin-bottom: 8px;
    font-size: 14px;
    display: flex;
    align-items: center;
    gap: 8px;
}

.security-notice p {
    color: #6b7280;
    font-size: 13px;
    margin: 0;
    line-height: 1.5;
}

/* Responsive Design */
@media (max-width: 1200px) {
    .dashboard-svg-container {
        max-width: 350px;
    }

    .right-panel-content {
        padding: 30px;
    }
}

@media (max-width: 992px) {
    .dashboard-svg-container {
        max-width: 300px;
    }
}

@media (max-width: 768px) {
    .left-panel {
        padding: 40px 30px;
    }

    .right-panel {
        display: none;
    }

    .welcome-text h1 {
        font-size: 24px;
    }

    .logo img {
        max-width: 150px;
        max-height: 60px;
    }
}

@media (max-width: 576px) {
    .left-panel {
        padding: 30px 20px;
    }

    .welcome-text h1 {
        font-size: 22px;
    }

    .back-link {
        top: 15px;
        left: 15px;
    }
}

/* High-DPI Display Optimization */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
    .dashboard-svg-container {
        filter: drop-shadow(0 10px 30px rgba(0, 0, 0, 0.3));
    }
}

/* Enhanced Visual Effects */
.right-panel-content {
    backdrop-filter: blur(1px);
    -webkit-backdrop-filter: blur(1px);
}

/* SVG Performance Optimizations */
.dashboard-svg {
    will-change: transform;
    backface-visibility: hidden;
    -webkit-backface-visibility: hidden;
}

/* Smooth transitions for interactive elements */
.dashboard-card {
    transition: transform 0.3s ease;
}

.dashboard-svg-container:hover .dashboard-card {
    animation-play-state: paused;
}

/* Additional visual depth */
.right-panel {
    box-shadow: inset 0 0 100px rgba(0, 0, 0, 0.1);
}

/* Text readability enhancement */
.bottom-text {
    text-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
    backdrop-filter: blur(2px);
    -webkit-backdrop-filter: blur(2px);
    background: rgba(255, 255, 255, 0.05);
    border-radius: 12px;
    padding: 20px;
    border: 1px solid rgba(255, 255, 255, 0.1);
}

/* Reduced motion for accessibility */
@media (prefers-reduced-motion: reduce) {
    .dashboard-card,
    .chart-line,
    .pulse-dot {
        animation: none;
    }

    .dashboard-svg-container {
        filter: drop-shadow(0 5px 15px rgba(0, 0, 0, 0.2));
    }
}

/* Loading State */
.btn-login.loading {
    opacity: 0.7;
    cursor: not-allowed;
}

.btn-login.loading::after {
    content: '';
    width: 16px;
    height: 16px;
    border: 2px solid transparent;
    border-top: 2px solid currentColor;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-left: 8px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Focus Styles for Accessibility */
.form-control:focus,
.btn-login:focus,
.back-link:focus,
.forgot-password a:focus,
.signup-link a:focus {
    outline: 2px solid #4f46e5;
    outline-offset: 2px;
}

/* High Contrast Mode Support */
@media (prefers-contrast: high) {
    .form-control {
        border-width: 2px;
    }

    .btn-login {
        border: 2px solid #4f46e5;
    }
}

/* Enhanced SVG Banking Animations */
.dashboard-svg {
    animation: svgFloat 6s ease-in-out infinite;
}

@keyframes svgFloat {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-5px); }
}

/* Banking Card Animations */
.banking-card {
    transition: all 0.3s ease;
    transform-origin: center;
}

.banking-card:hover {
    filter: brightness(1.1);
}

/* Security Shield Pulse */
.security-shield {
    animation: securityPulse 4s ease-in-out infinite;
}

@keyframes securityPulse {
    0%, 100% { opacity: 0.8; }
    50% { opacity: 1; }
}

/* Money Transfer Flow */
.money-transfer .money-dot {
    filter: drop-shadow(0 0 4px rgba(255,255,255,0.8));
}

/* Floating Elements */
.floating-elements circle {
    filter: drop-shadow(0 0 2px rgba(255,255,255,0.6));
}

/* Analytics Chart Glow */
.analytics-chart {
    filter: drop-shadow(0 0 3px rgba(255,255,255,0.4));
}

/* Hover Effects for Interactive Elements */
.dashboard-svg-container:hover .banking-card {
    animation-play-state: paused;
}

.dashboard-svg-container:hover .security-shield {
    animation-duration: 2s;
}

/* Performance Optimizations for Animations */
.credit-cards, .security-shield, .money-transfer, .floating-elements, .analytics-chart {
    will-change: transform, opacity;
}

/* ========================================
   MOBILE APP-LIKE RESPONSIVE DESIGN
   Matches OTP page design patterns
   ======================================== */

@media (max-width: 768px) {
    /* Reset body for mobile app experience */
    body {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        min-height: 100vh;
        display: flex;
        align-items: center;
        justify-content: center;
        padding: 1rem;
        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
    }

    /* Hide desktop layout completely */
    .container-fluid, .row {
        display: none !important;
    }

    /* Mobile login container matching OTP page */
    .mobile-login-container {
        width: 100%;
        max-width: 480px;
        background: white;
        border-radius: 16px;
        box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
        padding: 3rem 2rem;
        text-align: center;
        position: relative;
        overflow: hidden;
        z-index: 1000;
    }

    /* Top gradient bar like OTP page */
    .mobile-login-container::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 4px;
        background: linear-gradient(90deg, #6366f1, #8b5cf6, #ec4899);
    }

    /* Mobile logo section */
    .mobile-logo {
        width: 120px;
        height: 80px;
        display: flex;
        align-items: center;
        justify-content: center;
        margin: 0 auto 1.5rem;
        position: relative;
    }

    .mobile-logo img {
        max-width: 100%;
        max-height: 100%;
        object-fit: contain;
        border-radius: 8px;
    }

    .mobile-logo-fallback {
        width: 80px;
        height: 80px;
        background: linear-gradient(135deg, #6366f1, #8b5cf6);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        box-shadow: 0 8px 24px rgba(99, 102, 241, 0.3);
    }

    .mobile-logo-fallback i {
        font-size: 2rem;
        color: white;
    }

    /* Mobile title */
    .mobile-title {
        font-size: 1.75rem;
        font-weight: 700;
        color: #1f2937;
        margin-bottom: 0.5rem;
    }

    .mobile-subtitle {
        color: #6b7280;
        font-size: 1rem;
        line-height: 1.5;
        max-width: 400px;
        margin: 0 auto 2rem;
    }

    /* Mobile form styling matching OTP page */
    .mobile-form-group {
        margin-bottom: 1.5rem;
        text-align: left;
    }

    .mobile-form-label {
        display: block;
        font-weight: 600;
        color: #374151;
        margin-bottom: 0.5rem;
        font-size: 0.875rem;
    }

    .mobile-input-group {
        position: relative;
    }

    .mobile-form-control {
        width: 100%;
        padding: 1rem 1rem 1rem 3rem;
        border: 2px solid #e5e7eb;
        border-radius: 8px;
        font-size: 1rem;
        color: #1f2937;
        background: #f9fafb;
        transition: all 0.2s ease;
        outline: none;
    }

    .mobile-form-control:focus {
        border-color: #6366f1;
        background: white;
        box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
    }

    .mobile-input-icon {
        position: absolute;
        left: 1rem;
        top: 50%;
        transform: translateY(-50%);
        color: #6b7280;
        font-size: 1rem;
    }

    /* Mobile button matching OTP page */
    .mobile-login-button {
        width: 100%;
        background: linear-gradient(135deg, #6366f1, #8b5cf6);
        color: white;
        border: none;
        border-radius: 8px;
        padding: 1rem 2rem;
        font-size: 1rem;
        font-weight: 600;
        cursor: pointer;
        transition: all 0.3s ease;
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 0.5rem;
        margin-bottom: 2rem;
    }

    .mobile-login-button:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 24px rgba(99, 102, 241, 0.4);
    }

    /* Mobile links */
    .mobile-forgot-password {
        text-align: right;
        margin-top: 0.5rem;
    }

    .mobile-forgot-password a {
        color: #6366f1;
        text-decoration: none;
        font-size: 0.875rem;
        font-weight: 500;
    }

    .mobile-signup-link {
        padding-top: 1rem;
        border-top: 1px solid #e5e7eb;
        color: #6b7280;
        font-size: 0.875rem;
    }

    .mobile-signup-link a {
        color: #6366f1;
        text-decoration: none;
        font-weight: 600;
    }

    /* Mobile alerts matching OTP page */
    .mobile-alert {
        padding: 1rem;
        border-radius: 8px;
        margin-bottom: 1.5rem;
        display: flex;
        align-items: center;
        gap: 0.5rem;
        font-size: 0.875rem;
    }

    .mobile-alert-error {
        background: #fef2f2;
        color: #dc2626;
        border: 1px solid #fecaca;
    }

    .mobile-alert-success {
        background: #f0fdf4;
        color: #16a34a;
        border: 1px solid #bbf7d0;
    }

    /* Security notice for mobile */
    .mobile-security-notice {
        background: rgba(99, 102, 241, 0.1);
        border: 1px solid rgba(99, 102, 241, 0.2);
        border-radius: 8px;
        padding: 1rem;
        margin-top: 1rem;
        text-align: center;
    }

    .mobile-security-notice h6 {
        color: #6366f1;
        font-size: 0.875rem;
        font-weight: 600;
        margin-bottom: 0.5rem;
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 0.5rem;
    }

    .mobile-security-notice p {
        color: #6b7280;
        font-size: 0.75rem;
        margin: 0;
        line-height: 1.4;
    }

    /* ========================================
       MOBILE TOUCH-FRIENDLY INTERACTIONS
       ======================================== */

    /* Touch feedback for mobile elements */
    .mobile-form-control:active {
        transform: scale(0.98);
    }

    .mobile-login-button:active {
        transform: scale(0.95) translateY(-1px);
    }

    /* Enhanced touch targets */
    .mobile-form-control {
        min-height: 48px; /* iOS/Android recommended minimum */
        touch-action: manipulation;
    }

    .mobile-login-button {
        min-height: 48px;
        touch-action: manipulation;
    }

    /* Smooth animations for mobile interactions */
    .mobile-login-container {
        animation: mobileSlideIn 0.5s ease-out;
    }

    @keyframes mobileSlideIn {
        from {
            opacity: 0;
            transform: translateY(30px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }

    /* Input focus animations */
    .mobile-form-control:focus {
        animation: mobileFocusPulse 0.3s ease-out;
    }

    @keyframes mobileFocusPulse {
        0% {
            box-shadow: 0 0 0 0 rgba(99, 102, 241, 0.4);
        }
        70% {
            box-shadow: 0 0 0 6px rgba(99, 102, 241, 0);
        }
        100% {
            box-shadow: 0 0 0 0 rgba(99, 102, 241, 0);
        }
    }

    /* Button press animation */
    .mobile-login-button:not(:disabled):active {
        animation: mobileButtonPress 0.1s ease-out;
    }

    @keyframes mobileButtonPress {
        0% {
            transform: scale(1) translateY(-2px);
        }
        50% {
            transform: scale(0.95) translateY(0);
        }
        100% {
            transform: scale(1) translateY(-2px);
        }
    }

    /* Loading state animation */
    .mobile-login-button.loading {
        animation: mobileButtonLoading 1s ease-in-out infinite;
    }

    @keyframes mobileButtonLoading {
        0%, 100% {
            opacity: 1;
        }
        50% {
            opacity: 0.7;
        }
    }

    /* Alert slide-in animation */
    .mobile-alert {
        animation: mobileAlertSlide 0.3s ease-out;
    }

    @keyframes mobileAlertSlide {
        from {
            opacity: 0;
            transform: translateX(-100%);
        }
        to {
            opacity: 1;
            transform: translateX(0);
        }
    }

    /* Link hover effects for mobile */
    .mobile-forgot-password a:active,
    .mobile-signup-link a:active {
        color: #4f46e5;
        transform: scale(0.95);
    }

    /* Improved mobile typography */
    .mobile-title {
        animation: mobileTitleFade 0.6s ease-out 0.2s both;
    }

    .mobile-subtitle {
        animation: mobileTitleFade 0.6s ease-out 0.4s both;
    }

    @keyframes mobileTitleFade {
        from {
            opacity: 0;
            transform: translateY(20px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }

    /* Form group stagger animation */
    .mobile-form-group:nth-child(1) {
        animation: mobileFormSlide 0.5s ease-out 0.6s both;
    }

    .mobile-form-group:nth-child(2) {
        animation: mobileFormSlide 0.5s ease-out 0.8s both;
    }

    @keyframes mobileFormSlide {
        from {
            opacity: 0;
            transform: translateY(30px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }

    /* Security notice animation */
    .mobile-security-notice {
        animation: mobileSecurityFade 0.5s ease-out 1.2s both;
    }

    @keyframes mobileSecurityFade {
        from {
            opacity: 0;
            transform: scale(0.9);
        }
        to {
            opacity: 1;
            transform: scale(1);
        }
    }

    /* Reduce motion for users who prefer it */
    @media (prefers-reduced-motion: reduce) {
        .mobile-login-container,
        .mobile-title,
        .mobile-subtitle,
        .mobile-form-group,
        .mobile-security-notice,
        .mobile-alert {
            animation: none;
        }

        .mobile-form-control:focus {
            animation: none;
        }

        .mobile-login-button:active {
            transform: none;
        }
    }

    /* High contrast mode for mobile */
    @media (prefers-contrast: high) {
        .mobile-form-control {
            border-width: 3px;
        }

        .mobile-login-button {
            border: 3px solid #4f46e5;
        }
    }
}
