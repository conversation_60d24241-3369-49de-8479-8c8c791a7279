<?php
// Enable comprehensive error reporting and debugging
require_once '../config/debug-system.php';

try {
    require_once '../config/config.php';

    // Require admin authentication
    requireAdmin();

    // Log successful admin index load
    logDebugInfo('admin_index_loaded');

} catch (Exception $e) {
    error_log("Admin Index Error: " . $e->getMessage());
    displayError('ADMIN INDEX ERROR', $e->getMessage(), __FILE__, __LINE__);
    exit;
} catch (Error $e) {
    error_log("Admin Index Fatal Error: " . $e->getMessage());
    displayError('ADMIN INDEX FATAL ERROR', $e->getMessage(), __FILE__, __LINE__);
    exit;
}

$page_title = 'Admin Dashboard';

// Define page actions
$page_actions = [
    [
        'url' => 'users.php',
        'label' => 'Manage Users',
        'icon' => 'fas fa-users'
    ]
];

// Get comprehensive system statistics
try {
    $db = getDB();

    // User Statistics (excluding deleted accounts)
    $total_users_result = $db->query("SELECT COUNT(*) as count FROM accounts WHERE is_admin = 0 AND deleted_at IS NULL");
    $total_users = $total_users_result->fetch_assoc()['count'];

    $active_users_result = $db->query("SELECT COUNT(*) as count FROM accounts WHERE is_admin = 0 AND status = 'active' AND deleted_at IS NULL");
    $active_users = $active_users_result->fetch_assoc()['count'];

    $suspended_users_result = $db->query("SELECT COUNT(*) as count FROM accounts WHERE is_admin = 0 AND status = 'suspended' AND deleted_at IS NULL");
    $suspended_users = $suspended_users_result->fetch_assoc()['count'];

    $pending_users_result = $db->query("SELECT COUNT(*) as count FROM accounts WHERE is_admin = 0 AND status = 'pending' AND deleted_at IS NULL");
    $pending_users = $pending_users_result->fetch_assoc()['count'];

    // Transaction Statistics (using account_transactions table)
    $today_transactions_result = $db->query("SELECT COUNT(*) as count, SUM(amount) as volume FROM account_transactions WHERE DATE(created_at) = CURDATE()");
    $today_stats = $today_transactions_result->fetch_assoc();
    $today_transactions = $today_stats['count'] ?? 0;
    $today_volume = $today_stats['volume'] ?? 0;

    $total_transactions_result = $db->query("SELECT COUNT(*) as count, SUM(amount) as volume FROM account_transactions");
    $total_stats = $total_transactions_result->fetch_assoc();
    $total_transactions = $total_stats['count'] ?? 0;
    $total_volume = $total_stats['volume'] ?? 0;

    $pending_transfers_result = $db->query("SELECT COUNT(*) as count FROM account_transactions WHERE status = 'pending'");
    $pending_transfers = $pending_transfers_result->fetch_assoc()['count'];

    $failed_transfers_result = $db->query("SELECT COUNT(*) as count FROM account_transactions WHERE status = 'failed' AND DATE(created_at) = CURDATE()");
    $failed_transfers = $failed_transfers_result->fetch_assoc()['count'];

    // KYC and Verification Statistics (excluding deleted accounts)
    $pending_kyc_result = $db->query("SELECT COUNT(*) as count FROM accounts WHERE kyc_status = 'pending' AND is_admin = 0 AND deleted_at IS NULL");
    $pending_kyc = $pending_kyc_result->fetch_assoc()['count'];

    // Check what KYC statuses actually exist in the database
    $kyc_statuses_result = $db->query("SELECT DISTINCT kyc_status FROM accounts WHERE is_admin = 0 AND deleted_at IS NULL");
    $available_statuses = [];
    while ($row = $kyc_statuses_result->fetch_assoc()) {
        $available_statuses[] = $row['kyc_status'];
    }

    // Use the correct status for verified users (could be 'verified', 'approved', or 'completed')
    $verified_status = 'verified';
    if (in_array('approved', $available_statuses)) {
        $verified_status = 'approved';
    } elseif (in_array('completed', $available_statuses)) {
        $verified_status = 'completed';
    }

    $approved_kyc_result = $db->query("SELECT COUNT(*) as count FROM accounts WHERE kyc_status = '$verified_status' AND is_admin = 0 AND deleted_at IS NULL");
    $approved_kyc = $approved_kyc_result->fetch_assoc()['count'];

    $rejected_kyc_result = $db->query("SELECT COUNT(*) as count FROM accounts WHERE kyc_status = 'rejected' AND is_admin = 0 AND deleted_at IS NULL");
    $rejected_kyc = $rejected_kyc_result->fetch_assoc()['count'];

    // System Health Indicators
    $total_balance_result = $db->query("SELECT SUM(balance) as total FROM accounts WHERE is_admin = 0 AND deleted_at IS NULL");
    $total_system_balance = $total_balance_result->fetch_assoc()['total'] ?? 0;

    $new_registrations_today_result = $db->query("SELECT COUNT(*) as count FROM accounts WHERE is_admin = 0 AND deleted_at IS NULL AND DATE(created_at) = CURDATE()");
    $new_registrations_today = $new_registrations_today_result->fetch_assoc()['count'];

    $login_attempts_today_result = $db->query("SELECT COUNT(*) as count FROM login_attempts WHERE DATE(attempted_at) = CURDATE()");
    $login_attempts_today = $login_attempts_today_result->fetch_assoc()['count'];

    // Recent Activities
    $recent_users_result = $db->query("
        SELECT id, username, first_name, last_name, email, created_at, status, kyc_status
        FROM accounts
        WHERE is_admin = 0 AND deleted_at IS NULL AND created_at >= DATE_SUB(NOW(), INTERVAL 7 DAY)
        ORDER BY created_at DESC
        LIMIT 5
    ");

    $recent_transactions_result = $db->query("
        SELECT at.*,
               a.username, a.first_name, a.last_name, a.account_number,
               ra.username as recipient_username, ra.first_name as recipient_first_name, ra.last_name as recipient_last_name
        FROM account_transactions at
        LEFT JOIN accounts a ON at.account_id = a.id
        LEFT JOIN accounts ra ON at.recipient_account_id = ra.id
        ORDER BY at.created_at DESC
        LIMIT 5
    ");

    // Alerts and Pending Actions
    $high_value_transfers_result = $db->query("SELECT COUNT(*) as count FROM account_transactions WHERE amount > 10000 AND status = 'pending'");
    $high_value_transfers = $high_value_transfers_result->fetch_assoc()['count'];

    // Check for suspicious activities (failed login attempts)
    $suspicious_activities_result = $db->query("SELECT COUNT(*) as count FROM login_attempts WHERE success = 0 AND DATE(attempted_at) = CURDATE()");
    $suspicious_activities = $suspicious_activities_result->fetch_assoc()['count'];

} catch (Exception $e) {
    error_log("Admin dashboard error: " . $e->getMessage());
    // Initialize all variables to prevent undefined variable errors
    $total_users = $active_users = $suspended_users = $pending_users = 0;
    $today_transactions = $today_volume = $week_transactions = $week_volume = 0;
    $pending_transfers = $failed_transfers = $pending_kyc = $approved_kyc = $rejected_kyc = 0;
    $total_system_balance = $new_registrations_today = $login_attempts_today = 0;
    $high_value_transfers = $suspicious_activities = 0;
    $recent_users_result = $recent_transactions_result = null;
}

include 'includes/admin-header.php';
?>

<!-- Modern Dashboard Styles -->
<style>
.modern-dashboard {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    padding: 2rem 0;
}

.dashboard-header {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-radius: 20px;
    padding: 2rem;
    margin-bottom: 2rem;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.dashboard-header h1 {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    font-weight: 700;
    font-size: 2.5rem;
    margin-bottom: 0.5rem;
}

.dashboard-subtitle {
    color: #6c757d;
    font-size: 1.1rem;
    font-weight: 400;
}

.modern-card {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-radius: 15px;
    border: 1px solid rgba(255, 255, 255, 0.2);
    box-shadow: 0 15px 30px rgba(0, 0, 0, 0.08);
    transition: all 0.3s ease;
    overflow: hidden;
    position: relative;
    padding: 1.25rem;
}

.modern-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 30px 60px rgba(0, 0, 0, 0.15);
}

/* Removed thick top border */

@keyframes gradientShift {
    0%, 100% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
}

.stat-card {
    padding: 2rem;
    text-align: center;
    position: relative;
    overflow: hidden;
}

.stat-icon {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1.5rem;
    font-size: 2rem;
    color: white;
    position: relative;
    z-index: 2;
}

.stat-icon.users { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); }
.stat-icon.transactions { background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%); }
.stat-icon.kyc { background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%); }
.stat-icon.system { background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%); }

.stat-number {
    font-size: 2rem;
    font-weight: 700;
    color: #2d3748;
    margin-bottom: 0.5rem;
    line-height: 1;
}

.stat-label {
    color: #718096;
    font-size: 1rem;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.stat-change {
    position: absolute;
    top: 1rem;
    right: 1rem;
    padding: 0.25rem 0.75rem;
    border-radius: 50px;
    font-size: 0.75rem;
    font-weight: 600;
}

.stat-change.positive {
    background: rgba(72, 187, 120, 0.1);
    color: #48bb78;
}

.stat-change.negative {
    background: rgba(245, 101, 101, 0.1);
    color: #f56565;
}

.alert-modern {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-radius: 15px;
    border: none;
    padding: 1.5rem;
    margin-bottom: 1rem;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    position: relative;
    overflow: hidden;
}

/* Removed thick left border from alert cards */

.quick-actions {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-radius: 20px;
    padding: 2rem;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
}

.action-btn {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border: none;
    border-radius: 15px;
    padding: 1rem 1.5rem;
    color: white;
    font-weight: 600;
    text-decoration: none;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
    margin-bottom: 1rem;
    box-shadow: 0 10px 30px rgba(102, 126, 234, 0.3);
}

.action-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 15px 40px rgba(102, 126, 234, 0.4);
    color: white;
}

.action-btn i {
    margin-right: 0.75rem;
    font-size: 1.1rem;
}

.chart-container {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-radius: 20px;
    padding: 2rem;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
    margin-bottom: 2rem;
}

.activity-feed {
    max-height: 400px;
    overflow-y: auto;
}

.activity-item {
    padding: 1rem;
    border-radius: 10px;
    margin-bottom: 1rem;
    background: rgba(255, 255, 255, 0.5);
    transition: all 0.3s ease;
}

.activity-item:hover {
    background: rgba(255, 255, 255, 0.8);
    transform: translateX(5px);
}

@media (max-width: 768px) {
    .dashboard-header h1 {
        font-size: 2rem;
    }

    .stat-card {
        padding: 1.5rem;
    }

    .stat-number {
        font-size: 2rem;
    }

    .stat-icon {
        width: 60px;
        height: 60px;
        font-size: 1.5rem;
    }
}
</style>

<!-- Dashboard Header -->
<div class="dashboard-header">
    <div class="row align-items-center">
        <div class="col-md-8">
            <h1>Welcome back, <?php echo htmlspecialchars($_SESSION['first_name']); ?>!</h1>
            <p class="dashboard-subtitle">Here's what's happening with your banking system today.</p>
        </div>
        <div class="col-md-4 text-end">
            <div class="d-flex align-items-center justify-content-end">
                <div class="me-3">
                    <small class="text-muted">Last updated</small><br>
                    <strong><?php echo date('M d, Y H:i'); ?></strong>
                </div>
                <!-- Removed System Online indicator -->
            </div>
        </div>
    </div>
</div>

<!-- Critical Alerts -->
<?php if ($pending_kyc > 0 || $pending_transfers > 0 || $high_value_transfers > 0 || $suspicious_activities > 0): ?>
<div class="row mb-4">
    <div class="col-12">
        <div class="alert-modern">
            <div class="d-flex align-items-center mb-3">
                <div class="alert-icon me-3">
                    <i class="fas fa-exclamation-triangle text-warning fs-3"></i>
                </div>
                <div>
                    <h5 class="mb-1">Immediate Attention Required</h5>
                    <p class="mb-0 text-muted">The following items need your review</p>
                </div>
            </div>
            <div class="row g-3">
                <?php if ($pending_kyc > 0): ?>
                <div class="col-lg-3 col-md-6">
                    <div class="d-flex align-items-center justify-content-between p-3 bg-warning bg-opacity-10 rounded-3">
                        <div>
                            <div class="fw-bold text-warning"><?php echo $pending_kyc; ?></div>
                            <small class="text-muted">KYC Reviews</small>
                        </div>
                        <a href="users.php?filter=kyc_pending" class="btn btn-warning btn-sm">Review</a>
                    </div>
                </div>
                <?php endif; ?>

                <?php if ($pending_transfers > 0): ?>
                <div class="col-lg-3 col-md-6">
                    <div class="d-flex align-items-center justify-content-between p-3 bg-info bg-opacity-10 rounded-3">
                        <div>
                            <div class="fw-bold text-info"><?php echo $pending_transfers; ?></div>
                            <small class="text-muted">Pending Transfers</small>
                        </div>
                        <a href="transfers.php?status=pending" class="btn btn-info btn-sm">Review</a>
                    </div>
                </div>
                <?php endif; ?>

                <?php if ($high_value_transfers > 0): ?>
                <div class="col-lg-3 col-md-6">
                    <div class="d-flex align-items-center justify-content-between p-3 bg-danger bg-opacity-10 rounded-3">
                        <div>
                            <div class="fw-bold text-danger"><?php echo $high_value_transfers; ?></div>
                            <small class="text-muted">High-Value Transfers</small>
                        </div>
                        <a href="transfers.php?filter=high_value" class="btn btn-danger btn-sm">Review</a>
                    </div>
                </div>
                <?php endif; ?>

                <?php if ($suspicious_activities > 0): ?>
                <div class="col-lg-3 col-md-6">
                    <div class="d-flex align-items-center justify-content-between p-3 bg-dark bg-opacity-10 rounded-3">
                        <div>
                            <div class="fw-bold text-dark"><?php echo $suspicious_activities; ?></div>
                            <small class="text-muted">Suspicious Activities</small>
                        </div>
                        <a href="security-logs.php" class="btn btn-dark btn-sm">Review</a>
                    </div>
                </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>
<?php endif; ?>

<!-- Modern Statistics Cards -->
<div class="row mb-4 g-4">
    <!-- User Statistics -->
    <div class="col-lg-4 col-md-6">
        <div class="modern-card stat-card">
            <div class="stat-change positive">+<?php echo $new_registrations_today; ?> today</div>
            <div class="stat-icon users">
                <i class="fas fa-users"></i>
            </div>
            <div class="stat-number" data-count="<?php echo $total_users; ?>">0</div>
            <div class="stat-label">Total Users</div>
            <div class="row mt-3 text-center">
                <div class="col-4">
                    <div class="fw-bold text-success"><?php echo number_format($active_users); ?></div>
                    <small class="text-muted">Active</small>
                </div>
                <div class="col-4">
                    <div class="fw-bold text-warning"><?php echo number_format($suspended_users); ?></div>
                    <small class="text-muted">Suspended</small>
                </div>
                <div class="col-4">
                    <div class="fw-bold text-info"><?php echo number_format($pending_users); ?></div>
                    <small class="text-muted">Pending</small>
                </div>
            </div>
            <div class="mt-3">
                <a href="users.php" class="action-btn w-100">
                    <i class="fas fa-users-cog"></i>Manage Users
                </a>
            </div>
        </div>
    </div>

    <!-- Transaction Statistics -->
    <div class="col-lg-4 col-md-6">
        <div class="modern-card stat-card">
            <div class="stat-change positive">+<?php echo $today_transactions; ?> today</div>
            <div class="stat-icon transactions">
                <i class="fas fa-exchange-alt"></i>
            </div>
            <div class="stat-number" data-count="<?php echo $total_transactions; ?>">0</div>
            <div class="stat-label">Total Transactions</div>
            <div class="mt-3 text-center">
                <div class="fw-bold fs-6 text-success"><?php echo formatCurrency($total_volume); ?></div>
                <small class="text-muted">Total Volume</small>
            </div>
            <div class="mt-3">
                <a href="transactions.php" class="action-btn w-100">
                    <i class="fas fa-chart-line"></i>View Transactions
                </a>
            </div>
        </div>
    </div>

    <!-- KYC Verification Status -->
    <div class="col-lg-4 col-md-6">
        <div class="modern-card stat-card">
            <div class="stat-change <?php echo $pending_kyc > 0 ? 'negative' : 'positive'; ?>">
                <?php echo $pending_kyc; ?> pending
            </div>
            <div class="stat-icon kyc">
                <i class="fas fa-id-card-alt"></i>
            </div>
            <div class="stat-number" data-count="<?php echo $approved_kyc; ?>">0</div>
            <div class="stat-label">Verified Users</div>
            <div class="row mt-3 text-center">
                <div class="col-6">
                    <div class="fw-bold text-warning"><?php echo number_format($pending_kyc); ?></div>
                    <small class="text-muted">Pending</small>
                </div>
                <div class="col-6">
                    <div class="fw-bold text-danger"><?php echo number_format($rejected_kyc); ?></div>
                    <small class="text-muted">Rejected</small>
                </div>
            </div>
            <div class="mt-3">
                <a href="users.php?filter=kyc_pending" class="action-btn w-100">
                    <i class="fas fa-clipboard-check"></i>Review KYC
                </a>
            </div>
        </div>
    </div>

    <!-- System Health card removed -->
</div>
<!-- Modern Dashboard Content -->
<div class="row mb-4 g-4">
    <!-- Quick Actions Panel -->
    <div class="col-lg-4">
        <div class="quick-actions">
            <div class="d-flex align-items-center mb-3">
                <div class="me-3">
                    <div class="stat-icon" style="width: 50px; height: 50px; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);">
                        <i class="fas fa-bolt"></i>
                    </div>
                </div>
                <div>
                    <h5 class="mb-1">Quick Actions</h5>
                    <p class="text-muted mb-0">Frequently used admin tools</p>
                </div>
            </div>

            <div class="d-grid gap-3">
                <a href="add-user.php" class="action-btn">
                    <i class="fas fa-user-plus"></i>Add New User
                </a>
                <a href="credit-debit.php" class="action-btn" style="background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%); box-shadow: 0 10px 30px rgba(240, 147, 251, 0.3);">
                    <i class="fas fa-coins"></i>Credit/Debit Account
                </a>
                <a href="transfers.php?status=pending" class="action-btn" style="background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%); box-shadow: 0 10px 30px rgba(79, 172, 254, 0.3);">
                    <i class="fas fa-clock"></i>Review Pending Transfers
                </a>
                <a href="users.php?filter=kyc_pending" class="action-btn" style="background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%); box-shadow: 0 10px 30px rgba(67, 233, 123, 0.3);">
                    <i class="fas fa-id-card"></i>Review KYC Applications
                </a>
                <a href="user-status-management.php" class="action-btn" style="background: linear-gradient(135deg, #fa709a 0%, #fee140 100%); box-shadow: 0 10px 30px rgba(250, 112, 154, 0.3);">
                    <i class="fas fa-user-cog"></i>Manage User Status
                </a>
            </div>

            <!-- Performance Metrics -->
            <div class="mt-4 p-3 bg-light bg-opacity-50 rounded-3">
                <h6 class="mb-3">Today's Performance</h6>
                <div class="row text-center">
                    <div class="col-4">
                        <div class="fw-bold text-primary"><?php echo $today_transactions; ?></div>
                        <small class="text-muted">Transactions</small>
                    </div>
                    <div class="col-4">
                        <div class="fw-bold text-success"><?php echo $new_registrations_today; ?></div>
                        <small class="text-muted">New Users</small>
                    </div>
                    <div class="col-4">
                        <div class="fw-bold text-info"><?php echo $login_attempts_today; ?></div>
                        <small class="text-muted">Logins</small>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Recent Activities & Analytics -->
    <div class="col-lg-8">
        <div class="chart-container">
            <div class="d-flex align-items-center justify-content-between mb-4">
                <div>
                    <h5 class="mb-1">Recent Activities</h5>
                    <p class="text-muted mb-0">Latest user registrations and system events</p>
                </div>
                <div class="d-flex gap-2">
                    <a href="users.php" class="btn btn-outline-primary btn-sm">
                        <i class="fas fa-users me-1"></i>All Users
                    </a>
                    <a href="transactions.php" class="btn btn-outline-success btn-sm">
                        <i class="fas fa-chart-line me-1"></i>Analytics
                    </a>
                </div>
            </div>

            <div class="activity-feed" style="max-height: 400px; overflow: hidden;">
                <?php
                // Get recent users (last 4 to fit better without scrolling)
                $recent_users_query = $db->query("
                    SELECT id, username, first_name, last_name, email, created_at, status, kyc_status
                    FROM accounts
                    WHERE is_admin = 0 AND deleted_at IS NULL
                    ORDER BY created_at DESC
                    LIMIT 4
                ");

                if ($recent_users_query && $recent_users_query->num_rows > 0): ?>
                    <?php
                    $activity_counter = 0;
                    while ($user = $recent_users_query->fetch_assoc()):
                        $activity_counter++;
                        $bg_color = ($activity_counter % 2 == 0) ? 'rgba(248, 249, 250, 0.8)' : 'rgba(255, 255, 255, 0.8)';
                    ?>
                    <div class="activity-item" style="padding: 0.75rem; margin-bottom: 0.5rem; border-radius: 8px; background: <?php echo $bg_color; ?>; border: 1px solid rgba(0,0,0,0.05);">
                        <div class="d-flex align-items-center">
                            <div class="me-3">
                                <div class="avatar avatar-sm" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; border-radius: 50%; width: 36px; height: 36px; display: flex; align-items: center; justify-content: center; font-weight: 600; font-size: 0.75rem;">
                                    <?php echo strtoupper(substr($user['first_name'], 0, 1) . substr($user['last_name'], 0, 1)); ?>
                                </div>
                            </div>
                            <div class="flex-grow-1">
                                <div class="d-flex align-items-center justify-content-between">
                                    <div>
                                        <h6 class="mb-1" style="font-size: 0.9rem;"><?php echo htmlspecialchars($user['first_name'] . ' ' . $user['last_name']); ?></h6>
                                        <p class="text-muted mb-0" style="font-size: 0.75rem;">
                                            <i class="fas fa-user me-1"></i>
                                            <?php echo htmlspecialchars($user['email']); ?>
                                        </p>
                                    </div>
                                    <div class="text-end">
                                        <div class="d-flex gap-1 mb-1">
                                            <span class="badge bg-<?php echo $user['status'] === 'active' ? 'success' : ($user['status'] === 'suspended' ? 'danger' : 'warning'); ?>" style="font-size: 0.7rem; font-weight: 500;">
                                                <?php echo ucfirst($user['status']); ?>
                                            </span>
                                        </div>
                                        <small class="text-muted" style="font-size: 0.7rem;"><?php echo formatDate($user['created_at'], 'M d, Y'); ?></small>
                                    </div>
                                </div>
                            </div>
                            <div class="ms-2">
                                <a href="edit-user.php?id=<?php echo $user['id']; ?>" class="btn btn-outline-primary btn-sm" style="padding: 0.2rem 0.4rem;">
                                    <i class="fas fa-edit" style="font-size: 0.7rem;"></i>
                                </a>
                            </div>
                        </div>
                    </div>
                    <?php endwhile; ?>
                <?php else: ?>
                    <div class="text-center py-3">
                        <div class="mb-2">
                            <i class="fas fa-users text-muted" style="font-size: 2rem; opacity: 0.3;"></i>
                        </div>
                        <h6 class="text-muted">No Users Found</h6>
                        <p class="text-muted mb-0 small">User registrations will appear here.</p>
                    </div>
                <?php endif; ?>
            </div>

            <!-- Simple Activity Summary -->
            <div class="mt-3 p-2 bg-white bg-opacity-80 rounded-2">
                <div class="row text-center">
                    <div class="col-4">
                        <div class="fw-bold text-primary" style="font-size: 1.1rem;"><?php echo number_format($total_users); ?></div>
                        <small class="text-muted" style="font-size: 0.75rem;">Total Users</small>
                    </div>
                    <div class="col-4">
                        <div class="fw-bold text-success" style="font-size: 1.1rem;"><?php echo number_format($active_users); ?></div>
                        <small class="text-muted" style="font-size: 0.75rem;">Active</small>
                    </div>
                    <div class="col-4">
                        <div class="fw-bold text-warning" style="font-size: 1.1rem;"><?php echo number_format($pending_kyc); ?></div>
                        <small class="text-muted" style="font-size: 0.75rem;">KYC Pending</small>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Modern Transaction Overview -->
<div class="row mb-4">
    <div class="col-12">
        <div class="chart-container">
            <div class="d-flex align-items-center justify-content-between mb-4">
                <div>
                    <h5 class="mb-1">Recent Transactions</h5>
                    <p class="text-muted mb-0">Latest financial activities across the platform</p>
                </div>
                <div class="d-flex gap-2">
                    <div class="btn-group" role="group">
                        <input type="radio" class="btn-check" name="transactionFilter" id="all" autocomplete="off" checked>
                        <label class="btn btn-outline-primary btn-sm" for="all">All</label>

                        <input type="radio" class="btn-check" name="transactionFilter" id="pending" autocomplete="off">
                        <label class="btn btn-outline-warning btn-sm" for="pending">Pending</label>

                        <input type="radio" class="btn-check" name="transactionFilter" id="completed" autocomplete="off">
                        <label class="btn btn-outline-success btn-sm" for="completed">Completed</label>
                    </div>
                    <a href="transactions.php" class="btn btn-primary btn-sm">
                        <i class="fas fa-chart-line me-1"></i>View All
                    </a>
                </div>
            </div>

            <div class="table-responsive">
                <?php if ($recent_transactions_result && $recent_transactions_result->num_rows > 0): ?>
                    <table class="table table-hover bg-white rounded-3 overflow-hidden">
                        <thead class="bg-white">
                            <tr>
                                <th class="border-0 text-muted fw-normal small">#</th>
                                <th class="border-0 text-muted fw-normal small">Reference</th>
                                <th class="border-0 text-muted fw-normal small">User</th>
                                <th class="border-0 text-muted fw-normal small">Type</th>
                                <th class="border-0 text-muted fw-normal small">Amount</th>
                                <th class="border-0 text-muted fw-normal small">Status</th>
                                <th class="border-0 text-muted fw-normal small">Date</th>
                                <th class="border-0 text-muted fw-normal small">Action</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php
                            $counter = 1;
                            while ($transaction = $recent_transactions_result->fetch_assoc()): ?>
                            <tr class="transaction-row" data-status="<?php echo $transaction['status']; ?>" style="background-color: <?php echo ($counter % 2 == 0) ? 'rgba(248, 249, 250, 0.5)' : 'transparent'; ?>;">
                                <td class="border-0 text-muted small"><?php echo $counter++; ?></td>
                                <td class="border-0">
                                    <span class="fw-medium small"><?php echo htmlspecialchars($transaction['reference_number']); ?></span>
                                </td>
                                <td class="border-0">
                                    <?php if ($transaction['username']): ?>
                                        <div class="d-flex align-items-center">
                                            <div class="avatar avatar-xs me-2" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; border-radius: 50%; width: 24px; height: 24px; display: flex; align-items: center; justify-content: center; font-size: 0.7rem; font-weight: 600;">
                                                <?php echo strtoupper(substr($transaction['first_name'], 0, 1) . substr($transaction['last_name'], 0, 1)); ?>
                                            </div>
                                            <span class="small"><?php echo htmlspecialchars($transaction['username']); ?></span>
                                        </div>
                                    <?php else: ?>
                                        <span class="text-muted small">System</span>
                                    <?php endif; ?>
                                </td>
                                <td class="border-0">
                                    <span class="small">
                                        <?php
                                        echo $transaction['transaction_type'] === 'credit' ? 'Credit' :
                                            ($transaction['transaction_type'] === 'debit' ? 'Debit' :
                                            ($transaction['transaction_type'] === 'deposit' ? 'Deposit' :
                                            ($transaction['transaction_type'] === 'withdrawal' ? 'Withdrawal' :
                                            ($transaction['transaction_type'] === 'transfer_in' ? 'Transfer In' : 'Transfer Out'))));
                                        ?>
                                    </span>
                                </td>
                                <td class="border-0">
                                    <span class="fw-bold small text-<?php echo in_array($transaction['transaction_type'], ['credit', 'deposit', 'transfer_in']) ? 'success' : 'danger'; ?>">
                                        <?php echo in_array($transaction['transaction_type'], ['credit', 'deposit', 'transfer_in']) ? '+' : '-'; ?>
                                        <?php echo formatCurrency($transaction['amount']); ?>
                                    </span>
                                </td>
                                <td class="border-0">
                                    <span class="badge bg-<?php
                                        echo $transaction['status'] === 'completed' ? 'success' :
                                            ($transaction['status'] === 'failed' ? 'danger' :
                                            ($transaction['status'] === 'pending' ? 'warning' :
                                            ($transaction['status'] === 'processing' ? 'info' : 'secondary')));
                                    ?>" style="font-size: 0.75rem; font-weight: 500;">
                                        <?php echo ucfirst($transaction['status']); ?>
                                    </span>
                                </td>
                                <td class="border-0">
                                    <span class="text-muted small"><?php echo formatDate($transaction['created_at'], 'M d, H:i'); ?></span>
                                </td>
                                <td class="border-0">
                                    <a href="transactions.php?id=<?php echo $transaction['id']; ?>" class="btn btn-outline-primary btn-sm" style="padding: 0.25rem 0.5rem;">
                                        <i class="fas fa-eye" style="font-size: 0.75rem;"></i>
                                    </a>
                                </td>
                            </tr>
                            <?php endwhile; ?>
                        </tbody>
                    </table>

                    <!-- Simple Pagination -->
                    <div class="d-flex justify-content-between align-items-center mt-3">
                        <small class="text-muted">Showing <?php echo min(5, $counter-1); ?> of <?php echo $total_transactions; ?> transactions</small>
                        <div>
                            <a href="transactions.php" class="btn btn-outline-primary btn-sm">
                                <i class="fas fa-list me-1"></i>View All Transactions
                            </a>
                        </div>
                    </div>
                <?php else: ?>
                    <div class="text-center py-4 bg-white rounded-3">
                        <div class="mb-2">
                            <i class="fas fa-exchange-alt text-muted" style="font-size: 2rem; opacity: 0.3;"></i>
                        </div>
                        <h6 class="text-muted">No Recent Transactions</h6>
                        <p class="text-muted mb-0 small">Transaction activities will appear here as they occur.</p>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<!-- Footer section removed -->

<!-- Modern JavaScript Enhancements -->
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Animate counter numbers
    function animateCounters() {
        const counters = document.querySelectorAll('[data-count]');
        counters.forEach(counter => {
            const target = parseInt(counter.getAttribute('data-count'));
            const duration = 2000;
            const step = target / (duration / 16);
            let current = 0;

            const timer = setInterval(() => {
                current += step;
                if (current >= target) {
                    counter.textContent = target.toLocaleString();
                    clearInterval(timer);
                } else {
                    counter.textContent = Math.floor(current).toLocaleString();
                }
            }, 16);
        });
    }

    // Animate cards on scroll
    function animateOnScroll() {
        const cards = document.querySelectorAll('.modern-card');
        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.style.opacity = '1';
                    entry.target.style.transform = 'translateY(0)';
                }
            });
        }, { threshold: 0.1 });

        cards.forEach(card => {
            card.style.opacity = '0';
            card.style.transform = 'translateY(20px)';
            card.style.transition = 'all 0.6s ease';
            observer.observe(card);
        });
    }

    // Transaction filter functionality
    function setupTransactionFilters() {
        const filterButtons = document.querySelectorAll('input[name="transactionFilter"]');
        const transactionCards = document.querySelectorAll('.transaction-card');

        filterButtons.forEach(button => {
            button.addEventListener('change', function() {
                const filter = this.id;

                transactionCards.forEach(card => {
                    if (filter === 'all') {
                        card.style.display = 'block';
                    } else {
                        const status = card.getAttribute('data-status');
                        card.style.display = status === filter ? 'block' : 'none';
                    }
                });
            });
        });
    }

    // Add hover effects to action buttons
    function setupHoverEffects() {
        const actionButtons = document.querySelectorAll('.action-btn');
        actionButtons.forEach(btn => {
            btn.addEventListener('mouseenter', function() {
                this.style.transform = 'translateY(-2px) scale(1.02)';
            });

            btn.addEventListener('mouseleave', function() {
                this.style.transform = 'translateY(-2px) scale(1)';
            });
        });
    }

    // Real-time clock update
    function updateClock() {
        const clockElement = document.querySelector('.fw-bold');
        if (clockElement && clockElement.textContent.includes(':')) {
            setInterval(() => {
                const now = new Date();
                clockElement.textContent = now.toLocaleTimeString('en-US', {
                    hour: '2-digit',
                    minute: '2-digit',
                    hour12: false
                });
            }, 1000);
        }
    }

    // Initialize all animations and interactions
    setTimeout(animateCounters, 500);
    animateOnScroll();
    setupTransactionFilters();
    setupHoverEffects();
    updateClock();

    // Add loading animation completion
    document.body.classList.add('dashboard-loaded');
});

// Additional CSS for enhanced animations
const additionalStyles = `
    <style>
    .transaction-icon {
        width: 30px;
        height: 30px;
        border-radius: 50%;
        background: rgba(102, 126, 234, 0.1);
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 0.875rem;
    }

    .transaction-card {
        transition: all 0.3s ease;
        cursor: pointer;
    }

    .transaction-card:hover {
        transform: translateY(-2px);
        box-shadow: 0 25px 50px rgba(0, 0, 0, 0.15);
    }

    .dashboard-loaded .modern-card {
        animation: slideInUp 0.6s ease forwards;
    }

    @keyframes slideInUp {
        from {
            opacity: 0;
            transform: translateY(30px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }

    .btn-check:checked + .btn {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border-color: #667eea;
        color: white;
    }

    .progress-bar {
        transition: width 0.6s ease;
    }

    @media (max-width: 768px) {
        .transaction-grid .col-lg-6 {
            margin-bottom: 1rem;
        }

        .dashboard-header {
            padding: 1.5rem;
        }

        .chart-container {
            padding: 1.5rem;
        }

        .modern-card {
            margin-bottom: 1rem;
        }

        .stat-number {
            font-size: 1.5rem;
        }

        .stat-icon {
            width: 50px;
            height: 50px;
            font-size: 1.5rem;
        }

        .fw-bold.small {
            font-size: 0.8rem !important;
        }

        .text-muted {
            font-size: 0.75rem;
        }
    }

    @media (max-width: 576px) {
        .dashboard-header h1 {
            font-size: 1.5rem;
        }

        .modern-card {
            padding: 1rem;
        }

        .stat-card .row {
            font-size: 0.8rem;
        }

        .action-btn {
            padding: 0.5rem 1rem;
            font-size: 0.85rem;
        }
    }
    </style>
`;

document.head.insertAdjacentHTML('beforeend', additionalStyles);
</script>

<?php include 'includes/admin-footer.php'; ?>
