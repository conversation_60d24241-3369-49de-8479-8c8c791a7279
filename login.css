* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    background: #f8f9fa;
    min-height: 100vh;
    margin: 0;
    padding: 0;
}

.container-fluid {
    min-height: 100vh;
    padding: 0;
}

.row {
    min-height: 100vh;
    margin: 0;
}

/* Left Panel - User Login Form */
.left-panel {
    background: white;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: flex-start;
    padding: 60px 80px;
    position: relative;
}

.back-link {
    position: absolute;
    top: 30px;
    left: 30px;
    color: #6b7280;
    text-decoration: none;
    font-size: 14px;
    display: flex;
    align-items: center;
    gap: 8px;
    transition: color 0.2s ease;
}

.back-link:hover {
    color: #4f46e5;
}

.logo {
    margin-bottom: 40px;
    text-align: left;
    width: 100%;
}

.logo img {
    max-width: 200px;
    max-height: 80px;
    width: auto;
    height: auto;
}

.logo-fallback {
    font-size: 24px;
    font-weight: 700;
    color: #4f46e5;
    margin: 0;
}

.welcome-text {
    text-align: left;
    margin-bottom: 40px;
    width: 100%;
}

.welcome-text h1 {
    font-size: 28px;
    font-weight: 600;
    color: #1a1a1a;
    margin-bottom: 10px;
}

.welcome-text p {
    color: #6b7280;
    font-size: 16px;
    margin: 0;
}

.login-form {
    width: 100%;
    max-width: 400px;
    text-align: left;
}

.form-group {
    margin-bottom: 20px;
    width: 100%;
}

.form-label {
    font-weight: 500;
    color: #374151;
    margin-bottom: 8px;
    display: block;
    font-size: 14px;
}

.form-control {
    border: 1px solid #d1d5db;
    border-radius: 8px;
    padding: 12px 16px;
    font-size: 16px;
    transition: all 0.2s ease;
    background: white;
    width: 100%;
    box-sizing: border-box;
}

.form-control:focus {
    border-color: #4f46e5;
    box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.1);
    outline: none;
}

.input-group {
    position: relative;
}

.input-icon {
    position: absolute;
    left: 12px;
    top: 50%;
    transform: translateY(-50%);
    color: #9ca3af;
    font-size: 16px;
}

.input-group .form-control {
    padding-left: 40px;
}

.forgot-password {
    text-align: right;
    margin-top: 8px;
}

.forgot-password a {
    color: #4f46e5;
    text-decoration: none;
    font-size: 14px;
}

.forgot-password a:hover {
    text-decoration: underline;
}

.btn-login {
    background: #4f46e5;
    border: none;
    color: white;
    padding: 12px 24px;
    border-radius: 8px;
    font-weight: 500;
    font-size: 16px;
    width: 100%;
    transition: all 0.2s ease;
    cursor: pointer;
    box-sizing: border-box;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
}

.btn-login:hover {
    background: #4338ca;
    transform: translateY(-1px);
}

.signup-link {
    text-align: center;
    margin-top: 20px;
    color: #6b7280;
    font-size: 14px;
}

.signup-link a {
    color: #4f46e5;
    text-decoration: none;
    font-weight: 500;
}

.signup-link a:hover {
    text-decoration: underline;
}

.alert {
    border-radius: 8px;
    border: 1px solid #e5e7eb;
    padding: 12px 16px;
    margin-bottom: 20px;
    display: flex;
    align-items: center;
    gap: 8px;
}

.alert-danger {
    background: #fef2f2;
    color: #dc2626;
    border-color: #fecaca;
}

.alert-warning {
    background: #fffbeb;
    color: #d97706;
    border-color: #fed7aa;
}

.alert-success {
    background: #f0fdf4;
    color: #16a34a;
    border-color: #bbf7d0;
}

/* Right Panel - User Banking Visual */
.right-panel {
    background: linear-gradient(135deg, #4f46e5 0%, #3b82f6 50%, #2563eb 100%);
    position: relative;
    overflow: hidden;
    display: flex;
    flex-direction: column;
}

.right-panel::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><circle cx="20" cy="20" r="2" fill="rgba(255,255,255,0.1)"/><circle cx="80" cy="30" r="1.5" fill="rgba(255,255,255,0.1)"/><circle cx="30" cy="80" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="70" cy="70" r="1" fill="rgba(255,255,255,0.1)"/></svg>');
    opacity: 0.2;
    z-index: 2;
}

.right-panel-content {
    display: flex;
    flex-direction: column;
    height: 100%;
    z-index: 3;
    position: relative;
    padding: 40px;
}

.feature-illustration {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 40px;
}

.bottom-text {
    flex: 0 0 auto;
    text-align: center;
    color: white;
}

.bottom-text h3 {
    font-size: 20px;
    font-weight: 600;
    margin-bottom: 10px;
    opacity: 0.95;
}

.bottom-text p {
    font-size: 14px;
    opacity: 0.8;
    margin: 0;
    line-height: 1.5;
}

/* Responsive Design */
@media (max-width: 768px) {
    .left-panel {
        padding: 40px 30px;
    }

    .right-panel {
        display: none;
    }

    .welcome-text h1 {
        font-size: 24px;
    }

    .logo img {
        max-width: 150px;
        max-height: 60px;
    }
}

@media (max-width: 576px) {
    .left-panel {
        padding: 30px 20px;
    }

    .welcome-text h1 {
        font-size: 22px;
    }
}

.header-logo {
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.header-logo img {
    max-height: 40px;
    width: auto;
}

.header-logo h2 {
    font-size: 1.5rem;
    font-weight: 700;
    color: #1f2937;
    margin: 0;
}

.header-nav {
    display: flex;
    gap: 2rem;
    align-items: center;
}

.nav-link {
    color: #6b7280;
    text-decoration: none;
    font-weight: 500;
    transition: color 0.2s ease;
}

.nav-link:hover {
    color: #4f46e5;
}

.header-cta {
    display: flex;
    gap: 1rem;
    align-items: center;
}

.btn-outline-primary {
    border: 1px solid #4f46e5;
    color: #4f46e5;
    background: transparent;
    padding: 0.5rem 1rem;
    border-radius: 6px;
    text-decoration: none;
    font-weight: 500;
    transition: all 0.2s ease;
}

.btn-outline-primary:hover {
    background: #4f46e5;
    color: white;
}

.btn-primary {
    background: #4f46e5;
    color: white;
    border: none;
    padding: 0.5rem 1rem;
    border-radius: 6px;
    text-decoration: none;
    font-weight: 500;
    transition: all 0.2s ease;
}

.btn-primary:hover {
    background: #4338ca;
    transform: translateY(-1px);
}

/* Hero Section */
.hero-section {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    display: flex;
    align-items: center;
    position: relative;
    overflow: hidden;
    padding-top: 80px;
}

.hero-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><circle cx="20" cy="20" r="2" fill="rgba(255,255,255,0.1)"/><circle cx="80" cy="30" r="1.5" fill="rgba(255,255,255,0.1)"/><circle cx="30" cy="80" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="70" cy="70" r="1" fill="rgba(255,255,255,0.1)"/></svg>');
    opacity: 0.3;
}

.hero-content {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 2rem;
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 4rem;
    align-items: center;
    position: relative;
    z-index: 2;
}

.hero-text {
    color: white;
}

.hero-text h1 {
    font-size: 3.5rem;
    font-weight: 700;
    line-height: 1.1;
    margin-bottom: 1.5rem;
}

.hero-text p {
    font-size: 1.25rem;
    opacity: 0.9;
    margin-bottom: 2rem;
    line-height: 1.6;
}

.hero-features {
    display: flex;
    flex-wrap: wrap;
    gap: 1.5rem;
    margin-bottom: 2rem;
}

.hero-feature {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    color: white;
    opacity: 0.9;
}

.hero-feature i {
    color: #10b981;
    font-size: 1.1rem;
}

.hero-cta {
    display: flex;
    gap: 1rem;
    flex-wrap: wrap;
}

.btn-hero-primary {
    background: white;
    color: #4f46e5;
    border: none;
    padding: 1rem 2rem;
    border-radius: 8px;
    font-weight: 600;
    font-size: 1.1rem;
    text-decoration: none;
    transition: all 0.3s ease;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
}

.btn-hero-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
    color: #4f46e5;
}

.btn-hero-secondary {
    background: transparent;
    color: white;
    border: 2px solid rgba(255, 255, 255, 0.3);
    padding: 1rem 2rem;
    border-radius: 8px;
    font-weight: 600;
    font-size: 1.1rem;
    text-decoration: none;
    transition: all 0.3s ease;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
}

.btn-hero-secondary:hover {
    background: rgba(255, 255, 255, 0.1);
    border-color: rgba(255, 255, 255, 0.5);
    color: white;
}

/* Login Form Section */
.login-section {
    background: white;
    border-radius: 20px;
    padding: 3rem;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.1);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    max-width: 480px;
    width: 100%;
}

.logo {
    margin-bottom: 40px;
    text-align: left;
    width: 100%;
}

.logo img {
    max-width: 200px;
    height: auto;
    max-height: 80px;
    object-fit: contain;
    background: transparent;
    mix-blend-mode: multiply;
}

.logo-fallback {
    font-size: 24px;
    font-weight: 700;
    color: #4361ee;
    margin: 0;
}

.welcome-text {
    text-align: left;
    margin-bottom: 40px;
    width: 100%;
}

.welcome-text h1 {
    font-size: 28px;
    font-weight: 600;
    color: #1a1a1a;
    margin-bottom: 10px;
}

.welcome-text p {
    color: #6b7280;
    font-size: 16px;
    margin: 0;
}

.login-form {
    width: 100%;
    max-width: 400px;
    text-align: left;
}

.form-group {
    margin-bottom: 20px;
    width: 100%;
}

.form-label {
    font-weight: 500;
    color: #374151;
    margin-bottom: 8px;
    display: block;
    font-size: 14px;
}

.form-control {
    border: 1px solid #d1d5db;
    border-radius: 8px;
    padding: 12px 16px 12px 45px;
    font-size: 16px;
    transition: all 0.2s ease;
    background: white;
    width: 100%;
    box-sizing: border-box;
}

.form-control:focus {
    border-color: #4361ee;
    box-shadow: 0 0 0 3px rgba(67, 97, 238, 0.1);
    outline: none;
}

.input-group {
    position: relative;
}

.input-icon {
    position: absolute;
    left: 16px;
    top: 50%;
    transform: translateY(-50%);
    color: #6b7280;
    z-index: 2;
}

.btn-login {
    background: #4361ee;
    border: none;
    color: white;
    padding: 12px 24px;
    border-radius: 8px;
    font-weight: 500;
    font-size: 16px;
    width:
