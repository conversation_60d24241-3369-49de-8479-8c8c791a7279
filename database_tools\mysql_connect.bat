@echo off
REM MySQL Connection Script for Online Banking Database
REM This script provides quick access to MySQL command line

title Online Banking - MySQL Connection

echo.
echo ========================================
echo   MySQL Database Connection Tool
echo ========================================
echo.

REM Check if MySQL client is available
mysql --version >nul 2>&1
if errorlevel 1 (
    echo ERROR: MySQL client is not found in PATH
    echo.
    echo Please make sure MySQL is installed and added to PATH
    echo.
    echo For MAMP users, MySQL is typically located at:
    echo C:\MAMP\bin\mysql\bin\mysql.exe
    echo.
    echo For XAMPP users, MySQL is typically located at:
    echo C:\xampp\mysql\bin\mysql.exe
    echo.
    echo You can also add the MySQL bin directory to your PATH environment variable.
    echo.
    pause
    exit /b 1
)

REM Database connection parameters
set DB_HOST=localhost
set DB_USER=root
set DB_PASS=root
set DB_NAME=online_banking

echo Connecting to MySQL database...
echo Host: %DB_HOST%
echo Database: %DB_NAME%
echo User: %DB_USER%
echo.

:MENU
echo Choose connection option:
echo.
echo 1. Connect to Online Banking Database
echo 2. Connect to MySQL (no database selected)
echo 3. Show Database Information
echo 4. Create Database Backup
echo 5. Import SQL File
echo 6. Run Quick Queries
echo 7. Exit
echo.
set /p choice="Enter your choice (1-7): "

if "%choice%"=="1" goto CONNECT_DB
if "%choice%"=="2" goto CONNECT_MYSQL
if "%choice%"=="3" goto DB_INFO
if "%choice%"=="4" goto BACKUP
if "%choice%"=="5" goto IMPORT
if "%choice%"=="6" goto QUICK_QUERIES
if "%choice%"=="7" goto EXIT
goto INVALID_CHOICE

:CONNECT_DB
echo.
echo Connecting to %DB_NAME% database...
echo Type 'exit' or 'quit' to return to this menu
echo.
mysql -h %DB_HOST% -u %DB_USER% -p%DB_PASS% %DB_NAME%
echo.
goto CONTINUE

:CONNECT_MYSQL
echo.
echo Connecting to MySQL server...
echo Type 'exit' or 'quit' to return to this menu
echo.
mysql -h %DB_HOST% -u %DB_USER% -p%DB_PASS%
echo.
goto CONTINUE

:DB_INFO
echo.
echo Database Information:
echo =====================
echo Host: %DB_HOST%
echo Database: %DB_NAME%
echo User: %DB_USER%
echo.
echo Checking database status...
mysql -h %DB_HOST% -u %DB_USER% -p%DB_PASS% -e "SELECT 'Database connection successful!' as Status; USE %DB_NAME%; SHOW TABLES;"
echo.
goto CONTINUE

:BACKUP
echo.
echo Creating database backup...
set backup_file=backup_%DB_NAME%_%date:~-4,4%%date:~-10,2%%date:~-7,2%_%time:~0,2%%time:~3,2%%time:~6,2%.sql
set backup_file=%backup_file: =0%
echo Backup file: %backup_file%
echo.
mysqldump -h %DB_HOST% -u %DB_USER% -p%DB_PASS% --single-transaction --routines --triggers %DB_NAME% > %backup_file%
if errorlevel 1 (
    echo ERROR: Backup failed!
) else (
    echo SUCCESS: Backup created successfully!
    echo File: %backup_file%
)
echo.
goto CONTINUE

:IMPORT
echo.
set /p sql_file="Enter SQL file path to import: "
if not exist "%sql_file%" (
    echo ERROR: File not found: %sql_file%
    goto CONTINUE
)
echo.
echo Importing %sql_file% into %DB_NAME%...
mysql -h %DB_HOST% -u %DB_USER% -p%DB_PASS% %DB_NAME% < "%sql_file%"
if errorlevel 1 (
    echo ERROR: Import failed!
) else (
    echo SUCCESS: Import completed successfully!
)
echo.
goto CONTINUE

:QUICK_QUERIES
echo.
echo Quick Database Queries:
echo =======================
echo.
echo 1. Show all tables
echo 2. Count users
echo 3. Show recent transactions
echo 4. Show database size
echo 5. Show user status summary
echo 6. Back to main menu
echo.
set /p query_choice="Enter your choice (1-6): "

if "%query_choice%"=="1" (
    mysql -h %DB_HOST% -u %DB_USER% -p%DB_PASS% %DB_NAME% -e "SHOW TABLES;"
) else if "%query_choice%"=="2" (
    mysql -h %DB_HOST% -u %DB_USER% -p%DB_PASS% %DB_NAME% -e "SELECT COUNT(*) as 'Total Users' FROM accounts;"
) else if "%query_choice%"=="3" (
    mysql -h %DB_HOST% -u %DB_USER% -p%DB_PASS% %DB_NAME% -e "SELECT id, sender_id, amount, status, created_at FROM transfers ORDER BY created_at DESC LIMIT 10;"
) else if "%query_choice%"=="4" (
    mysql -h %DB_HOST% -u %DB_USER% -p%DB_PASS% %DB_NAME% -e "SELECT ROUND(SUM(data_length + index_length) / 1024 / 1024, 2) AS 'Database Size (MB)' FROM information_schema.TABLES WHERE table_schema = '%DB_NAME%';"
) else if "%query_choice%"=="5" (
    mysql -h %DB_HOST% -u %DB_USER% -p%DB_PASS% %DB_NAME% -e "SELECT status, COUNT(*) as count FROM accounts GROUP BY status;"
) else if "%query_choice%"=="6" (
    goto CONTINUE
) else (
    echo Invalid choice!
)
echo.
goto CONTINUE

:INVALID_CHOICE
echo.
echo Invalid choice! Please enter a number between 1-7.
echo.
goto MENU

:CONTINUE
echo.
echo Press any key to continue...
pause >nul
echo.
goto MENU

:EXIT
echo.
echo Thank you for using the MySQL Connection Tool!
echo.
pause
exit /b 0
