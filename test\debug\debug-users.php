<?php
require_once '../config/config.php';

$page_title = 'Users Debug';
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $page_title; ?></title>
    <link href="https://cdn.jsdelivr.net/npm/@tabler/core@1.3.2/dist/css/tabler.min.css" rel="stylesheet">
</head>
<body>
    <div class="page-wrapper">
        <div class="container-xl">
            <div class="page-header">
                <h1>Users Database Debug</h1>
            </div>
            
            <div class="row">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">All Users in Database</h3>
                        </div>
                        <div class="card-body">
                            <?php
                            try {
                                $db = getDB();
                                $result = $db->query("SELECT id, username, first_name, last_name, is_admin, status, created_at FROM accounts ORDER BY is_admin DESC, id ASC");
                                
                                if ($result && $result->num_rows > 0) {
                                    echo "<div class='table-responsive'>";
                                    echo "<table class='table table-vcenter'>";
                                    echo "<thead>";
                                    echo "<tr>";
                                    echo "<th>ID</th>";
                                    echo "<th>Username</th>";
                                    echo "<th>Name</th>";
                                    echo "<th>Is Admin</th>";
                                    echo "<th>Status</th>";
                                    echo "<th>Created</th>";
                                    echo "</tr>";
                                    echo "</thead>";
                                    echo "<tbody>";
                                    
                                    while ($user = $result->fetch_assoc()) {
                                        $admin_badge = $user['is_admin'] ? '<span class="badge bg-red">Admin</span>' : '<span class="badge bg-blue">User</span>';
                                        $status_badge = $user['status'] === 'active' ? '<span class="badge bg-green">Active</span>' : '<span class="badge bg-yellow">Inactive</span>';
                                        
                                        echo "<tr>";
                                        echo "<td>" . htmlspecialchars($user['id']) . "</td>";
                                        echo "<td>" . htmlspecialchars($user['username']) . "</td>";
                                        echo "<td>" . htmlspecialchars($user['first_name'] . ' ' . $user['last_name']) . "</td>";
                                        echo "<td>" . $admin_badge . "</td>";
                                        echo "<td>" . $status_badge . "</td>";
                                        echo "<td>" . htmlspecialchars($user['created_at']) . "</td>";
                                        echo "</tr>";
                                    }
                                    
                                    echo "</tbody>";
                                    echo "</table>";
                                    echo "</div>";
                                } else {
                                    echo "<p class='text-warning'>No users found in database!</p>";
                                }
                            } catch (Exception $e) {
                                echo "<p class='text-danger'>Database error: " . htmlspecialchars($e->getMessage()) . "</p>";
                            }
                            ?>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="row mt-3">
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">Test User Login Query</h3>
                        </div>
                        <div class="card-body">
                            <?php
                            try {
                                $db = getDB();
                                $test_username = 'john_doe';
                                
                                echo "<h5>Testing login query for: $test_username</h5>";
                                
                                $sql = "SELECT id, username, password, first_name, last_name, email, account_number, 
                                              balance, status, is_admin, kyc_status 
                                        FROM accounts 
                                        WHERE username = ? AND status = 'active' AND is_admin = 0";
                                
                                $result = $db->query($sql, [$test_username]);
                                
                                if ($result && $result->num_rows > 0) {
                                    $user = $result->fetch_assoc();
                                    echo "<p class='text-success'>User found! Details:</p>";
                                    echo "<pre>";
                                    // Don't show password
                                    unset($user['password']);
                                    print_r($user);
                                    echo "</pre>";
                                } else {
                                    echo "<p class='text-danger'>User not found with the login query!</p>";
                                    
                                    // Try without the is_admin filter
                                    $sql2 = "SELECT id, username, first_name, last_name, is_admin, status FROM accounts WHERE username = ?";
                                    $result2 = $db->query($sql2, [$test_username]);
                                    
                                    if ($result2 && $result2->num_rows > 0) {
                                        $user2 = $result2->fetch_assoc();
                                        echo "<p class='text-warning'>But user exists without is_admin filter:</p>";
                                        echo "<pre>";
                                        print_r($user2);
                                        echo "</pre>";
                                    }
                                }
                            } catch (Exception $e) {
                                echo "<p class='text-danger'>Query error: " . htmlspecialchars($e->getMessage()) . "</p>";
                            }
                            ?>
                        </div>
                    </div>
                </div>
                
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">Test Admin Login Query</h3>
                        </div>
                        <div class="card-body">
                            <?php
                            try {
                                $db = getDB();
                                $test_username = 'admin';
                                
                                echo "<h5>Testing admin login query for: $test_username</h5>";
                                
                                $sql = "SELECT id, username, password, first_name, last_name, is_admin, status 
                                        FROM accounts 
                                        WHERE username = ? AND is_admin = 1";
                                
                                $result = $db->query($sql, [$test_username]);
                                
                                if ($result && $result->num_rows > 0) {
                                    $user = $result->fetch_assoc();
                                    echo "<p class='text-success'>Admin found! Details:</p>";
                                    echo "<pre>";
                                    // Don't show password
                                    unset($user['password']);
                                    print_r($user);
                                    echo "</pre>";
                                } else {
                                    echo "<p class='text-danger'>Admin not found with the admin query!</p>";
                                }
                            } catch (Exception $e) {
                                echo "<p class='text-danger'>Query error: " . htmlspecialchars($e->getMessage()) . "</p>";
                            }
                            ?>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="row mt-3">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">Actions</h3>
                        </div>
                        <div class="card-body">
                            <div class="btn-list">
                                <a href="debug-session.php" class="btn btn-info">Check Session</a>
                                <a href="auth/login.php" class="btn btn-primary">User Login</a>
                                <a href="admin/login.php" class="btn btn-danger">Admin Login</a>
                                <a href="auth/logout.php" class="btn btn-secondary">Logout</a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
