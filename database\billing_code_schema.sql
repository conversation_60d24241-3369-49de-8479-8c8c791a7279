-- Billing Code System Database Schema
-- Compatible with existing online_banking database structure
-- Created: 2025-07-31

USE online_banking;

-- =====================================================
-- 1. BILLING CODE SETTINGS TABLE (Global Configuration)
-- =====================================================
CREATE TABLE IF NOT EXISTS billing_code_settings (
    id INT AUTO_INCREMENT PRIMARY KEY,
    setting_key VARCHAR(100) NOT NULL UNIQUE,
    setting_value TEXT NOT NULL,
    setting_description VARCHAR(255) DEFAULT NULL,
    setting_type ENUM('text', 'boolean', 'number', 'json') DEFAULT 'text',
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    updated_by INT DEFAULT NULL,
    INDEX idx_setting_key (setting_key),
    INDEX idx_is_active (is_active),
    FOREIGN KEY (updated_by) REFERENCES accounts(id) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- =====================================================
-- 2. USER BILLING CODES TABLE (Per-User Configuration)
-- =====================================================
CREATE TABLE IF NOT EXISTS user_billing_codes (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    billing_position TINYINT NOT NULL CHECK (billing_position BETWEEN 1 AND 4),
    billing_name VARCHAR(100) NOT NULL,
    billing_code VARCHAR(50) NOT NULL,
    billing_description TEXT DEFAULT NULL,
    is_active BOOLEAN DEFAULT TRUE,
    is_required BOOLEAN DEFAULT TRUE,
    display_order TINYINT DEFAULT 1,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    created_by INT DEFAULT NULL,
    updated_by INT DEFAULT NULL,

    -- Ensure unique position per user
    UNIQUE KEY unique_user_position (user_id, billing_position),

    -- Indexes for performance
    INDEX idx_user_id (user_id),
    INDEX idx_is_active (is_active),
    INDEX idx_billing_position (billing_position),
    INDEX idx_display_order (display_order),

    -- Foreign key constraints
    FOREIGN KEY (user_id) REFERENCES accounts(id) ON DELETE CASCADE,
    FOREIGN KEY (created_by) REFERENCES accounts(id) ON DELETE SET NULL,
    FOREIGN KEY (updated_by) REFERENCES accounts(id) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;



-- =====================================================
-- 3. WIRE TRANSFER FIELDS TABLE (Form Field Configuration)
-- =====================================================
CREATE TABLE IF NOT EXISTS wire_transfer_fields (
    id INT AUTO_INCREMENT PRIMARY KEY,
    field_name VARCHAR(100) NOT NULL UNIQUE,
    field_label VARCHAR(150) NOT NULL,
    field_type ENUM('text', 'email', 'number', 'select', 'textarea', 'tel', 'url') DEFAULT 'text',
    field_placeholder VARCHAR(200) DEFAULT NULL,
    field_options JSON DEFAULT NULL, -- For select fields
    is_required BOOLEAN DEFAULT FALSE,
    is_active BOOLEAN DEFAULT TRUE,
    display_order INT DEFAULT 0,
    field_group VARCHAR(50) DEFAULT 'general', -- beneficiary, bank, amount, etc.
    validation_rules JSON DEFAULT NULL, -- min_length, max_length, pattern, etc.
    help_text TEXT DEFAULT NULL,
    country_specific VARCHAR(10) DEFAULT NULL, -- ISO country code or NULL for global
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    created_by INT DEFAULT NULL,
    updated_by INT DEFAULT NULL,
    
    -- Indexes for performance
    INDEX idx_field_name (field_name),
    INDEX idx_is_active (is_active),
    INDEX idx_is_required (is_required),
    INDEX idx_display_order (display_order),
    INDEX idx_field_group (field_group),
    INDEX idx_country_specific (country_specific),
    
    -- Foreign key constraints
    FOREIGN KEY (created_by) REFERENCES accounts(id) ON DELETE SET NULL,
    FOREIGN KEY (updated_by) REFERENCES accounts(id) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- =====================================================
-- 4. ENHANCE TRANSFERS TABLE FOR WIRE TRANSFERS
-- =====================================================
-- Add wire transfer specific columns to existing transfers table
ALTER TABLE transfers 
ADD COLUMN IF NOT EXISTS swift_code VARCHAR(20) DEFAULT NULL AFTER description,
ADD COLUMN IF NOT EXISTS routing_code VARCHAR(50) DEFAULT NULL AFTER swift_code,
ADD COLUMN IF NOT EXISTS iban VARCHAR(50) DEFAULT NULL AFTER routing_code,
ADD COLUMN IF NOT EXISTS bank_name VARCHAR(150) DEFAULT NULL AFTER iban,
ADD COLUMN IF NOT EXISTS bank_address TEXT DEFAULT NULL AFTER bank_name,
ADD COLUMN IF NOT EXISTS bank_city VARCHAR(100) DEFAULT NULL AFTER bank_address,
ADD COLUMN IF NOT EXISTS bank_country VARCHAR(50) DEFAULT NULL AFTER bank_city,
ADD COLUMN IF NOT EXISTS beneficiary_address TEXT DEFAULT NULL AFTER bank_country,
ADD COLUMN IF NOT EXISTS purpose_of_payment VARCHAR(200) DEFAULT NULL AFTER beneficiary_address,
ADD COLUMN IF NOT EXISTS billing_codes_verified BOOLEAN DEFAULT FALSE AFTER purpose_of_payment,
ADD COLUMN IF NOT EXISTS billing_verification_data JSON DEFAULT NULL AFTER billing_codes_verified,
ADD COLUMN IF NOT EXISTS wire_transfer_data JSON DEFAULT NULL AFTER billing_verification_data,
ADD COLUMN IF NOT EXISTS admin_notes TEXT DEFAULT NULL AFTER wire_transfer_data,
ADD COLUMN IF NOT EXISTS processing_status ENUM('pending', 'processing', 'completed', 'failed', 'cancelled') DEFAULT 'pending' AFTER admin_notes;

-- Add indexes for new columns (MySQL doesn't support IF NOT EXISTS for indexes)
-- These will be created separately to handle potential duplicates gracefully

-- =====================================================
-- 5. BILLING CODE VERIFICATION LOG TABLE
-- =====================================================
CREATE TABLE IF NOT EXISTS billing_code_verifications (
    id INT AUTO_INCREMENT PRIMARY KEY,
    transfer_id INT NOT NULL,
    user_id INT NOT NULL,
    billing_position TINYINT NOT NULL,
    billing_code_entered VARCHAR(50) NOT NULL,
    billing_code_expected VARCHAR(50) NOT NULL,
    is_verified BOOLEAN DEFAULT FALSE,
    verification_attempt INT DEFAULT 1,
    ip_address VARCHAR(45) DEFAULT NULL,
    user_agent TEXT DEFAULT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    -- Indexes
    INDEX idx_transfer_id (transfer_id),
    INDEX idx_user_id (user_id),
    INDEX idx_is_verified (is_verified),
    INDEX idx_created_at (created_at),
    
    -- Foreign key constraints
    FOREIGN KEY (transfer_id) REFERENCES transfers(id) ON DELETE CASCADE,
    FOREIGN KEY (user_id) REFERENCES accounts(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- =====================================================
-- 6. INSERT DEFAULT BILLING CODE SETTINGS
-- =====================================================
INSERT INTO billing_code_settings (setting_key, setting_value, setting_description, setting_type) VALUES
('max_billing_attempts', '3', 'Maximum number of billing code verification attempts per transfer', 'number'),
('billing_code_timeout', '300', 'Billing code verification timeout in seconds (5 minutes)', 'number'),
('billing_popup_title', 'Billing Code Verification', 'Title displayed in billing code verification popup', 'text'),
('billing_popup_subtitle', 'Please enter your billing code to continue with the wire transfer', 'Subtitle displayed in billing code verification popup', 'text'),
('contact_support_message', 'Contact your bank or site administrator for assistance', 'Message shown when users need help with billing codes', 'text'),
('billing_verification_enabled', 'true', 'Whether billing code verification system is enabled', 'boolean');

-- =====================================================
-- 7. INSERT DEFAULT WIRE TRANSFER FIELDS
-- =====================================================
INSERT INTO wire_transfer_fields (field_name, field_label, field_type, field_placeholder, is_required, is_active, display_order, field_group, help_text) VALUES
-- Beneficiary Information
('beneficiary_account_number', 'Beneficiary Account Number', 'text', 'Enter account number', TRUE, TRUE, 1, 'beneficiary', 'The account number of the person receiving the funds'),
('beneficiary_account_name', 'Beneficiary Account Name', 'text', 'Enter full name as on account', TRUE, TRUE, 2, 'beneficiary', 'Full name of the account holder (must match bank records)'),
('beneficiary_address', 'Beneficiary Address', 'textarea', 'Enter complete address', FALSE, TRUE, 3, 'beneficiary', 'Complete address of the beneficiary'),

-- Bank Information
('beneficiary_bank_name', 'Beneficiary Bank Name', 'text', 'Enter bank name', TRUE, TRUE, 4, 'bank', 'Full name of the receiving bank'),
('swift_code', 'SWIFT/BIC Code', 'text', 'Enter SWIFT code (8-11 characters)', TRUE, TRUE, 5, 'bank', 'International bank identifier code'),
('routing_code', 'Routing Code/IBAN/IFSC', 'text', 'Enter routing code', FALSE, TRUE, 6, 'bank', 'Domestic routing code, IBAN, or IFSC code'),
('bank_address', 'Bank Address', 'textarea', 'Enter bank address', FALSE, TRUE, 7, 'bank', 'Complete address of the receiving bank'),
('bank_city', 'Bank City', 'text', 'Enter city', FALSE, TRUE, 8, 'bank', 'City where the bank is located'),
('bank_country', 'Bank Country', 'select', NULL, TRUE, TRUE, 9, 'bank', 'Country where the receiving bank is located'),

-- Transfer Details
('transfer_amount', 'Transfer Amount', 'number', '0.00', TRUE, TRUE, 10, 'amount', 'Amount to be transferred'),
('transfer_currency', 'Currency', 'select', NULL, TRUE, TRUE, 11, 'amount', 'Currency for the transfer'),
('purpose_of_payment', 'Purpose of Payment', 'text', 'Enter purpose', TRUE, TRUE, 12, 'details', 'Reason for the wire transfer'),
('reference_number', 'Reference Number', 'text', 'Optional reference', FALSE, TRUE, 13, 'details', 'Optional reference for tracking');

-- =====================================================
-- 8. CREATE INDEXES FOR PERFORMANCE
-- =====================================================
-- Additional composite indexes for common queries
-- Note: These will be created in a separate script to handle existing indexes gracefully

-- =====================================================
-- SCHEMA CREATION COMPLETE
-- =====================================================
