<?php
/**
 * Test script for debugging top-up functionality
 */

session_start();
require_once '../../config/config.php';

// Set JSON response header
header('Content-Type: application/json');

// Enable error reporting for debugging
error_reporting(E_ALL);
ini_set('display_errors', 1);

try {
    // Test database connection
    $db = getDB();
    echo json_encode(['success' => true, 'message' => 'Database connection successful']);
    
    // Test if virtual_card_transactions table exists
    $test_query = "SHOW TABLES LIKE 'virtual_card_transactions'";
    $result = $db->query($test_query);
    
    if ($result->num_rows > 0) {
        echo json_encode(['success' => true, 'message' => 'virtual_card_transactions table exists']);
    } else {
        echo json_encode(['success' => false, 'message' => 'virtual_card_transactions table does not exist']);
    }
    
} catch (Exception $e) {
    echo json_encode(['success' => false, 'message' => 'Error: ' . $e->getMessage()]);
}
?>
