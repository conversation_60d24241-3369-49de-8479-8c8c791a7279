<?php
require_once '../config/config.php';
requireAdmin();

$page_title = 'Cryptocurrency Wallets';

// Pagination settings
$records_per_page = 25;
$current_page = intval($_GET['page'] ?? 1);
$offset = ($current_page - 1) * $records_per_page;

// Filter parameters
$filter_status = $_GET['status'] ?? '';
$filter_crypto = $_GET['crypto'] ?? '';
$filter_user = $_GET['user'] ?? '';
$filter_balance_min = floatval($_GET['balance_min'] ?? 0);
$filter_balance_max = floatval($_GET['balance_max'] ?? 0);

// Build WHERE clause for filters
$where_conditions = [];
$params = [];

if (!empty($filter_status)) {
    $where_conditions[] = "cw.status = ?";
    $params[] = $filter_status;
}

if (!empty($filter_crypto)) {
    $where_conditions[] = "cw.cryptocurrency = ?";
    $params[] = $filter_crypto;
}

if (!empty($filter_user)) {
    $where_conditions[] = "(a.first_name LIKE ? OR a.last_name LIKE ? OR a.username LIKE ?)";
    $search_term = "%$filter_user%";
    $params[] = $search_term;
    $params[] = $search_term;
    $params[] = $search_term;
}

if ($filter_balance_min > 0) {
    $where_conditions[] = "cw.wallet_balance >= ?";
    $params[] = $filter_balance_min;
}

if ($filter_balance_max > 0) {
    $where_conditions[] = "cw.wallet_balance <= ?";
    $params[] = $filter_balance_max;
}

$where_clause = !empty($where_conditions) ? "WHERE " . implode(" AND ", $where_conditions) : "";

try {
    $db = getDB();

    // Get total count for pagination
    $count_query = "SELECT COUNT(*) as total
                    FROM crypto_wallets cw
                    LEFT JOIN accounts a ON cw.account_id = a.id
                    $where_clause";
    $count_result = $db->query($count_query, $params);
    $total_records = $count_result->fetch_assoc()['total'];
    $total_pages = ceil($total_records / $records_per_page);

    // Get crypto wallets with pagination
    $wallets_query = "SELECT cw.*,
                      a.first_name, a.last_name, a.username, a.account_number,
                      admin.first_name as admin_first_name, admin.last_name as admin_last_name
                      FROM crypto_wallets cw
                      LEFT JOIN accounts a ON cw.account_id = a.id
                      LEFT JOIN accounts admin ON cw.approved_by = admin.id
                      $where_clause
                      ORDER BY cw.created_at DESC
                      LIMIT $records_per_page OFFSET $offset";

    $wallets_result = $db->query($wallets_query, $params);
    $wallets = [];
    while ($row = $wallets_result->fetch_assoc()) {
        $wallets[] = $row;
    }

    // Get summary statistics
    $stats_query = "SELECT
                    COUNT(*) as total_wallets,
                    COUNT(CASE WHEN status = 'active' THEN 1 END) as active_wallets,
                    COUNT(CASE WHEN status = 'pending' THEN 1 END) as pending_wallets,
                    COUNT(CASE WHEN status = 'blocked' THEN 1 END) as blocked_wallets,
                    COUNT(DISTINCT cryptocurrency) as crypto_types
                    FROM crypto_wallets";
    $stats_result = $db->query($stats_query);
    $stats = $stats_result->fetch_assoc();

    // Get cryptocurrency types for filter
    $crypto_types_result = $db->query("SELECT DISTINCT cryptocurrency FROM crypto_wallets ORDER BY cryptocurrency");
    $crypto_types = [];
    while ($crypto = $crypto_types_result->fetch_assoc()) {
        $crypto_types[] = $crypto['cryptocurrency'];
    }

} catch (Exception $e) {
    $error = "Failed to load crypto wallets: " . $e->getMessage();
    $wallets = [];
    $total_records = 0;
    $total_pages = 0;
    $stats = ['total_wallets' => 0, 'active_wallets' => 0, 'pending_wallets' => 0, 'blocked_wallets' => 0, 'crypto_types' => 0];
    $crypto_types = [];
}

include 'includes/admin-header.php';
?>

<!-- Breadcrumb -->
<nav aria-label="breadcrumb" style="margin-bottom: 1rem;">
    <ol class="breadcrumb">
        <li class="breadcrumb-item"><a href="index.php">Admin</a></li>
        <li class="breadcrumb-item active" aria-current="page">Crypto Wallets</li>
    </ol>
</nav>

<!-- Error Message -->
<?php if (isset($error)): ?>
<div class="alert alert-danger" role="alert">
    <div class="d-flex">
        <div><i class="fas fa-exclamation-circle me-2"></i></div>
        <div>
            <h4 class="alert-title">Error!</h4>
            <div class="text-muted"><?php echo htmlspecialchars($error); ?></div>
        </div>
    </div>
</div>
<?php endif; ?>

<!-- Statistics Cards -->
<div class="row row-cards mb-4">
    <div class="col-sm-6 col-lg-3">
        <div class="card card-sm">
            <div class="card-body">
                <div class="row align-items-center">
                    <div class="col-auto">
                        <span class="bg-primary text-white avatar">
                            <i class="fab fa-bitcoin"></i>
                        </span>
                    </div>
                    <div class="col">
                        <div class="font-weight-medium"><?php echo number_format($stats['total_wallets']); ?></div>
                        <div class="text-muted">Total Wallets</div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-sm-6 col-lg-3">
        <div class="card card-sm">
            <div class="card-body">
                <div class="row align-items-center">
                    <div class="col-auto">
                        <span class="bg-success text-white avatar">
                            <i class="fas fa-check-circle"></i>
                        </span>
                    </div>
                    <div class="col">
                        <div class="font-weight-medium"><?php echo number_format($stats['active_wallets']); ?></div>
                        <div class="text-muted">Active Wallets</div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-sm-6 col-lg-3">
        <div class="card card-sm">
            <div class="card-body">
                <div class="row align-items-center">
                    <div class="col-auto">
                        <span class="bg-warning text-white avatar">
                            <i class="fas fa-clock"></i>
                        </span>
                    </div>
                    <div class="col">
                        <div class="font-weight-medium"><?php echo number_format($stats['pending_wallets']); ?></div>
                        <div class="text-muted">Pending</div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-sm-6 col-lg-3">
        <div class="card card-sm">
            <div class="card-body">
                <div class="row align-items-center">
                    <div class="col-auto">
                        <span class="bg-info text-white avatar">
                            <i class="fas fa-coins"></i>
                        </span>
                    </div>
                    <div class="col">
                        <div class="font-weight-medium"><?php echo number_format($stats['crypto_types']); ?></div>
                        <div class="text-muted">Crypto Types</div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Filters Card -->
<div class="row row-cards mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h3 class="card-title">
                    <i class="fas fa-filter me-2"></i>
                    Wallet Filters
                </h3>
                <div class="card-actions">
                    <a href="crypto-wallets.php" class="btn btn-outline-secondary btn-sm">
                        <i class="fas fa-times me-2"></i>
                        Clear Filters
                    </a>
                </div>
            </div>
            <div class="card-body">
                <form method="GET" action="" class="row g-2">
                    <div class="col-md-2">
                        <label class="form-label">Status</label>
                        <select name="status" class="form-select form-select-sm">
                            <option value="">All Status</option>
                            <option value="pending" <?php echo $filter_status === 'pending' ? 'selected' : ''; ?>>Pending</option>
                            <option value="active" <?php echo $filter_status === 'active' ? 'selected' : ''; ?>>Active</option>
                            <option value="inactive" <?php echo $filter_status === 'inactive' ? 'selected' : ''; ?>>Inactive</option>
                            <option value="blocked" <?php echo $filter_status === 'blocked' ? 'selected' : ''; ?>>Blocked</option>
                            <option value="suspended" <?php echo $filter_status === 'suspended' ? 'selected' : ''; ?>>Suspended</option>
                        </select>
                    </div>

                    <div class="col-md-2">
                        <label class="form-label">Cryptocurrency</label>
                        <select name="crypto" class="form-select form-select-sm">
                            <option value="">All Crypto</option>
                            <option value="BTC" <?php echo $filter_crypto === 'BTC' ? 'selected' : ''; ?>>Bitcoin (BTC)</option>
                            <option value="ETH" <?php echo $filter_crypto === 'ETH' ? 'selected' : ''; ?>>Ethereum (ETH)</option>
                            <option value="LTC" <?php echo $filter_crypto === 'LTC' ? 'selected' : ''; ?>>Litecoin (LTC)</option>
                            <option value="BCH" <?php echo $filter_crypto === 'BCH' ? 'selected' : ''; ?>>Bitcoin Cash (BCH)</option>
                            <option value="ADA" <?php echo $filter_crypto === 'ADA' ? 'selected' : ''; ?>>Cardano (ADA)</option>
                            <option value="DOT" <?php echo $filter_crypto === 'DOT' ? 'selected' : ''; ?>>Polkadot (DOT)</option>
                        </select>
                    </div>

                    <div class="col-md-2">
                        <label class="form-label">User</label>
                        <input type="text" name="user" class="form-control form-control-sm" placeholder="Search user..." value="<?php echo htmlspecialchars($filter_user); ?>">
                    </div>

                    <div class="col-md-3">
                        <label class="form-label">Balance Range</label>
                        <div class="input-group input-group-sm">
                            <input type="number" name="balance_min" class="form-control" step="0.00000001" placeholder="Min" value="<?php echo $filter_balance_min > 0 ? $filter_balance_min : ''; ?>">
                            <span class="input-group-text">-</span>
                            <input type="number" name="balance_max" class="form-control" step="0.00000001" placeholder="Max" value="<?php echo $filter_balance_max > 0 ? $filter_balance_max : ''; ?>">
                        </div>
                    </div>

                    <div class="col-md-3">
                        <label class="form-label">&nbsp;</label>
                        <div class="btn-group d-block" role="group">
                            <button type="submit" class="btn btn-primary btn-sm">
                                <i class="fas fa-search me-1"></i>
                                Filter
                            </button>
                            <a href="crypto-wallets.php" class="btn btn-outline-secondary btn-sm">
                                <i class="fas fa-times me-1"></i>
                                Clear
                            </a>
                        </div>
                    </div>

                    <div class="col-12 mt-3">
                        <div class="btn-group" role="group">
                            <a href="generate-crypto-wallet.php" class="btn btn-success btn-sm">
                                <i class="fas fa-plus me-1"></i>
                                Generate Wallet
                            </a>
                            <a href="crypto-operations.php" class="btn btn-info btn-sm">
                                <i class="fas fa-exchange-alt me-1"></i>
                                Crypto Operations
                            </a>
                            <a href="crypto-applications.php" class="btn btn-warning btn-sm">
                                <i class="fas fa-file-alt me-1"></i>
                                Applications
                            </a>
                            <a href="crypto-transactions.php" class="btn btn-outline-primary btn-sm">
                                <i class="fas fa-list me-1"></i>
                                Crypto Transactions
                            </a>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- Crypto Wallets Table -->
<div class="row row-cards">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h3 class="card-title">
                    <i class="fab fa-bitcoin me-2"></i>
                    Cryptocurrency Wallets
                    <span class="badge bg-secondary ms-2"><?php echo number_format($total_records); ?> total</span>
                </h3>
                <div class="card-actions">
                    <span class="text-muted">
                        Showing <?php echo number_format(($current_page - 1) * $records_per_page + 1); ?> -
                        <?php echo number_format(min($current_page * $records_per_page, $total_records)); ?>
                        of <?php echo number_format($total_records); ?> wallets
                    </span>
                </div>
            </div>
            <div class="card-body p-0">
                <?php if (!empty($wallets)): ?>
                <div class="table-responsive">
                    <table class="table table-vcenter card-table table-sm">
                        <thead>
                            <tr>
                                <th class="w-1">#</th>
                                <th>Wallet Details</th>
                                <th>Owner</th>
                                <th>Cryptocurrency</th>
                                <th>Balance</th>
                                <th>Status</th>
                                <th>Created</th>
                                <th class="w-1">Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php
                            $row_number = ($current_page - 1) * $records_per_page + 1;
                            foreach ($wallets as $wallet):
                            ?>
                            <tr>
                                <!-- Row Number -->
                                <td>
                                    <span class="text-muted"><?php echo $row_number++; ?></span>
                                </td>

                                <!-- Wallet Details -->
                                <td>
                                    <div class="d-flex flex-column">
                                        <div class="fw-bold">ID: <?php echo $wallet['wallet_id']; ?></div>
                                        <small class="text-muted font-monospace"><?php echo substr($wallet['wallet_address'], 0, 8) . '...' . substr($wallet['wallet_address'], -8); ?></small>
                                        <small class="text-muted"><?php echo htmlspecialchars($wallet['wallet_name']); ?></small>
                                    </div>
                                </td>

                                <!-- Owner -->
                                <td>
                                    <div class="d-flex align-items-center">
                                        <div class="avatar avatar-xs me-2">
                                            <?php echo strtoupper(substr($wallet['first_name'] ?? 'U', 0, 1) . substr($wallet['last_name'] ?? 'U', 0, 1)); ?>
                                        </div>
                                        <div>
                                            <div class="fw-bold" style="font-size: 0.85rem;"><?php echo htmlspecialchars(($wallet['first_name'] ?? 'Unknown') . ' ' . ($wallet['last_name'] ?? 'User')); ?></div>
                                            <small class="text-muted">@<?php echo htmlspecialchars($wallet['username'] ?? 'unknown'); ?></small>
                                        </div>
                                    </div>
                                </td>

                                <!-- Cryptocurrency -->
                                <td>
                                    <?php
                                    $crypto_colors = [
                                        'BTC' => 'warning',
                                        'ETH' => 'info',
                                        'LTC' => 'secondary',
                                        'BCH' => 'success',
                                        'ADA' => 'primary',
                                        'DOT' => 'danger'
                                    ];
                                    $crypto_icons = [
                                        'BTC' => 'fab fa-bitcoin',
                                        'ETH' => 'fab fa-ethereum',
                                        'LTC' => 'fas fa-coins',
                                        'BCH' => 'fab fa-bitcoin',
                                        'ADA' => 'fas fa-coins',
                                        'DOT' => 'fas fa-circle'
                                    ];
                                    $color = $crypto_colors[$wallet['cryptocurrency']] ?? 'secondary';
                                    $icon = $crypto_icons[$wallet['cryptocurrency']] ?? 'fas fa-coins';
                                    ?>
                                    <span class="badge bg-<?php echo $color; ?> badge-sm">
                                        <i class="<?php echo $icon; ?> me-1"></i>
                                        <?php echo $wallet['cryptocurrency']; ?>
                                    </span>
                                </td>

                                <!-- Balance -->
                                <td>
                                    <span class="fw-bold text-primary">
                                        <?php echo number_format($wallet['wallet_balance'], 8); ?> <?php echo $wallet['cryptocurrency']; ?>
                                    </span>
                                    <br>
                                    <small class="text-muted">Limit: <?php echo number_format($wallet['daily_limit'], 8); ?></small>
                                </td>

                                <!-- Status -->
                                <td>
                                    <?php
                                    $status_colors = [
                                        'pending' => 'warning',
                                        'active' => 'success',
                                        'inactive' => 'secondary',
                                        'blocked' => 'danger',
                                        'suspended' => 'dark'
                                    ];
                                    $status_color = $status_colors[$wallet['status']] ?? 'secondary';
                                    ?>
                                    <span class="badge bg-<?php echo $status_color; ?> badge-sm">
                                        <?php echo ucfirst($wallet['status']); ?>
                                    </span>
                                </td>

                                <!-- Created -->
                                <td>
                                    <div class="d-flex flex-column">
                                        <div class="text-muted" style="font-size: 0.85rem;"><?php echo date('M j, Y', strtotime($wallet['created_at'])); ?></div>
                                        <small class="text-muted"><?php echo date('g:i A', strtotime($wallet['created_at'])); ?></small>
                                    </div>
                                </td>

                                <!-- Actions -->
                                <td>
                                    <div class="btn-list">
                                        <button type="button" class="btn btn-sm btn-outline-primary" onclick="viewWallet(<?php echo htmlspecialchars(json_encode($wallet)); ?>)" title="View Details">
                                            <i class="fas fa-eye"></i>
                                        </button>
                                        <?php if ($wallet['status'] === 'pending'): ?>
                                        <button type="button" class="btn btn-sm btn-outline-success" onclick="updateWalletStatus(<?php echo $wallet['wallet_id']; ?>, 'active')" title="Activate">
                                            <i class="fas fa-check"></i>
                                        </button>
                                        <?php elseif ($wallet['status'] === 'active'): ?>
                                        <button type="button" class="btn btn-sm btn-outline-warning" onclick="updateWalletStatus(<?php echo $wallet['wallet_id']; ?>, 'blocked')" title="Block">
                                            <i class="fas fa-ban"></i>
                                        </button>
                                        <?php endif; ?>
                                    </div>
                                </td>
                            </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
                <?php else: ?>
                <div class="empty">
                    <div class="empty-img">
                        <i class="fab fa-bitcoin" style="font-size: 3rem; color: #6c757d;"></i>
                    </div>
                    <p class="empty-title">No crypto wallets found</p>
                    <p class="empty-subtitle text-muted">
                        <?php if (!empty(array_filter([$filter_status, $filter_crypto, $filter_user]))): ?>
                        Try adjusting your filters to see more results.
                        <?php else: ?>
                        No cryptocurrency wallets have been created yet.
                        <?php endif; ?>
                    </p>
                    <div class="empty-action">
                        <a href="generate-crypto-wallet.php" class="btn btn-primary">
                            <i class="fas fa-plus me-2"></i>
                            Generate First Wallet
                        </a>
                    </div>
                </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<?php include 'includes/admin-footer.php'; ?>
