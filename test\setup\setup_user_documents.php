<?php
require_once '../config/config.php';

try {
    $db = getDB();
    
    echo "Setting up user_documents table...\n";
    
    // Read and execute the SQL file
    $sql = file_get_contents('database/create_user_documents_table.sql');
    
    // Split SQL statements
    $statements = explode(';', $sql);
    
    foreach ($statements as $statement) {
        $statement = trim($statement);
        if (!empty($statement)) {
            try {
                $db->query($statement);
                echo "✓ Executed: " . substr($statement, 0, 50) . "...\n";
            } catch (Exception $e) {
                echo "✗ Error executing statement: " . $e->getMessage() . "\n";
                echo "Statement: " . substr($statement, 0, 100) . "...\n";
            }
        }
    }
    
    // Check if table was created successfully
    $result = $db->query("SELECT COUNT(*) as count FROM user_documents");
    if ($result) {
        $count = $result->fetch_assoc()['count'];
        echo "\n✓ Table created successfully! Found " . $count . " documents.\n";
        
        // Test a sample document
        if ($count > 0) {
            $sample = $db->query("SELECT id, document_type, document_name, verification_status FROM user_documents LIMIT 1");
            if ($sample && $sample->num_rows > 0) {
                $doc = $sample->fetch_assoc();
                echo "Sample document: ID=" . $doc['id'] . ", Type=" . $doc['document_type'] . ", Name=" . $doc['document_name'] . ", Status=" . $doc['verification_status'] . "\n";
            }
        }
    }
    
    echo "\nSetup complete! You can now test the document preview functionality.\n";
    
} catch (Exception $e) {
    echo "Error: " . $e->getMessage() . "\n";
}
?>
