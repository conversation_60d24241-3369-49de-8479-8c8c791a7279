<?php
/**
 * Test Script for 2FA Login Flow
 * Tests the new conditional 2FA logic
 */

require_once 'config/config.php';
require_once 'includes/user-2fa-functions.php';

echo "<h2>2FA Login Flow Test</h2>";

// Test user credentials (you can modify these)
$test_username = 'testuser';
$test_password = 'password123';

echo "<h3>Testing Login Flow Logic</h3>";

try {
    $db = getDB();
    
    // Find test user
    $sql = "SELECT id, username, first_name, last_name, email, password, account_status, is_admin 
            FROM accounts WHERE username = ? OR email = ? OR account_number = ?";
    $result = $db->query($sql, [$test_username, $test_username, $test_username]);
    
    if ($result && $result->num_rows === 1) {
        $user = $result->fetch_assoc();
        echo "<p style='color: green;'>✓ User found: " . htmlspecialchars($user['username']) . "</p>";
        
        // Test password verification
        if (verifyPassword($test_password, $user['password'])) {
            echo "<p style='color: green;'>✓ Password verification successful</p>";
            
            // Test 2FA logic
            $user_id = $user['id'];
            
            // Get security settings
            $security_settings = getUserSecuritySettings($user_id);
            echo "<h4>Current Security Settings:</h4>";
            if ($security_settings) {
                echo "<ul>";
                echo "<li>OTP Enabled: " . ($security_settings['otp_enabled'] ? 'Yes' : 'No') . "</li>";
                echo "<li>Google 2FA Enabled: " . ($security_settings['google_2fa_enabled'] ? 'Yes' : 'No') . "</li>";
                echo "<li>2FA Required: " . ($security_settings['require_2fa'] ? 'Yes' : 'No') . "</li>";
                echo "<li>Allow Remember Device: " . ($security_settings['allow_remember_device'] ? 'Yes' : 'No') . "</li>";
                echo "</ul>";
            } else {
                echo "<p style='color: red;'>✗ Could not retrieve security settings</p>";
            }
            
            // Test the main logic
            $should_verify_otp = shouldUserVerifyOTP($user_id);
            echo "<h4>Login Flow Decision:</h4>";
            
            if ($should_verify_otp) {
                echo "<p style='color: orange;'>🔒 User should be redirected to OTP verification</p>";
                echo "<p><strong>Reason:</strong> ";
                if ($security_settings['otp_enabled'] && $security_settings['require_2fa']) {
                    echo "OTP is enabled AND 2FA is required";
                } elseif ($security_settings['google_2fa_enabled']) {
                    echo "Google 2FA is enabled";
                } else {
                    echo "Default security policy (should not happen)";
                }
                echo "</p>";
            } else {
                echo "<p style='color: green;'>🚀 User should be logged in directly to dashboard</p>";
                echo "<p><strong>Reason:</strong> ";
                if (!$security_settings['otp_enabled']) {
                    echo "OTP is disabled";
                } elseif (!$security_settings['require_2fa']) {
                    echo "2FA is not required";
                } else {
                    echo "No 2FA methods are enabled";
                }
                echo "</p>";
            }
            
            // Test 2FA status
            $two_factor_status = getUserTwoFactorStatus($user_id);
            echo "<h4>2FA Status Summary:</h4>";
            echo "<ul>";
            echo "<li>Is Protected: " . ($two_factor_status['is_protected'] ? 'Yes' : 'No') . "</li>";
            echo "<li>OTP Enabled: " . ($two_factor_status['otp_enabled'] ? 'Yes' : 'No') . "</li>";
            echo "<li>Google 2FA Enabled: " . ($two_factor_status['google_2fa_enabled'] ? 'Yes' : 'No') . "</li>";
            echo "<li>2FA Required: " . ($two_factor_status['require_2fa'] ? 'Yes' : 'No') . "</li>";
            echo "</ul>";
            
            if (!empty($two_factor_status['recommendations'])) {
                echo "<h4>Security Recommendations:</h4>";
                echo "<ul>";
                foreach ($two_factor_status['recommendations'] as $recommendation) {
                    echo "<li>" . htmlspecialchars($recommendation) . "</li>";
                }
                echo "</ul>";
            }
            
        } else {
            echo "<p style='color: red;'>✗ Password verification failed</p>";
        }
        
    } else {
        echo "<p style='color: red;'>✗ User not found. Please create a test user first or update the username.</p>";
        echo "<p>You can create a test user by running <code>create_test_user.php</code></p>";
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'>✗ Error: " . htmlspecialchars($e->getMessage()) . "</p>";
}

echo "<hr>";
echo "<h3>Test Different 2FA Scenarios</h3>";

// Test scenarios
$scenarios = [
    [
        'name' => 'OTP Enabled + 2FA Required',
        'otp_enabled' => 1,
        'require_2fa' => 1,
        'google_2fa_enabled' => 0,
        'expected' => 'OTP Verification'
    ],
    [
        'name' => 'OTP Enabled + 2FA Optional',
        'otp_enabled' => 1,
        'require_2fa' => 0,
        'google_2fa_enabled' => 0,
        'expected' => 'Direct Login'
    ],
    [
        'name' => 'OTP Disabled + 2FA Required',
        'otp_enabled' => 0,
        'require_2fa' => 1,
        'google_2fa_enabled' => 0,
        'expected' => 'Direct Login'
    ],
    [
        'name' => 'Google 2FA Enabled',
        'otp_enabled' => 0,
        'require_2fa' => 0,
        'google_2fa_enabled' => 1,
        'expected' => 'OTP Verification'
    ],
    [
        'name' => 'All Disabled',
        'otp_enabled' => 0,
        'require_2fa' => 0,
        'google_2fa_enabled' => 0,
        'expected' => 'Direct Login'
    ]
];

echo "<table border='1' cellpadding='5' cellspacing='0' style='border-collapse: collapse;'>";
echo "<tr><th>Scenario</th><th>OTP</th><th>2FA Req</th><th>Google 2FA</th><th>Expected</th><th>Actual</th><th>Result</th></tr>";

foreach ($scenarios as $scenario) {
    // Simulate the logic
    $should_verify = ($scenario['otp_enabled'] == 1 && $scenario['require_2fa'] == 1) || 
                     ($scenario['google_2fa_enabled'] == 1);
    
    $actual = $should_verify ? 'OTP Verification' : 'Direct Login';
    $result = ($actual === $scenario['expected']) ? '✓ Pass' : '✗ Fail';
    $result_color = ($actual === $scenario['expected']) ? 'green' : 'red';
    
    echo "<tr>";
    echo "<td>" . htmlspecialchars($scenario['name']) . "</td>";
    echo "<td>" . ($scenario['otp_enabled'] ? 'On' : 'Off') . "</td>";
    echo "<td>" . ($scenario['require_2fa'] ? 'Yes' : 'No') . "</td>";
    echo "<td>" . ($scenario['google_2fa_enabled'] ? 'On' : 'Off') . "</td>";
    echo "<td>" . htmlspecialchars($scenario['expected']) . "</td>";
    echo "<td>" . htmlspecialchars($actual) . "</td>";
    echo "<td style='color: $result_color;'><strong>$result</strong></td>";
    echo "</tr>";
}

echo "</table>";

echo "<hr>";
echo "<h3>Quick Actions</h3>";
echo "<p><a href='login.php'>Test Login Page</a></p>";
echo "<p><a href='dashboard/security/'>View Security Settings</a></p>";
echo "<p><a href='admin/user-security-management.php'>Admin Security Management</a></p>";
echo "<p><a href='create_test_user.php'>Create Test User</a></p>";

?>
