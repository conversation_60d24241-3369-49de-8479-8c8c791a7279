-- Create user security settings table
CREATE TABLE IF NOT EXISTS user_security_settings (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    otp_enabled TINYINT(1) DEFAULT 1,
    google_2fa_enabled TINYINT(1) DEFAULT 0,
    require_2fa TINYINT(1) DEFAULT 1,
    allow_remember_device TINYINT(1) DEFAULT 0,
    login_attempts_limit INT DEFAULT 5,
    lockout_duration INT DEFAULT 30,
    otp_expiry_minutes INT DEFAULT 10,
    failed_attempts INT DEFAULT 0,
    locked_until DATETIME NULL,
    last_login_attempt DATETIME NULL,
    created_by INT NULL,
    updated_by INT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    UNIQUE KEY unique_user_id (user_id),
    INDEX idx_user_id (user_id),
    INDEX idx_otp_enabled (otp_enabled),
    INDEX idx_locked_until (locked_until),
    FOREIGN KEY (user_id) REFERENCES accounts(id) ON DELETE CASCADE,
    FOREIGN KEY (created_by) REFERENCES accounts(id) ON DELETE SET NULL,
    FOREIGN KEY (updated_by) REFERENCES accounts(id) ON DELETE SET NULL
);

-- Create user security history table for audit trail
CREATE TABLE IF NOT EXISTS user_security_history (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    setting_changed VARCHAR(50) NOT NULL,
    old_value VARCHAR(255),
    new_value VARCHAR(255),
    reason TEXT,
    changed_by INT NOT NULL,
    changed_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    ip_address VARCHAR(45),
    user_agent TEXT,
    INDEX idx_user_id (user_id),
    INDEX idx_changed_by (changed_by),
    INDEX idx_changed_at (changed_at),
    FOREIGN KEY (user_id) REFERENCES accounts(id) ON DELETE CASCADE,
    FOREIGN KEY (changed_by) REFERENCES accounts(id) ON DELETE CASCADE
);

-- Insert default security settings for existing users
INSERT IGNORE INTO user_security_settings (user_id, otp_enabled, google_2fa_enabled, require_2fa, allow_remember_device, login_attempts_limit, lockout_duration, otp_expiry_minutes)
SELECT id, 0, 0, 0, 1, 5, 30, 10 FROM accounts WHERE is_admin = 0;

-- Insert default security settings for admin users (more secure)
INSERT IGNORE INTO user_security_settings (user_id, otp_enabled, google_2fa_enabled, require_2fa, allow_remember_device, login_attempts_limit, lockout_duration, otp_expiry_minutes)
SELECT id, 1, 0, 1, 0, 3, 60, 5 FROM accounts WHERE is_admin = 1;
