<?php
require_once '../config/config.php';
requireLogin();

$page_title = 'Payments & Transfers';
$site_name = getBankName();

// Get user data
try {
    $db = getDB();
    $user_id = $_SESSION['user_id'];

    // Get user's recent transfers
    $transfers_sql = "SELECT t.*,
                             CASE
                                 WHEN t.sender_id = ? THEN 'sent'
                                 ELSE 'received'
                             END as direction,
                             CASE
                                 WHEN t.sender_id = ? THEN t.recipient_name
                                 ELSE (SELECT CONCAT(first_name, ' ', last_name) FROM accounts WHERE id = t.sender_id)
                             END as other_party
                      FROM transfers t
                      WHERE (t.sender_id = ? OR t.recipient_id = ?)
                      ORDER BY t.created_at DESC
                      LIMIT 10";

    $transfers_result = $db->query($transfers_sql, [$user_id, $user_id, $user_id, $user_id]);
    $recent_transfers = [];
    while ($transfer = $transfers_result->fetch_assoc()) {
        $recent_transfers[] = $transfer;
    }

    // Get user's beneficiaries
    $beneficiaries_sql = "SELECT * FROM beneficiaries WHERE user_id = ? ORDER BY is_favorite DESC, name ASC";
    $beneficiaries_result = $db->query($beneficiaries_sql, [$user_id]);
    $beneficiaries = [];
    while ($beneficiary = $beneficiaries_result->fetch_assoc()) {
        $beneficiaries[] = $beneficiary;
    }

    // Get transfer statistics
    $stats_sql = "SELECT
                    COUNT(*) as total_transfers,
                    SUM(CASE WHEN sender_id = ? THEN amount ELSE 0 END) as total_sent,
                    SUM(CASE WHEN recipient_id = ? THEN amount ELSE 0 END) as total_received,
                    COUNT(CASE WHEN status = 'pending' THEN 1 END) as pending_transfers
                  FROM transfers
                  WHERE (sender_id = ? OR recipient_id = ?)
                  AND MONTH(created_at) = MONTH(CURRENT_DATE())
                  AND YEAR(created_at) = YEAR(CURRENT_DATE())";

    $stats_result = $db->query($stats_sql, [$user_id, $user_id, $user_id, $user_id]);
    $transfer_stats = $stats_result->fetch_assoc();

    // Get account balance
    $balance_sql = "SELECT balance FROM accounts WHERE id = ?";
    $balance_result = $db->query($balance_sql, [$user_id]);
    $account_balance = $balance_result->fetch_assoc()['balance'];

} catch (Exception $e) {
    error_log("Payments page error: " . $e->getMessage());
    $recent_transfers = [];
    $beneficiaries = [];
    $transfer_stats = ['total_transfers' => 0, 'total_sent' => 0, 'total_received' => 0, 'pending_transfers' => 0];
    $account_balance = 0;
}

// Include header
require_once '../includes/dashboard/header.php';

// Include sidebar
require_once '../includes/dashboard/sidebar.php';
?>

<!-- Main Content -->
<div class="main-content">
    <!-- Top Bar -->
    <div class="top-bar">
        <h1>Payments & Transfers</h1>
        <div class="top-bar-actions">
            <button class="btn-outline" onclick="showSendMoneyModal()">
                <svg width="16" height="16" fill="currentColor" viewBox="0 0 16 16">
                    <path d="M15.854.146a.5.5 0 01.11.54L13.026 8.03A4.5 4.5 0 018 12.5a4.5 4.5 0 115.026-7.47L15.964.686a.5.5 0 01-.11-.54z"/>
                    <path d="M11.5 6.027a.5.5 0 11.998-.06A3.5 3.5 0 018.5 9 3.5 3.5 0 0112 5.5a.5.5 0 01-.5.527z"/>
                </svg>
                Send Money
            </button>
            <button class="btn-primary" onclick="showRequestMoneyModal()">
                <svg width="16" height="16" fill="currentColor" viewBox="0 0 16 16">
                    <path d="M8 15A7 7 0 118 1a7 7 0 010 14zm0 1A8 8 0 108 0a8 8 0 000 16z"/>
                    <path d="M8 4a.5.5 0 01.5.5v3h3a.5.5 0 010 1h-3v3a.5.5 0 01-1 0v-3h-3a.5.5 0 010-1h3v-3A.5.5 0 018 4z"/>
                </svg>
                Request Money
            </button>
            <div style="width: 40px; height: 40px; border-radius: 50%; background: #6366f1; display: flex; align-items: center; justify-content: center; color: white; font-weight: 600;">
                <?php echo strtoupper(substr($_SESSION['first_name'] ?? 'U', 0, 1)); ?>
            </div>
        </div>
    </div>

    <!-- Account Balance Card -->
    <div class="balance-card" style="margin-bottom: 2rem;">
        <div class="balance-header">
            <h2 class="balance-title">Available Balance</h2>
            <div style="display: flex; gap: 0.5rem;">
                <button class="balance-btn" onclick="showSendMoneyModal()">
                    <svg width="16" height="16" fill="currentColor" viewBox="0 0 16 16">
                        <path d="M15.854.146a.5.5 0 01.11.54L13.026 8.03A4.5 4.5 0 018 12.5a4.5 4.5 0 115.026-7.47L15.964.686a.5.5 0 01-.11-.54z"/>
                    </svg>
                    Send
                </button>
                <button class="balance-btn" onclick="showRequestMoneyModal()">
                    <svg width="16" height="16" fill="currentColor" viewBox="0 0 16 16">
                        <path d="M8 15A7 7 0 118 1a7 7 0 010 14zm0 1A8 8 0 108 0a8 8 0 000 16z"/>
                        <path d="M8 4a.5.5 0 01.5.5v3h3a.5.5 0 010 1h-3v3a.5.5 0 01-1 0v-3h-3a.5.5 0 010-1h3v-3A.5.5 0 018 4z"/>
                    </svg>
                    Request
                </button>
            </div>
        </div>
        <h1 class="balance-amount">$<?php echo number_format($account_balance, 2); ?></h1>
        <div style="display: flex; gap: 1rem; margin-top: 1rem;">
            <div>
                <div style="font-size: 0.875rem; color: #6b7280;">This Month Sent</div>
                <div style="font-weight: 600; color: #ef4444;">-$<?php echo number_format($transfer_stats['total_sent'], 2); ?></div>
            </div>
            <div>
                <div style="font-size: 0.875rem; color: #6b7280;">This Month Received</div>
                <div style="font-weight: 600; color: #10b981;">+$<?php echo number_format($transfer_stats['total_received'], 2); ?></div>
            </div>
        </div>
    </div>

    <!-- Transfer Statistics -->
    <div class="stats-grid" style="margin-bottom: 2rem;">
        <div class="stat-card">
            <h3 class="stat-title">Total Transfers</h3>
            <p class="stat-value"><?php echo number_format($transfer_stats['total_transfers']); ?></p>
            <p class="stat-change">This month</p>
        </div>
        <div class="stat-card">
            <h3 class="stat-title">Money Sent</h3>
            <p class="stat-value">$<?php echo number_format($transfer_stats['total_sent'], 2); ?></p>
            <p class="stat-change">This month</p>
        </div>
        <div class="stat-card">
            <h3 class="stat-title">Money Received</h3>
            <p class="stat-value">$<?php echo number_format($transfer_stats['total_received'], 2); ?></p>
            <p class="stat-change">This month</p>
        </div>
        <div class="stat-card">
            <h3 class="stat-title">Pending</h3>
            <p class="stat-value"><?php echo number_format($transfer_stats['pending_transfers']); ?></p>
            <p class="stat-change">Awaiting processing</p>
        </div>
    </div>

    <!-- Main Dashboard Grid -->
    <div class="dashboard-grid">
        <div class="main-section">
            <!-- Recent Transfers -->
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">Recent Transfers</h3>
                    <a href="../transfers/history.php" style="color: #6366f1; font-size: 0.875rem; text-decoration: none;">View All</a>
                </div>
                <div class="card-body">
                    <?php if (!empty($recent_transfers)): ?>
                        <?php foreach ($recent_transfers as $transfer): ?>
                            <div class="transaction-item">
                                <div class="transaction-icon" style="background: <?php echo $transfer['direction'] === 'sent' ? '#ef4444' : '#10b981'; ?>;">
                                    <?php if ($transfer['direction'] === 'sent'): ?>
                                        <svg width="16" height="16" fill="currentColor" viewBox="0 0 16 16">
                                            <path d="M15.854.146a.5.5 0 01.11.54L13.026 8.03A4.5 4.5 0 018 12.5a4.5 4.5 0 115.026-7.47L15.964.686a.5.5 0 01-.11-.54z"/>
                                        </svg>
                                    <?php else: ?>
                                        <svg width="16" height="16" fill="currentColor" viewBox="0 0 16 16">
                                            <path d="M8 15A7 7 0 118 1a7 7 0 010 14zm0 1A8 8 0 108 0a8 8 0 000 16z"/>
                                            <path d="M8 4a.5.5 0 01.5.5v3h3a.5.5 0 010 1h-3v3a.5.5 0 01-1 0v-3h-3a.5.5 0 010-1h3v-3A.5.5 0 018 4z"/>
                                        </svg>
                                    <?php endif; ?>
                                </div>
                                <div class="transaction-content">
                                    <div class="transaction-title">
                                        <?php echo $transfer['direction'] === 'sent' ? 'Sent to' : 'Received from'; ?>
                                        <?php echo htmlspecialchars($transfer['other_party']); ?>
                                    </div>
                                    <div class="transaction-subtitle">
                                        <?php echo htmlspecialchars($transfer['description'] ?? 'No description'); ?>
                                    </div>
                                </div>
                                <div style="text-align: right;">
                                    <div class="transaction-amount <?php echo $transfer['direction'] === 'sent' ? 'amount-negative' : 'amount-positive'; ?>">
                                        <?php echo $transfer['direction'] === 'sent' ? '-' : '+'; ?>$<?php echo number_format($transfer['amount'], 2); ?>
                                    </div>
                                    <div class="transaction-date">
                                        <?php echo date('M j, Y', strtotime($transfer['created_at'])); ?>
                                    </div>
                                    <span class="<?php echo $transfer['status'] === 'completed' ? 'status-completed' : 'status-pending'; ?>">
                                        <?php echo ucfirst($transfer['status']); ?>
                                    </span>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    <?php else: ?>
                        <div style="text-align: center; padding: 3rem 1rem;">
                            <div style="font-size: 3rem; color: #e5e7eb; margin-bottom: 1rem;">💸</div>
                            <h3 style="color: #6b7280; margin-bottom: 0.5rem;">No Transfers Yet</h3>
                            <p style="color: #9ca3af; margin-bottom: 2rem;">Start by sending money to someone or requesting a payment.</p>
                            <button class="btn-primary" onclick="showSendMoneyModal()">Send Your First Payment</button>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>

        <!-- Sidebar Section -->
        <div class="sidebar-section">
            <!-- Quick Send -->
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">Quick Send</h3>
                </div>
                <div style="padding: 1.5rem;">
                    <div style="display: flex; flex-direction: column; gap: 0.75rem;">
                        <button class="btn-primary" onclick="showSendMoneyModal()" style="width: 100%;">
                            <svg width="16" height="16" fill="currentColor" viewBox="0 0 16 16" style="margin-right: 0.5rem;">
                                <path d="M15.854.146a.5.5 0 01.11.54L13.026 8.03A4.5 4.5 0 018 12.5a4.5 4.5 0 115.026-7.47L15.964.686a.5.5 0 01-.11-.54z"/>
                            </svg>
                            Send Money
                        </button>
                        <button class="btn-outline" onclick="showRequestMoneyModal()" style="width: 100%;">
                            <svg width="16" height="16" fill="currentColor" viewBox="0 0 16 16" style="margin-right: 0.5rem;">
                                <path d="M8 15A7 7 0 118 1a7 7 0 010 14zm0 1A8 8 0 108 0a8 8 0 000 16z"/>
                                <path d="M8 4a.5.5 0 01.5.5v3h3a.5.5 0 010 1h-3v3a.5.5 0 01-1 0v-3h-3a.5.5 0 010-1h3v-3A.5.5 0 018 4z"/>
                            </svg>
                            Request Money
                        </button>
                        <button class="btn-outline" onclick="showAddBeneficiaryModal()" style="width: 100%;">
                            <svg width="16" height="16" fill="currentColor" viewBox="0 0 16 16" style="margin-right: 0.5rem;">
                                <path d="M8 4a.5.5 0 01.5.5v3h3a.5.5 0 010 1h-3v3a.5.5 0 01-1 0v-3h-3a.5.5 0 010-1h3v-3A.5.5 0 018 4z"/>
                            </svg>
                            Add Beneficiary
                        </button>
                    </div>
                </div>
            </div>

            <!-- Saved Beneficiaries -->
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">Saved Recipients</h3>
                    <button class="btn-outline" onclick="showAddBeneficiaryModal()" style="font-size: 0.75rem; padding: 0.25rem 0.5rem;">Add +</button>
                </div>
                <div style="padding: 1rem;">
                    <?php if (!empty($beneficiaries)): ?>
                        <div style="display: flex; flex-direction: column; gap: 0.75rem;">
                            <?php foreach (array_slice($beneficiaries, 0, 5) as $beneficiary): ?>
                                <div style="display: flex; align-items: center; gap: 0.75rem; padding: 0.75rem; border: 1px solid #e5e7eb; border-radius: 8px; cursor: pointer;" onclick="quickSendTo('<?php echo htmlspecialchars($beneficiary['name']); ?>', '<?php echo htmlspecialchars($beneficiary['account_number']); ?>')">
                                    <div style="width: 40px; height: 40px; border-radius: 50%; background: #6366f1; display: flex; align-items: center; justify-content: center; color: white; font-weight: 600; font-size: 0.875rem;">
                                        <?php echo strtoupper(substr($beneficiary['name'], 0, 2)); ?>
                                    </div>
                                    <div style="flex: 1;">
                                        <div style="font-weight: 500; margin-bottom: 0.25rem;">
                                            <?php echo htmlspecialchars($beneficiary['name']); ?>
                                            <?php if ($beneficiary['is_favorite']): ?>
                                                <span style="color: #f59e0b; margin-left: 0.25rem;">⭐</span>
                                            <?php endif; ?>
                                        </div>
                                        <div style="font-size: 0.75rem; color: #6b7280;">
                                            <?php echo htmlspecialchars($beneficiary['bank_name'] ?? 'Same Bank'); ?>
                                        </div>
                                    </div>
                                    <svg width="16" height="16" fill="currentColor" viewBox="0 0 16 16" style="color: #9ca3af;">
                                        <path fill-rule="evenodd" d="M4.646 1.646a.5.5 0 01.708 0l6 6a.5.5 0 010 .708l-6 6a.5.5 0 01-.708-.708L10.293 8 4.646 2.354a.5.5 0 010-.708z"/>
                                    </svg>
                                </div>
                            <?php endforeach; ?>
                        </div>
                        <?php if (count($beneficiaries) > 5): ?>
                            <div style="text-align: center; margin-top: 1rem;">
                                <a href="#" style="color: #6366f1; font-size: 0.875rem; text-decoration: none;">View All (<?php echo count($beneficiaries); ?>)</a>
                            </div>
                        <?php endif; ?>
                    <?php else: ?>
                        <div style="text-align: center; padding: 1rem;">
                            <div style="font-size: 2rem; color: #e5e7eb; margin-bottom: 0.5rem;">👥</div>
                            <p style="color: #6b7280; font-size: 0.875rem; margin-bottom: 1rem;">No saved recipients</p>
                            <button class="btn-outline" onclick="showAddBeneficiaryModal()" style="font-size: 0.75rem;">Add First Recipient</button>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function showSendMoneyModal() {
    // Redirect to transfers page for now
    window.location.href = '../transfers/';
}

function showRequestMoneyModal() {
    alert('Request money functionality coming soon!');
}

function showAddBeneficiaryModal() {
    alert('Add beneficiary functionality coming soon!');
}

function quickSendTo(name, accountNumber) {
    // Quick send functionality
    if (confirm(`Send money to ${name}?`)) {
        window.location.href = `../transfers/?recipient=${encodeURIComponent(accountNumber)}`;
    }
}
</script>

<?php
// Include footer
require_once '../includes/dashboard/footer.php';
?>
