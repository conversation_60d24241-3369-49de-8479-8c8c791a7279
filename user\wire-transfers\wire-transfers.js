/**
 * Wire Transfer System JavaScript
 * Handles billing code verification and wire transfer processing
 */

// Global variables
let wireTransferData = {};
let currentBillingCodePosition = 1;
let userBillingCodes = [];
let billingCodeModal;
let otpModal;

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    console.log('🚀 Wire Transfer System Initialized');
    
    // Initialize Bootstrap modals
    billingCodeModal = new bootstrap.Modal(document.getElementById('billingCodeModal'));
    otpModal = new bootstrap.Modal(document.getElementById('otpModal'));
    
    // Bind form submission
    document.getElementById('wireTransferForm').addEventListener('submit', handleWireTransferSubmit);
    
    // Bind billing code verification
    document.getElementById('verifyBillingCodeBtn').addEventListener('click', verifyBillingCode);
    
    // Bind OTP verification
    document.getElementById('verifyOtpBtn').addEventListener('click', verifyOTP);
    
    // Handle Enter key in billing code input
    document.getElementById('billingCodeInput').addEventListener('keypress', function(e) {
        if (e.key === 'Enter') {
            verifyBillingCode();
        }
    });
    
    // Handle Enter key in OTP input
    document.getElementById('otpCode').addEventListener('keypress', function(e) {
        if (e.key === 'Enter') {
            verifyOTP();
        }
    });
});

/**
 * Update currency symbol when currency selection changes
 */
function updateCurrencySymbol() {
    const currencySelect = document.getElementById('transfer_currency');
    const currencySymbol = document.getElementById('currency-symbol');

    if (currencySelect && currencySymbol) {
        const selectedOption = currencySelect.options[currencySelect.selectedIndex];
        if (selectedOption && selectedOption.dataset.symbol) {
            currencySymbol.textContent = selectedOption.dataset.symbol;
        }
    }
}

/**
 * Handle wire transfer form submission
 */
function handleWireTransferSubmit(e) {
    e.preventDefault();
    console.log('📤 Processing wire transfer submission...');
    
    // Validate form
    if (!validateWireTransferForm()) {
        console.log('❌ Form validation failed');
        return;
    }
    
    // Collect transfer data
    collectWireTransferData();
    
    // Check if user has billing codes
    checkUserBillingCodes()
        .then(hasBillingCodes => {
            if (hasBillingCodes) {
                console.log('🔐 User has billing codes - starting verification');
                startBillingCodeVerification();
            } else {
                console.log('📱 No billing codes - proceeding to OTP');
                showOTPModal();
            }
        })
        .catch(error => {
            console.error('Error checking billing codes:', error);
            showErrorMessage('Failed to check security requirements. Please try again.');
        });
}

/**
 * Validate wire transfer form
 */
function validateWireTransferForm() {
    const form = document.getElementById('wireTransferForm');
    let isValid = true;
    
    // Clear previous errors
    clearAllFormErrors();
    
    // Validate required fields
    const requiredFields = form.querySelectorAll('input[required], select[required]');
    requiredFields.forEach(field => {
        if (!field.value.trim()) {
            showFieldError(field, 'This field is required');
            isValid = false;
        }
    });
    
    // Validate amount
    const amount = parseFloat(document.getElementById('amount').value);
    const maxBalance = parseFloat(document.getElementById('amount').getAttribute('max'));
    
    if (isNaN(amount) || amount <= 0) {
        showFieldError(document.getElementById('amount'), 'Please enter a valid amount');
        isValid = false;
    } else if (amount > maxBalance) {
        showFieldError(document.getElementById('amount'), 'Amount exceeds available balance');
        isValid = false;
    }
    
    return isValid;
}

/**
 * Collect wire transfer data from form
 */
function collectWireTransferData() {
    const form = document.getElementById('wireTransferForm');
    const formData = new FormData(form);
    
    wireTransferData = {
        transfer_type: 'international',
        amount: parseFloat(formData.get('amount')),
        purpose_of_payment: formData.get('purpose_of_payment'),
        narration: formData.get('narration') || 'Wire Transfer'
    };
    
    // Collect all form fields dynamically
    const inputs = form.querySelectorAll('input, select, textarea');
    inputs.forEach(input => {
        if (input.name && input.value) {
            wireTransferData[input.name] = input.value;
        }
    });
    
    console.log('📋 Wire transfer data collected:', wireTransferData);
}

/**
 * Check if user has billing codes assigned
 */
async function checkUserBillingCodes() {
    try {
        const response = await fetch('check-billing-codes.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            }
        });

        // Use robust JSON parsing like local/inter-bank transfers
        const text = await response.text();
        console.log('🔍 Raw billing codes response:', text);

        if (!text.trim()) {
            throw new Error('Empty response from server');
        }

        let result;
        try {
            result = JSON.parse(text);
        } catch (e) {
            console.error('Invalid JSON response:', text);
            throw new Error('Invalid response format from server');
        }
        
        if (result.success) {
            userBillingCodes = result.billing_codes || [];
            return userBillingCodes.length > 0;
        } else {
            throw new Error(result.error || 'Failed to check billing codes');
        }
    } catch (error) {
        console.error('Error checking billing codes:', error);
        throw error;
    }
}

/**
 * Start billing code verification process
 */
function startBillingCodeVerification() {
    currentBillingCodePosition = 1;
    showBillingCodeModal();
}

/**
 * Show billing code verification modal
 */
function showBillingCodeModal() {
    const currentCode = userBillingCodes.find(code => code.billing_position == currentBillingCodePosition);
    
    if (!currentCode) {
        console.log('✅ All billing codes verified - proceeding to OTP');
        showOTPModal();
        return;
    }
    
    // Update modal content with individual code details
    document.getElementById('billingCodeTitle').textContent = 'Billing Code Verification';
    document.getElementById('billingCodeName').textContent = currentCode.billing_name;
    document.getElementById('billingCodeDescription').textContent = currentCode.billing_description;
    
    // Clear input and errors
    document.getElementById('billingCodeInput').value = '';
    document.getElementById('billingCodeInput').classList.remove('is-invalid');
    document.getElementById('billingCodeError').textContent = '';
    
    // Show modal
    billingCodeModal.show();
    
    // Focus on input
    setTimeout(() => {
        document.getElementById('billingCodeInput').focus();
    }, 300);
}

/**
 * Verify billing code
 */
async function verifyBillingCode() {
    const codeInput = document.getElementById('billingCodeInput');
    const enteredCode = codeInput.value.trim();
    
    if (!enteredCode) {
        showFieldError(codeInput, 'Please enter the billing code');
        return;
    }
    
    // Show loading state
    showBillingCodeProgress(true);
    
    try {
        const response = await fetch('verify-billing-code.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                billing_position: currentBillingCodePosition,
                billing_code: enteredCode
            })
        });

        // Use robust JSON parsing like local/inter-bank transfers
        const text = await response.text();
        console.log('🔍 Raw billing code response:', text);

        if (!text.trim()) {
            throw new Error('Empty response from server');
        }

        let result;
        try {
            result = JSON.parse(text);
        } catch (e) {
            console.error('Invalid JSON response:', text);
            throw new Error('Invalid response format from server');
        }
        
        if (result.success) {
            console.log(`✅ Billing code ${currentBillingCodePosition} verified`);
            
            // Move to next billing code
            currentBillingCodePosition++;
            
            // Hide current modal
            billingCodeModal.hide();
            
            // Show next billing code or proceed to OTP
            setTimeout(() => {
                showBillingCodeModal();
            }, 300);
            
        } else {
            showFieldError(codeInput, result.error || 'Invalid billing code');
        }
        
    } catch (error) {
        console.error('Error verifying billing code:', error);
        showFieldError(codeInput, 'Verification failed. Please try again.');
    } finally {
        showBillingCodeProgress(false);
    }
}

/**
 * Show OTP verification modal
 */
function showOTPModal() {
    console.log('📱 Showing OTP modal...');
    
    // Generate and send OTP
    generateWireOTP()
        .then(() => {
            otpModal.show();
            
            // Focus on OTP input
            setTimeout(() => {
                document.getElementById('otpCode').focus();
            }, 300);
        })
        .catch(error => {
            console.error('OTP generation failed:', error);
            showErrorMessage('Failed to generate OTP. Please try again.');
        });
}

/**
 * Generate OTP for wire transfer
 */
async function generateWireOTP() {
    try {
        const response = await fetch('generate-wire-otp.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(wireTransferData)
        });

        // Use robust JSON parsing like local/inter-bank transfers
        const text = await response.text();
        console.log('🔍 Raw OTP generation response:', text);

        if (!text.trim()) {
            throw new Error('Empty response from server');
        }

        let result;
        try {
            result = JSON.parse(text);
        } catch (e) {
            console.error('Invalid JSON response:', text);
            throw new Error('Invalid response format from server');
        }
        
        if (!result.success) {
            throw new Error(result.error || 'Failed to generate OTP');
        }
        
        console.log('📱 OTP generated successfully');
        
    } catch (error) {
        console.error('Error generating OTP:', error);
        throw error;
    }
}

/**
 * Verify OTP and process wire transfer
 */
async function verifyOTP() {
    const otpInput = document.getElementById('otpCode');
    const enteredOTP = otpInput.value.trim();
    
    if (!enteredOTP || enteredOTP.length !== 6) {
        showFieldError(otpInput, 'Please enter a valid 6-digit OTP');
        return;
    }
    
    // Show loading state
    showOTPProgress(true);
    
    try {
        // Add OTP to transfer data
        wireTransferData.otp_code = enteredOTP;
        
        const response = await fetch('process-wire-transfer.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(wireTransferData)
        });

        // Use robust JSON parsing like local/inter-bank transfers
        const text = await response.text();
        console.log('🔍 Raw wire transfer response:', text);

        if (!text.trim()) {
            throw new Error('Empty response from server');
        }

        let result;
        try {
            result = JSON.parse(text);
        } catch (e) {
            console.error('Invalid JSON response:', text);
            throw new Error('Invalid response format from server');
        }
        
        if (result.success) {
            console.log('✅ Wire transfer processed successfully');

            // Hide modal
            otpModal.hide();

            // Show progress bar before success modal
            showWireTransferProgress().then(() => {
                // Show enhanced success confirmation after progress completes
                showWireTransferSuccessModal(result);
            });

            // No auto-redirect - user must manually close modal
            
        } else {
            showFieldError(otpInput, result.error || 'Invalid OTP');
        }
        
    } catch (error) {
        console.error('Error processing wire transfer:', error);
        showFieldError(otpInput, 'Transfer failed. Please try again.');
    } finally {
        showOTPProgress(false);
    }
}

/**
 * Utility Functions
 */

function formatCurrency(amount, currency = 'USD') {
    if (!amount || isNaN(amount)) return '$0.00';

    const numAmount = parseFloat(amount);
    const currencySymbols = {
        'USD': '$',
        'EUR': '€',
        'GBP': '£',
        'JPY': '¥',
        'CAD': 'C$',
        'AUD': 'A$'
    };

    const symbol = currencySymbols[currency] || '$';
    return symbol + numAmount.toLocaleString('en-US', {
        minimumFractionDigits: 2,
        maximumFractionDigits: 2
    });
}

function showWireTransferProgress() {
    return new Promise((resolve) => {
        // Create progress modal
        const progressHtml = `
            <div id="wireProgressModal" class="progress-modal" style="display: block; position: fixed; top: 0; left: 0; width: 100%; height: 100%; background: rgba(0,0,0,0.7); z-index: 9999; animation: fadeIn 0.3s ease-out;">
                <div class="progress-container" style="position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%); background: white; border-radius: 12px; padding: 2rem; max-width: 400px; width: 90%; text-align: center; box-shadow: 0 10px 30px rgba(0,0,0,0.3);">
                    <div style="margin-bottom: 1.5rem;">
                        <i class="fas fa-university" style="font-size: 2.5rem; color: var(--primary-color, #206bc4); margin-bottom: 1rem;"></i>
                        <h4 style="color: var(--primary-color, #206bc4); margin: 0; font-family: Arial, sans-serif;">Processing Wire Transfer</h4>
                    </div>

                    <div class="progress-bar-container" style="background: #f0f0f0; border-radius: 10px; height: 8px; margin: 1.5rem 0; overflow: hidden;">
                        <div class="progress-bar" style="background: linear-gradient(90deg, var(--primary-color, #206bc4), #4dabf7); height: 100%; width: 0%; border-radius: 10px; transition: width 0.3s ease;"></div>
                    </div>

                    <div class="progress-message" style="color: #666; font-size: 0.9rem; min-height: 1.5rem;">
                        Initializing transfer...
                    </div>
                </div>
            </div>
        `;

        // Add progress modal to page
        document.body.insertAdjacentHTML('beforeend', progressHtml);

        const progressBar = document.querySelector('#wireProgressModal .progress-bar');
        const progressMessage = document.querySelector('#wireProgressModal .progress-message');

        const messages = [
            'Initializing transfer...',
            'Validating transfer details...',
            'Connecting to banking network...',
            'Processing international wire...',
            'Updating account balance...',
            'Generating confirmation...',
            'Transfer completed successfully!'
        ];

        let currentStep = 0;
        const totalSteps = messages.length;

        const updateProgress = () => {
            if (currentStep < totalSteps) {
                const percentage = ((currentStep + 1) / totalSteps) * 100;
                progressBar.style.width = percentage + '%';
                progressMessage.textContent = messages[currentStep];
                currentStep++;

                setTimeout(updateProgress, 800); // 800ms per step
            } else {
                // Progress complete, remove modal and resolve
                setTimeout(() => {
                    const modal = document.getElementById('wireProgressModal');
                    if (modal) {
                        modal.remove();
                    }
                    resolve();
                }, 500); // Brief pause before showing success
            }
        };

        // Start progress animation
        setTimeout(updateProgress, 300);
    });
}

function showBillingCodeProgress(show) {
    const progress = document.getElementById('billingCodeProgress');
    const button = document.getElementById('verifyBillingCodeBtn');
    
    if (show) {
        progress.style.display = 'block';
        button.disabled = true;
        button.textContent = 'Verifying...';
    } else {
        progress.style.display = 'none';
        button.disabled = false;
        button.textContent = 'Verify Code';
    }
}

function showOTPProgress(show) {
    const progress = document.getElementById('otpProgress');
    const button = document.getElementById('verifyOtpBtn');
    
    if (show) {
        progress.style.display = 'block';
        button.disabled = true;
        button.textContent = 'Processing...';
    } else {
        progress.style.display = 'none';
        button.disabled = false;
        button.textContent = 'Verify OTP';
    }
}

function showFieldError(field, message) {
    field.classList.add('is-invalid');
    const errorElement = field.parentNode.querySelector('.invalid-feedback') || 
                        document.getElementById(field.id + 'Error');
    if (errorElement) {
        errorElement.textContent = message;
    }
}

function clearAllFormErrors() {
    document.querySelectorAll('.is-invalid').forEach(field => {
        field.classList.remove('is-invalid');
    });
    document.querySelectorAll('.invalid-feedback').forEach(error => {
        error.textContent = '';
    });
}

function showErrorMessage(message) {
    // Create or update error alert
    const alertHtml = `
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <i class="fas fa-exclamation-triangle me-2"></i>${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    `;
    
    const container = document.querySelector('.content-container');
    container.insertAdjacentHTML('afterbegin', alertHtml);
    
    // Auto-dismiss after 5 seconds
    setTimeout(() => {
        const alert = container.querySelector('.alert-danger');
        if (alert) {
            alert.remove();
        }
    }, 5000);
}

function showSuccessMessage(message) {
    // Create success alert
    const alertHtml = `
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            <i class="fas fa-check-circle me-2"></i>${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    `;

    const container = document.querySelector('.content-container');
    container.insertAdjacentHTML('afterbegin', alertHtml);
}

function showWireTransferSuccessModal(result) {
    // Create banking receipt-style modal with primary color theme (no header to avoid blocking success message)
    const modalHtml = `
        <div id="wireSuccessModal" class="receipt-modal" style="display: block; animation: fadeIn 0.3s ease-out;">
            <div class="modal-overlay" onclick="closeWireSuccessModal()"></div>
            <div class="receipt-container" style="max-width: 800px; width: 95%; font-family: 'Courier New', 'Monaco', monospace; background: white; border-radius: 8px; box-shadow: 0 10px 30px rgba(0,0,0,0.3); position: relative;">
                <!-- Close Button -->
                <button class="receipt-close" onclick="closeWireSuccessModal()" style="position: absolute; top: 15px; right: 15px; background: rgba(0,0,0,0.1); border: none; color: #666; width: 32px; height: 32px; border-radius: 50%; cursor: pointer; display: flex; align-items: center; justify-content: center; transition: background 0.2s; z-index: 10;">
                    <i class="fas fa-times"></i>
                </button>

                <!-- Receipt Body -->
                <div class="receipt-body" style="padding: 2rem; max-height: 70vh; overflow-y: auto; background: white; border-radius: 8px;">
                    <!-- Success Message -->
                    <div style="text-center; margin-bottom: 2rem; padding-bottom: 1.5rem; border-bottom: 1px solid #ddd;">
                        <div style="margin-bottom: 1rem;">
                            <i class="fas fa-check-circle" style="font-size: 3rem; color: var(--primary-color, #206bc4);"></i>
                        </div>
                        <h4 style="color: var(--primary-color, #206bc4); margin-bottom: 0.5rem; font-family: Arial, sans-serif;">Transfer Submitted Successfully</h4>
                        <p style="color: #6b7280; margin: 0; font-size: 0.9rem;">Your international wire transfer is now pending review</p>
                    </div>

                    <!-- Receipt Content Grid -->
                    <div class="receipt-content" style="display: grid; grid-template-columns: 1fr 1fr; gap: 2rem; font-size: 0.9rem; line-height: 1.6;">

                        <!-- Transaction Details -->
                        <div class="receipt-section">
                            <div class="receipt-section-title" style="font-size: 1rem; font-weight: bold; color: #2c3e50; margin-bottom: 0.75rem; padding-bottom: 0.5rem; border-bottom: 1px solid #ddd; font-family: Arial, sans-serif; text-transform: uppercase; letter-spacing: 1px;">
                                Transaction Details
                            </div>
                            <div class="receipt-row" style="display: flex; justify-content: space-between; align-items: center; padding: 0.4rem 0; border-bottom: 1px dotted #ddd;">
                                <span class="receipt-label" style="font-weight: 500; color: #555; font-size: 0.85rem; text-transform: uppercase; letter-spacing: 0.5px; flex: 0 0 35%;">Reference</span>
                                <span class="receipt-value" style="font-weight: 600; color: #2c3e50; text-align: right; font-family: 'Courier New', monospace; flex: 1; word-break: break-word; font-size: 0.9rem;">${result.transfer_reference || 'N/A'}</span>
                            </div>
                            <div class="receipt-row" style="display: flex; justify-content: space-between; align-items: center; padding: 0.4rem 0; border-bottom: 1px dotted #ddd;">
                                <span class="receipt-label" style="font-weight: 500; color: #555; font-size: 0.85rem; text-transform: uppercase; letter-spacing: 0.5px; flex: 0 0 35%;">Amount</span>
                                <span class="receipt-value receipt-amount" style="font-weight: 600; color: #2c3e50; text-align: right; font-family: 'Courier New', monospace; flex: 1; font-size: 1.1rem; font-weight: bold;">${formatCurrency(result.amount, result.currency)}</span>
                            </div>
                            <div class="receipt-row" style="display: flex; justify-content: space-between; align-items: center; padding: 0.4rem 0; border-bottom: 1px dotted #ddd;">
                                <span class="receipt-label" style="font-weight: 500; color: #555; font-size: 0.85rem; text-transform: uppercase; letter-spacing: 0.5px; flex: 0 0 35%;">Status</span>
                                <span class="receipt-status pending" style="display: inline-block; padding: 0.25rem 0.75rem; border-radius: 3px; font-size: 0.75rem; font-weight: bold; text-transform: uppercase; letter-spacing: 1px; font-family: Arial, sans-serif; background: #fff3cd; color: #856404; border: 1px solid #ffeaa7; margin-left: auto;">PENDING REVIEW</span>
                            </div>
                            <div class="receipt-row" style="display: flex; justify-content: space-between; align-items: center; padding: 0.4rem 0;">
                                <span class="receipt-label" style="font-weight: 500; color: #555; font-size: 0.85rem; text-transform: uppercase; letter-spacing: 0.5px; flex: 0 0 35%;">New Balance</span>
                                <span class="receipt-value receipt-amount positive" style="font-weight: 600; text-align: right; font-family: 'Courier New', monospace; flex: 1; font-size: 1.1rem; font-weight: bold; color: #27ae60;">$${result.new_balance ? parseFloat(result.new_balance).toFixed(2) : '0.00'}</span>
                            </div>
                        </div>

                        <!-- Beneficiary Information -->
                        <div class="receipt-section">
                            <div class="receipt-section-title" style="font-size: 1rem; font-weight: bold; color: #2c3e50; margin-bottom: 0.75rem; padding-bottom: 0.5rem; border-bottom: 1px solid #ddd; font-family: Arial, sans-serif; text-transform: uppercase; letter-spacing: 1px;">
                                Beneficiary Details
                            </div>
                            <div class="receipt-row" style="display: flex; justify-content: space-between; align-items: center; padding: 0.4rem 0; border-bottom: 1px dotted #ddd;">
                                <span class="receipt-label" style="font-weight: 500; color: #555; font-size: 0.85rem; text-transform: uppercase; letter-spacing: 0.5px; flex: 0 0 35%;">Name</span>
                                <span class="receipt-value" style="font-weight: 600; color: #2c3e50; text-align: right; font-family: 'Courier New', monospace; flex: 1; word-break: break-word; font-size: 0.9rem;">${result.transfer_data?.beneficiary_account_name || 'N/A'}</span>
                            </div>
                            <div class="receipt-row" style="display: flex; justify-content: space-between; align-items: center; padding: 0.4rem 0; border-bottom: 1px dotted #ddd;">
                                <span class="receipt-label" style="font-weight: 500; color: #555; font-size: 0.85rem; text-transform: uppercase; letter-spacing: 0.5px; flex: 0 0 35%;">Account</span>
                                <span class="receipt-value" style="font-weight: 600; color: #2c3e50; text-align: right; font-family: 'Courier New', monospace; flex: 1; word-break: break-word; font-size: 0.9rem;">${result.transfer_data?.beneficiary_account_number || 'N/A'}</span>
                            </div>
                            <div class="receipt-row" style="display: flex; justify-content: space-between; align-items: center; padding: 0.4rem 0; border-bottom: 1px dotted #ddd;">
                                <span class="receipt-label" style="font-weight: 500; color: #555; font-size: 0.85rem; text-transform: uppercase; letter-spacing: 0.5px; flex: 0 0 35%;">Bank</span>
                                <span class="receipt-value" style="font-weight: 600; color: #2c3e50; text-align: right; font-family: 'Courier New', monospace; flex: 1; word-break: break-word; font-size: 0.9rem;">${result.transfer_data?.bank_name || 'N/A'}</span>
                            </div>
                            <div class="receipt-row" style="display: flex; justify-content: space-between; align-items: center; padding: 0.4rem 0; border-bottom: 1px dotted #ddd;">
                                <span class="receipt-label" style="font-weight: 500; color: #555; font-size: 0.85rem; text-transform: uppercase; letter-spacing: 0.5px; flex: 0 0 35%;">Country</span>
                                <span class="receipt-value" style="font-weight: 600; color: #2c3e50; text-align: right; font-family: 'Courier New', monospace; flex: 1; word-break: break-word; font-size: 0.9rem;">${result.transfer_data?.beneficiary_country || result.transfer_data?.bank_country || 'N/A'}</span>
                            </div>
                            <div class="receipt-row" style="display: flex; justify-content: space-between; align-items: center; padding: 0.4rem 0;">
                                <span class="receipt-label" style="font-weight: 500; color: #555; font-size: 0.85rem; text-transform: uppercase; letter-spacing: 0.5px; flex: 0 0 35%;">SWIFT Code</span>
                                <span class="receipt-value" style="font-weight: 600; color: #2c3e50; text-align: right; font-family: 'Courier New', monospace; flex: 1; word-break: break-word; font-size: 0.9rem;">${result.transfer_data?.swift_code || 'N/A'}</span>
                            </div>
                        </div>
                    </div>

                    <!-- Important Notice -->
                    <div class="receipt-section full-width" style="grid-column: 1 / -1; margin-top: 1rem; padding-top: 1rem; border-top: 1px solid #ddd;">
                        <div style="background: #f0f9ff; border: 1px solid var(--primary-color, #206bc4); border-radius: 6px; padding: 1rem; color: #1e3a8a;">
                            <div style="display: flex; align-items: flex-start; gap: 0.75rem;">
                                <i class="fas fa-info-circle" style="color: var(--primary-color, #206bc4); margin-top: 0.125rem; flex-shrink: 0;"></i>
                                <div style="font-size: 0.85rem; line-height: 1.5;">
                                    <strong>Processing Information:</strong> International wire transfers typically take 1-3 business days to process. You will receive email updates when your transfer status changes.
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Receipt Actions -->
                    <div class="receipt-actions" style="display: flex; justify-content: center; gap: 1rem; margin-top: 2rem; padding-top: 1.5rem; border-top: 1px solid #ddd;">
                        <a href="${result.pdf_receipt_url || '#'}" class="btn-print" target="_blank" style="background: linear-gradient(135deg, var(--primary-color, #206bc4) 0%, var(--primary-dark, #1a5490) 100%); color: white; border: none; padding: 0.75rem 1.5rem; border-radius: 6px; font-size: 0.875rem; font-weight: 600; cursor: pointer; display: flex; align-items: center; gap: 0.5rem; transition: all 0.2s ease; text-decoration: none; text-transform: uppercase; letter-spacing: 0.5px;">
                            <i class="fas fa-print"></i>Print Receipt
                        </a>
                        <button class="btn-close-receipt" onclick="closeWireSuccessModal()" style="background: linear-gradient(135deg, #6b7280 0%, #4b5563 100%); color: white; border: none; padding: 0.75rem 1.5rem; border-radius: 6px; font-size: 0.875rem; font-weight: 600; cursor: pointer; display: flex; align-items: center; gap: 0.5rem; transition: all 0.2s ease; text-transform: uppercase; letter-spacing: 0.5px;">
                            <i class="fas fa-check"></i>Continue
                        </button>
                    </div>
                </div>
            </div>
        </div>
    `;

    // Remove existing modal if any
    const existingModal = document.getElementById('wireSuccessModal');
    if (existingModal) {
        existingModal.remove();
    }

    // Add modal to page
    document.body.insertAdjacentHTML('beforeend', modalHtml);
}

/**
 * Close wire transfer success modal and redirect to fresh wire transfer page
 */
function closeWireSuccessModal() {
    const modal = document.getElementById('wireSuccessModal');
    if (modal) {
        modal.style.animation = 'fadeOut 0.3s ease-out';
        setTimeout(() => {
            modal.remove();
            // Clear the form and redirect to fresh wire transfer page
            clearWireTransferForm();
            window.location.href = 'index.php';
        }, 300);
    }
}

/**
 * Clear all wire transfer form fields
 */
function clearWireTransferForm() {
    // Clear all form inputs
    const form = document.getElementById('wireTransferForm');
    if (form) {
        form.reset();
    }

    // Clear any dynamically added fields
    const dynamicFields = document.querySelectorAll('.dynamic-field input, .dynamic-field select, .dynamic-field textarea');
    dynamicFields.forEach(field => {
        field.value = '';
    });

    // Reset any validation states
    const errorElements = document.querySelectorAll('.field-error');
    errorElements.forEach(error => error.remove());

    const invalidFields = document.querySelectorAll('.is-invalid');
    invalidFields.forEach(field => field.classList.remove('is-invalid'));
}
