<?php
/**
 * Super Admin Login System
 * Separate authentication for super administrators
 */

// Enable error reporting for debugging
error_reporting(E_ALL);
ini_set('display_errors', 1);
ini_set('log_errors', 1);
ini_set('error_log', __DIR__ . '/../logs/error.log');

session_start();

// Log the start of login process
error_log("Super Admin Login: Starting login process");

// Redirect if already logged in as super admin
if (isset($_SESSION['super_admin_logged_in']) && $_SESSION['super_admin_logged_in'] === true) {
    error_log("Super Admin Login: Already logged in, redirecting to dashboard");
    header('Location: dashboard.php');
    exit;
}

$error = '';
$success = '';

// Get site settings for logo, name and favicon
function getSiteSettings() {
    try {
        require_once '../config/database.php';
        $db = getDB();

        $settings = [];
        $result = $db->query("SELECT setting_key, setting_value FROM super_admin_settings WHERE setting_key IN ('site_name', 'site_logo', 'site_favicon')");

        if ($result) {
            while ($row = $result->fetch_assoc()) {
                $settings[$row['setting_key']] = $row['setting_value'];
            }
        }

        return [
            'site_name' => $settings['site_name'] ?? 'SecureBank Online Banking',
            'site_logo' => $settings['site_logo'] ?? '',
            'site_favicon' => $settings['site_favicon'] ?? ''
        ];
    } catch (Exception $e) {
        error_log("Error getting site settings: " . $e->getMessage());
        return [
            'site_name' => 'SecureBank Online Banking',
            'site_logo' => '',
            'site_favicon' => ''
        ];
    }
}

$site_settings = getSiteSettings();

// Handle login form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $username = trim($_POST['username'] ?? '');
    $password = $_POST['password'] ?? '';
    
    // Super admin credentials (in production, store these securely in database)
    $super_admin_username = 'superadmin';
    $super_admin_password = 'admin123';
    
    if ($username === $super_admin_username && $password === $super_admin_password) {
        try {
            error_log("Super Admin Login: Password verified, checking 2FA requirements");

            // Check if 2FA is required (considering both system settings and user settings)
            require_once 'includes/2fa-functions.php';
            error_log("Super Admin Login: Successfully included 2FA functions");

            // Set basic session info first so functions can access username
            $_SESSION['super_admin_username'] = $username;

            // Check if system-wide 2FA is required
            $system_2fa_required = isSuperAdmin2FARequired();
            error_log("Super Admin Login: System 2FA required: " . ($system_2fa_required ? 'yes' : 'no'));

            // Check if user has any authentication method enabled (dual auth system)
            $user_auth_enabled = isSuperAdminAuthEnabled($username);
            error_log("Super Admin Login: User auth enabled: " . ($user_auth_enabled ? 'yes' : 'no'));

            // Get available authentication methods
            $auth_methods = getSuperAdminAuthMethods($username);
            error_log("Super Admin Login: Available auth methods: " . implode(', ', $auth_methods));

            // 2FA is required if system requires it AND user has any auth method enabled
            $is_2fa_required = $system_2fa_required && $user_auth_enabled;
            error_log("Super Admin Login: 2FA required: " . ($is_2fa_required ? 'yes' : 'no'));

        if ($is_2fa_required) {
            // Set partial session - password verified but 2FA pending
            $_SESSION['super_admin_logged_in'] = true;
            $_SESSION['super_admin_2fa_pending'] = true;
            $_SESSION['super_admin_username'] = $username;
            $_SESSION['super_admin_login_time'] = time();
            $_SESSION['super_admin_ip'] = $_SERVER['REMOTE_ADDR'] ?? 'unknown';

            // Log successful first factor
            logSuperAdminAction('login_step1', 'Super admin password verified, 2FA pending', [
                'ip_address' => $_SESSION['super_admin_ip'],
                'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? 'unknown'
            ]);

            // Proceed to 2FA verification
            header('Location: verify-2fa.php');
            exit;
        } else {
            // 2FA not enabled, complete login
            $_SESSION['super_admin_logged_in'] = true;
            $_SESSION['super_admin_2fa_verified'] = true;
            $_SESSION['super_admin_username'] = $username;
            $_SESSION['super_admin_login_time'] = time();
            $_SESSION['super_admin_ip'] = $_SERVER['REMOTE_ADDR'] ?? 'unknown';

            // Log successful login
            logSuperAdminAction('login_success', 'Super admin login successful (no 2FA)', [
                'ip_address' => $_SESSION['super_admin_ip'],
                'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? 'unknown'
            ]);

            // Redirect to dashboard
            error_log("Super Admin Login: Redirecting to dashboard");
            header('Location: dashboard.php');
            exit;
        }

        } catch (Exception $e) {
            error_log("Super Admin Login Error: " . $e->getMessage() . " in " . $e->getFile() . " on line " . $e->getLine());
            $error = 'An error occurred during login. Please try again.';
        }
    } else {
        $error = 'Invalid username or password. Please try again.';
        
        // Log failed login attempt
        logSuperAdminAction('login_failed', 'Failed super admin login attempt', [
            'attempted_username' => $username,
            'ip_address' => $_SERVER['REMOTE_ADDR'] ?? 'unknown',
            'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? 'unknown'
        ]);
    }
}

/**
 * Log super admin actions for audit trail
 */
function logSuperAdminAction($action, $description, $details = []) {
    try {
        require_once '../config/database.php';
        $db = getDB();
        
        $sql = "INSERT INTO super_admin_audit_log (action, description, details, ip_address, user_agent, created_at) 
                VALUES (?, ?, ?, ?, ?, NOW())";
        
        $db->query($sql, [
            $action,
            $description,
            json_encode($details),
            $_SERVER['REMOTE_ADDR'] ?? 'unknown',
            $_SERVER['HTTP_USER_AGENT'] ?? 'unknown'
        ]);
    } catch (Exception $e) {
        error_log("Failed to log super admin action: " . $e->getMessage());
    }
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Super Admin Login - Online Banking System</title>

    <!-- Favicon -->
    <?php if (!empty($site_settings['site_favicon']) && file_exists('../' . $site_settings['site_favicon'])): ?>
        <link rel="icon" type="image/x-icon" href="../<?php echo htmlspecialchars($site_settings['site_favicon']); ?>">
    <?php else: ?>
        <link rel="icon" type="image/x-icon" href="../assets/img/favicon.ico">
    <?php endif; ?>

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <style>
        body {
            font-family: 'Inter', sans-serif;
            background: #f8f9fa;
            min-height: 100vh;
            margin: 0;
            padding: 0;
        }

        .container-fluid {
            min-height: 100vh;
            padding: 0;
        }

        .row {
            min-height: 100vh;
            margin: 0;
        }

        .left-panel {
            background: white;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: flex-start;
            padding: 60px 80px;
            position: relative;
        }

        .right-panel {
            background: linear-gradient(135deg, #4361ee 0%, #3730a3 100%);
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            padding: 40px;
            color: white;
            position: relative;
            overflow: hidden;
        }

        .right-panel::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-image: url('../demo-images/superadmin_background.jpg');
            background-size: cover;
            background-position: center;
            background-repeat: no-repeat;
            opacity: 0.15;
            z-index: 1;
        }

        .right-panel::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><circle cx="20" cy="20" r="2" fill="rgba(255,255,255,0.1)"/><circle cx="80" cy="30" r="1.5" fill="rgba(255,255,255,0.1)"/><circle cx="30" cy="80" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="70" cy="70" r="1" fill="rgba(255,255,255,0.1)"/></svg>');
            opacity: 0.2;
            z-index: 2;
        }

        .logo {
            margin-bottom: 40px;
            text-align: left;
            width: 100%;
        }

        .logo img {
            max-width: 200px;
            height: auto;
            max-height: 80px;
            object-fit: contain;
            background: transparent;
            mix-blend-mode: multiply;
        }

        .logo-fallback {
            font-size: 24px;
            font-weight: 700;
            color: #4361ee;
            margin: 0;
        }

        .welcome-text {
            text-align: left;
            margin-bottom: 40px;
            width: 100%;
        }

        .welcome-text h1 {
            font-size: 28px;
            font-weight: 600;
            color: #1a1a1a;
            margin-bottom: 10px;
        }

        .welcome-text p {
            color: #6b7280;
            font-size: 16px;
            margin: 0;
        }

        .login-form {
            width: 100%;
            max-width: 400px;
            text-align: left;
        }

        .form-group {
            margin-bottom: 20px;
            width: 100%;
        }

        .form-label {
            font-weight: 500;
            color: #374151;
            margin-bottom: 8px;
            display: block;
            font-size: 14px;
        }

        .form-control {
            border: 1px solid #d1d5db;
            border-radius: 8px;
            padding: 12px 16px;
            font-size: 16px;
            transition: all 0.2s ease;
            background: white;
            width: 100%;
            box-sizing: border-box;
        }

        .form-control:focus {
            border-color: #4361ee;
            box-shadow: 0 0 0 3px rgba(67, 97, 238, 0.1);
            outline: none;
        }

        .btn-login {
            background: #4361ee;
            border: none;
            color: white;
            padding: 12px 24px;
            border-radius: 8px;
            font-weight: 500;
            font-size: 16px;
            width: 100%;
            transition: all 0.2s ease;
            cursor: pointer;
            box-sizing: border-box;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
        }

        .btn-login:hover {
            background: #3730a3;
            transform: translateY(-1px);
        }

        .alert {
            border-radius: 8px;
            border: none;
            padding: 12px 16px;
            margin-bottom: 20px;
            font-size: 14px;
        }

        .alert-danger {
            background: #fef2f2;
            color: #991b1b;
        }

        .alert-success {
            background: #ecfdf5;
            color: #065f46;
        }

        .right-panel-content {
            display: flex;
            align-items: center;
            justify-content: center;
            height: 100%;
            z-index: 3;
            position: relative;
        }

        .feature-illustration {
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .back-link {
            position: absolute;
            top: 20px;
            left: 20px;
            color: #6b7280;
            text-decoration: none;
            font-size: 14px;
            transition: all 0.2s ease;
        }

        .back-link:hover {
            color: #4361ee;
            text-decoration: none;
        }

        .security-notice {
            background: #f9fafb;
            border-radius: 8px;
            padding: 16px;
            margin-top: 20px;
            text-align: left;
            border: 1px solid #e5e7eb;
        }

        .security-notice h6 {
            color: #374151;
            font-weight: 500;
            margin-bottom: 8px;
            font-size: 14px;
        }

        .security-notice p {
            color: #6b7280;
            font-size: 12px;
            margin: 0;
            line-height: 1.4;
        }

        @media (max-width: 768px) {
            .right-panel {
                display: none;
            }

            .left-panel {
                padding: 20px;
            }

            .welcome-text h1 {
                font-size: 24px;
            }
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- Left Panel - Login Form -->
            <div class="col-md-5 left-panel">
                <a href="../admin/login.php" class="back-link">
                    <i class="fas fa-arrow-left"></i> Back to Admin Login
                </a>

                <!-- Logo -->
                <div class="logo">
                    <?php if (!empty($site_settings['site_logo']) && file_exists('../' . $site_settings['site_logo'])): ?>
                        <img src="../<?php echo htmlspecialchars($site_settings['site_logo']); ?>"
                             alt="<?php echo htmlspecialchars($site_settings['site_name']); ?>"
                             onerror="this.style.display='none'; this.nextElementSibling.style.display='block';">
                        <h2 class="logo-fallback" style="display: none;"><?php echo htmlspecialchars($site_settings['site_name']); ?></h2>
                    <?php else: ?>
                        <h2 class="logo-fallback"><?php echo htmlspecialchars($site_settings['site_name']); ?></h2>
                    <?php endif; ?>
                </div>

                <!-- Welcome Text -->
                <div class="welcome-text">
                    <h1>Welcome to Super Admin Portal</h1>
                    <p>Please sign in with your super administrator credentials</p>
                </div>

                <!-- Login Form -->
                <div class="login-form">
                    <?php if ($error): ?>
                        <div class="alert alert-danger">
                            <i class="fas fa-exclamation-triangle"></i>
                            <?php echo htmlspecialchars($error); ?>
                        </div>
                    <?php endif; ?>

                    <?php if ($success): ?>
                        <div class="alert alert-success">
                            <i class="fas fa-check-circle"></i>
                            <?php echo htmlspecialchars($success); ?>
                        </div>
                    <?php endif; ?>

                    <form method="POST" action="">
                        <div class="form-group">
                            <label for="username" class="form-label">Username</label>
                            <input type="text"
                                   class="form-control"
                                   id="username"
                                   name="username"
                                   placeholder="Enter super admin username"
                                   value="<?php echo htmlspecialchars($_POST['username'] ?? ''); ?>"
                                   required
                                   autocomplete="username">
                        </div>

                        <div class="form-group">
                            <label for="password" class="form-label">Password</label>
                            <input type="password"
                                   class="form-control"
                                   id="password"
                                   name="password"
                                   placeholder="Enter your password"
                                   required
                                   autocomplete="current-password">
                        </div>

                        <button type="submit" class="btn-login">
                            <i class="fas fa-sign-in-alt"></i> Sign In
                        </button>
                    </form>

                    <div class="security-notice">
                        <h6><i class="fas fa-shield-alt"></i> Security Notice</h6>
                        <p>
                            This is a restricted area for super administrators only.
                            All access attempts are logged and monitored for security purposes.
                        </p>
                    </div>
                </div>
            </div>

            <!-- Right Panel - SVG Illustration Only -->
            <div class="col-md-7 right-panel">
                <div class="right-panel-content">
                    <!-- Banking System Illustration -->
                    <div class="feature-illustration">
                        <svg width="550" height="400" viewBox="0 0 550 400" xmlns="http://www.w3.org/2000/svg">
                            <defs>
                                <linearGradient id="buildingGradient" x1="0%" y1="0%" x2="100%" y2="100%">
                                    <stop offset="0%" style="stop-color:#ffffff;stop-opacity:0.3" />
                                    <stop offset="100%" style="stop-color:#ffffff;stop-opacity:0.1" />
                                </linearGradient>
                                <linearGradient id="screenGlow" x1="0%" y1="0%" x2="100%" y2="100%">
                                    <stop offset="0%" style="stop-color:#ffffff;stop-opacity:0.4" />
                                    <stop offset="100%" style="stop-color:#ffffff;stop-opacity:0.1" />
                                </linearGradient>
                                <linearGradient id="shieldGradient" x1="0%" y1="0%" x2="100%" y2="100%">
                                    <stop offset="0%" style="stop-color:#ffffff;stop-opacity:0.5" />
                                    <stop offset="100%" style="stop-color:#ffffff;stop-opacity:0.2" />
                                </linearGradient>
                                <linearGradient id="cloudGradient" x1="0%" y1="0%" x2="100%" y2="100%">
                                    <stop offset="0%" style="stop-color:#ffffff;stop-opacity:0.4" />
                                    <stop offset="100%" style="stop-color:#ffffff;stop-opacity:0.15" />
                                </linearGradient>
                                <linearGradient id="networkGradient" x1="0%" y1="0%" x2="100%" y2="0%">
                                    <stop offset="0%" style="stop-color:#ffffff;stop-opacity:0.6" />
                                    <stop offset="50%" style="stop-color:#ffffff;stop-opacity:0.3" />
                                    <stop offset="100%" style="stop-color:#ffffff;stop-opacity:0.6" />
                                </linearGradient>
                            </defs>

                            <!-- Bank Building -->
                            <rect x="100" y="220" width="220" height="140" rx="10" fill="url(#buildingGradient)" stroke="rgba(255,255,255,0.3)" stroke-width="3"/>

                            <!-- Building Columns -->
                            <rect x="120" y="240" width="18" height="100" rx="5" fill="rgba(255,255,255,0.25)"/>
                            <rect x="150" y="240" width="18" height="100" rx="5" fill="rgba(255,255,255,0.25)"/>
                            <rect x="180" y="240" width="18" height="100" rx="5" fill="rgba(255,255,255,0.25)"/>
                            <rect x="210" y="240" width="18" height="100" rx="5" fill="rgba(255,255,255,0.25)"/>
                            <rect x="240" y="240" width="18" height="100" rx="5" fill="rgba(255,255,255,0.25)"/>
                            <rect x="270" y="240" width="18" height="100" rx="5" fill="rgba(255,255,255,0.25)"/>
                            <rect x="300" y="240" width="18" height="100" rx="5" fill="rgba(255,255,255,0.25)"/>

                            <!-- Bank Name Plaque -->
                            <rect x="140" y="255" width="140" height="10" rx="5" fill="rgba(255,255,255,0.4)"/>

                            <!-- Bank Steps -->
                            <rect x="90" y="350" width="240" height="8" rx="4" fill="rgba(255,255,255,0.2)"/>
                            <rect x="95" y="358" width="230" height="6" rx="3" fill="rgba(255,255,255,0.15)"/>
                            <rect x="100" y="364" width="220" height="4" rx="2" fill="rgba(255,255,255,0.1)"/>

                            <!-- Digital Dashboard -->
                            <rect x="350" y="90" width="140" height="120" rx="12" fill="url(#screenGlow)" stroke="rgba(255,255,255,0.4)" stroke-width="3"/>

                            <!-- Dashboard Header -->
                            <rect x="370" y="110" width="100" height="10" rx="5" fill="rgba(255,255,255,0.5)"/>

                            <!-- Dashboard Cards -->
                            <rect x="370" y="135" width="45" height="30" rx="5" fill="rgba(255,255,255,0.3)"/>
                            <rect x="425" y="135" width="45" height="30" rx="5" fill="rgba(255,255,255,0.3)"/>

                            <!-- Dashboard Stats -->
                            <rect x="370" y="180" width="100" height="6" rx="3" fill="rgba(255,255,255,0.4)"/>
                            <rect x="370" y="192" width="75" height="6" rx="3" fill="rgba(255,255,255,0.3)"/>

                            <!-- Cloud Infrastructure -->
                            <ellipse cx="450" cy="50" rx="35" ry="20" fill="url(#cloudGradient)" stroke="rgba(255,255,255,0.3)" stroke-width="2"/>
                            <ellipse cx="430" cy="45" rx="25" ry="15" fill="rgba(255,255,255,0.2)"/>
                            <ellipse cx="470" cy="45" rx="25" ry="15" fill="rgba(255,255,255,0.2)"/>

                            <!-- Network Nodes -->
                            <circle cx="80" cy="120" r="8" fill="url(#networkGradient)" stroke="rgba(255,255,255,0.4)" stroke-width="2"/>
                            <circle cx="520" cy="180" r="8" fill="url(#networkGradient)" stroke="rgba(255,255,255,0.4)" stroke-width="2"/>
                            <circle cx="480" cy="280" r="8" fill="url(#networkGradient)" stroke="rgba(255,255,255,0.4)" stroke-width="2"/>

                            <!-- Connection Lines -->
                            <path d="M320 250 Q335 240 350 230" stroke="rgba(255,255,255,0.4)" stroke-width="4" fill="none"/>
                            <path d="M320 270 Q335 260 350 250" stroke="rgba(255,255,255,0.3)" stroke-width="3" fill="none"/>
                            <path d="M88 120 Q150 100 200 120" stroke="rgba(255,255,255,0.3)" stroke-width="3" fill="none"/>
                            <path d="M490 150 Q505 165 520 180" stroke="rgba(255,255,255,0.3)" stroke-width="3" fill="none"/>
                            <path d="M450 210 Q465 245 480 280" stroke="rgba(255,255,255,0.3)" stroke-width="3" fill="none"/>

                            <!-- Large Security Shield -->
                            <path d="M250 50 L280 30 L310 50 L310 90 Q310 115 280 135 Q250 115 250 90 Z" fill="url(#shieldGradient)" stroke="rgba(255,255,255,0.5)" stroke-width="3"/>
                            <path d="M270 70 L280 85 L300 60" stroke="rgba(255,255,255,0.8)" stroke-width="4" fill="none"/>

                            <!-- ATM Machine -->
                            <rect x="40" y="280" width="60" height="90" rx="8" fill="url(#buildingGradient)" stroke="rgba(255,255,255,0.3)" stroke-width="2"/>
                            <rect x="50" y="295" width="40" height="25" rx="4" fill="rgba(255,255,255,0.3)"/>
                            <rect x="55" y="330" width="30" height="4" rx="2" fill="rgba(255,255,255,0.3)"/>
                            <rect x="55" y="340" width="20" height="4" rx="2" fill="rgba(255,255,255,0.3)"/>
                            <rect x="55" y="350" width="25" height="4" rx="2" fill="rgba(255,255,255,0.3)"/>
                            <circle cx="85" cy="310" r="3" fill="rgba(255,255,255,0.4)"/>

                            <!-- Floating Data Points -->
                            <circle cx="120" cy="80" r="4" fill="rgba(255,255,255,0.5)"/>
                            <circle cx="400" cy="60" r="3" fill="rgba(255,255,255,0.4)"/>
                            <circle cx="60" cy="150" r="3" fill="rgba(255,255,255,0.3)"/>
                            <circle cx="420" cy="300" r="4" fill="rgba(255,255,255,0.4)"/>
                            <circle cx="50" cy="250" r="2" fill="rgba(255,255,255,0.3)"/>
                            <circle cx="180" cy="40" r="2.5" fill="rgba(255,255,255,0.4)"/>
                            <circle cx="500" cy="120" r="3" fill="rgba(255,255,255,0.4)"/>
                            <circle cx="30" cy="180" r="2" fill="rgba(255,255,255,0.3)"/>
                            <circle cx="380" cy="30" r="2.5" fill="rgba(255,255,255,0.4)"/>

                            <!-- Data Flow Lines -->
                            <path d="M120 85 Q150 75 180 80" stroke="rgba(255,255,255,0.3)" stroke-width="3" fill="none"/>
                            <path d="M400 65 Q370 55 340 60" stroke="rgba(255,255,255,0.3)" stroke-width="3" fill="none"/>
                            <path d="M63 150 Q85 145 120 150" stroke="rgba(255,255,255,0.2)" stroke-width="2" fill="none"/>
                            <path d="M500 125 Q470 135 440 130" stroke="rgba(255,255,255,0.2)" stroke-width="2" fill="none"/>

                            <!-- Additional Banking Elements -->
                            <rect x="120" y="320" width="80" height="45" rx="8" fill="rgba(255,255,255,0.2)" stroke="rgba(255,255,255,0.3)" stroke-width="2"/>
                            <rect x="130" y="330" width="60" height="5" rx="2" fill="rgba(255,255,255,0.3)"/>
                            <rect x="130" y="340" width="40" height="5" rx="2" fill="rgba(255,255,255,0.3)"/>
                            <rect x="130" y="350" width="50" height="5" rx="2" fill="rgba(255,255,255,0.3)"/>

                            <!-- Mobile Banking Device -->
                            <rect x="420" y="320" width="60" height="95" rx="10" fill="rgba(255,255,255,0.2)" stroke="rgba(255,255,255,0.3)" stroke-width="2"/>
                            <rect x="430" y="335" width="40" height="25" rx="4" fill="rgba(255,255,255,0.3)"/>
                            <rect x="430" y="370" width="40" height="4" rx="2" fill="rgba(255,255,255,0.3)"/>
                            <rect x="430" y="380" width="25" height="4" rx="2" fill="rgba(255,255,255,0.3)"/>
                            <rect x="430" y="390" width="35" height="4" rx="2" fill="rgba(255,255,255,0.3)"/>
                            <circle cx="450" cy="405" r="4" fill="rgba(255,255,255,0.4)"/>

                            <!-- Server Racks -->
                            <rect x="500" y="240" width="30" height="60" rx="4" fill="rgba(255,255,255,0.2)" stroke="rgba(255,255,255,0.3)" stroke-width="2"/>
                            <rect x="505" y="250" width="20" height="4" rx="2" fill="rgba(255,255,255,0.3)"/>
                            <rect x="505" y="260" width="20" height="4" rx="2" fill="rgba(255,255,255,0.3)"/>
                            <rect x="505" y="270" width="20" height="4" rx="2" fill="rgba(255,255,255,0.3)"/>
                            <rect x="505" y="280" width="20" height="4" rx="2" fill="rgba(255,255,255,0.3)"/>
                            <circle cx="520" cy="255" r="2" fill="rgba(255,255,255,0.4)"/>
                            <circle cx="520" cy="265" r="2" fill="rgba(255,255,255,0.4)"/>
                            <circle cx="520" cy="275" r="2" fill="rgba(255,255,255,0.4)"/>
                            <circle cx="520" cy="285" r="2" fill="rgba(255,255,255,0.4)"/>
                        </svg>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        // Auto-focus username field
        document.getElementById('username').focus();
        
        // Add loading state to form submission
        document.querySelector('form').addEventListener('submit', function() {
            const button = document.querySelector('.btn-super-admin');
            button.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Authenticating...';
            button.disabled = true;
        });
    </script>
</body>
</html>
