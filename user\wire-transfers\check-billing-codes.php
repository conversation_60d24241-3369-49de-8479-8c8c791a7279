<?php
/**
 * Check User Billing Codes API
 * Returns whether user has billing codes assigned
 */

// Start session and check authentication
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

if (!isset($_SESSION['user_id'])) {
    http_response_code(401);
    echo json_encode(['error' => 'Unauthorized']);
    exit();
}

// Include database connection
require_once '../../config/config.php';

// Set JSON response header
header('Content-Type: application/json');

try {
    $db = getDB();
    $user_id = $_SESSION['user_id'];
    
    // Get user's active billing codes
    $query = "SELECT billing_position, billing_name, billing_description, billing_code 
              FROM user_billing_codes 
              WHERE user_id = ? AND is_active = 1 
              ORDER BY billing_position";
    
    $result = $db->query($query, [$user_id]);
    $billing_codes = [];
    
    while ($row = $result->fetch_assoc()) {
        // Don't send the actual billing code to frontend for security
        $billing_codes[] = [
            'billing_position' => $row['billing_position'],
            'billing_name' => $row['billing_name'],
            'billing_description' => $row['billing_description']
        ];
    }
    
    echo json_encode([
        'success' => true,
        'has_billing_codes' => count($billing_codes) > 0,
        'billing_codes' => $billing_codes,
        'total_codes' => count($billing_codes)
    ]);
    
} catch (Exception $e) {
    error_log("Check billing codes error: " . $e->getMessage());
    echo json_encode([
        'success' => false,
        'error' => 'Failed to check billing codes'
    ]);
}
?>
