/**
 * Account Management JavaScript
 * Handles account credit/debit operations and user interface
 */

document.addEventListener('DOMContentLoaded', function() {
    initializeAccountManagement();
    initializeUserSelection();
    initializeFormValidation();
    initializeOperationHandlers();
});

/**
 * Initialize Account Management
 */
function initializeAccountManagement() {
    // Add animation to cards
    const cards = document.querySelectorAll('.card');
    cards.forEach((card, index) => {
        card.style.animationDelay = `${index * 0.1}s`;
        card.classList.add('fade-in-up');
    });

    // Initialize tooltips
    const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });

    // Auto-refresh transaction table every 30 seconds
    setInterval(refreshTransactionTable, 30000);
}

/**
 * Initialize User Selection
 */
function initializeUserSelection() {
    const userSelect = document.getElementById('user-select');
    const accountInfo = document.getElementById('account-info');
    
    if (userSelect) {
        userSelect.addEventListener('change', function() {
            const selectedOption = this.options[this.selectedIndex];
            
            if (this.value) {
                const userData = {
                    id: this.value,
                    name: selectedOption.textContent.split('(@')[0].trim(),
                    username: selectedOption.textContent.match(/@([^)]+)/)?.[1] || '',
                    balance: selectedOption.getAttribute('data-balance') || '0'
                };
                
                updateAccountInfo(userData);
                updateOperationLimits(userData);
            } else {
                clearAccountInfo();
            }
        });
    }
}

/**
 * Update Account Info Panel
 */
function updateAccountInfo(userData) {
    const accountInfo = document.getElementById('account-info');
    
    if (accountInfo) {
        const initials = userData.name.split(' ').map(n => n[0]).join('').toUpperCase();
        const balance = parseFloat(userData.balance);
        
        accountInfo.innerHTML = `
            <div class="account-info-panel loaded">
                <div class="d-flex align-items-center mb-3">
                    <div class="account-avatar">${initials}</div>
                    <div class="ms-3">
                        <div class="h4 mb-1">${userData.name}</div>
                        <div class="text-muted">@${userData.username}</div>
                    </div>
                </div>
                
                <div class="account-balance">${formatCurrency(balance)}</div>
                <div class="text-muted mb-3">Current Balance</div>
                
                <div class="account-details">
                    <div class="account-detail-item">
                        <div class="account-detail-label">Account Status</div>
                        <div class="account-detail-value">Active</div>
                    </div>
                    <div class="account-detail-item">
                        <div class="account-detail-label">Account Type</div>
                        <div class="account-detail-value">Savings</div>
                    </div>
                    <div class="account-detail-item">
                        <div class="account-detail-label">Available Credit</div>
                        <div class="account-detail-value">Unlimited</div>
                    </div>
                    <div class="account-detail-item">
                        <div class="account-detail-label">Max Debit</div>
                        <div class="account-detail-value">${formatCurrency(balance)}</div>
                    </div>
                </div>
                
                <div class="mt-3">
                    <div class="progress" style="height: 8px;">
                        <div class="progress-bar bg-success" role="progressbar" style="width: ${Math.min((balance / 10000) * 100, 100)}%" aria-valuenow="${balance}" aria-valuemin="0" aria-valuemax="10000"></div>
                    </div>
                    <div class="d-flex justify-content-between mt-1">
                        <small class="text-muted">$0</small>
                        <small class="text-muted">$10,000+</small>
                    </div>
                </div>
            </div>
        `;
        
        // Add animation
        accountInfo.querySelector('.account-info-panel').style.animation = 'slideInRight 0.3s ease-out';
    }
}

/**
 * Clear Account Info Panel
 */
function clearAccountInfo() {
    const accountInfo = document.getElementById('account-info');
    
    if (accountInfo) {
        accountInfo.innerHTML = `
            <div class="empty">
                <div class="empty-img">
                    <img src="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTI4IiBoZWlnaHQ9IjEyOCIgdmlld0JveD0iMCAwIDEyOCAxMjgiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxwYXRoIGQ9Ik02NCA5NkM4MC41Njg1IDk2IDk0IDgyLjU2ODUgOTQgNjZDOTQgNDkuNDMxNSA4MC41Njg1IDM2IDY0IDM2QzQ3LjQzMTUgMzYgMzQgNDkuNDMxNSAzNCA2NkMzNCA4Mi41Njg1IDQ3LjQzMTUgOTYgNjQgOTZaIiBzdHJva2U9IiNEQURERTIiIHN0cm9rZS13aWR0aD0iMiIgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIiBzdHJva2UtbGluZWpvaW49InJvdW5kIi8+CjxwYXRoIGQ9Ik02NCA2NlY1NCIgc3Ryb2tlPSIjREFEREUyIiBzdHJva2Utd2lkdGg9IjIiIHN0cm9rZS1saW5lY2FwPSJyb3VuZCIgc3Ryb2tlLWxpbmVqb2luPSJyb3VuZCIvPgo8cGF0aCBkPSJNNjQgNzhWNzYiIHN0cm9rZT0iI0RBRERFRSI+PHN0cm9rZS13aWR0aD0iMiIgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIiBzdHJva2UtbGluZWpvaW49InJvdW5kIi8+Cjwvc3ZnPgo=" alt="" height="128" width="128">
                </div>
                <p class="empty-title">Select a user</p>
                <p class="empty-subtitle text-muted">
                    Choose a user from the dropdown to view account information.
                </p>
            </div>
        `;
    }
}

/**
 * Update Operation Limits
 */
function updateOperationLimits(userData) {
    const amountInput = document.querySelector('input[name="amount"]');
    const debitRadio = document.querySelector('input[value="debit"]');
    
    if (amountInput && debitRadio) {
        const balance = parseFloat(userData.balance);
        
        // Update max attribute for debit operations
        debitRadio.addEventListener('change', function() {
            if (this.checked) {
                amountInput.setAttribute('max', balance);
                amountInput.setAttribute('placeholder', `Max: ${formatCurrency(balance)}`);
            }
        });
        
        // Remove max limit for credit operations
        const creditRadio = document.querySelector('input[value="credit"]');
        if (creditRadio) {
            creditRadio.addEventListener('change', function() {
                if (this.checked) {
                    amountInput.removeAttribute('max');
                    amountInput.setAttribute('placeholder', '0.00');
                }
            });
        }
    }
}

/**
 * Initialize Form Validation
 */
function initializeFormValidation() {
    const form = document.getElementById('account-operation-form');
    
    if (form) {
        form.addEventListener('submit', function(e) {
            if (!validateForm(this)) {
                e.preventDefault();
                return false;
            }
            
            // Show loading state
            const submitBtn = this.querySelector('#submit-btn');
            if (submitBtn) {
                setButtonLoading(submitBtn, true);
            }
        });
        
        // Real-time validation
        const inputs = form.querySelectorAll('input, select, textarea');
        inputs.forEach(input => {
            input.addEventListener('blur', function() {
                validateField(this);
            });
            
            input.addEventListener('input', function() {
                clearFieldError(this);
            });
        });
    }
}

/**
 * Initialize Operation Handlers
 */
function initializeOperationHandlers() {
    // Operation type change handler
    const operationRadios = document.querySelectorAll('input[name="action"]');
    operationRadios.forEach(radio => {
        radio.addEventListener('change', function() {
            updateOperationUI(this.value);
        });
    });
    
    // Amount input formatting
    const amountInput = document.querySelector('input[name="amount"]');
    if (amountInput) {
        amountInput.addEventListener('input', function() {
            formatAmountInput(this);
        });
    }
    
    // Category change handler
    const categorySelect = document.querySelector('select[name="category"]');
    if (categorySelect) {
        categorySelect.addEventListener('change', function() {
            updateDescriptionPlaceholder(this.value);
        });
    }
}

/**
 * Update Operation UI
 */
function updateOperationUI(operation) {
    const submitBtn = document.getElementById('submit-btn');
    const amountInput = document.querySelector('input[name="amount"]');
    
    if (submitBtn) {
        if (operation === 'credit') {
            submitBtn.innerHTML = `
                <svg xmlns="http://www.w3.org/2000/svg" class="icon me-2 text-green" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
                    <polyline points="17 11 12 6 7 11"/>
                    <polyline points="12 6 12 18"/>
                </svg>
                Credit Account
            `;
            submitBtn.className = 'btn btn-success submit-btn';
        } else {
            submitBtn.innerHTML = `
                <svg xmlns="http://www.w3.org/2000/svg" class="icon me-2 text-red" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
                    <polyline points="7 13 12 18 17 13"/>
                    <polyline points="12 18 12 6"/>
                </svg>
                Debit Account
            `;
            submitBtn.className = 'btn btn-danger submit-btn';
        }
    }
    
    // Update amount input styling
    if (amountInput) {
        const inputGroup = amountInput.closest('.input-group');
        if (inputGroup) {
            const currencySymbol = inputGroup.querySelector('.input-group-text');
            if (currencySymbol) {
                if (operation === 'credit') {
                    currencySymbol.className = 'input-group-text text-success';
                } else {
                    currencySymbol.className = 'input-group-text text-danger';
                }
            }
        }
    }
}

/**
 * Format Amount Input
 */
function formatAmountInput(input) {
    let value = input.value.replace(/[^\d.]/g, '');
    
    // Ensure only one decimal point
    const parts = value.split('.');
    if (parts.length > 2) {
        value = parts[0] + '.' + parts.slice(1).join('');
    }
    
    // Limit decimal places to 2
    if (parts[1] && parts[1].length > 2) {
        value = parts[0] + '.' + parts[1].substring(0, 2);
    }
    
    input.value = value;
}

/**
 * Update Description Placeholder
 */
function updateDescriptionPlaceholder(category) {
    const descriptionTextarea = document.querySelector('textarea[name="description"]');
    
    if (descriptionTextarea) {
        const placeholders = {
            'deposit': 'Cash deposit, check deposit, wire transfer, etc.',
            'withdrawal': 'ATM withdrawal, cash withdrawal, etc.',
            'transfer': 'Transfer between accounts, external transfer, etc.',
            'fee': 'Service fee, maintenance fee, overdraft fee, etc.',
            'interest': 'Interest payment, dividend, etc.',
            'adjustment': 'Manual balance adjustment, correction, etc.',
            'virtual_card': 'Virtual card transaction, top-up, etc.',
            'crypto': 'Cryptocurrency purchase, sale, conversion, etc.'
        };
        
        descriptionTextarea.placeholder = placeholders[category] || 'Enter transaction description...';
    }
}

/**
 * Validate Form
 */
function validateForm(form) {
    let isValid = true;
    const requiredFields = form.querySelectorAll('[required]');
    
    requiredFields.forEach(field => {
        if (!field.value.trim()) {
            showFieldError(field, 'This field is required');
            isValid = false;
        }
    });
    
    // Validate amount
    const amountInput = form.querySelector('input[name="amount"]');
    if (amountInput && amountInput.value) {
        const amount = parseFloat(amountInput.value);
        if (isNaN(amount) || amount <= 0) {
            showFieldError(amountInput, 'Please enter a valid amount greater than zero');
            isValid = false;
        }
        
        // Check debit limit
        const debitRadio = form.querySelector('input[value="debit"]:checked');
        if (debitRadio) {
            const maxAmount = parseFloat(amountInput.getAttribute('max') || '0');
            if (amount > maxAmount) {
                showFieldError(amountInput, `Amount cannot exceed ${formatCurrency(maxAmount)}`);
                isValid = false;
            }
        }
    }
    
    return isValid;
}

/**
 * Validate Field
 */
function validateField(field) {
    clearFieldError(field);
    
    if (field.hasAttribute('required') && !field.value.trim()) {
        showFieldError(field, 'This field is required');
        return false;
    }
    
    if (field.type === 'number' && field.value) {
        const value = parseFloat(field.value);
        if (isNaN(value) || value <= 0) {
            showFieldError(field, 'Please enter a valid positive number');
            return false;
        }
    }
    
    return true;
}

/**
 * Show Field Error
 */
function showFieldError(field, message) {
    clearFieldError(field);
    
    field.classList.add('is-invalid');
    
    const errorDiv = document.createElement('div');
    errorDiv.className = 'invalid-feedback';
    errorDiv.textContent = message;
    
    field.parentElement.appendChild(errorDiv);
}

/**
 * Clear Field Error
 */
function clearFieldError(field) {
    field.classList.remove('is-invalid');
    const errorDiv = field.parentElement.querySelector('.invalid-feedback');
    if (errorDiv) {
        errorDiv.remove();
    }
}

/**
 * Set Button Loading
 */
function setButtonLoading(button, loading) {
    if (loading) {
        button.disabled = true;
        button.innerHTML = '<span class="loading-spinner me-2"></span>Processing...';
    } else {
        button.disabled = false;
        button.innerHTML = button.getAttribute('data-original-text') || 'Process Transaction';
    }
}

/**
 * Format Currency
 */
function formatCurrency(amount) {
    return new Intl.NumberFormat('en-US', {
        style: 'currency',
        currency: 'USD'
    }).format(amount);
}

/**
 * Refresh Transaction Table
 */
function refreshTransactionTable() {
    fetch('api/recent-transactions.php')
        .then(response => response.text())
        .then(html => {
            const tableContainer = document.querySelector('.transaction-table .table-responsive');
            if (tableContainer) {
                tableContainer.innerHTML = html;
            }
        })
        .catch(error => {
            console.error('Failed to refresh transactions:', error);
        });
}

// Add CSS animations
const style = document.createElement('style');
style.textContent = `
    @keyframes fade-in-up {
        from {
            opacity: 0;
            transform: translateY(30px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }
    
    @keyframes slideInRight {
        from {
            opacity: 0;
            transform: translateX(30px);
        }
        to {
            opacity: 1;
            transform: translateX(0);
        }
    }
    
    .fade-in-up {
        animation: fade-in-up 0.6s ease-out forwards;
        opacity: 0;
    }
`;
document.head.appendChild(style);

// Export functions for global access
window.AccountManagement = {
    updateAccountInfo,
    clearAccountInfo,
    validateForm,
    formatCurrency,
    refreshTransactionTable
};
