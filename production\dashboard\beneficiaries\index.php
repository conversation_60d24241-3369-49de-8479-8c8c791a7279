<?php
// Set page variables
$page_title = 'Beneficiaries';
$additional_css = ['dashboard.css'];
$additional_js = ['dashboard.js'];

// Include header template
require_once '../../templates/user/header.php';

// Include database connection
require_once '../../config/config.php';
requireLogin();

// Handle form submissions
$success_message = '';
$error_message = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    try {
        $db = getDB();
        $user_id = $_SESSION['user_id'];
        
        if (isset($_POST['action'])) {
            switch ($_POST['action']) {
                case 'add_beneficiary':
                    $name = sanitizeInput($_POST['name']);
                    $account_number = sanitizeInput($_POST['account_number']);
                    $bank_name = sanitizeInput($_POST['bank_name']);
                    $bank_code = sanitizeInput($_POST['bank_code'] ?? '');
                    $country = sanitizeInput($_POST['country'] ?? 'USA');
                    $currency = sanitizeInput($_POST['currency'] ?? 'USD');
                    $is_favorite = isset($_POST['is_favorite']) ? 1 : 0;
                    
                    // Validation
                    if (empty($name) || empty($account_number) || empty($bank_name)) {
                        throw new Exception('Please fill in all required fields.');
                    }
                    
                    // Check if beneficiary already exists
                    $check_sql = "SELECT id FROM beneficiaries WHERE user_id = ? AND account_number = ?";
                    $check_result = $db->query($check_sql, [$user_id, $account_number]);
                    
                    if ($check_result->num_rows > 0) {
                        throw new Exception('This beneficiary already exists in your list.');
                    }
                    
                    // Insert new beneficiary
                    $insert_sql = "INSERT INTO beneficiaries (user_id, name, account_number, bank_name, bank_code, country, currency, is_favorite, created_at) 
                                   VALUES (?, ?, ?, ?, ?, ?, ?, ?, NOW())";
                    $db->query($insert_sql, [$user_id, $name, $account_number, $bank_name, $bank_code, $country, $currency, $is_favorite]);
                    
                    $success_message = 'Beneficiary added successfully!';
                    break;
                    
                case 'delete_beneficiary':
                    $beneficiary_id = (int)$_POST['beneficiary_id'];
                    
                    // Delete beneficiary
                    $delete_sql = "DELETE FROM beneficiaries WHERE id = ? AND user_id = ?";
                    $db->query($delete_sql, [$beneficiary_id, $user_id]);
                    
                    $success_message = 'Beneficiary removed successfully!';
                    break;
                    
                case 'toggle_favorite':
                    $beneficiary_id = (int)$_POST['beneficiary_id'];
                    $is_favorite = (int)$_POST['is_favorite'];
                    
                    // Update favorite status
                    $update_sql = "UPDATE beneficiaries SET is_favorite = ? WHERE id = ? AND user_id = ?";
                    $db->query($update_sql, [$is_favorite, $beneficiary_id, $user_id]);
                    
                    $success_message = $is_favorite ? 'Added to favorites!' : 'Removed from favorites!';
                    break;
            }
        }
    } catch (Exception $e) {
        $error_message = $e->getMessage();
    }
}

// Get user's beneficiaries and statistics
try {
    $db = getDB();
    $user_id = $_SESSION['user_id'];
    
    // Get all beneficiaries
    $beneficiaries_sql = "SELECT * FROM beneficiaries WHERE user_id = ? ORDER BY is_favorite DESC, name ASC";
    $beneficiaries_result = $db->query($beneficiaries_sql, [$user_id]);
    $beneficiaries = [];
    while ($row = $beneficiaries_result->fetch_assoc()) {
        $beneficiaries[] = $row;
    }
    
    // Get statistics
    $stats_sql = "SELECT 
                    COUNT(*) as total_beneficiaries,
                    COUNT(CASE WHEN is_favorite = 1 THEN 1 END) as favorite_count,
                    COUNT(DISTINCT country) as countries_count
                  FROM beneficiaries WHERE user_id = ?";
    $stats_result = $db->query($stats_sql, [$user_id]);
    $stats = $stats_result->fetch_assoc();
    
    // Get recent transfers to beneficiaries
    $recent_sql = "SELECT t.*, b.name as beneficiary_name 
                   FROM transfers t 
                   JOIN beneficiaries b ON t.recipient_account = b.account_number 
                   WHERE t.sender_id = ? AND b.user_id = ? 
                   ORDER BY t.created_at DESC LIMIT 5";
    $recent_result = $db->query($recent_sql, [$user_id, $user_id]);
    $recent_transfers = [];
    while ($row = $recent_result->fetch_assoc()) {
        $recent_transfers[] = $row;
    }
    
} catch (Exception $e) {
    error_log("Beneficiaries page error: " . $e->getMessage());
    $beneficiaries = [];
    $stats = ['total_beneficiaries' => 0, 'favorite_count' => 0, 'countries_count' => 0];
    $recent_transfers = [];
}
?>

<!-- Include Sidebar -->
<?php require_once '../../templates/user/sidebar.php'; ?>

<!-- Main Content -->
<div class="main-content">
    <!-- Page Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h1 class="h2 mb-1">Beneficiaries</h1>
                    <p class="text-muted">Manage your trusted recipients for quick transfers</p>
                </div>
                <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addBeneficiaryModal">
                    <i class="fas fa-plus me-2"></i>Add Beneficiary
                </button>
            </div>
        </div>
    </div>

    <!-- Success/Error Messages -->
    <?php if ($success_message): ?>
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            <i class="fas fa-check-circle me-2"></i><?php echo htmlspecialchars($success_message); ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <?php if ($error_message): ?>
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <i class="fas fa-exclamation-circle me-2"></i><?php echo htmlspecialchars($error_message); ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <!-- Statistics Cards -->
    <div class="row mb-4">
        <div class="col-md-4">
            <div class="card border-0 shadow-sm">
                <div class="card-body text-center">
                    <div class="text-primary mb-2">
                        <i class="fas fa-users" style="font-size: 2rem;"></i>
                    </div>
                    <h3 class="mb-1"><?php echo $stats['total_beneficiaries']; ?></h3>
                    <p class="text-muted mb-0">Total Beneficiaries</p>
                </div>
            </div>
        </div>
        <div class="col-md-4">
            <div class="card border-0 shadow-sm">
                <div class="card-body text-center">
                    <div class="text-warning mb-2">
                        <i class="fas fa-star" style="font-size: 2rem;"></i>
                    </div>
                    <h3 class="mb-1"><?php echo $stats['favorite_count']; ?></h3>
                    <p class="text-muted mb-0">Favorites</p>
                </div>
            </div>
        </div>
        <div class="col-md-4">
            <div class="card border-0 shadow-sm">
                <div class="card-body text-center">
                    <div class="text-success mb-2">
                        <i class="fas fa-globe" style="font-size: 2rem;"></i>
                    </div>
                    <h3 class="mb-1"><?php echo $stats['countries_count']; ?></h3>
                    <p class="text-muted mb-0">Countries</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Beneficiaries List -->
    <div class="row">
        <div class="col-lg-8">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-white">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-address-book me-2"></i>Your Beneficiaries
                    </h5>
                </div>
                <div class="card-body">
                    <?php if (empty($beneficiaries)): ?>
                        <div class="text-center py-5">
                            <i class="fas fa-users text-muted" style="font-size: 3rem;"></i>
                            <h5 class="mt-3 text-muted">No Beneficiaries Added</h5>
                            <p class="text-muted">Add your first beneficiary to start making quick transfers</p>
                            <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addBeneficiaryModal">
                                <i class="fas fa-plus me-2"></i>Add Beneficiary
                            </button>
                        </div>
                    <?php else: ?>
                        <div class="row g-3">
                            <?php foreach ($beneficiaries as $beneficiary): ?>
                                <div class="col-md-6">
                                    <div class="card border <?php echo $beneficiary['is_favorite'] ? 'border-warning' : ''; ?>">
                                        <div class="card-body">
                                            <div class="d-flex justify-content-between align-items-start mb-2">
                                                <h6 class="card-title mb-0">
                                                    <?php echo htmlspecialchars($beneficiary['name']); ?>
                                                    <?php if ($beneficiary['is_favorite']): ?>
                                                        <i class="fas fa-star text-warning ms-1"></i>
                                                    <?php endif; ?>
                                                </h6>
                                                <div class="dropdown">
                                                    <button class="btn btn-sm btn-outline-secondary" type="button" data-bs-toggle="dropdown">
                                                        <i class="fas fa-ellipsis-v"></i>
                                                    </button>
                                                    <ul class="dropdown-menu">
                                                        <li>
                                                            <form method="POST" class="d-inline">
                                                                <input type="hidden" name="action" value="toggle_favorite">
                                                                <input type="hidden" name="beneficiary_id" value="<?php echo $beneficiary['id']; ?>">
                                                                <input type="hidden" name="is_favorite" value="<?php echo $beneficiary['is_favorite'] ? 0 : 1; ?>">
                                                                <button type="submit" class="dropdown-item">
                                                                    <i class="fas fa-star me-2"></i>
                                                                    <?php echo $beneficiary['is_favorite'] ? 'Remove from Favorites' : 'Add to Favorites'; ?>
                                                                </button>
                                                            </form>
                                                        </li>
                                                        <li><hr class="dropdown-divider"></li>
                                                        <li>
                                                            <form method="POST" class="d-inline" onsubmit="return confirm('Are you sure you want to delete this beneficiary?')">
                                                                <input type="hidden" name="action" value="delete_beneficiary">
                                                                <input type="hidden" name="beneficiary_id" value="<?php echo $beneficiary['id']; ?>">
                                                                <button type="submit" class="dropdown-item text-danger">
                                                                    <i class="fas fa-trash me-2"></i>Delete
                                                                </button>
                                                            </form>
                                                        </li>
                                                    </ul>
                                                </div>
                                            </div>
                                            <p class="text-muted small mb-1">
                                                <i class="fas fa-university me-1"></i>
                                                <?php echo htmlspecialchars($beneficiary['bank_name']); ?>
                                            </p>
                                            <p class="text-muted small mb-1">
                                                <i class="fas fa-credit-card me-1"></i>
                                                <?php echo htmlspecialchars($beneficiary['account_number']); ?>
                                            </p>
                                            <p class="text-muted small mb-2">
                                                <i class="fas fa-flag me-1"></i>
                                                <?php echo htmlspecialchars($beneficiary['country']); ?> • <?php echo htmlspecialchars($beneficiary['currency']); ?>
                                            </p>
                                            <a href="<?php echo $base_url; ?>/dashboard/transfers/?beneficiary=<?php echo $beneficiary['id']; ?>" class="btn btn-sm btn-primary">
                                                <i class="fas fa-paper-plane me-1"></i>Send Money
                                            </a>
                                        </div>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>

        <!-- Recent Transfers Sidebar -->
        <div class="col-lg-4">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-white">
                    <h6 class="card-title mb-0">
                        <i class="fas fa-history me-2"></i>Recent Transfers
                    </h6>
                </div>
                <div class="card-body">
                    <?php if (empty($recent_transfers)): ?>
                        <div class="text-center py-3">
                            <i class="fas fa-exchange-alt text-muted" style="font-size: 2rem;"></i>
                            <p class="text-muted mt-2 mb-0">No recent transfers</p>
                        </div>
                    <?php else: ?>
                        <div class="list-group list-group-flush">
                            <?php foreach ($recent_transfers as $transfer): ?>
                                <div class="list-group-item px-0">
                                    <div class="d-flex justify-content-between align-items-start">
                                        <div>
                                            <h6 class="mb-1"><?php echo htmlspecialchars($transfer['beneficiary_name']); ?></h6>
                                            <p class="mb-1 text-muted small">
                                                $<?php echo number_format($transfer['amount'], 2); ?>
                                            </p>
                                            <small class="text-muted">
                                                <?php echo date('M j, Y', strtotime($transfer['created_at'])); ?>
                                            </small>
                                        </div>
                                        <span class="badge bg-success">Completed</span>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Add Beneficiary Modal -->
<div class="modal fade" id="addBeneficiaryModal" tabindex="-1" aria-labelledby="addBeneficiaryModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="addBeneficiaryModalLabel">
                    <i class="fas fa-user-plus me-2"></i>Add New Beneficiary
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form method="POST">
                <div class="modal-body">
                    <input type="hidden" name="action" value="add_beneficiary">

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="name" class="form-label">Beneficiary Name *</label>
                                <input type="text" class="form-control" id="name" name="name" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="account_number" class="form-label">Account Number *</label>
                                <input type="text" class="form-control" id="account_number" name="account_number" required>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="bank_name" class="form-label">Bank Name *</label>
                                <input type="text" class="form-control" id="bank_name" name="bank_name" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="bank_code" class="form-label">Bank Code</label>
                                <input type="text" class="form-control" id="bank_code" name="bank_code">
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="country" class="form-label">Country</label>
                                <select class="form-select" id="country" name="country">
                                    <option value="USA">United States</option>
                                    <option value="CAN">Canada</option>
                                    <option value="GBR">United Kingdom</option>
                                    <option value="EUR">European Union</option>
                                    <option value="AUS">Australia</option>
                                    <option value="JPN">Japan</option>
                                    <option value="CHN">China</option>
                                    <option value="IND">India</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="currency" class="form-label">Currency</label>
                                <select class="form-select" id="currency" name="currency">
                                    <option value="USD">USD - US Dollar</option>
                                    <option value="CAD">CAD - Canadian Dollar</option>
                                    <option value="GBP">GBP - British Pound</option>
                                    <option value="EUR">EUR - Euro</option>
                                    <option value="AUD">AUD - Australian Dollar</option>
                                    <option value="JPY">JPY - Japanese Yen</option>
                                    <option value="CNY">CNY - Chinese Yuan</option>
                                    <option value="INR">INR - Indian Rupee</option>
                                </select>
                            </div>
                        </div>
                    </div>

                    <div class="mb-3">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="is_favorite" name="is_favorite">
                            <label class="form-check-label" for="is_favorite">
                                <i class="fas fa-star text-warning me-1"></i>Add to favorites
                            </label>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-plus me-2"></i>Add Beneficiary
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<?php
// Include footer template
require_once '../../templates/user/footer.php';
?>
