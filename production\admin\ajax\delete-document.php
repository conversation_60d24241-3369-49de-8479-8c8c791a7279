<?php
require_once '../../config/config.php';
requireAdmin();

header('Content-Type: application/json');

// Ensure clean output
ob_clean();

try {
    // Get JSON input
    $input = json_decode(file_get_contents('php://input'), true);
    
    if (!$input || !isset($input['document_id'])) {
        throw new Exception('Document ID is required');
    }
    
    $document_id = intval($input['document_id']);
    
    if ($document_id <= 0) {
        throw new Exception('Invalid document ID');
    }
    
    $db = getDB();
    
    // Get document details first
    $doc_query = "SELECT * FROM user_documents WHERE id = ?";
    $doc_result = $db->query($doc_query, [$document_id]);
    
    if (!$doc_result || $doc_result->num_rows === 0) {
        throw new Exception('Document not found');
    }
    
    $document = $doc_result->fetch_assoc();
    
    // Delete the physical file if it exists
    if (!empty($document['file_path']) && file_exists($document['file_path'])) {
        if (!unlink($document['file_path'])) {
            error_log("Failed to delete file: " . $document['file_path']);
            // Continue with database deletion even if file deletion fails
        }
    }
    
    // Delete any related records first (document verification history)
    try {
        $delete_history_query = "DELETE FROM document_verification_history WHERE document_id = ?";
        $db->delete($delete_history_query, [$document_id]);
    } catch (Exception $e) {
        // History table might not exist, continue
        error_log("Delete history error (non-critical): " . $e->getMessage());
    }
    
    // Delete from database - use the same approach as the working test
    $delete_query = "DELETE FROM user_documents WHERE id = ?";
    $db->delete($delete_query, [$document_id]);
    
    // Verify the document was actually deleted by checking if it still exists
    $verify_query = "SELECT COUNT(*) as count FROM user_documents WHERE id = ?";
    $verify_result = $db->query($verify_query, [$document_id]);
    $remaining_count = $verify_result->fetch_assoc()['count'];
    
    if ($remaining_count > 0) {
        throw new Exception('Failed to delete document from database');
    }
    
    // Log the deletion
    error_log("Document deleted: ID {$document_id}, User ID {$document['user_id']}, File: {$document['file_path']}");
    
    echo json_encode([
        'success' => true,
        'message' => 'Document deleted successfully'
    ]);
    
} catch (Exception $e) {
    error_log("Delete document error: " . $e->getMessage());
    
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ]);
}
?>
