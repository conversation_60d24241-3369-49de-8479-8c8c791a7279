<?php
require_once '../config/config.php';
requireAdmin();

$page_title = 'Virtual Card Management';

// Pagination settings
$records_per_page = 25;
$current_page = intval($_GET['page'] ?? 1);
$offset = ($current_page - 1) * $records_per_page;

// Filter parameters
$filter_status = $_GET['status'] ?? '';
$filter_user = $_GET['user'] ?? '';
$filter_balance_min = floatval($_GET['balance_min'] ?? 0);
$filter_balance_max = floatval($_GET['balance_max'] ?? 0);

// Build WHERE clause for filters
$where_conditions = [];
$params = [];

if (!empty($filter_status)) {
    $where_conditions[] = "vc.status = ?";
    $params[] = $filter_status;
}

if (!empty($filter_user)) {
    $where_conditions[] = "(a.first_name LIKE ? OR a.last_name LIKE ? OR a.username LIKE ?)";
    $search_term = "%$filter_user%";
    $params[] = $search_term;
    $params[] = $search_term;
    $params[] = $search_term;
}

if ($filter_balance_min > 0) {
    $where_conditions[] = "vc.card_balance >= ?";
    $params[] = $filter_balance_min;
}

if ($filter_balance_max > 0) {
    $where_conditions[] = "vc.card_balance <= ?";
    $params[] = $filter_balance_max;
}

$where_clause = !empty($where_conditions) ? "WHERE " . implode(" AND ", $where_conditions) : "";

try {
    $db = getDB();
    
    // Get total count for pagination
    $count_query = "SELECT COUNT(*) as total 
                    FROM virtual_cards vc 
                    LEFT JOIN accounts a ON vc.account_id = a.id 
                    $where_clause";
    $count_result = $db->query($count_query, $params);
    $total_records = $count_result->fetch_assoc()['total'];
    $total_pages = ceil($total_records / $records_per_page);
    
    // Get virtual cards with pagination
    $cards_query = "SELECT vc.*, 
                    a.first_name, a.last_name, a.username, a.account_number,
                    admin.first_name as admin_first_name, admin.last_name as admin_last_name
                    FROM virtual_cards vc 
                    LEFT JOIN accounts a ON vc.account_id = a.id 
                    LEFT JOIN accounts admin ON vc.approved_by = admin.id 
                    $where_clause
                    ORDER BY vc.created_at DESC 
                    LIMIT $records_per_page OFFSET $offset";
    
    $cards_result = $db->query($cards_query, $params);
    $cards = [];
    while ($row = $cards_result->fetch_assoc()) {
        $cards[] = $row;
    }
    
    // Get summary statistics
    $stats_query = "SELECT 
                    COUNT(*) as total_cards,
                    COUNT(CASE WHEN status = 'active' THEN 1 END) as active_cards,
                    COUNT(CASE WHEN status = 'pending' THEN 1 END) as pending_cards,
                    COUNT(CASE WHEN status = 'blocked' THEN 1 END) as blocked_cards,
                    SUM(card_balance) as total_balance
                    FROM virtual_cards";
    $stats_result = $db->query($stats_query);
    $stats = $stats_result->fetch_assoc();
    
} catch (Exception $e) {
    $error = "Failed to load virtual cards: " . $e->getMessage();
    $cards = [];
    $total_records = 0;
    $total_pages = 0;
    $stats = ['total_cards' => 0, 'active_cards' => 0, 'pending_cards' => 0, 'blocked_cards' => 0, 'total_balance' => 0];
}

include 'includes/admin-header.php';
?>

<!-- Breadcrumb -->
<nav aria-label="breadcrumb" style="margin-bottom: 1rem;">
    <ol class="breadcrumb">
        <li class="breadcrumb-item"><a href="index.php">Admin</a></li>
        <li class="breadcrumb-item active" aria-current="page">Virtual Cards</li>
    </ol>
</nav>

<!-- Error Message -->
<?php if (isset($error)): ?>
<div class="alert alert-danger" role="alert">
    <div class="d-flex">
        <div><i class="fas fa-exclamation-circle me-2"></i></div>
        <div>
            <h4 class="alert-title">Error!</h4>
            <div class="text-muted"><?php echo htmlspecialchars($error); ?></div>
        </div>
    </div>
</div>
<?php endif; ?>

<!-- Statistics Cards -->
<div class="row row-cards mb-4">
    <div class="col-sm-6 col-lg-3">
        <div class="card card-sm">
            <div class="card-body">
                <div class="row align-items-center">
                    <div class="col-auto">
                        <span class="bg-primary text-white avatar">
                            <i class="fas fa-credit-card"></i>
                        </span>
                    </div>
                    <div class="col">
                        <div class="font-weight-medium"><?php echo number_format($stats['total_cards']); ?></div>
                        <div class="text-muted">Total Cards</div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-sm-6 col-lg-3">
        <div class="card card-sm">
            <div class="card-body">
                <div class="row align-items-center">
                    <div class="col-auto">
                        <span class="bg-success text-white avatar">
                            <i class="fas fa-check-circle"></i>
                        </span>
                    </div>
                    <div class="col">
                        <div class="font-weight-medium"><?php echo number_format($stats['active_cards']); ?></div>
                        <div class="text-muted">Active Cards</div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-sm-6 col-lg-3">
        <div class="card card-sm">
            <div class="card-body">
                <div class="row align-items-center">
                    <div class="col-auto">
                        <span class="bg-warning text-white avatar">
                            <i class="fas fa-clock"></i>
                        </span>
                    </div>
                    <div class="col">
                        <div class="font-weight-medium"><?php echo number_format($stats['pending_cards']); ?></div>
                        <div class="text-muted">Pending</div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-sm-6 col-lg-3">
        <div class="card card-sm">
            <div class="card-body">
                <div class="row align-items-center">
                    <div class="col-auto">
                        <span class="bg-info text-white avatar">
                            <i class="fas fa-dollar-sign"></i>
                        </span>
                    </div>
                    <div class="col">
                        <div class="font-weight-medium"><?php echo formatCurrency($stats['total_balance']); ?></div>
                        <div class="text-muted">Total Balance</div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Filters Card -->
<div class="row row-cards mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h3 class="card-title">
                    <i class="fas fa-filter me-2"></i>
                    Card Filters
                </h3>
                <div class="card-actions">
                    <a href="virtual-cards.php" class="btn btn-outline-secondary btn-sm">
                        <i class="fas fa-times me-2"></i>
                        Clear Filters
                    </a>
                </div>
            </div>
            <div class="card-body">
                <form method="GET" action="" class="row g-2">
                    <div class="col-md-3">
                        <label class="form-label">Status</label>
                        <select name="status" class="form-select form-select-sm">
                            <option value="">All Status</option>
                            <option value="pending" <?php echo $filter_status === 'pending' ? 'selected' : ''; ?>>Pending</option>
                            <option value="active" <?php echo $filter_status === 'active' ? 'selected' : ''; ?>>Active</option>
                            <option value="inactive" <?php echo $filter_status === 'inactive' ? 'selected' : ''; ?>>Inactive</option>
                            <option value="blocked" <?php echo $filter_status === 'blocked' ? 'selected' : ''; ?>>Blocked</option>
                            <option value="expired" <?php echo $filter_status === 'expired' ? 'selected' : ''; ?>>Expired</option>
                        </select>
                    </div>

                    <div class="col-md-3">
                        <label class="form-label">User</label>
                        <input type="text" name="user" class="form-control form-control-sm" placeholder="Search user..." value="<?php echo htmlspecialchars($filter_user); ?>">
                    </div>

                    <div class="col-md-3">
                        <label class="form-label">Balance Range</label>
                        <div class="input-group input-group-sm">
                            <input type="number" name="balance_min" class="form-control" step="0.01" placeholder="Min" value="<?php echo $filter_balance_min > 0 ? $filter_balance_min : ''; ?>">
                            <span class="input-group-text">-</span>
                            <input type="number" name="balance_max" class="form-control" step="0.01" placeholder="Max" value="<?php echo $filter_balance_max > 0 ? $filter_balance_max : ''; ?>">
                        </div>
                    </div>

                    <div class="col-md-3">
                        <label class="form-label">&nbsp;</label>
                        <div class="btn-group d-block" role="group">
                            <button type="submit" class="btn btn-primary btn-sm">
                                <i class="fas fa-search me-1"></i>
                                Filter
                            </button>
                            <a href="virtual-cards.php" class="btn btn-outline-secondary btn-sm">
                                <i class="fas fa-times me-1"></i>
                                Clear
                            </a>
                        </div>
                    </div>

                    <div class="col-12 mt-3">
                        <div class="btn-group" role="group">
                            <a href="generate-card.php" class="btn btn-success btn-sm">
                                <i class="fas fa-plus me-1"></i>
                                Generate New Card
                            </a>
                            <a href="card-operations.php" class="btn btn-info btn-sm">
                                <i class="fas fa-exchange-alt me-1"></i>
                                Card Operations
                            </a>
                            <a href="card-applications.php" class="btn btn-warning btn-sm">
                                <i class="fas fa-file-alt me-1"></i>
                                Applications
                            </a>
                            <a href="card-transactions.php" class="btn btn-outline-primary btn-sm">
                                <i class="fas fa-list me-1"></i>
                                Card Transactions
                            </a>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- Virtual Cards Table -->
<div class="row row-cards">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h3 class="card-title">
                    <i class="fas fa-credit-card me-2"></i>
                    Virtual Cards
                    <span class="badge bg-secondary ms-2"><?php echo number_format($total_records); ?> total</span>
                </h3>
                <div class="card-actions">
                    <span class="text-muted">
                        Showing <?php echo number_format(($current_page - 1) * $records_per_page + 1); ?> -
                        <?php echo number_format(min($current_page * $records_per_page, $total_records)); ?>
                        of <?php echo number_format($total_records); ?> cards
                    </span>
                </div>
            </div>
            <div class="card-body p-0">
                <?php if (!empty($cards)): ?>
                <div class="table-responsive">
                    <table class="table table-vcenter card-table table-sm">
                        <thead>
                            <tr>
                                <th class="w-1">#</th>
                                <th>Card Details</th>
                                <th>Card Holder</th>
                                <th>Balance</th>
                                <th>Status</th>
                                <th>Expiry</th>
                                <th>Created</th>
                                <th class="w-1">Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php
                            $row_number = ($current_page - 1) * $records_per_page + 1;
                            foreach ($cards as $card):
                            ?>
                            <tr>
                                <!-- Row Number -->
                                <td>
                                    <span class="text-muted"><?php echo $row_number++; ?></span>
                                </td>

                                <!-- Card Details -->
                                <td>
                                    <div class="d-flex flex-column">
                                        <div class="fw-bold">ID: <?php echo $card['card_id']; ?></div>
                                        <small class="text-muted font-monospace">**** **** **** <?php echo substr($card['card_number'], -4); ?></small>
                                        <small class="text-muted"><?php echo ucfirst($card['card_type']); ?> Card</small>
                                    </div>
                                </td>

                                <!-- Card Holder -->
                                <td>
                                    <div class="d-flex align-items-center">
                                        <div class="avatar avatar-xs me-2">
                                            <?php echo strtoupper(substr($card['first_name'] ?? 'U', 0, 1) . substr($card['last_name'] ?? 'U', 0, 1)); ?>
                                        </div>
                                        <div>
                                            <div class="fw-bold" style="font-size: 0.85rem;"><?php echo htmlspecialchars($card['card_holder_name']); ?></div>
                                            <small class="text-muted">@<?php echo htmlspecialchars($card['username'] ?? 'unknown'); ?></small>
                                        </div>
                                    </div>
                                </td>

                                <!-- Balance -->
                                <td>
                                    <span class="fw-bold text-primary">
                                        <?php echo formatCurrency($card['card_balance'], $card['currency']); ?>
                                    </span>
                                    <br>
                                    <small class="text-muted">Limit: <?php echo formatCurrency($card['daily_limit']); ?></small>
                                </td>

                                <!-- Status -->
                                <td>
                                    <?php
                                    $status_colors = [
                                        'pending' => 'warning',
                                        'active' => 'success',
                                        'inactive' => 'secondary',
                                        'blocked' => 'danger',
                                        'expired' => 'dark'
                                    ];
                                    $status_color = $status_colors[$card['status']] ?? 'secondary';
                                    ?>
                                    <span class="badge bg-<?php echo $status_color; ?> badge-sm">
                                        <?php echo ucfirst($card['status']); ?>
                                    </span>
                                </td>

                                <!-- Expiry -->
                                <td>
                                    <div class="d-flex flex-column">
                                        <div class="text-muted" style="font-size: 0.85rem;"><?php echo date('m/y', strtotime($card['expiry_date'])); ?></div>
                                        <?php
                                        $days_to_expiry = (strtotime($card['expiry_date']) - time()) / (60 * 60 * 24);
                                        if ($days_to_expiry < 30 && $days_to_expiry > 0):
                                        ?>
                                        <small class="text-warning">Expires soon</small>
                                        <?php elseif ($days_to_expiry <= 0): ?>
                                        <small class="text-danger">Expired</small>
                                        <?php endif; ?>
                                    </div>
                                </td>

                                <!-- Created -->
                                <td>
                                    <div class="d-flex flex-column">
                                        <div class="text-muted" style="font-size: 0.85rem;"><?php echo date('M j, Y', strtotime($card['created_at'])); ?></div>
                                        <small class="text-muted"><?php echo date('g:i A', strtotime($card['created_at'])); ?></small>
                                    </div>
                                </td>

                                <!-- Actions -->
                                <td>
                                    <div class="btn-list">
                                        <button type="button" class="btn btn-sm btn-outline-primary" onclick="viewCard(<?php echo htmlspecialchars(json_encode($card)); ?>)" title="View Details">
                                            <i class="fas fa-eye"></i>
                                        </button>
                                        <?php if ($card['status'] === 'pending'): ?>
                                        <button type="button" class="btn btn-sm btn-outline-success" onclick="updateCardStatus(<?php echo $card['card_id']; ?>, 'active')" title="Activate">
                                            <i class="fas fa-check"></i>
                                        </button>
                                        <?php elseif ($card['status'] === 'active'): ?>
                                        <button type="button" class="btn btn-sm btn-outline-warning" onclick="updateCardStatus(<?php echo $card['card_id']; ?>, 'blocked')" title="Block">
                                            <i class="fas fa-ban"></i>
                                        </button>
                                        <?php endif; ?>
                                    </div>
                                </td>
                            </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
                <?php else: ?>
                <div class="empty">
                    <div class="empty-img">
                        <i class="fas fa-credit-card" style="font-size: 3rem; color: #6c757d;"></i>
                    </div>
                    <p class="empty-title">No virtual cards found</p>
                    <p class="empty-subtitle text-muted">
                        <?php if (!empty(array_filter([$filter_status, $filter_user]))): ?>
                        Try adjusting your filters to see more results.
                        <?php else: ?>
                        No virtual cards have been created yet.
                        <?php endif; ?>
                    </p>
                    <div class="empty-action">
                        <a href="generate-card.php" class="btn btn-primary">
                            <i class="fas fa-plus me-2"></i>
                            Generate First Card
                        </a>
                    </div>
                </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<!-- Compact Pagination -->
<?php if ($total_pages > 1): ?>
<div class="row mt-3">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center">
            <small class="text-muted">
                Page <?php echo $current_page; ?> of <?php echo $total_pages; ?>
                (<?php echo number_format($total_records); ?> total)
            </small>
            <nav aria-label="Card pagination">
                <ul class="pagination pagination-sm mb-0">
                    <?php if ($current_page > 1): ?>
                    <li class="page-item">
                        <a class="page-link" href="?<?php echo http_build_query(array_merge($_GET, ['page' => 1])); ?>">
                            <i class="fas fa-angle-double-left"></i>
                        </a>
                    </li>
                    <li class="page-item">
                        <a class="page-link" href="?<?php echo http_build_query(array_merge($_GET, ['page' => $current_page - 1])); ?>">
                            <i class="fas fa-angle-left"></i>
                        </a>
                    </li>
                    <?php endif; ?>

                    <?php
                    $start_page = max(1, $current_page - 1);
                    $end_page = min($total_pages, $current_page + 1);

                    for ($i = $start_page; $i <= $end_page; $i++):
                    ?>
                    <li class="page-item <?php echo $i === $current_page ? 'active' : ''; ?>">
                        <a class="page-link" href="?<?php echo http_build_query(array_merge($_GET, ['page' => $i])); ?>">
                            <?php echo $i; ?>
                        </a>
                    </li>
                    <?php endfor; ?>

                    <?php if ($current_page < $total_pages): ?>
                    <li class="page-item">
                        <a class="page-link" href="?<?php echo http_build_query(array_merge($_GET, ['page' => $current_page + 1])); ?>">
                            <i class="fas fa-angle-right"></i>
                        </a>
                    </li>
                    <li class="page-item">
                        <a class="page-link" href="?<?php echo http_build_query(array_merge($_GET, ['page' => $total_pages])); ?>">
                            <i class="fas fa-angle-double-right"></i>
                        </a>
                    </li>
                    <?php endif; ?>
                </ul>
            </nav>
        </div>
    </div>
</div>
<?php endif; ?>

<!-- Card Details Modal -->
<div class="modal modal-blur fade" id="cardModal" tabindex="-1" role="dialog" aria-hidden="true">
    <div class="modal-dialog modal-lg modal-dialog-centered" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Virtual Card Details</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body" id="cardDetails">
                <!-- Card details will be loaded here -->
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                <button type="button" class="btn btn-primary" onclick="printCard()">
                    <i class="fas fa-print me-2"></i>
                    Print Details
                </button>
            </div>
        </div>
    </div>
</div>

<script>
// Card management functions
function viewCard(cardData) {
    const card = JSON.parse(cardData);

    // Format the card details
    const detailsHtml = `
        <div class="row">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">Card Information</h3>
                    </div>
                    <div class="card-body">
                        <dl class="row">
                            <dt class="col-sm-4">Card ID:</dt>
                            <dd class="col-sm-8"><strong>#${card.card_id}</strong></dd>

                            <dt class="col-sm-4">Card Number:</dt>
                            <dd class="col-sm-8"><code class="font-monospace">${card.card_number}</code></dd>

                            <dt class="col-sm-4">Card Holder:</dt>
                            <dd class="col-sm-8"><strong>${card.card_holder_name}</strong></dd>

                            <dt class="col-sm-4">CVV:</dt>
                            <dd class="col-sm-8"><code>${card.cvv}</code></dd>

                            <dt class="col-sm-4">Expiry Date:</dt>
                            <dd class="col-sm-8">
                                <strong>${formatCardDate(card.expiry_date)}</strong>
                                ${getExpiryStatus(card.expiry_date)}
                            </dd>

                            <dt class="col-sm-4">Card Type:</dt>
                            <dd class="col-sm-8">
                                <span class="badge bg-primary">${card.card_type.charAt(0).toUpperCase() + card.card_type.slice(1)}</span>
                            </dd>

                            <dt class="col-sm-4">Status:</dt>
                            <dd class="col-sm-8">
                                <span class="badge bg-${getCardStatusColor(card.status)}">
                                    ${card.status.charAt(0).toUpperCase() + card.status.slice(1)}
                                </span>
                            </dd>
                        </dl>
                    </div>
                </div>
            </div>

            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">Balance & Limits</h3>
                    </div>
                    <div class="card-body">
                        <dl class="row">
                            <dt class="col-sm-4">Current Balance:</dt>
                            <dd class="col-sm-8">
                                <span class="fw-bold text-primary fs-4">
                                    ${formatCurrency(card.card_balance, card.currency)}
                                </span>
                            </dd>

                            <dt class="col-sm-4">Currency:</dt>
                            <dd class="col-sm-8">${card.currency}</dd>

                            <dt class="col-sm-4">Daily Limit:</dt>
                            <dd class="col-sm-8">${formatCurrency(card.daily_limit, card.currency)}</dd>

                            <dt class="col-sm-4">Monthly Limit:</dt>
                            <dd class="col-sm-8">${formatCurrency(card.monthly_limit, card.currency)}</dd>
                        </dl>
                    </div>
                </div>

                <div class="card mt-3">
                    <div class="card-header">
                        <h3 class="card-title">Account Details</h3>
                    </div>
                    <div class="card-body">
                        <dl class="row">
                            <dt class="col-sm-4">Account Holder:</dt>
                            <dd class="col-sm-8">
                                <div class="d-flex align-items-center">
                                    <div class="avatar avatar-sm me-2">
                                        ${(card.first_name || 'U').charAt(0).toUpperCase()}${(card.last_name || 'U').charAt(0).toUpperCase()}
                                    </div>
                                    <div>
                                        <div class="fw-bold">${card.first_name || 'Unknown'} ${card.last_name || 'User'}</div>
                                        <small class="text-muted">@${card.username || 'unknown'}</small>
                                    </div>
                                </div>
                            </dd>

                            <dt class="col-sm-4">Account Number:</dt>
                            <dd class="col-sm-8"><code>${card.account_number || 'N/A'}</code></dd>

                            <dt class="col-sm-4">Approved By:</dt>
                            <dd class="col-sm-8">
                                ${card.admin_first_name ?
                                    `${card.admin_first_name} ${card.admin_last_name}` :
                                    '<span class="text-muted">System</span>'}
                            </dd>

                            <dt class="col-sm-4">Created:</dt>
                            <dd class="col-sm-8">
                                <div>${formatDate(card.created_at)}</div>
                                <small class="text-muted">${formatTime(card.created_at)}</small>
                            </dd>

                            <dt class="col-sm-4">Last Updated:</dt>
                            <dd class="col-sm-8">
                                <div>${formatDate(card.updated_at)}</div>
                                <small class="text-muted">${formatTime(card.updated_at)}</small>
                                ${card.updated_at !== card.created_at ?
                                    '<small class="badge bg-warning ms-2">Modified</small>' : ''}
                            </dd>
                        </dl>
                    </div>
                </div>
            </div>
        </div>
    `;

    // Load details into modal and show it
    document.getElementById('cardDetails').innerHTML = detailsHtml;
    const modal = new bootstrap.Modal(document.getElementById('cardModal'));
    modal.show();
}

// Helper functions for card modal
function getCardStatusColor(status) {
    const colors = {
        'pending': 'warning',
        'active': 'success',
        'inactive': 'secondary',
        'blocked': 'danger',
        'expired': 'dark'
    };
    return colors[status] || 'secondary';
}

function formatCardDate(dateString) {
    return new Date(dateString).toLocaleDateString('en-US', {
        year: '2-digit',
        month: '2-digit'
    });
}

function getExpiryStatus(expiryDate) {
    const daysToExpiry = (new Date(expiryDate) - new Date()) / (1000 * 60 * 60 * 24);
    if (daysToExpiry < 0) {
        return '<small class="badge bg-danger ms-2">Expired</small>';
    } else if (daysToExpiry < 30) {
        return '<small class="badge bg-warning ms-2">Expires Soon</small>';
    }
    return '';
}

function formatCurrency(amount, currency = 'USD') {
    return new Intl.NumberFormat('en-US', {
        style: 'currency',
        currency: currency
    }).format(amount);
}

function formatDate(dateString) {
    return new Date(dateString).toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'short',
        day: 'numeric'
    });
}

function formatTime(dateString) {
    return new Date(dateString).toLocaleTimeString('en-US', {
        hour: 'numeric',
        minute: '2-digit',
        hour12: true
    });
}

function printCard() {
    window.print();
}

function updateCardStatus(cardId, newStatus) {
    if (!confirm('Are you sure you want to change the card status to "' + newStatus + '"?')) {
        return;
    }

    fetch('update-card-status.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            card_id: cardId,
            status: newStatus
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showNotification('Card status updated successfully', 'success');
            setTimeout(() => {
                window.location.reload();
            }, 1000);
        } else {
            showNotification('Failed to update card status: ' + data.message, 'error');
        }
    })
    .catch(error => {
        console.error('Error updating card status:', error);
        showNotification('Error updating card status', 'error');
    });
}

// Notification system
function showNotification(message, type) {
    const alertClass = type === 'success' ? 'alert-success' :
                     type === 'error' ? 'alert-danger' :
                     type === 'info' ? 'alert-info' : 'alert-warning';

    const notification = document.createElement('div');
    notification.className = `alert ${alertClass} alert-dismissible fade show position-fixed`;
    notification.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
    notification.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    document.body.appendChild(notification);

    // Auto remove after 5 seconds
    setTimeout(() => {
        if (notification.parentNode) {
            notification.parentNode.removeChild(notification);
        }
    }, 5000);
}
</script>

<?php include 'includes/admin-footer.php'; ?>
