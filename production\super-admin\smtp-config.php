<?php
/**
 * Super Admin SMTP Configuration
 * Manage email server settings
 */

$page_title = 'SMTP Configuration';
$page_subtitle = 'Configure email server settings';

// Include header
include 'includes/header.php';

// Handle form submission
if ($_POST && isset($_POST['csrf_token']) && verifyCSRFToken($_POST['csrf_token'])) {
    try {
        require_once '../config/database.php';
        $db = getDB();
        
        // Create settings table if it doesn't exist
        $create_table_sql = "CREATE TABLE IF NOT EXISTS `super_admin_settings` (
            `id` int(11) NOT NULL AUTO_INCREMENT,
            `setting_key` varchar(100) NOT NULL UNIQUE,
            `setting_value` text NOT NULL,
            `setting_type` varchar(50) DEFAULT 'string',
            `description` text DEFAULT NULL,
            `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            PRIMARY KEY (`id`),
            UNIQUE KEY `setting_key` (`setting_key`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
        
        $db->query($create_table_sql);
        
        // Update SMTP settings
        $smtp_settings = [
            'smtp_host' => $_POST['smtp_host'],
            'smtp_port' => $_POST['smtp_port'],
            'smtp_username' => $_POST['smtp_username'],
            'smtp_password' => $_POST['smtp_password'],
            'smtp_encryption' => $_POST['smtp_encryption'],
            'smtp_from_email' => $_POST['smtp_from_email'],
            'smtp_from_name' => $_POST['smtp_from_name']
        ];
        
        foreach ($smtp_settings as $key => $value) {
            $sql = "INSERT INTO super_admin_settings (setting_key, setting_value, setting_type, description) 
                    VALUES (?, ?, 'string', ?) 
                    ON DUPLICATE KEY UPDATE setting_value = VALUES(setting_value)";
            
            $description = ucfirst(str_replace('_', ' ', $key));
            $db->query($sql, [$key, $value, $description]);
        }
        
        // Log the action
        logSuperAdminAction('smtp_config_update', 'SMTP configuration updated', $smtp_settings);
        
        $success_message = "SMTP configuration updated successfully!";
        
    } catch (Exception $e) {
        $error_message = "Failed to update SMTP configuration: " . $e->getMessage();
        error_log($error_message);
    }
}

// Get current SMTP settings
try {
    require_once '../config/database.php';
    $db = getDB();
    
    $smtp_settings = [];
    $result = $db->query("SELECT setting_key, setting_value FROM super_admin_settings WHERE setting_key LIKE 'smtp_%'");
    
    if ($result) {
        while ($row = $result->fetch_assoc()) {
            $smtp_settings[$row['setting_key']] = $row['setting_value'];
        }
    }
} catch (Exception $e) {
    error_log("Failed to load SMTP settings: " . $e->getMessage());
    $smtp_settings = [];
}

// Default values
$defaults = [
    'smtp_host' => 'smtp.hostinger.com',
    'smtp_port' => '465',
    'smtp_username' => '',
    'smtp_password' => '',
    'smtp_encryption' => 'ssl',
    'smtp_from_email' => '',
    'smtp_from_name' => 'Online Banking System'
];

foreach ($defaults as $key => $default_value) {
    if (!isset($smtp_settings[$key])) {
        $smtp_settings[$key] = $default_value;
    }
}
?>

<!-- Success/Error Messages -->
<?php if (isset($success_message)): ?>
    <div class="alert alert-success alert-dismissible fade show" role="alert">
        <i class="fas fa-check-circle"></i> <?php echo htmlspecialchars($success_message); ?>
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
<?php endif; ?>

<?php if (isset($error_message)): ?>
    <div class="alert alert-danger alert-dismissible fade show" role="alert">
        <i class="fas fa-exclamation-triangle"></i> <?php echo htmlspecialchars($error_message); ?>
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
<?php endif; ?>

<!-- SMTP Configuration Form -->
<div class="row">
    <div class="col-lg-8">
        <div class="card border-0 shadow-sm">
            <div class="card-header bg-white border-0 py-3">
                <h5 class="card-title mb-0">
                    <i class="fas fa-server text-warning"></i> SMTP Server Configuration
                </h5>
            </div>
            <div class="card-body">
                <form method="POST" action="">
                    <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
                    
                    <div class="row">
                        <div class="col-md-8 mb-3">
                            <label for="smtp_host" class="form-label">SMTP Host</label>
                            <input type="text" class="form-control" id="smtp_host" name="smtp_host" 
                                   value="<?php echo htmlspecialchars($smtp_settings['smtp_host']); ?>" required>
                            <div class="form-text">Example: smtp.hostinger.com, smtp.gmail.com</div>
                        </div>
                        <div class="col-md-4 mb-3">
                            <label for="smtp_port" class="form-label">Port</label>
                            <input type="number" class="form-control" id="smtp_port" name="smtp_port" 
                                   value="<?php echo htmlspecialchars($smtp_settings['smtp_port']); ?>" required>
                            <div class="form-text">Usually 465 (SSL) or 587 (TLS)</div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="smtp_username" class="form-label">Username</label>
                            <input type="text" class="form-control" id="smtp_username" name="smtp_username" 
                                   value="<?php echo htmlspecialchars($smtp_settings['smtp_username']); ?>" required>
                            <div class="form-text">Your email address</div>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="smtp_password" class="form-label">Password</label>
                            <input type="password" class="form-control" id="smtp_password" name="smtp_password" 
                                   value="<?php echo htmlspecialchars($smtp_settings['smtp_password']); ?>" required>
                            <div class="form-text">Your email password or app password</div>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="smtp_encryption" class="form-label">Encryption</label>
                        <select class="form-select" id="smtp_encryption" name="smtp_encryption" required>
                            <option value="ssl" <?php echo $smtp_settings['smtp_encryption'] === 'ssl' ? 'selected' : ''; ?>>SSL</option>
                            <option value="tls" <?php echo $smtp_settings['smtp_encryption'] === 'tls' ? 'selected' : ''; ?>>TLS</option>
                            <option value="none" <?php echo $smtp_settings['smtp_encryption'] === 'none' ? 'selected' : ''; ?>>None</option>
                        </select>
                    </div>
                    
                    <hr>
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="smtp_from_email" class="form-label">From Email</label>
                            <input type="email" class="form-control" id="smtp_from_email" name="smtp_from_email" 
                                   value="<?php echo htmlspecialchars($smtp_settings['smtp_from_email']); ?>" required>
                            <div class="form-text">Email address that appears as sender</div>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="smtp_from_name" class="form-label">From Name</label>
                            <input type="text" class="form-control" id="smtp_from_name" name="smtp_from_name" 
                                   value="<?php echo htmlspecialchars($smtp_settings['smtp_from_name']); ?>" required>
                            <div class="form-text">Name that appears as sender</div>
                        </div>
                    </div>
                    
                    <div class="d-flex gap-2">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save"></i> Save Configuration
                        </button>
                        <button type="button" class="btn btn-outline-secondary" onclick="testSMTPConnection()">
                            <i class="fas fa-paper-plane"></i> Test Connection
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
    
    <div class="col-lg-4">
        <div class="card border-0 shadow-sm">
            <div class="card-header bg-white border-0 py-3">
                <h5 class="card-title mb-0">
                    <i class="fas fa-info-circle text-info"></i> Configuration Help
                </h5>
            </div>
            <div class="card-body">
                <h6>Common SMTP Settings:</h6>
                
                <div class="mb-3">
                    <strong>Hostinger:</strong><br>
                    <small class="text-muted">
                        Host: smtp.hostinger.com<br>
                        Port: 465 (SSL)<br>
                        Encryption: SSL
                    </small>
                </div>
                
                <div class="mb-3">
                    <strong>Gmail:</strong><br>
                    <small class="text-muted">
                        Host: smtp.gmail.com<br>
                        Port: 587 (TLS)<br>
                        Encryption: TLS<br>
                        Note: Use App Password
                    </small>
                </div>
                
                <div class="mb-3">
                    <strong>Outlook:</strong><br>
                    <small class="text-muted">
                        Host: smtp-mail.outlook.com<br>
                        Port: 587 (TLS)<br>
                        Encryption: TLS
                    </small>
                </div>
                
                <hr>
                
                <div class="alert alert-warning">
                    <i class="fas fa-exclamation-triangle"></i>
                    <strong>Security Note:</strong><br>
                    <small>Always use encrypted connections (SSL/TLS) for production environments.</small>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function testSMTPConnection() {
    // Get form data
    const formData = new FormData();
    formData.append('test_smtp', '1');
    formData.append('smtp_host', document.getElementById('smtp_host').value);
    formData.append('smtp_port', document.getElementById('smtp_port').value);
    formData.append('smtp_username', document.getElementById('smtp_username').value);
    formData.append('smtp_password', document.getElementById('smtp_password').value);
    formData.append('smtp_encryption', document.getElementById('smtp_encryption').value);
    formData.append('smtp_from_email', document.getElementById('smtp_from_email').value);
    formData.append('smtp_from_name', document.getElementById('smtp_from_name').value);
    
    // Show loading state
    const button = event.target;
    const originalText = button.innerHTML;
    button.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Testing...';
    button.disabled = true;
    
    // Test connection
    fetch('test-smtp.php', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            alert('SMTP connection test successful!');
        } else {
            alert('SMTP connection test failed: ' + data.message);
        }
    })
    .catch(error => {
        alert('Error testing SMTP connection: ' + error.message);
    })
    .finally(() => {
        button.innerHTML = originalText;
        button.disabled = false;
    });
}
</script>

<?php
// Include footer
include 'includes/footer.php';
?>
