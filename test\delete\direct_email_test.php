<?php
/**
 * Direct Email <NAME_EMAIL>
 * Simple, focused test to verify email delivery
 */

// Target email
$to = '<EMAIL>';
$subject = '🏦 BANKING SYSTEM EMAIL TEST - ' . date('Y-m-d H:i:s');

// Create a comprehensive test email
$message = '
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>Banking System Email Test</title>
</head>
<body style="font-family: Arial, sans-serif; margin: 0; padding: 0; background-color: #f0f2f5;">
    <div style="max-width: 600px; margin: 20px auto; background-color: white; border-radius: 12px; overflow: hidden; box-shadow: 0 4px 12px rgba(0,0,0,0.15);">
        
        <!-- Header -->
        <div style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 30px; text-align: center;">
            <h1 style="margin: 0; font-size: 2.2em;">🏦 Online Banking System</h1>
            <h2 style="margin: 10px 0 0 0; font-weight: normal; opacity: 0.9;">Email Functionality Test</h2>
        </div>
        
        <!-- Main Content -->
        <div style="padding: 30px;">
            
            <!-- Success Alert -->
            <div style="background: linear-gradient(135deg, #28a745 0%, #20c997 100%); color: white; padding: 20px; border-radius: 8px; text-align: center; margin-bottom: 25px;">
                <h2 style="margin: 0 0 10px 0;">🎉 EMAIL SYSTEM WORKING!</h2>
                <p style="margin: 0; font-size: 1.1em;">If you receive this email, the banking system email functionality is confirmed working!</p>
            </div>
            
            <!-- Test Information -->
            <h3 style="color: #2c3e50; margin: 0 0 15px 0; border-bottom: 2px solid #3498db; padding-bottom: 8px;">📋 Test Details</h3>
            <div style="background: #f8f9fa; padding: 20px; border-radius: 8px; border-left: 4px solid #3498db;">
                <table style="width: 100%; border-collapse: collapse;">
                    <tr>
                        <td style="padding: 8px 0; color: #7f8c8d; width: 30%; font-weight: bold;">Recipient:</td>
                        <td style="padding: 8px 0; color: #2c3e50;">' . $to . '</td>
                    </tr>
                    <tr>
                        <td style="padding: 8px 0; color: #7f8c8d; font-weight: bold;">Test Date:</td>
                        <td style="padding: 8px 0; color: #2c3e50;">' . date('Y-m-d H:i:s') . '</td>
                    </tr>
                    <tr>
                        <td style="padding: 8px 0; color: #7f8c8d; font-weight: bold;">Server:</td>
                        <td style="padding: 8px 0; color: #2c3e50;">localhost (MAMP)</td>
                    </tr>
                    <tr>
                        <td style="padding: 8px 0; color: #7f8c8d; font-weight: bold;">Email Method:</td>
                        <td style="padding: 8px 0; color: #2c3e50;">PHP mail() function</td>
                    </tr>
                    <tr>
                        <td style="padding: 8px 0; color: #7f8c8d; font-weight: bold;">From Address:</td>
                        <td style="padding: 8px 0; color: #2c3e50;"><EMAIL></td>
                    </tr>
                </table>
            </div>
            
            <!-- What This Confirms -->
            <h3 style="color: #2c3e50; margin: 25px 0 15px 0; border-bottom: 2px solid #e74c3c; padding-bottom: 8px;">✅ What This Confirms</h3>
            <div style="background: #d4edda; padding: 20px; border-radius: 8px; border-left: 4px solid #28a745;">
                <ul style="margin: 0; padding-left: 20px; color: #155724; line-height: 1.8;">
                    <li><strong>Email Delivery Works:</strong> Server can successfully send emails</li>
                    <li><strong>SMTP Configuration:</strong> Email settings are properly configured</li>
                    <li><strong>HTML Rendering:</strong> Rich email templates display correctly</li>
                    <li><strong>Banking System Ready:</strong> OTP and notification emails will work</li>
                    <li><strong>User Registration:</strong> Welcome emails can be sent to new users</li>
                    <li><strong>Security Features:</strong> Login verification codes can be delivered</li>
                </ul>
            </div>
            
            <!-- Banking Features -->
            <h3 style="color: #2c3e50; margin: 25px 0 15px 0; border-bottom: 2px solid #f39c12; padding-bottom: 8px;">🏦 Banking Email Features</h3>
            <div style="background: #fff3cd; padding: 20px; border-radius: 8px; border-left: 4px solid #ffc107;">
                <p style="color: #856404; margin: 0 0 15px 0; font-weight: bold;">The following email features are now confirmed working:</p>
                <ul style="margin: 0; padding-left: 20px; color: #856404; line-height: 1.6;">
                    <li>🔐 <strong>OTP Verification Emails</strong> - For secure login</li>
                    <li>👋 <strong>Welcome Emails</strong> - For new account creation</li>
                    <li>📧 <strong>Account Notifications</strong> - For status changes</li>
                    <li>💰 <strong>Transaction Alerts</strong> - For money transfers</li>
                    <li>🎫 <strong>Support Tickets</strong> - For customer service</li>
                    <li>⚠️ <strong>Security Alerts</strong> - For suspicious activity</li>
                </ul>
            </div>
            
            <!-- Next Steps -->
            <h3 style="color: #2c3e50; margin: 25px 0 15px 0; border-bottom: 2px solid #9b59b6; padding-bottom: 8px;">🎯 Next Steps</h3>
            <div style="background: #e8e2ff; padding: 20px; border-radius: 8px; border-left: 4px solid #9b59b6;">
                <ol style="margin: 0; padding-left: 20px; color: #6c5ce7; line-height: 1.8;">
                    <li>Verify you received this email in your inbox</li>
                    <li>Check spam/junk folder if not in main inbox</li>
                    <li>Test user registration with email verification</li>
                    <li>Test login OTP email functionality</li>
                    <li>Configure any additional email templates needed</li>
                    <li>Set up email monitoring and logging</li>
                </ol>
            </div>
            
            <!-- Technical Info -->
            <div style="background: #f1f3f4; padding: 15px; border-radius: 8px; margin-top: 25px;">
                <h4 style="color: #5f6368; margin: 0 0 10px 0; font-size: 0.9em;">📊 Technical Information</h4>
                <p style="color: #5f6368; margin: 0; font-size: 0.85em; line-height: 1.5;">
                    <strong>PHP Version:</strong> ' . phpversion() . ' | 
                    <strong>Server:</strong> ' . ($_SERVER['SERVER_SOFTWARE'] ?? 'Unknown') . ' | 
                    <strong>Mail Function:</strong> ' . (function_exists('mail') ? 'Available' : 'Not Available') . ' | 
                    <strong>Test ID:</strong> ' . uniqid() . '
                </p>
            </div>
        </div>
        
        <!-- Footer -->
        <div style="background: #2c3e50; color: white; padding: 20px; text-align: center;">
            <p style="margin: 0; opacity: 0.8; font-size: 0.9em;">This email was sent automatically by the Online Banking System Email Test</p>
            <p style="margin: 5px 0 0 0; opacity: 0.6; font-size: 0.8em;">&copy; ' . date('Y') . ' Online Banking System. All rights reserved.</p>
        </div>
    </div>
</body>
</html>';

// Set proper headers for HTML email
$headers = array();
$headers[] = 'MIME-Version: 1.0';
$headers[] = 'Content-type: text/html; charset=UTF-8';
$headers[] = 'From: Online Banking System <<EMAIL>>';
$headers[] = 'Reply-To: <EMAIL>';
$headers[] = 'X-Mailer: PHP/' . phpversion();
$headers[] = 'X-Priority: 1';
$headers[] = 'X-MSMail-Priority: High';

// Send the email
$result = mail($to, $subject, $message, implode("\r\n", $headers));

// Display result page
echo "<!DOCTYPE html>";
echo "<html><head><title>Email Test Result</title>";
echo "<style>
body { font-family: Arial, sans-serif; margin: 0; padding: 20px; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); min-height: 100vh; }
.container { max-width: 600px; margin: 0 auto; background: white; border-radius: 15px; overflow: hidden; box-shadow: 0 8px 32px rgba(0,0,0,0.3); }
.header { background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%); color: white; padding: 30px; text-align: center; }
.content { padding: 30px; }
.success { background: linear-gradient(135deg, #00b894 0%, #00cec9 100%); color: white; padding: 20px; border-radius: 10px; text-align: center; margin: 20px 0; }
.error { background: linear-gradient(135deg, #e17055 0%, #d63031 100%); color: white; padding: 20px; border-radius: 10px; text-align: center; margin: 20px 0; }
.info { background: #e7f3ff; border: 1px solid #b3d9ff; color: #0c5460; padding: 20px; border-radius: 10px; margin: 20px 0; }
</style>";
echo "</head><body>";

echo "<div class='container'>";
echo "<div class='header'>";
echo "<h1>🏦 Email Test Result</h1>";
echo "<p>Direct <NAME_EMAIL></p>";
echo "</div>";

echo "<div class='content'>";

if ($result) {
    echo "<div class='success'>";
    echo "<h2>🎉 EMAIL SENT SUCCESSFULLY!</h2>";
    echo "<p>The test email has been sent to <strong>$to</strong></p>";
    echo "<p>Subject: <em>$subject</em></p>";
    echo "<p>Time: " . date('Y-m-d H:i:s') . "</p>";
    echo "</div>";
    
    echo "<div class='info'>";
    echo "<h3>📧 What to Check:</h3>";
    echo "<ol>";
    echo "<li>Check your inbox at <strong><EMAIL></strong></li>";
    echo "<li>Look for the email with subject: <em>BANKING SYSTEM EMAIL TEST</em></li>";
    echo "<li>Check spam/junk folder if not in main inbox</li>";
    echo "<li>Email may take a few minutes to arrive</li>";
    echo "<li>The email should display with full HTML formatting</li>";
    echo "</ol>";
    echo "</div>";
    
} else {
    echo "<div class='error'>";
    echo "<h2>❌ EMAIL SENDING FAILED</h2>";
    echo "<p>The test email could not be sent to <strong>$to</strong></p>";
    echo "<p>This may indicate server configuration issues.</p>";
    echo "</div>";
}

// Log the attempt
$log_entry = "=== DIRECT EMAIL TEST ===\n";
$log_entry .= "To: $to\n";
$log_entry .= "Subject: $subject\n";
$log_entry .= "Time: " . date('Y-m-d H:i:s') . "\n";
$log_entry .= "Result: " . ($result ? 'SUCCESS' : 'FAILED') . "\n";
$log_entry .= "PHP Version: " . phpversion() . "\n";
$log_entry .= "Mail Function: " . (function_exists('mail') ? 'Available' : 'Not Available') . "\n";
$log_entry .= "========================\n\n";

if (!is_dir('logs')) {
    mkdir('logs', 0755, true);
}
file_put_contents('logs/direct_email_test.log', $log_entry, FILE_APPEND | LOCK_EX);

echo "<div class='info'>";
echo "<h3>📄 Test Information:</h3>";
echo "<ul>";
echo "<li><strong>Target Email:</strong> $to</li>";
echo "<li><strong>Test Time:</strong> " . date('Y-m-d H:i:s') . "</li>";
echo "<li><strong>PHP Version:</strong> " . phpversion() . "</li>";
echo "<li><strong>Mail Function:</strong> " . (function_exists('mail') ? 'Available' : 'Not Available') . "</li>";
echo "<li><strong>Result:</strong> " . ($result ? 'SUCCESS' : 'FAILED') . "</li>";
echo "</ul>";
echo "</div>";

echo "</div>";
echo "</div>";
echo "</body></html>";
?>
