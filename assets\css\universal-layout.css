/**
 * Universal Layout CSS for User Dashboard
 * Ensures consistent layout across all user pages
 * Sidebar + Content layout without gaps
 */

/* Universal Layout Structure */
.main-content-wrapper {
    margin-left: 280px; /* Match sidebar width */
    min-height: 100vh;
    display: flex;
    flex-direction: column;
    background: var(--background-light);
    width: calc(100% - 280px);
}

.main-content {
    flex: 1;
    padding: 2rem;
}

/* Ensure header and footer span full width of content area */
.user-dashboard-header {
    width: 100%;
    margin-bottom: 0;
}

.user-dashboard-footer {
    width: 100%;
    margin-top: auto;
}

/* Remove any Bootstrap container margins/padding that might cause gaps */
.container-fluid {
    padding: 0;
    margin: 0;
}

/* Ensure body and html don't have margins */
body.user-dashboard {
    margin: 0;
    padding: 0;
    overflow-x: hidden;
}

.dashboard-wrapper {
    margin: 0;
    padding: 0;
    min-height: 100vh;
}

/* Responsive layout */
@media (max-width: 768px) {
    .main-content-wrapper {
        margin-left: 0;
        width: 100%;
    }
    
    .main-content {
        padding: 1rem;
    }
}

/* Ensure no gaps between components */
.main-content-wrapper > * {
    margin-left: 0;
    margin-right: 0;
}

/* Fix any potential Bootstrap column padding issues */
.col-md-9, .col-lg-10, .col-md-3, .col-lg-2 {
    padding-left: 0;
    padding-right: 0;
}
