<?php
/**
 * Account Validation API
 * Validates account numbers and checks for internal users
 */

// Disable HTML error display for API
ini_set('display_errors', 0);
error_reporting(E_ALL);

// Set JSON response header first
header('Content-Type: application/json');

// Start output buffering to catch any unexpected output
ob_start();

try {
    // Start session and check authentication
    session_start();
    if (!isset($_SESSION['user_id'])) {
        throw new Exception('Unauthorized access');
    }

    // Include database connection
    require_once '../../config/config.php';

    // Get database connection
    $db = getDB();
    $user_id = $_SESSION['user_id'];
    
    // Get account number from POST data
    $account_number = sanitizeInput($_POST['account_number'] ?? '');
    
    if (empty($account_number)) {
        throw new Exception('Account number is required');
    }
    
    // Validate account number format (allow 8-20 digits)
    if (!preg_match('/^[0-9]{8,20}$/', $account_number)) {
        echo json_encode([
            'success' => true,
            'exists' => false,
            'is_internal' => false,
            'message' => 'Account number must be 8-20 digits',
            'debug' => [
                'account_number' => $account_number,
                'length' => strlen($account_number),
                'is_numeric' => is_numeric($account_number)
            ]
        ]);
        exit();
    }
    
    // Check if account exists in our system
    $check_sql = "SELECT id, first_name, last_name, email, is_admin, status 
                  FROM accounts 
                  WHERE account_number = ? AND id != ?";
    $result = $db->query($check_sql, [$account_number, $user_id]);
    
    if ($result->num_rows > 0) {
        $account = $result->fetch_assoc();
        
        // Check if it's an internal user (not admin and active)
        $is_internal = ($account['is_admin'] == 0 && $account['status'] === 'active');
        
        if ($is_internal) {
            // Internal user found
            echo json_encode([
                'success' => true,
                'exists' => true,
                'is_internal' => true,
                'name' => trim($account['first_name'] . ' ' . $account['last_name']),
                'account_id' => $account['id'],
                'message' => 'Internal user found'
            ]);
        } else {
            // Account exists but not a valid internal user
            echo json_encode([
                'success' => true,
                'exists' => true,
                'is_internal' => false,
                'message' => 'Account exists but not available for inter-bank transfer'
            ]);
        }
    } else {
        // Account not found in our system - external account
        echo json_encode([
            'success' => true,
            'exists' => false,
            'is_internal' => false,
            'message' => 'Account format valid - External account',
            'debug' => [
                'account_number' => $account_number,
                'user_id' => $user_id,
                'query_executed' => true
            ]
        ]);
    }
    
} catch (Exception $e) {
    // Clear any output buffer
    ob_clean();

    error_log("Account validation error: " . $e->getMessage());

    http_response_code(500);
    echo json_encode([
        'success' => false,
        'error' => 'Validation failed',
        'message' => $e->getMessage()
    ]);
} finally {
    // End output buffering and clean any unexpected output
    if (ob_get_level()) {
        ob_end_clean();
    }
}
?>
