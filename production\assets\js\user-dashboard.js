// User Dashboard JavaScript

document.addEventListener('DOMContentLoaded', function() {
    // Initialize dashboard functionality
    initializeDashboard();
    
    // Initialize sidebar functionality
    initializeSidebar();
    
    // Initialize responsive features
    initializeResponsive();
    
    // Initialize smooth scrolling
    initializeSmoothScrolling();
});

// Dashboard initialization
function initializeDashboard() {
    console.log('User Dashboard initialized');
    
    // Add loading states to buttons
    const buttons = document.querySelectorAll('.btn');
    buttons.forEach(button => {
        button.addEventListener('click', function(e) {
            if (this.classList.contains('btn-loading')) {
                e.preventDefault();
                return;
            }
            
            // Add loading state for external links
            if (this.href && !this.href.includes('#')) {
                this.classList.add('btn-loading');
                const originalText = this.innerHTML;
                this.innerHTML = '<span class="loading"></span> Loading...';
                
                // Remove loading state after 3 seconds (fallback)
                setTimeout(() => {
                    this.classList.remove('btn-loading');
                    this.innerHTML = originalText;
                }, 3000);
            }
        });
    });
}

// Sidebar functionality
function initializeSidebar() {
    const sidebar = document.querySelector('.banking-sidebar');
    const navLinks = document.querySelectorAll('.nav-link');
    
    // Highlight active navigation item
    navLinks.forEach(link => {
        link.addEventListener('click', function() {
            // Remove active class from all links
            navLinks.forEach(l => l.classList.remove('active'));
            // Add active class to clicked link
            this.classList.add('active');
        });
    });
    
    // Smooth scrolling within sidebar
    if (sidebar) {
        sidebar.style.scrollBehavior = 'smooth';
    }
}

// Responsive functionality
function initializeResponsive() {
    const sidebar = document.querySelector('.banking-sidebar');
    const mainContent = document.querySelector('.main-content');
    
    // Create mobile menu toggle if needed
    if (window.innerWidth <= 1024) {
        createMobileMenuToggle();
    }
    
    // Handle window resize
    window.addEventListener('resize', function() {
        if (window.innerWidth <= 1024) {
            if (!document.querySelector('.mobile-menu-toggle')) {
                createMobileMenuToggle();
            }
        } else {
            const toggle = document.querySelector('.mobile-menu-toggle');
            if (toggle) {
                toggle.remove();
            }
            if (sidebar) {
                sidebar.classList.remove('open');
            }
        }
    });
}

// Create mobile menu toggle
function createMobileMenuToggle() {
    const existingToggle = document.querySelector('.mobile-menu-toggle');
    if (existingToggle) return;
    
    const toggle = document.createElement('button');
    toggle.className = 'mobile-menu-toggle';
    toggle.innerHTML = `
        <svg width="24" height="24" fill="currentColor" viewBox="0 0 20 20">
            <path fill-rule="evenodd" d="M3 5a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zM3 10a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zM3 15a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1z" clip-rule="evenodd"/>
        </svg>
    `;
    
    // Style the toggle button
    toggle.style.cssText = `
        position: fixed;
        top: 1rem;
        left: 1rem;
        z-index: 1001;
        background: white;
        border: 1px solid var(--border-color);
        border-radius: 8px;
        padding: 0.5rem;
        cursor: pointer;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        color: var(--text-primary);
    `;
    
    // Add click handler
    toggle.addEventListener('click', function() {
        const sidebar = document.querySelector('.banking-sidebar');
        if (sidebar) {
            sidebar.classList.toggle('open');
        }
    });
    
    // Add to body
    document.body.appendChild(toggle);
    
    // Close sidebar when clicking outside
    document.addEventListener('click', function(e) {
        const sidebar = document.querySelector('.banking-sidebar');
        const toggle = document.querySelector('.mobile-menu-toggle');
        
        if (sidebar && toggle && 
            !sidebar.contains(e.target) && 
            !toggle.contains(e.target) && 
            sidebar.classList.contains('open')) {
            sidebar.classList.remove('open');
        }
    });
}

// Smooth scrolling functionality
function initializeSmoothScrolling() {
    // Enable smooth scrolling for all internal links
    const internalLinks = document.querySelectorAll('a[href^="#"]');
    
    internalLinks.forEach(link => {
        link.addEventListener('click', function(e) {
            e.preventDefault();
            
            const targetId = this.getAttribute('href').substring(1);
            const targetElement = document.getElementById(targetId);
            
            if (targetElement) {
                targetElement.scrollIntoView({
                    behavior: 'smooth',
                    block: 'start'
                });
            }
        });
    });
}

// Utility functions
function showAlert(message, type = 'info') {
    const alertContainer = document.querySelector('.alert-container') || createAlertContainer();
    
    const alert = document.createElement('div');
    alert.className = `alert alert-${type}`;
    alert.textContent = message;
    
    alertContainer.appendChild(alert);
    
    // Auto remove after 5 seconds
    setTimeout(() => {
        alert.remove();
    }, 5000);
}

function createAlertContainer() {
    const container = document.createElement('div');
    container.className = 'alert-container';
    container.style.cssText = `
        position: fixed;
        top: 1rem;
        right: 1rem;
        z-index: 1002;
        max-width: 400px;
    `;
    
    document.body.appendChild(container);
    return container;
}

// Loading state management
function setLoading(element, loading = true) {
    if (loading) {
        element.classList.add('btn-loading');
        element.disabled = true;
        const originalText = element.innerHTML;
        element.dataset.originalText = originalText;
        element.innerHTML = '<span class="loading"></span> Loading...';
    } else {
        element.classList.remove('btn-loading');
        element.disabled = false;
        element.innerHTML = element.dataset.originalText || element.innerHTML;
    }
}

// Format currency
function formatCurrency(amount, currency = 'USD') {
    return new Intl.NumberFormat('en-US', {
        style: 'currency',
        currency: currency
    }).format(amount);
}

// Format date
function formatDate(date, options = {}) {
    const defaultOptions = {
        year: 'numeric',
        month: 'short',
        day: 'numeric'
    };
    
    return new Intl.DateTimeFormat('en-US', { ...defaultOptions, ...options }).format(new Date(date));
}

// Export functions for use in other scripts
window.DashboardUtils = {
    showAlert,
    setLoading,
    formatCurrency,
    formatDate
};
