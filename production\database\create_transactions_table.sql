-- Create transactions table for compatibility
-- This table will be used for general transaction queries
CREATE TABLE IF NOT EXISTS transactions (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    transaction_type ENUM('credit', 'debit') NOT NULL,
    amount DECIMAL(15,2) NOT NULL,
    currency VARCHAR(3) DEFAULT 'USD',
    description TEXT,
    reference_number VARCHAR(50),
    category ENUM('deposit', 'withdrawal', 'transfer', 'fee', 'interest', 'adjustment', 'virtual_card', 'crypto') DEFAULT 'adjustment',
    status ENUM('pending', 'completed', 'failed', 'cancelled') DEFAULT 'completed',
    processed_by INT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES accounts(id) ON DELETE CASCADE,
    FOREIGN KEY (processed_by) REFERENCES accounts(id) ON DELETE SET NULL,
    INDEX idx_user_id (user_id),
    INDEX idx_status (status),
    INDEX idx_created_at (created_at),
    INDEX idx_transaction_type (transaction_type)
);

-- Insert sample data from account_transactions if it exists
INSERT IGNORE INTO transactions (user_id, transaction_type, amount, currency, description, reference_number, category, status, processed_by, created_at, updated_at)
SELECT account_id as user_id, transaction_type, amount, currency, description, reference_number, category, status, processed_by, created_at, updated_at
FROM account_transactions
WHERE account_id IS NOT NULL;
