<?php
require_once '../config/config.php';
requireAdmin();

$page_title = 'Google 2FA Setup';

// Include Google2FA class
require_once '../vendor/GoogleAuthenticator/Google2FA.php';
$google2fa = new Google2FA();

// Initialize database connection
$db = getDB();

$success = '';
$error = '';
$qr_code_url = '';
$secret = '';
$selected_user = null;

// Handle form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    try {
        $db = getDB();
        
        if (isset($_POST['action'])) {
            switch ($_POST['action']) {
                case 'generate_qr':
                    $user_id = intval($_POST['user_id']);
                    if ($user_id > 0) {
                        // Get user details
                        $user_query = "SELECT * FROM accounts WHERE id = ? AND is_admin = 0";
                        $user_result = $db->query($user_query, [$user_id]);
                        
                        if ($user_result && $user_result->num_rows === 1) {
                            $selected_user = $user_result->fetch_assoc();
                            
                            // Generate new secret
                            $secret = $google2fa->generateSecretKey();
                            
                            // Generate QR code URL
                            $company = 'SecureBank Online';
                            $holder = $selected_user['email'];
                            $qr_code_url = $google2fa->getQRCodeUrl($company, $holder, $secret);
                            
                            // Store secret temporarily in session for verification
                            $_SESSION['temp_2fa_secret'] = $secret;
                            $_SESSION['temp_2fa_user_id'] = $user_id;
                        } else {
                            $error = 'User not found.';
                        }
                    }
                    break;
                    
                case 'verify_setup':
                    $verification_code = $_POST['verification_code'];
                    $user_id = $_SESSION['temp_2fa_user_id'] ?? 0;
                    $secret = $_SESSION['temp_2fa_secret'] ?? '';
                    
                    if ($user_id && $secret && $verification_code) {
                        // Verify the code
                        if ($google2fa->verifyKey($secret, $verification_code)) {
                            // Save the secret to database
                            $update_sql = "UPDATE user_security_settings SET 
                                          google_2fa_enabled = 1, 
                                          google_2fa_secret = ?, 
                                          updated_by = ?
                                          WHERE user_id = ?";
                            
                            $result = $db->query($update_sql, [$secret, $_SESSION['user_id'], $user_id]);
                            
                            if ($result) {
                                // Clear temporary session data
                                unset($_SESSION['temp_2fa_secret']);
                                unset($_SESSION['temp_2fa_user_id']);
                                
                                $success = 'Google 2FA has been successfully enabled for the user!';
                                $qr_code_url = '';
                                $secret = '';
                                $selected_user = null;
                            } else {
                                $error = 'Failed to save 2FA settings to database.';
                            }
                        } else {
                            $error = 'Invalid verification code. Please try again.';
                            // Keep the QR code visible for retry
                            $user_id = $_SESSION['temp_2fa_user_id'];
                            $secret = $_SESSION['temp_2fa_secret'];
                            
                            $user_query = "SELECT * FROM accounts WHERE id = ? AND is_admin = 0";
                            $user_result = $db->query($user_query, [$user_id]);
                            if ($user_result && $user_result->num_rows === 1) {
                                $selected_user = $user_result->fetch_assoc();
                                $company = 'SecureBank Online';
                                $holder = $selected_user['email'];
                                $qr_code_url = $google2fa->getQRCodeUrl($company, $holder, $secret);
                            }
                        }
                    } else {
                        $error = 'Missing verification data. Please start over.';
                    }
                    break;
            }
        }
    } catch (Exception $e) {
        error_log("Google 2FA Setup Error: " . $e->getMessage());
        $error = 'An error occurred while setting up 2FA.';
    }
}

// Get all users for selection
$users_query = "SELECT a.id, a.username, a.first_name, a.last_name, a.email,
                       uss.google_2fa_enabled
                FROM accounts a 
                LEFT JOIN user_security_settings uss ON a.id = uss.user_id 
                WHERE a.is_admin = 0 
                ORDER BY a.first_name, a.last_name";
$users_result = $db->query($users_query);
$users = [];
if ($users_result) {
    while ($row = $users_result->fetch_assoc()) {
        $users[] = $row;
    }
}

// Define page actions
$page_actions = [
    [
        'url' => 'configure-2fa.php',
        'label' => 'Back to 2FA Config',
        'icon' => 'fas fa-arrow-left'
    ]
];

include 'includes/admin-header.php';
?>

<!-- Breadcrumb -->
<nav aria-label="breadcrumb" style="margin-bottom: 1rem;">
    <ol class="breadcrumb">
        <li class="breadcrumb-item"><a href="index.php">Admin</a></li>
        <li class="breadcrumb-item"><a href="configure-2fa.php">2FA Configuration</a></li>
        <li class="breadcrumb-item active" aria-current="page">Google 2FA Setup</li>
    </ol>
</nav>

<!-- Error Messages -->
<?php if (!empty($error)): ?>
<div class="alert alert-danger" role="alert">
    <div class="d-flex">
        <div><i class="fas fa-exclamation-circle me-2"></i></div>
        <div>
            <h4 class="alert-title">Error!</h4>
            <div class="text-muted"><?php echo htmlspecialchars($error); ?></div>
        </div>
    </div>
</div>
<?php endif; ?>

<!-- Success Messages -->
<?php if (!empty($success)): ?>
<div class="alert alert-success" role="alert">
    <div class="d-flex">
        <div><i class="fas fa-check-circle me-2"></i></div>
        <div>
            <h4 class="alert-title">Success!</h4>
            <div class="text-muted"><?php echo htmlspecialchars($success); ?></div>
        </div>
    </div>
</div>
<?php endif; ?>

<div class="row row-cards">
    <!-- Setup Form -->
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h3 class="card-title">
                    <i class="fas fa-qrcode me-2"></i>
                    Google 2FA Setup
                </h3>
            </div>
            <div class="card-body">
                <?php if (!$qr_code_url): ?>
                    <!-- Step 1: Select User -->
                    <div class="mb-4">
                        <h4>Step 1: Select User</h4>
                        <p class="text-muted">Choose a user to set up Google Authenticator for.</p>
                    </div>

                    <form method="POST">
                        <input type="hidden" name="action" value="generate_qr">
                        <div class="mb-3">
                            <label class="form-label required">Select User</label>
                            <select name="user_id" class="form-select" required>
                                <option value="">Choose a user...</option>
                                <?php foreach ($users as $user): ?>
                                    <option value="<?php echo $user['id']; ?>" <?php echo $user['google_2fa_enabled'] ? 'disabled' : ''; ?>>
                                        <?php echo htmlspecialchars($user['first_name'] . ' ' . $user['last_name'] . ' (' . $user['username'] . ')'); ?>
                                        <?php if ($user['google_2fa_enabled']): ?>
                                            - 2FA Already Enabled
                                        <?php endif; ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                            <div class="form-hint">Select the user account to enable Google 2FA for</div>
                        </div>

                        <div class="form-footer">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-qrcode me-2"></i>
                                Generate QR Code
                            </button>
                            <a href="configure-2fa.php" class="btn btn-secondary ms-2">
                                <i class="fas fa-arrow-left me-2"></i>
                                Back to Configuration
                            </a>
                        </div>
                    </form>
                <?php else: ?>
                    <!-- Step 2: Show QR Code and Verify -->
                    <div class="mb-4">
                        <h4>Step 2: Scan QR Code</h4>
                        <p class="text-muted">
                            Setting up 2FA for: <strong><?php echo htmlspecialchars($selected_user['first_name'] . ' ' . $selected_user['last_name']); ?></strong>
                        </p>
                    </div>

                    <!-- QR Code Display -->
                    <div class="text-center mb-4">
                        <img src="<?php echo htmlspecialchars($qr_code_url); ?>" alt="QR Code" class="img-fluid border rounded" style="max-width: 200px;">
                    </div>

                    <!-- Secret Key -->
                    <div class="mb-3">
                        <label class="form-label">Secret Key (Manual Entry)</label>
                        <div class="input-group">
                            <input type="text" class="form-control font-monospace" value="<?php echo htmlspecialchars($secret); ?>" readonly>
                            <button class="btn btn-outline-secondary" type="button" onclick="copyToClipboard('<?php echo htmlspecialchars($secret); ?>')">
                                <i class="fas fa-copy"></i>
                            </button>
                        </div>
                        <div class="form-hint">Use this if you can't scan the QR code</div>
                    </div>

                    <!-- Verification Form -->
                    <form method="POST">
                        <input type="hidden" name="action" value="verify_setup">
                        <div class="mb-3">
                            <label class="form-label required">Verification Code</label>
                            <input type="text" name="verification_code" class="form-control text-center font-monospace" placeholder="000000" maxlength="6" required style="font-size: 1.2rem; letter-spacing: 0.2rem;">
                            <div class="form-hint">Enter the 6-digit code from Google Authenticator</div>
                        </div>

                        <div class="form-footer">
                            <button type="submit" class="btn btn-success">
                                <i class="fas fa-check me-2"></i>
                                Verify & Enable 2FA
                            </button>
                            <a href="google-2fa-setup.php" class="btn btn-secondary ms-2">
                                <i class="fas fa-times me-2"></i>
                                Cancel
                            </a>
                        </div>
                    </form>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <!-- Setup Guidelines -->
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h3 class="card-title">
                    <i class="fas fa-info-circle me-2"></i>
                    Setup Instructions
                </h3>
            </div>
            <div class="card-body">
                <!-- Setup Steps -->
                <div class="alert alert-info mb-3">
                    <h4 class="alert-title">Setup Steps</h4>
                    <ol class="mb-0" style="font-size: 0.9rem;">
                        <li>Install Google Authenticator app on your phone</li>
                        <li>Open the app and tap the "+" button</li>
                        <li>Select "Scan QR code" option</li>
                        <li>Scan the QR code shown on the left</li>
                        <li>Enter the 6-digit code from the app</li>
                        <li>Click "Verify & Enable 2FA" to complete</li>
                    </ol>
                </div>

                <!-- Supported Apps -->
                <div class="alert alert-success mb-3">
                    <h4 class="alert-title">Supported Apps</h4>
                    <div class="row text-center">
                        <div class="col-4">
                            <div class="text-success fw-bold">Google</div>
                            <small class="text-muted">Authenticator</small>
                        </div>
                        <div class="col-4">
                            <div class="text-success fw-bold">Microsoft</div>
                            <small class="text-muted">Authenticator</small>
                        </div>
                        <div class="col-4">
                            <div class="text-success fw-bold">Authy</div>
                            <small class="text-muted">2FA App</small>
                        </div>
                    </div>
                </div>

                <!-- Important Notes -->
                <div class="alert alert-warning mb-3">
                    <h4 class="alert-title">Important Notes</h4>
                    <div style="font-size: 0.9rem;">
                        <strong>Backup:</strong> Save the secret key in a secure location<br>
                        <strong>Device:</strong> Keep your phone secure and backed up<br>
                        <strong>Reset:</strong> Contact admin if you lose access to your device
                    </div>
                </div>

                <!-- Troubleshooting -->
                <div class="alert alert-secondary mb-0">
                    <h4 class="alert-title">Troubleshooting</h4>
                    <div style="font-size: 0.85rem;">
                        <strong>QR Code Issues:</strong> Use manual entry with secret key<br>
                        <strong>Wrong Code:</strong> Check device time synchronization<br>
                        <strong>App Problems:</strong> Try a different authenticator app
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function copyToClipboard(text) {
    navigator.clipboard.writeText(text).then(function() {
        // Show success message
        const btn = event.target.closest('button');
        const originalHtml = btn.innerHTML;
        btn.innerHTML = '<i class="fas fa-check"></i>';
        btn.classList.add('btn-success');
        btn.classList.remove('btn-outline-secondary');
        
        setTimeout(() => {
            btn.innerHTML = originalHtml;
            btn.classList.remove('btn-success');
            btn.classList.add('btn-outline-secondary');
        }, 2000);
    });
}
</script>

<?php include 'includes/admin-footer.php'; ?>
