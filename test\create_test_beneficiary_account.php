<?php
/**
 * Create Test Account for Beneficiary Testing
 * Creates account with number ************ for testing
 */

require_once '../config/config.php';

try {
    $db = getDB();
    
    echo "<h2>🧪 Creating Test Account for Beneficiary Testing</h2>";
    
    $test_account_number = '************';
    
    // Check if account already exists
    $check_sql = "SELECT id, first_name, last_name FROM accounts WHERE account_number = ?";
    $check_result = $db->query($check_sql, [$test_account_number]);
    
    if ($check_result->num_rows > 0) {
        $existing = $check_result->fetch_assoc();
        echo "✅ Account {$test_account_number} already exists!<br>";
        echo "Account holder: {$existing['first_name']} {$existing['last_name']}<br>";
        echo "This account can be used for testing internal user detection.<br>";
    } else {
        echo "Creating new test account...<br>";
        
        // Create test account
        $insert_sql = "INSERT INTO accounts (
            account_number, username, password, email, first_name, last_name,
            phone, address, currency, account_type, balance, status, kyc_status, is_admin
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";
        
        $params = [
            $test_account_number,
            'testbeneficiary',
            hashPassword('test123'),
            '<EMAIL>',
            'Test',
            'Beneficiary',
            '******-0199',
            '123 Test Street',
            'USD',
            'savings',
            2500.00,
            'active',
            'verified',
            0 // Not admin
        ];
        
        $user_id = $db->insert($insert_sql, $params);
        
        if ($user_id) {
            echo "✅ Successfully created test account!<br>";
            echo "Account Number: {$test_account_number}<br>";
            echo "Account Holder: Test Beneficiary<br>";
            echo "Username: testbeneficiary<br>";
            echo "Password: test123<br>";
            echo "Status: Active (Internal User)<br>";
            echo "<br>";
            echo "🎯 <strong>Now you can test:</strong><br>";
            echo "1. Add this account as a beneficiary<br>";
            echo "2. The system should detect it as an internal user<br>";
            echo "3. Inter-bank transfers should be available<br>";
        } else {
            echo "❌ Failed to create test account<br>";
        }
    }
    
    echo "<br><hr><br>";
    echo "<h3>📋 Test Instructions:</h3>";
    echo "<ol>";
    echo "<li>Go to <a href='../user/beneficiaries/' target='_blank'>Beneficiaries Page</a></li>";
    echo "<li>Click 'Add Beneficiary'</li>";
    echo "<li>Enter account number: <strong>{$test_account_number}</strong></li>";
    echo "<li>The system should detect it as an internal user</li>";
    echo "<li>Add the beneficiary and test transfers</li>";
    echo "</ol>";
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage();
}
?>
