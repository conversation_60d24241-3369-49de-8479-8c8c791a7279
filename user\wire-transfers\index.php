<?php
/**
 * Wire Transfer System - Separate from Regular Transfers
 * Includes billing code verification and enhanced security
 */

// Enable error reporting for debugging
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Start session and check authentication
session_start();
if (!isset($_SESSION['user_id'])) {
    header('Location: ../../auth/login.php');
    exit();
}

// Include database connection and dynamic CSS
require_once '../../config/config.php';
require_once '../../config/dynamic-css.php';

// Get user data from database
$db = getDB();
$user_id = $_SESSION['user_id'];

// Get user account information
$user_query = "SELECT * FROM accounts WHERE id = ?";
$user_result = $db->query($user_query, [$user_id]);
$user = $user_result->fetch_assoc();

if (!$user) {
    header('Location: ../../auth/login.php');
    exit();
}

// Check if user has billing codes assigned
$billing_codes_query = "SELECT * FROM user_billing_codes WHERE user_id = ? AND is_active = 1 ORDER BY billing_position";
$billing_codes_result = $db->query($billing_codes_query, [$user_id]);
$user_billing_codes = [];
while ($row = $billing_codes_result->fetch_assoc()) {
    $user_billing_codes[] = $row;
}

// Get wire transfer fields configuration
$fields_query = "SELECT * FROM wire_transfer_fields WHERE is_active = 1 ORDER BY field_group, display_order";
$fields_result = $db->query($fields_query);
$wire_fields = [];
while ($row = $fields_result->fetch_assoc()) {
    $wire_fields[$row['field_group']][] = $row;
}

// Get active currencies for currency selection
$currencies_query = "SELECT code, name, symbol FROM currencies WHERE status = 'active' ORDER BY code";
$currencies_result = $db->query($currencies_query);
$active_currencies = [];
while ($row = $currencies_result->fetch_assoc()) {
    $active_currencies[] = $row;
}

// Get user's virtual cards with balances for funding source options
$virtual_cards_query = "SELECT card_id, card_number, card_holder_name, card_balance, currency, card_type
                        FROM virtual_cards
                        WHERE account_id = ? AND status = 'active' AND card_balance > 0
                        ORDER BY card_balance DESC";
$virtual_cards_result = $db->query($virtual_cards_query, [$user_id]);
$virtual_cards = [];
while ($row = $virtual_cards_result->fetch_assoc()) {
    $virtual_cards[] = $row;
}

// Get wire transfer beneficiaries for quick selection
$wire_beneficiaries_query = "SELECT * FROM wire_beneficiaries WHERE user_id = ? ORDER BY is_favorite DESC, name ASC";
$wire_beneficiaries_result = $db->query($wire_beneficiaries_query, [$user_id]);
$wire_beneficiaries = [];
while ($row = $wire_beneficiaries_result->fetch_assoc()) {
    $wire_beneficiaries[] = $row;
}

// Set page title and subtitle
$page_title = 'Wire Transfer';
$page_subtitle = 'International money transfer via SWIFT network';

// Include header
require_once '../shared/header.php';
?>

<!-- Include Wire Transfer CSS -->
<link rel="stylesheet" href="wire-transfers.css">

<!-- Dynamic CSS Variables -->
<style>
    <?php echo getInlineDynamicCSS(); ?>
</style>

<!-- Sidebar -->
<?php require_once '../shared/sidebar.php'; ?>

<!-- Main Content Wrapper -->
<div class="main-content-wrapper">
    <!-- User Header Component -->
    <?php require_once '../shared/user_header.php'; ?>

    <div class="main-content" style="min-height: calc(100vh - 200px); display: flex; flex-direction: column;">

        <!-- Content Container -->
        <div class="content-container" style="flex: 1;">

        <!-- Wire Transfer Hero Section -->
        <div class="wire-transfer-hero mb-4">
            <div class="hero-content">
                <div class="hero-main">
                    <div class="hero-title">
                        <i class="fas fa-globe me-2"></i>Wire Transfer
                    </div>
                    <div class="hero-subtitle">Send money internationally via SWIFT network</div>
                    <div class="hero-stats">
                        Available Balance: <?php echo formatCurrency($user['balance'], $user['currency']); ?>
                        <?php if (!empty($user_billing_codes)): ?>
                        | Security Level: <?php echo count($user_billing_codes); ?> Billing Code(s)
                        <?php endif; ?>
                    </div>
                </div>
                <div class="hero-actions">
                    <a href="../transfers/" class="btn btn-outline-light">
                        <i class="fas fa-arrow-left me-2"></i>Back to Transfers
                    </a>
                </div>
            </div>
        </div>



        <!-- Wire Transfer Form -->
        <div class="wire-transfer-form-section">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-paper-plane me-2"></i>Wire Transfer Details
                    </h5>
                </div>
                <div class="card-body">
                    <form id="wireTransferForm" class="wire-transfer-form">

                        <!-- Saved Beneficiaries -->
                        <?php if (!empty($wire_beneficiaries)): ?>
                        <div class="form-section mb-4">
                            <h6><i class="fas fa-address-book me-2"></i>Quick Select Beneficiary</h6>
                            <div class="form-group">
                                <label for="savedBeneficiary" class="form-label">
                                    Select a saved beneficiary to auto-fill the form
                                </label>
                                <select class="form-select" id="savedBeneficiary" onchange="fillBeneficiaryData(this.value)">
                                    <option value="">Choose a saved beneficiary...</option>
                                    <?php foreach ($wire_beneficiaries as $beneficiary): ?>
                                    <option value="<?php echo htmlspecialchars(json_encode($beneficiary), ENT_QUOTES, 'UTF-8'); ?>">
                                        <?php echo htmlspecialchars($beneficiary['name']); ?>
                                        <?php if ($beneficiary['is_favorite']): ?>
                                        ⭐
                                        <?php endif; ?>
                                        - <?php echo htmlspecialchars($beneficiary['bank_name']); ?> (<?php echo htmlspecialchars($beneficiary['beneficiary_country']); ?>)
                                    </option>
                                    <?php endforeach; ?>
                                </select>
                                <div class="form-text">
                                    <i class="fas fa-info-circle me-1"></i>
                                    Selecting a beneficiary will automatically fill all the form fields below
                                </div>
                            </div>
                        </div>
                        <?php endif; ?>

                        <!-- Source Account -->
                        <div class="form-section mb-4">
                            <h6><i class="fas fa-wallet me-2"></i>From Account</h6>
                            <div class="form-group">
                                <label class="form-label">Select funding source <span class="text-danger">*</span></label>
                                <select name="source_account" id="sourceAccount" class="form-select" required onchange="updateSourceAccountInfo()">
                                    <option value="">Choose funding source...</option>
                                    <option value="main_account" data-balance="<?php echo $user['balance']; ?>" data-currency="<?php echo $user['currency']; ?>" data-account="<?php echo htmlspecialchars($user['account_number']); ?>">
                                        Main Account - <?php echo formatCurrency($user['balance'], $user['currency']); ?>
                                    </option>
                                    <?php if (!empty($virtual_cards)): ?>
                                        <?php foreach ($virtual_cards as $card): ?>
                                        <option value="virtual_card_<?php echo $card['card_id']; ?>"
                                                data-balance="<?php echo $card['card_balance']; ?>"
                                                data-currency="<?php echo $card['currency']; ?>"
                                                data-card="<?php echo htmlspecialchars($card['card_number']); ?>"
                                                data-type="<?php echo $card['card_type']; ?>">
                                            Virtual Card (<?php echo strtoupper($card['card_type']); ?>) - <?php echo formatCurrency($card['card_balance'], $card['currency']); ?>
                                        </option>
                                        <?php endforeach; ?>
                                    <?php endif; ?>
                                </select>
                            </div>

                            <!-- Selected Account Info Display -->
                            <div id="selectedAccountInfo" class="source-account-display mt-3" style="display: none;">
                                <div class="account-info">
                                    <div class="account-name" id="selectedAccountName"></div>
                                    <div class="account-number" id="selectedAccountNumber"></div>
                                    <div class="account-balance" id="selectedAccountBalance"></div>
                                </div>
                            </div>
                        </div>

                        <!-- Beneficiary Information -->
                        <?php if (isset($wire_fields['beneficiary'])): ?>
                        <div class="form-section mb-4">
                            <h6><i class="fas fa-user me-2"></i>Beneficiary Information</h6>
                            <div class="row">
                                <?php foreach ($wire_fields['beneficiary'] as $field): ?>
                                <div class="col-md-6 mb-3">
                                    <label for="<?php echo $field['field_name']; ?>" class="form-label">
                                        <?php echo htmlspecialchars($field['field_label']); ?>
                                        <?php if ($field['is_required']): ?><span class="text-danger">*</span><?php endif; ?>
                                    </label>
                                    <input type="<?php echo $field['field_type']; ?>" 
                                           class="form-control" 
                                           id="<?php echo $field['field_name']; ?>" 
                                           name="<?php echo $field['field_name']; ?>"
                                           placeholder="<?php echo htmlspecialchars($field['field_placeholder'] ?? ''); ?>"
                                           <?php echo $field['is_required'] ? 'required' : ''; ?>>
                                    <?php if ($field['help_text']): ?>
                                    <div class="form-text"><?php echo htmlspecialchars($field['help_text']); ?></div>
                                    <?php endif; ?>
                                </div>
                                <?php endforeach; ?>
                            </div>
                        </div>
                        <?php endif; ?>

                        <!-- Bank Information -->
                        <?php if (isset($wire_fields['bank'])): ?>
                        <div class="form-section mb-4">
                            <h6><i class="fas fa-university me-2"></i>Bank Information</h6>
                            <div class="row">
                                <?php foreach ($wire_fields['bank'] as $field): ?>
                                <div class="col-md-6 mb-3">
                                    <label for="<?php echo $field['field_name']; ?>" class="form-label">
                                        <?php echo htmlspecialchars($field['field_label']); ?>
                                        <?php if ($field['is_required']): ?><span class="text-danger">*</span><?php endif; ?>
                                    </label>
                                    <input type="<?php echo $field['field_type']; ?>" 
                                           class="form-control" 
                                           id="<?php echo $field['field_name']; ?>" 
                                           name="<?php echo $field['field_name']; ?>"
                                           placeholder="<?php echo htmlspecialchars($field['field_placeholder'] ?? ''); ?>"
                                           <?php echo $field['is_required'] ? 'required' : ''; ?>>
                                    <?php if ($field['help_text']): ?>
                                    <div class="form-text"><?php echo htmlspecialchars($field['help_text']); ?></div>
                                    <?php endif; ?>
                                </div>
                                <?php endforeach; ?>
                            </div>
                        </div>
                        <?php endif; ?>

                        <!-- Transfer Amount -->
                        <div class="form-section mb-4">
                            <h6><i class="fas fa-dollar-sign me-2"></i>Transfer Amount & Currency</h6>
                            <div class="alert alert-info mb-3">
                                <i class="fas fa-info-circle me-2"></i>
                                <strong>International Transfer:</strong> Select the currency you want to send. Your account balance is in <?php echo $user['currency']; ?>, but you can send any supported currency. Exchange rate calculations should be done externally.
                            </div>
                            <div class="row">
                                <div class="col-md-4 mb-3">
                                    <label for="amount" class="form-label">Amount <span class="text-danger">*</span></label>
                                    <div class="input-group">
                                        <span class="input-group-text" id="currency-symbol"><?php echo htmlspecialchars($user['currency']); ?></span>
                                        <input type="number" class="form-control" id="amount" name="amount"
                                               step="0.01" min="1" max="<?php echo $user['balance']; ?>" required>
                                    </div>
                                </div>
                                <div class="col-md-4 mb-3">
                                    <label for="transfer_currency" class="form-label">Transfer Currency <span class="text-danger">*</span></label>
                                    <select class="form-select" id="transfer_currency" name="transfer_currency" required onchange="updateCurrencySymbol()">
                                        <option value="">Select transfer currency</option>
                                        <?php foreach ($active_currencies as $currency): ?>
                                        <option value="<?php echo $currency['code']; ?>"
                                                data-symbol="<?php echo htmlspecialchars($currency['symbol']); ?>"
                                                <?php echo $currency['code'] === $user['currency'] ? 'selected' : ''; ?>>
                                            <?php echo $currency['code']; ?> - <?php echo htmlspecialchars($currency['name']); ?> (<?php echo htmlspecialchars($currency['symbol']); ?>)
                                        </option>
                                        <?php endforeach; ?>
                                    </select>
                                    <div class="form-text">
                                        <i class="fas fa-info-circle me-1"></i>
                                        Choose the currency for your international transfer. Default: <?php echo $user['currency']; ?>.
                                        <strong>Note:</strong> Exchange rates are handled externally.
                                    </div>
                                </div>
                                <div class="col-md-4 mb-3">
                                    <label for="purpose_of_payment" class="form-label">Purpose of Payment <span class="text-danger">*</span></label>
                                    <select class="form-select" id="purpose_of_payment" name="purpose_of_payment" required>
                                        <option value="">Select purpose</option>
                                        <option value="family_support">Family Support</option>
                                        <option value="business_payment">Business Payment</option>
                                        <option value="education">Education</option>
                                        <option value="medical">Medical</option>
                                        <option value="investment">Investment</option>
                                        <option value="other">Other</option>
                                    </select>
                                </div>
                            </div>
                        </div>

                        <!-- Additional Details -->
                        <div class="form-section mb-4">
                            <h6><i class="fas fa-edit me-2"></i>Additional Details</h6>
                            <div class="mb-3">
                                <label for="narration" class="form-label">Transfer Description</label>
                                <textarea class="form-control" id="narration" name="narration" rows="3" 
                                          placeholder="Enter transfer description or reference"></textarea>
                            </div>
                        </div>

                        <!-- Submit Button -->
                        <div class="form-section">
                            <button type="submit" class="btn btn-primary btn-lg">
                                <i class="fas fa-paper-plane me-2"></i>Initiate Wire Transfer
                            </button>
                            <a href="../transfers/" class="btn btn-outline-secondary btn-lg ms-2">
                                <i class="fas fa-times me-2"></i>Cancel
                            </a>
                        </div>

                    </form>
                </div>
            </div>
        </div>

        </div>
    </div>
</div>

<!-- Billing Code Verification Modal -->
<div class="modal fade" id="billingCodeModal" tabindex="-1" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="billingCodeTitle">Billing Code Verification</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="billing-code-info mb-3">
                    <h6 id="billingCodeName">Verification Required</h6>
                    <p id="billingCodeDescription" class="text-muted">Please enter your billing code to continue</p>
                </div>
                <div class="mb-3">
                    <label for="billingCodeInput" class="form-label">Enter Code</label>
                    <input type="text" class="form-control" id="billingCodeInput" placeholder="Enter billing code">
                    <div class="invalid-feedback" id="billingCodeError"></div>
                </div>
                <div class="progress" id="billingCodeProgress" style="display:none;">
                    <div class="progress-bar progress-bar-striped progress-bar-animated" style="width: 100%"></div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-primary" id="verifyBillingCodeBtn">Verify Code</button>
            </div>
        </div>
    </div>
</div>

<!-- OTP Verification Modal -->
<div class="modal fade" id="otpModal" tabindex="-1" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">OTP Verification</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <p>Please enter the OTP sent to your registered email address.</p>
                <div class="mb-3">
                    <label for="otpCode" class="form-label">OTP Code</label>
                    <input type="text" class="form-control" id="otpCode" placeholder="Enter 6-digit OTP" maxlength="6">
                    <div class="invalid-feedback" id="otpError"></div>
                </div>
                <div class="progress" id="otpProgress" style="display:none;">
                    <div class="progress-bar progress-bar-striped progress-bar-animated" style="width: 100%"></div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-primary" id="verifyOtpBtn">Verify OTP</button>
            </div>
        </div>
    </div>
</div>

<!-- Account Selection JavaScript -->
<script>
function updateSourceAccountInfo() {
    const select = document.getElementById('sourceAccount');
    const infoDiv = document.getElementById('selectedAccountInfo');
    const nameDiv = document.getElementById('selectedAccountName');
    const numberDiv = document.getElementById('selectedAccountNumber');
    const balanceDiv = document.getElementById('selectedAccountBalance');
    const amountField = document.getElementById('amount');

    if (select.value === '') {
        infoDiv.style.display = 'none';
        return;
    }

    const option = select.options[select.selectedIndex];
    const balance = option.dataset.balance;
    const currency = option.dataset.currency;

    // Update the amount field's max value based on selected funding source
    amountField.setAttribute('max', balance);

    if (select.value === 'main_account') {
        nameDiv.textContent = 'Main Account';
        numberDiv.textContent = option.dataset.account;
        balanceDiv.textContent = formatCurrency(balance, currency);
    } else if (select.value.startsWith('virtual_card_')) {
        const cardType = option.dataset.type;
        nameDiv.textContent = `Virtual Card (${cardType.toUpperCase()})`;
        numberDiv.textContent = option.dataset.card;
        balanceDiv.textContent = formatCurrency(balance, currency);
    }

    infoDiv.style.display = 'block';
}

function formatCurrency(amount, currency = 'USD') {
    return new Intl.NumberFormat('en-US', {
        style: 'currency',
        currency: currency
    }).format(amount);
}

// Fill beneficiary data when a saved beneficiary is selected
function fillBeneficiaryData(beneficiaryJson) {
    if (!beneficiaryJson) {
        // Clear all fields if no beneficiary selected
        clearBeneficiaryFields();
        return;
    }

    try {
        const beneficiary = JSON.parse(beneficiaryJson);

        // Fill beneficiary information
        document.querySelector('input[name="beneficiary_country"]').value = beneficiary.beneficiary_country || '';
        document.querySelector('input[name="beneficiary_account_number"]').value = beneficiary.beneficiary_account_number || '';
        document.querySelector('input[name="beneficiary_account_name"]').value = beneficiary.beneficiary_account_name || '';
        document.querySelector('input[name="beneficiary_address"]').value = beneficiary.beneficiary_address || '';

        // Fill bank information
        document.querySelector('input[name="bank_name"]').value = beneficiary.bank_name || '';
        document.querySelector('input[name="bank_address"]').value = beneficiary.bank_address || '';
        document.querySelector('input[name="bank_city"]').value = beneficiary.bank_city || '';
        document.querySelector('input[name="bank_country"]').value = beneficiary.bank_country || '';
        document.querySelector('input[name="swift_code"]').value = beneficiary.swift_code || '';
        document.querySelector('input[name="routing_code"]').value = beneficiary.routing_code || '';
        document.querySelector('input[name="iban"]').value = beneficiary.iban || '';

        console.log('✅ Beneficiary data filled:', beneficiary.name);

        // Show success message
        showNotification('Beneficiary details loaded successfully!', 'success');

    } catch (error) {
        console.error('Error parsing beneficiary data:', error);
        showNotification('Error loading beneficiary details', 'error');
    }
}

// Clear all beneficiary fields
function clearBeneficiaryFields() {
    const fields = [
        'beneficiary_country', 'beneficiary_account_number', 'beneficiary_account_name', 'beneficiary_address',
        'bank_name', 'bank_address', 'bank_city', 'bank_country', 'swift_code', 'routing_code', 'iban'
    ];

    fields.forEach(fieldName => {
        const field = document.querySelector(`input[name="${fieldName}"]`);
        if (field) {
            field.value = '';
        }
    });
}

// Show notification message
function showNotification(message, type = 'info') {
    // Create notification element
    const notification = document.createElement('div');
    notification.className = `alert alert-${type === 'success' ? 'success' : 'danger'} alert-dismissible fade show position-fixed`;
    notification.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
    notification.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;

    // Add to page
    document.body.appendChild(notification);

    // Auto-remove after 3 seconds
    setTimeout(() => {
        if (notification.parentNode) {
            notification.remove();
        }
    }, 3000);
}
</script>

<!-- Include Wire Transfer JavaScript -->
<script src="wire-transfers.js"></script>

<?php require_once '../shared/footer.php'; ?>
