<?php
/**
 * Super Admin User Management
 * Manage super admin and admin accounts
 */

$page_title = 'Admin Management';
$page_subtitle = 'Manage super admin and admin accounts';

// Include header
include 'includes/header.php';

// Handle admin actions
if ($_POST && isset($_POST['csrf_token']) && verifyCSRFToken($_POST['csrf_token'])) {
    try {
        require_once '../config/database.php';
        $db = getDB();

        // Create admin_users table if it doesn't exist
        $create_table_sql = "CREATE TABLE IF NOT EXISTS `admin_users` (
            `id` int(11) NOT NULL AUTO_INCREMENT,
            `username` varchar(50) NOT NULL,
            `email` varchar(100) NOT NULL,
            `password` varchar(255) NOT NULL,
            `full_name` varchar(100) NOT NULL,
            `role` enum('super_admin','admin') NOT NULL DEFAULT 'admin',
            `status` enum('active','suspended','inactive') NOT NULL DEFAULT 'active',
            `last_login` timestamp NULL DEFAULT NULL,
            `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
            `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            PRIMARY KEY (`id`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";

        $db->query($create_table_sql);

        // Add unique indexes only if they don't exist
        if (!$db->query("SELECT 1 FROM information_schema.statistics 
                        WHERE table_schema = DATABASE() 
                        AND table_name = 'admin_users' 
                        AND index_name = 'username'")->num_rows) {
            $db->query("ALTER TABLE admin_users ADD UNIQUE KEY `username` (`username`)");
        }
        
        if (!$db->query("SELECT 1 FROM information_schema.statistics 
                        WHERE table_schema = DATABASE() 
                        AND table_name = 'admin_users' 
                        AND index_name = 'email'")->num_rows) {
            $db->query("ALTER TABLE admin_users ADD UNIQUE KEY `email` (`email`)");
        }

        // Insert default super admin if not exists
        $check_superadmin = $db->query("SELECT id FROM admin_users WHERE username = 'superadmin'");
        if (!$check_superadmin || $check_superadmin->num_rows === 0) {
            $db->query("INSERT INTO admin_users (username, email, password, full_name, role) VALUES (?, ?, ?, ?, ?)", [
                'superadmin',
                '<EMAIL>',
                password_hash('admin123', PASSWORD_DEFAULT),
                'Super Administrator',
                'super_admin'
            ]);
        }

        if (isset($_POST['action'])) {
            switch ($_POST['action']) {
                case 'create_admin':
                    $username = trim($_POST['username']);
                    $email = trim($_POST['email']);
                    $password = $_POST['password'];
                    $full_name = trim($_POST['full_name']);
                    $role = $_POST['role'];

                    // Validate inputs
                    if (empty($username) || empty($email) || empty($password) || empty($full_name)) {
                        throw new Exception("All fields are required");
                    }

                    if (strlen($password) < 8) {
                        throw new Exception("Password must be at least 8 characters long");
                    }

                    // Hash password
                    $hashed_password = password_hash($password, PASSWORD_DEFAULT);

                    // Insert new admin
                    $db->query("INSERT INTO admin_users (username, email, password, full_name, role) VALUES (?, ?, ?, ?, ?)", [
                        $username, $email, $hashed_password, $full_name, $role
                    ]);

                    logSuperAdminAction('admin_created', "New $role created: $username", [
                        'username' => $username,
                        'email' => $email,
                        'role' => $role
                    ]);
                    $success_message = ucfirst($role) . " account created successfully!";
                    break;

                case 'suspend_admin':
                    $admin_id = (int)$_POST['admin_id'];
                    $admin_result = $db->query("SELECT username, role FROM admin_users WHERE id = ?", [$admin_id]);
                    $admin_info = $admin_result->fetch_assoc();

                    if ($admin_info['role'] === 'super_admin') {
                        throw new Exception("Cannot suspend super admin accounts");
                    }

                    $db->query("UPDATE admin_users SET status = 'suspended' WHERE id = ?", [$admin_id]);
                    logSuperAdminAction('admin_suspended', "Admin suspended: " . $admin_info['username'], ['admin_id' => $admin_id]);
                    $success_message = "Admin account suspended successfully!";
                    break;

                case 'activate_admin':
                    $admin_id = (int)$_POST['admin_id'];
                    $admin_result = $db->query("SELECT username FROM admin_users WHERE id = ?", [$admin_id]);
                    $admin_info = $admin_result->fetch_assoc();

                    $db->query("UPDATE admin_users SET status = 'active' WHERE id = ?", [$admin_id]);
                    logSuperAdminAction('admin_activated', "Admin activated: " . $admin_info['username'], ['admin_id' => $admin_id]);
                    $success_message = "Admin account activated successfully!";
                    break;

                case 'delete_admin':
                    $admin_id = (int)$_POST['admin_id'];
                    $admin_result = $db->query("SELECT username, role FROM admin_users WHERE id = ?", [$admin_id]);
                    $admin_info = $admin_result->fetch_assoc();

                    if ($admin_info['role'] === 'super_admin') {
                        throw new Exception("Cannot delete super admin accounts");
                    }

                    $db->query("DELETE FROM admin_users WHERE id = ?", [$admin_id]);
                    logSuperAdminAction('admin_deleted', "Admin deleted: " . $admin_info['username'], ['admin_id' => $admin_id]);
                    $success_message = "Admin account deleted successfully!";
                    break;

                case 'reset_password':
                    $admin_id = (int)$_POST['admin_id'];
                    $new_password = $_POST['new_password'];

                    if (strlen($new_password) < 8) {
                        throw new Exception("Password must be at least 8 characters long");
                    }

                    $admin_result = $db->query("SELECT username FROM admin_users WHERE id = ?", [$admin_id]);
                    $admin_info = $admin_result->fetch_assoc();

                    $hashed_password = password_hash($new_password, PASSWORD_DEFAULT);
                    $db->query("UPDATE admin_users SET password = ? WHERE id = ?", [$hashed_password, $admin_id]);

                    logSuperAdminAction('admin_password_reset', "Password reset for admin: " . $admin_info['username'], ['admin_id' => $admin_id]);
                    $success_message = "Admin password reset successfully!";
                    break;
                    
                case 'edit_admin':
                    $admin_id = (int)$_POST['admin_id'];
                    $email = trim($_POST['email']);
                    $full_name = trim($_POST['full_name']);

                    if (empty($email) || empty($full_name)) {
                        throw new Exception("Email and full name are required");
                    }

                    $admin_result = $db->query("SELECT username, role FROM admin_users WHERE id = ?", [$admin_id]);
                    $admin_info = $admin_result->fetch_assoc();

                    $db->query("UPDATE admin_users SET email = ?, full_name = ? WHERE id = ?", [$email, $full_name, $admin_id]);

                    logSuperAdminAction('admin_edited', "Admin details updated: " . $admin_info['username'], [
                        'admin_id' => $admin_id,
                        'email' => $email,
                        'full_name' => $full_name
                    ]);
                    $success_message = "Admin details updated successfully!";
                    break;
            }
        }

    } catch (Exception $e) {
        $error_message = "Failed to perform action: " . $e->getMessage();
        error_log($error_message);
    }
}

// Get admins with pagination
$page = isset($_GET['page']) ? (int)$_GET['page'] : 1;
$limit = 20;
$offset = ($page - 1) * $limit;
$search = isset($_GET['search']) ? trim($_GET['search']) : '';

try {
    require_once '../config/database.php';
    $db = getDB();

    // Ensure admin_users table exists and has default super admin
    $create_table_sql = "CREATE TABLE IF NOT EXISTS `admin_users` (
        `id` int(11) NOT NULL AUTO_INCREMENT,
        `username` varchar(50) NOT NULL,
        `email` varchar(100) NOT NULL,
        `password` varchar(255) NOT NULL,
        `full_name` varchar(100) NOT NULL,
        `role` enum('super_admin','admin') NOT NULL DEFAULT 'admin',
        `status` enum('active','suspended','inactive') NOT NULL DEFAULT 'active',
        `last_login` timestamp NULL DEFAULT NULL,
        `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
        `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        PRIMARY KEY (`id`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";

    $db->query($create_table_sql);

    // Add unique indexes only if they don't exist
    $index_check = $db->query("SHOW INDEX FROM admin_users WHERE Key_name = 'username'");
    if ($index_check->num_rows === 0) {
        $db->query("ALTER TABLE admin_users ADD UNIQUE KEY `username` (`username`)");
    }
    
    $index_check = $db->query("SHOW INDEX FROM admin_users WHERE Key_name = 'email'");
    if ($index_check->num_rows === 0) {
        $db->query("ALTER TABLE admin_users ADD UNIQUE KEY `email` (`email`)");
    }

    // Insert default super admin if not exists
    $check_superadmin = $db->query("SELECT id FROM admin_users WHERE username = 'superadmin'");
    if (!$check_superadmin || $check_superadmin->num_rows === 0) {
        $db->query("INSERT INTO admin_users (username, email, password, full_name, role) VALUES (?, ?, ?, ?, ?)", [
            'superadmin',
            '<EMAIL>',
            password_hash('admin123', PASSWORD_DEFAULT),
            'Super Administrator',
            'super_admin'
        ]);
    }

    // Build search query
    $where_clause = '';
    $params = [];

    if (!empty($search)) {
        $where_clause = "WHERE (email LIKE ? OR full_name LIKE ? OR username LIKE ?)";
        $search_param = "%$search%";
        $params = [$search_param, $search_param, $search_param];
    }

    // Get total count from both tables
    $count_sql1 = "SELECT COUNT(*) as total FROM admin_users $where_clause";
    $count_result1 = $db->query($count_sql1, $params);
    $total_admin_users = $count_result1->fetch_assoc()['total'];

    $where_clause_accounts_count = '';
    $params_count = [];
    if (!empty($search)) {
        $where_clause_accounts_count = "AND (email LIKE ? OR CONCAT(first_name, ' ', last_name) LIKE ? OR username LIKE ?)";
        $search_param = "%$search%";
        $params_count = [$search_param, $search_param, $search_param];
    }

    $count_sql2 = "SELECT COUNT(*) as total FROM accounts
                   WHERE (is_admin = 1 OR role = 'admin' OR role = 'super_admin')
                   $where_clause_accounts_count";
    $count_result2 = $db->query($count_sql2, $params_count);
    $total_accounts_admins = $count_result2->fetch_assoc()['total'];

    $total_admins = $total_admin_users + $total_accounts_admins;
    $total_pages = ceil($total_admins / $limit);

    // Get admins from both tables
    $admins = [];

    // First, get admins from admin_users table (Super Admin system)
    $sql1 = "SELECT id, username, email, full_name, role, status, created_at, last_login, 'admin_users' as source_table
            FROM admin_users $where_clause
            ORDER BY role DESC, created_at DESC
            LIMIT ? OFFSET ?";

    $params1 = $params;
    $params1[] = $limit;
    $params1[] = $offset;

    $admins_result1 = $db->query($sql1, $params1);
    if ($admins_result1) {
        while ($row = $admins_result1->fetch_assoc()) {
            $admins[] = $row;
        }
    }

    // Then, get admins from accounts table (Regular admin system)
    $where_clause_accounts = '';
    $params2 = [];

    if (!empty($search)) {
        $where_clause_accounts = "AND (email LIKE ? OR CONCAT(first_name, ' ', last_name) LIKE ? OR username LIKE ?)";
        $search_param = "%$search%";
        $params2 = [$search_param, $search_param, $search_param];
    }

    $sql2 = "SELECT id, username, email, CONCAT(first_name, ' ', last_name) as full_name,
                    CASE
                        WHEN role = 'super_admin' THEN 'super_admin'
                        WHEN is_admin = 1 OR role = 'admin' THEN 'admin'
                        ELSE 'user'
                    END as role,
                    status, created_at, last_login, 'accounts' as source_table
            FROM accounts
            WHERE (is_admin = 1 OR role = 'admin' OR role = 'super_admin')
            $where_clause_accounts
            ORDER BY role DESC, created_at DESC";

    $admins_result2 = $db->query($sql2, $params2);
    if ($admins_result2) {
        while ($row = $admins_result2->fetch_assoc()) {
            $admins[] = $row;
        }
    }

    // Sort combined results by role and creation date
    usort($admins, function($a, $b) {
        if ($a['role'] === $b['role']) {
            return strtotime($b['created_at']) - strtotime($a['created_at']);
        }
        return ($a['role'] === 'super_admin') ? -1 : 1;
    });

} catch (Exception $e) {
    error_log("Failed to load admins: " . $e->getMessage());
    $admins = [];
    $total_admins = 0;
    $total_pages = 0;
}

// Log page access
logSuperAdminAction('admin_management_access', 'Super admin accessed admin management');
?>

<!-- Success/Error Messages -->
<?php if (isset($success_message)): ?>
    <div class="alert alert-success alert-dismissible fade show" role="alert">
        <i class="fas fa-check-circle"></i> <?php echo htmlspecialchars($success_message); ?>
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
<?php endif; ?>

<?php if (isset($error_message)): ?>
    <div class="alert alert-danger alert-dismissible fade show" role="alert">
        <i class="fas fa-exclamation-triangle"></i> <?php echo htmlspecialchars($error_message); ?>
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
<?php endif; ?>

<!-- Admin Statistics -->
<div class="row mb-4">
    <div class="col-md-3">
        <div class="card border-0 shadow-sm">
            <div class="card-body text-center">
                <h3 class="text-primary"><?php echo number_format($total_admins); ?></h3>
                <p class="text-muted mb-0">Total Admins</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card border-0 shadow-sm">
            <div class="card-body text-center">
                <?php
                $super_admin_count = 0;
                foreach ($admins as $admin) {
                    if ($admin['role'] === 'super_admin') $super_admin_count++;
                }
                ?>
                <h3 class="text-danger"><?php echo number_format($super_admin_count); ?></h3>
                <p class="text-muted mb-0">Super Admins</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card border-0 shadow-sm">
            <div class="card-body text-center">
                <?php
                $admin_count = 0;
                foreach ($admins as $admin) {
                    if ($admin['role'] === 'admin') $admin_count++;
                }
                ?>
                <h3 class="text-success"><?php echo number_format($admin_count); ?></h3>
                <p class="text-muted mb-0">Regular Admins</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card border-0 shadow-sm">
            <div class="card-body text-center">
                <?php
                $active_count = 0;
                foreach ($admins as $admin) {
                    if ($admin['status'] === 'active') $active_count++;
                }
                ?>
                <h3 class="text-info"><?php echo number_format($active_count); ?></h3>
                <p class="text-muted mb-0">Active</p>
            </div>
        </div>
    </div>
</div>

<!-- Create New Admin -->
<div class="card border-0 shadow-sm mb-4">
    <div class="card-header bg-white border-0 py-3">
        <h5 class="card-title mb-0">
            <i class="fas fa-user-plus text-success"></i> Create New Admin Account
        </h5>
    </div>
    <div class="card-body">
        <form method="POST" action="">
            <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
            <input type="hidden" name="action" value="create_admin">

            <div class="row">
                <div class="col-md-6 mb-3">
                    <label for="username" class="form-label">Username</label>
                    <input type="text" class="form-control" id="username" name="username" required>
                    <div class="form-text">Unique username for login</div>
                </div>
                <div class="col-md-6 mb-3">
                    <label for="email" class="form-label">Email Address</label>
                    <input type="email" class="form-control" id="email" name="email" required>
                    <div class="form-text">Admin's email address</div>
                </div>
            </div>

            <div class="row">
                <div class="col-md-6 mb-3">
                    <label for="full_name" class="form-label">Full Name</label>
                    <input type="text" class="form-control" id="full_name" name="full_name" required>
                    <div class="form-text">Admin's full name</div>
                </div>
                <div class="col-md-6 mb-3">
                    <label for="role" class="form-label">Role</label>
                    <select class="form-select" id="role" name="role" required>
                        <option value="admin">Regular Admin</option>
                        <option value="super_admin">Super Admin</option>
                    </select>
                    <div class="form-text">Admin access level</div>
                </div>
            </div>

            <div class="row">
                <div class="col-md-6 mb-3">
                    <label for="password" class="form-label">Password</label>
                    <input type="password" class="form-control" id="password" name="password" required minlength="8">
                    <div class="form-text">Minimum 8 characters</div>
                </div>
                <div class="col-md-6 mb-3">
                    <label for="confirm_password" class="form-label">Confirm Password</label>
                    <input type="password" class="form-control" id="confirm_password" name="confirm_password" required minlength="8">
                    <div class="form-text">Re-enter the password</div>
                </div>
            </div>

            <button type="submit" class="btn btn-success">
                <i class="fas fa-user-plus"></i> Create Admin Account
            </button>
        </form>
    </div>
</div>

<!-- Search and Filters -->
<div class="card border-0 shadow-sm mb-4">
    <div class="card-body">
        <form method="GET" class="row g-3">
            <div class="col-md-6">
                <label for="search" class="form-label">Search Admins</label>
                <input type="text" class="form-control" id="search" name="search"
                       value="<?php echo htmlspecialchars($search); ?>"
                       placeholder="Search by username, email, or name...">
            </div>
            <div class="col-md-3">
                <label class="form-label">&nbsp;</label>
                <div class="d-grid">
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-search"></i> Search
                    </button>
                </div>
            </div>
            <div class="col-md-3">
                <label class="form-label">&nbsp;</label>
                <div class="d-grid">
                    <a href="user-management.php" class="btn btn-outline-secondary">
                        <i class="fas fa-times"></i> Clear
                    </a>
                </div>
            </div>
        </form>
    </div>
</div>

<!-- Admin Accounts Table -->
<div class="card border-0 shadow-sm">
    <div class="card-header bg-white border-0 py-3 d-flex justify-content-between align-items-center">
        <h5 class="card-title mb-0">
            <i class="fas fa-users-cog text-primary"></i> Admin Accounts
        </h5>
        <span class="badge bg-primary"><?php echo number_format($total_admins); ?> admins</span>
    </div>
    <div class="card-body p-0">
        <?php if (empty($admins)): ?>
            <div class="text-center py-5">
                <i class="fas fa-users-cog fa-3x text-muted mb-3"></i>
                <p class="text-muted">No admin accounts found.</p>
            </div>
        <?php else: ?>
            <div class="table-responsive">
                <table class="table table-hover mb-0">
                    <thead class="table-light">
                        <tr>
                            <th>Admin Details</th>
                            <th>Username</th>
                            <th>Role</th>
                            <th>Status</th>
                            <th>Last Login</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($admins as $admin): ?>
                            <tr>
                                <td>
                                    <div>
                                        <strong><?php echo htmlspecialchars($admin['full_name']); ?></strong><br>
                                        <small class="text-muted"><?php echo htmlspecialchars($admin['email']); ?></small>
                                    </div>
                                </td>
                                <td>
                                    <code><?php echo htmlspecialchars($admin['username']); ?></code>
                                </td>
                                <td>
                                    <?php
                                    $role_class = $admin['role'] === 'super_admin' ? 'danger' : 'primary';
                                    $role_text = $admin['role'] === 'super_admin' ? 'Super Admin' : 'Admin';
                                    ?>
                                    <span class="badge bg-<?php echo $role_class; ?>">
                                        <?php echo $role_text; ?>
                                    </span>
                                </td>
                                <td>
                                    <?php
                                    $status_class = 'secondary';
                                    switch ($admin['status']) {
                                        case 'active':
                                            $status_class = 'success';
                                            break;
                                        case 'suspended':
                                            $status_class = 'warning';
                                            break;
                                        case 'inactive':
                                            $status_class = 'danger';
                                            break;
                                    }
                                    ?>
                                    <span class="badge bg-<?php echo $status_class; ?>">
                                        <?php echo ucfirst($admin['status']); ?>
                                    </span>
                                </td>
                                <td>
                                    <?php if ($admin['last_login']): ?>
                                        <small><?php echo date('M j, Y g:i A', strtotime($admin['last_login'])); ?></small>
                                    <?php else: ?>
                                        <small class="text-muted">Never</small>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <div class="btn-group btn-group-sm">
                                        <?php if ($admin['source_table'] === 'accounts'): ?>
                                            <!-- Legacy admin from accounts table -->
                                            <span class="badge bg-warning text-dark">Legacy Admin</span>
                                            <small class="text-muted d-block mt-1">From accounts table</small>
                                        <?php else: ?>
                                            <!-- Admin from admin_users table -->
                                            <?php if ($admin['role'] !== 'super_admin'): ?>
                                                <?php if ($admin['status'] === 'active'): ?>
                                                    <button type="button" class="btn btn-outline-warning btn-sm"
                                                            onclick="suspendAdmin(<?php echo $admin['id']; ?>, '<?php echo htmlspecialchars($admin['username']); ?>')">
                                                        <i class="fas fa-pause"></i>
                                                    </button>
                                                <?php else: ?>
                                                    <button type="button" class="btn btn-outline-success btn-sm"
                                                            onclick="activateAdmin(<?php echo $admin['id']; ?>, '<?php echo htmlspecialchars($admin['username']); ?>')">
                                                        <i class="fas fa-play"></i>
                                                    </button>
                                                <?php endif; ?>

                                                <button type="button" class="btn btn-outline-info btn-sm"
                                                        onclick="resetPassword(<?php echo $admin['id']; ?>, '<?php echo htmlspecialchars($admin['username']); ?>')">
                                                    <i class="fas fa-key"></i>
                                                </button>

                                                <button type="button" class="btn btn-outline-danger btn-sm"
                                                        onclick="deleteAdmin(<?php echo $admin['id']; ?>, '<?php echo htmlspecialchars($admin['username']); ?>')">
                                                    <i class="fas fa-trash"></i>
                                                </button>
                                            <?php else: ?>
                                                <span class="badge bg-secondary">Protected</span>
                                                <button type="button" class="btn btn-outline-info btn-sm"
                                                        onclick="resetPassword(<?php echo $admin['id']; ?>, '<?php echo htmlspecialchars($admin['username']); ?>')">
                                                    <i class="fas fa-key"></i>
                                                </button>
                                            <?php endif; ?>
                                        <?php endif; ?>
                                    </div>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
        <?php endif; ?>
    </div>
</div>

<!-- Pagination -->
<?php if ($total_pages > 1): ?>
    <nav aria-label="Admin pagination" class="mt-4">
        <ul class="pagination justify-content-center">
            <?php if ($page > 1): ?>
                <li class="page-item">
                    <a class="page-link" href="?page=<?php echo $page - 1; ?><?php echo $search ? '&search=' . urlencode($search) : ''; ?>">
                        <i class="fas fa-chevron-left"></i> Previous
                    </a>
                </li>
            <?php endif; ?>

            <?php for ($i = max(1, $page - 2); $i <= min($total_pages, $page + 2); $i++): ?>
                <li class="page-item <?php echo $i === $page ? 'active' : ''; ?>">
                    <a class="page-link" href="?page=<?php echo $i; ?><?php echo $search ? '&search=' . urlencode($search) : ''; ?>">
                        <?php echo $i; ?>
                    </a>
                </li>
            <?php endfor; ?>

            <?php if ($page < $total_pages): ?>
                <li class="page-item">
                    <a class="page-link" href="?page=<?php echo $page + 1; ?><?php echo $search ? '&search=' . urlencode($search) : ''; ?>">
                        Next <i class="fas fa-chevron-right"></i>
                    </a>
                </li>
            <?php endif; ?>
        </ul>
    </nav>
<?php endif; ?>

<!-- Hidden forms for admin actions -->
<form id="adminActionForm" method="POST" style="display: none;">
    <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
    <input type="hidden" name="action" id="actionType">
    <input type="hidden" name="admin_id" id="actionAdminId">
</form>

        <form id="editAdminForm" method="POST" style="display: none;">
            <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
            <input type="hidden" name="action" value="edit_admin">
            <input type="hidden" name="admin_id" id="editAdminId">
            <input type="hidden" name="email" id="editEmail">
            <input type="hidden" name="full_name" id="editFullName">
        </form>
        
        <form id="passwordResetForm" method="POST" style="display: none;">
    <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
    <input type="hidden" name="action" value="reset_password">
    <input type="hidden" name="admin_id" id="resetAdminId">
    <input type="hidden" name="new_password" id="newPassword">
</form>

<script>
// Password confirmation validation
document.getElementById('confirm_password').addEventListener('input', function() {
    const password = document.getElementById('password').value;
    const confirmPassword = this.value;

    if (password !== confirmPassword) {
        this.setCustomValidity('Passwords do not match');
    } else {
        this.setCustomValidity('');
    }
});

function suspendAdmin(adminId, username) {
    if (confirm('Are you sure you want to suspend admin: ' + username + '?')) {
        document.getElementById('actionType').value = 'suspend_admin';
        document.getElementById('actionAdminId').value = adminId;
        document.getElementById('adminActionForm').submit();
    }
}

function activateAdmin(adminId, username) {
    if (confirm('Are you sure you want to activate admin: ' + username + '?')) {
        document.getElementById('actionType').value = 'activate_admin';
        document.getElementById('actionAdminId').value = adminId;
        document.getElementById('adminActionForm').submit();
    }
}

function deleteAdmin(adminId, username) {
    if (confirm('Are you sure you want to DELETE admin: ' + username + '?\n\nThis action cannot be undone!')) {
        if (confirm('This will permanently delete the admin account. Are you absolutely sure?')) {
            document.getElementById('actionType').value = 'delete_admin';
            document.getElementById('actionAdminId').value = adminId;
            document.getElementById('adminActionForm').submit();
        }
    }
}

function editAdmin(adminId, username, currentEmail, currentFullName) {
    const email = prompt('Enter new email for ' + username + ':', currentEmail);
    if (email === null) return;
    
    const fullName = prompt('Enter new full name for ' + username + ':', currentFullName);
    if (fullName === null) return;
    
    if (confirm('Are you sure you want to update details for admin: ' + username + '?')) {
        document.getElementById('editAdminId').value = adminId;
        document.getElementById('editEmail').value = email;
        document.getElementById('editFullName').value = fullName;
        document.getElementById('editAdminForm').submit();
    }
}

function resetPassword(adminId, username) {
    const newPassword = prompt('Enter new password for ' + username + ':\n(Minimum 8 characters)';

    if (newPassword === null) {
        return; // User cancelled
    }

    if (newPassword.length < 8) {
        alert('Password must be at least 8 characters long');
        return;
    }

    if (confirm('Are you sure you want to reset password for admin: ' + username + '?')) {
        document.getElementById('resetAdminId').value = adminId;
        document.getElementById('newPassword').value = newPassword;
        document.getElementById('passwordResetForm').submit();
    }
}
</script>

<?php
// Include footer
include 'includes/footer.php';
?>
