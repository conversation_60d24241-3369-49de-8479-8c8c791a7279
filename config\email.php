<?php
/**
 * Email Configuration for Online Banking System
 */

// SMTP Configuration - Now loaded dynamically from super admin settings
// Include super admin settings for dynamic SMTP configuration
require_once __DIR__ . '/super_admin_settings.php';
$smtp_config = getSMTPConfig();

define('SMTP_HOST', $smtp_config['host']);
define('SMTP_PORT', $smtp_config['port']);
define('SMTP_USERNAME', $smtp_config['username']);
define('SMTP_PASSWORD', $smtp_config['password']);
define('SMTP_ENCRYPTION', $smtp_config['encryption']);
define('FROM_EMAIL', $smtp_config['from_email']);
define('FROM_NAME', $smtp_config['from_name']);

/**
 * Validate email address
 */
function isValidEmail($email) {
    return filter_var($email, FILTER_VALIDATE_EMAIL) !== false;
}

/**
 * Send email using cURL to SMTP API or fallback methods
 */
function sendEmail($to, $subject, $message, $isHTML = true) {
    try {
        // Validate email first
        if (!isValidEmail($to)) {
            error_log("Invalid email address: $to");
            return false;
        }

        // For localhost testing, we'll use a more compatible approach
        if (strpos($_SERVER['HTTP_HOST'], 'localhost') !== false) {
            return sendEmailLocalhost($to, $subject, $message, $isHTML);
        }

        // Production SMTP sending
        return sendEmailSMTP($to, $subject, $message, $isHTML);

    } catch (Exception $e) {
        error_log("Email error: " . $e->getMessage());
        return false;
    }
}

/**
 * Send email for localhost development
 */
function sendEmailLocalhost($to, $subject, $message, $isHTML = true) {
    try {
        // For localhost, we'll simulate email sending and log it
        $logMessage = "=== EMAIL SIMULATION ===\n";
        $logMessage .= "To: $to\n";
        $logMessage .= "Subject: $subject\n";
        $logMessage .= "Time: " . date('Y-m-d H:i:s') . "\n";
        $logMessage .= "Message: " . strip_tags($message) . "\n";
        $logMessage .= "========================\n\n";

        // Log to file for debugging
        file_put_contents(__DIR__ . '/../logs/email_simulation.log', $logMessage, FILE_APPEND | LOCK_EX);

        // Also try to send via mail() function with better headers
        $headers = array();
        $headers[] = 'MIME-Version: 1.0';
        $headers[] = 'Content-type: ' . ($isHTML ? 'text/html' : 'text/plain') . '; charset=UTF-8';
        $headers[] = 'From: ' . FROM_NAME . ' <' . FROM_EMAIL . '>';
        $headers[] = 'Reply-To: ' . FROM_EMAIL;
        $headers[] = 'X-Mailer: PHP/' . phpversion();

        $result = @mail($to, $subject, $message, implode("\r\n", $headers));

        if ($result) {
            error_log("Email sent successfully to: $to");
            return true;
        } else {
            // Even if mail() fails, we'll return true for localhost to continue the flow
            error_log("Mail function failed for: $to, but continuing for localhost testing");
            return true;
        }

    } catch (Exception $e) {
        error_log("Localhost email error: " . $e->getMessage());
        // Return true for localhost to allow testing
        return true;
    }
}

/**
 * Send email via SMTP using PHPMailer
 */
function sendEmailSMTP($to, $subject, $message, $isHTML = true) {
    try {
        // Load PHPMailer manually to avoid autoloader issues
        $phpmailer_path = __DIR__ . '/../vendor/phpmailer/phpmailer/src/PHPMailer.php';
        $exception_path = __DIR__ . '/../vendor/phpmailer/phpmailer/src/Exception.php';
        $smtp_path = __DIR__ . '/../vendor/phpmailer/phpmailer/src/SMTP.php';

        if (!file_exists($phpmailer_path)) {
            throw new Exception("PHPMailer not found at: $phpmailer_path");
        }

        require_once $exception_path;
        require_once $smtp_path;
        require_once $phpmailer_path;

        $mail = new PHPMailer\PHPMailer\PHPMailer(true);

        // Server settings - Use working configuration
        $mail->isSMTP();
        $mail->Host       = SMTP_HOST;
        $mail->SMTPAuth   = true;
        $mail->Username   = SMTP_USERNAME;
        $mail->Password   = SMTP_PASSWORD;

        // Force SSL for port 465 or TLS for port 587
        if (SMTP_PORT == 465) {
            $mail->SMTPSecure = PHPMailer\PHPMailer\PHPMailer::ENCRYPTION_SMTPS; // SSL
        } else {
            $mail->SMTPSecure = PHPMailer\PHPMailer\PHPMailer::ENCRYPTION_STARTTLS; // TLS
        }
        $mail->Port       = SMTP_PORT;

        // Enable debug for troubleshooting email delivery (disabled for production)
        $mail->SMTPDebug  = 0; // Set to 0 to disable debugging
        // $mail->Debugoutput = function($str, $level) {
        //     error_log("SMTP debug (level $level): $str");
        // };

        // Recipients
        $mail->setFrom(FROM_EMAIL, FROM_NAME);
        $mail->addAddress($to);

        // Content
        $mail->isHTML($isHTML);
        $mail->Subject = $subject;
        $mail->Body    = $message;

        $mail->send();
        error_log("Email sent successfully to: $to using PHPMailer SMTP");
        return true;
    } catch (Exception $e) {
        error_log("PHPMailer SMTP error: " . $e->getMessage());
        return false;
    }
}

/**
 * Send email via SMTP with PDF attachment support
 */
function sendEmailSMTPWithAttachment($to, $subject, $message, $isHTML = true, $attachmentPath = null, $attachmentName = null) {
    try {
        // Load PHPMailer manually to avoid autoloader issues
        $phpmailer_path = __DIR__ . '/../vendor/phpmailer/phpmailer/src/PHPMailer.php';
        $exception_path = __DIR__ . '/../vendor/phpmailer/phpmailer/src/Exception.php';
        $smtp_path = __DIR__ . '/../vendor/phpmailer/phpmailer/src/SMTP.php';

        if (!file_exists($phpmailer_path)) {
            throw new Exception("PHPMailer not found at: $phpmailer_path");
        }

        require_once $exception_path;
        require_once $smtp_path;
        require_once $phpmailer_path;

        $mail = new PHPMailer\PHPMailer\PHPMailer(true);

        // Server settings - Use working configuration
        $mail->isSMTP();
        $mail->Host       = SMTP_HOST;
        $mail->SMTPAuth   = true;
        $mail->Username   = SMTP_USERNAME;
        $mail->Password   = SMTP_PASSWORD;

        // Force SSL for port 465 or TLS for port 587
        if (SMTP_PORT == 465) {
            $mail->SMTPSecure = PHPMailer\PHPMailer\PHPMailer::ENCRYPTION_SMTPS; // SSL
        } else {
            $mail->SMTPSecure = PHPMailer\PHPMailer\PHPMailer::ENCRYPTION_STARTTLS; // TLS
        }
        $mail->Port       = SMTP_PORT;

        // Enable debug for troubleshooting email delivery (disabled for production)
        $mail->SMTPDebug  = 0; // Set to 0 to disable debugging

        // Recipients
        $mail->setFrom(FROM_EMAIL, FROM_NAME);
        $mail->addAddress($to);

        // Content
        $mail->isHTML($isHTML);
        $mail->Subject = $subject;
        $mail->Body    = $message;

        // Add attachment if provided
        if ($attachmentPath && file_exists($attachmentPath)) {
            $mail->addAttachment($attachmentPath, $attachmentName ?: basename($attachmentPath));
            error_log("PDF attachment added: $attachmentPath");
        }

        $mail->send();
        error_log("Email with attachment sent successfully to: $to using PHPMailer SMTP");
        return true;
    } catch (Exception $e) {
        error_log("PHPMailer SMTP with attachment error: " . $e->getMessage());
        return false;
    }
}

/**
 * Generate OTP code
 */
function generateOTP($length = 6) {
    return str_pad(mt_rand(0, pow(10, $length) - 1), $length, '0', STR_PAD_LEFT);
}

/**
 * Store OTP in database
 */
function storeOTP($user_id, $otp, $expires_in_minutes = 10, $source = 'login') {
    try {
        $db = getDB();
        $expires_at = gmdate('Y-m-d H:i:s', time() + ($expires_in_minutes * 60));

        // Only delete existing OTPs if this is a login OTP (not admin-generated)
        if ($source === 'login') {
            // Delete any existing login OTPs for this user
            $db->query("DELETE FROM user_otps WHERE user_id = ? AND source = 'login'", [$user_id]);
        }

        // Insert new OTP with source tracking
        $sql = "INSERT INTO user_otps (user_id, otp_code, expires_at, source, created_at) VALUES (?, ?, ?, ?, NOW())";
        $db->query($sql, [$user_id, $otp, $expires_at, $source]);

        return true;
    } catch (Exception $e) {
        error_log("Error storing OTP: " . $e->getMessage());
        return false;
    }
}

/**
 * Verify OTP
 */
function verifyOTP($user_id, $otp) {
    try {
        $db = getDB();
        $sql = "SELECT id FROM user_otps WHERE user_id = ? AND otp_code = ? AND expires_at > UTC_TIMESTAMP() AND used = 0";
        $result = $db->query($sql, [$user_id, $otp]);
        
        if ($result && $result->num_rows > 0) {
            // Mark OTP as used
            $db->query("UPDATE user_otps SET used = 1, used_at = NOW() WHERE user_id = ? AND otp_code = ?", [$user_id, $otp]);
            return true;
        }
        
        return false;
    } catch (Exception $e) {
        error_log("Error verifying OTP: " . $e->getMessage());
        return false;
    }
}

/**
 * Get current OTP for user (for admin view)
 */
function getCurrentOTP($user_id) {
    try {
        $db = getDB();
        $sql = "SELECT otp_code, expires_at, source FROM user_otps WHERE user_id = ? AND expires_at > NOW() AND used = 0 ORDER BY created_at DESC LIMIT 1";
        $result = $db->query($sql, [$user_id]);

        if ($result && $result->num_rows > 0) {
            return $result->fetch_assoc();
        }

        return null;
    } catch (Exception $e) {
        error_log("Error getting current OTP: " . $e->getMessage());
        return null;
    }
}

/**
 * Get all active OTPs for user (for admin view)
 */
function getAllActiveOTPs($user_id) {
    try {
        $db = getDB();
        $sql = "SELECT otp_code, expires_at, source, created_at FROM user_otps WHERE user_id = ? AND expires_at > NOW() AND used = 0 ORDER BY created_at DESC";
        $result = $db->query($sql, [$user_id]);

        $otps = [];
        if ($result && $result->num_rows > 0) {
            while ($row = $result->fetch_assoc()) {
                $otps[] = $row;
            }
        }

        return $otps;
    } catch (Exception $e) {
        error_log("Error getting all active OTPs: " . $e->getMessage());
        return [];
    }
}

/**
 * Log email to file for localhost testing
 */
function logEmailToFile($email, $subject, $body, $type = 'general') {
    try {
        // Create logs directory if it doesn't exist
        $logs_dir = __DIR__ . '/../logs';
        if (!is_dir($logs_dir)) {
            mkdir($logs_dir, 0755, true);
        }

        $log_file = $logs_dir . '/email_' . $type . '.log';

        $log_entry = "=== EMAIL LOG ===\n";
        $log_entry .= "Date: " . date('Y-m-d H:i:s') . "\n";
        $log_entry .= "To: " . $email . "\n";
        $log_entry .= "Subject: " . $subject . "\n";
        $log_entry .= "Type: " . $type . "\n";
        $log_entry .= "Body (HTML):\n" . $body . "\n";
        $log_entry .= "Body (Text):\n" . strip_tags($body) . "\n";
        $log_entry .= "==================\n\n";

        file_put_contents($log_file, $log_entry, FILE_APPEND | LOCK_EX);

        // Also log to general email simulation log
        $general_log = $logs_dir . '/email_simulation.log';
        $simple_entry = "=== EMAIL SIMULATION ===\n";
        $simple_entry .= "To: $email\n";
        $simple_entry .= "Subject: $subject\n";
        $simple_entry .= "Time: " . date('Y-m-d H:i:s') . "\n";
        $simple_entry .= "Type: $type\n";
        $simple_entry .= "Message: " . strip_tags($body) . "\n";
        $simple_entry .= "========================\n\n";

        file_put_contents($general_log, $simple_entry, FILE_APPEND | LOCK_EX);

        error_log("Email logged to file for localhost testing: $email ($type)");
        return true;

    } catch (Exception $e) {
        error_log("Error logging email to file: " . $e->getMessage());
        return false;
    }
}

/**
 * Send pending approval email to new user
 */
function sendPendingApprovalEmail($email, $user_data) {
    try {
        // Include the new template system
        require_once __DIR__ . '/email_templates.php';

        // Create unique subject with timestamp to prevent email confusion
        $timestamp = date('H:i:s');
        $subject = "Registration Received - " . $timestamp;

        // Use the new template system
        $html_body = generatePendingApprovalEmailTemplate($user_data);

        // Validate email first
        if (!isValidEmail($email)) {
            error_log("Invalid email address for pending approval email: $email");
            return false;
        }

        // Force SMTP email sending (bypass localhost detection)
        $result = sendEmailSMTP($email, $subject, $html_body, true);

        // Also log for record keeping
        logEmailToFile($email, $subject, $html_body, 'pending_approval');

        return $result;

    } catch (Exception $e) {
        error_log("Pending approval email error: " . $e->getMessage());
        return false;
    }
}

/**
 * Send welcome email to new user
 */
function sendWelcomeEmail($email, $user_data) {
    try {
        // Include the new template system
        require_once __DIR__ . '/email_templates.php';

        // Create unique subject with timestamp to prevent email confusion
        $timestamp = date('H:i:s');
        $subject = "Welcome to Online Banking - " . $timestamp;

        // Use the new template system
        $html_body = generateWelcomeEmailTemplate($user_data);

        // Validate email first
        if (!isValidEmail($email)) {
            error_log("Invalid email address for welcome email: $email");
            return false;
        }

        // Force SMTP email sending (bypass localhost detection)
        $result = sendEmailSMTP($email, $subject, $html_body, true);

        // Also log for record keeping
        logEmailToFile($email, $subject, $html_body, 'welcome');

        return $result;

    } catch (Exception $e) {
        error_log("Welcome email error: " . $e->getMessage());
        return false;
    }
}

/**
 * Generate welcome email HTML
 */
function generateWelcomeEmailHTML($user_data, $bank_name, $appearance) {
    $logo_url = !empty($appearance['logo_url']) ? $appearance['logo_url'] : '';
    $primary_color = $appearance['color_scheme'] === 'blue' ? '#0054a6' :
                    ($appearance['color_scheme'] === 'green' ? '#2e7d32' : '#d32f2f');

    $html = '
    <!DOCTYPE html>
    <html>
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Welcome to ' . htmlspecialchars($bank_name) . '</title>
        <style>
            body { font-family: Arial, sans-serif; margin: 0; padding: 0; background-color: #f5f5f5; }
            .container { max-width: 600px; margin: 0 auto; background-color: white; }
            .header { background: linear-gradient(135deg, ' . $primary_color . ', ' . adjustBrightness($primary_color, -20) . '); color: white; padding: 30px; text-align: center; }
            .logo { font-size: 24px; font-weight: bold; margin-bottom: 10px; }
            .content { padding: 30px; }
            .welcome-card { background: #f8f9fa; border-radius: 10px; padding: 25px; margin: 20px 0; border-left: 4px solid ' . $primary_color . '; }
            .account-details { background: white; border: 1px solid #e9ecef; border-radius: 8px; padding: 20px; margin: 20px 0; }
            .detail-row { display: flex; justify-content: space-between; padding: 8px 0; border-bottom: 1px solid #f1f3f4; }
            .detail-row:last-child { border-bottom: none; }
            .detail-label { font-weight: bold; color: #495057; }
            .detail-value { color: #212529; }
            .highlight { background: ' . $primary_color . '; color: white; padding: 2px 8px; border-radius: 4px; font-weight: bold; }
            .footer { background: #f8f9fa; padding: 20px; text-align: center; color: #6c757d; font-size: 14px; }
            .button { background: ' . $primary_color . '; color: white; padding: 12px 24px; text-decoration: none; border-radius: 5px; display: inline-block; margin: 15px 0; }
            .security-note { background: #fff3cd; border: 1px solid #ffeaa7; border-radius: 5px; padding: 15px; margin: 20px 0; }
            @media (max-width: 600px) {
                .container { width: 100% !important; }
                .content { padding: 20px !important; }
                .detail-row { flex-direction: column; }
                .detail-label { margin-bottom: 5px; }
            }
        </style>
    </head>
    <body>
        <div class="container">
            <div class="header">
                <div class="logo">' . htmlspecialchars($bank_name) . '</div>
                <h1>Welcome to Your New Account!</h1>
                <p>Your banking journey starts here</p>
            </div>

            <div class="content">
                <div class="welcome-card">
                    <h2>🎉 Congratulations, ' . htmlspecialchars($user_data['first_name']) . '!</h2>
                    <p>Your account has been successfully created and is ready to use. We\'re excited to have you as part of the ' . htmlspecialchars($bank_name) . ' family.</p>
                </div>

                <h3>📋 Your Account Details</h3>
                <div class="account-details">
                    <div class="detail-row">
                        <span class="detail-label">Account Holder:</span>
                        <span class="detail-value">' . htmlspecialchars($user_data['first_name'] . ' ' . $user_data['last_name']) . '</span>
                    </div>
                    <div class="detail-row">
                        <span class="detail-label">Account Number:</span>
                        <span class="detail-value highlight">' . htmlspecialchars($user_data['account_number']) . '</span>
                    </div>
                    <div class="detail-row">
                        <span class="detail-label">Account Type:</span>
                        <span class="detail-value">' . htmlspecialchars(ucfirst($user_data['account_type'])) . '</span>
                    </div>
                    <div class="detail-row">
                        <span class="detail-label">Currency:</span>
                        <span class="detail-value">' . htmlspecialchars($user_data['currency']) . '</span>
                    </div>
                    <div class="detail-row">
                        <span class="detail-label">Initial Balance:</span>
                        <span class="detail-value">$' . number_format($user_data['balance'], 2) . '</span>
                    </div>
                    <div class="detail-row">
                        <span class="detail-label">Username:</span>
                        <span class="detail-value">' . htmlspecialchars($user_data['username']) . '</span>
                    </div>
                    <div class="detail-row">
                        <span class="detail-label">Email:</span>
                        <span class="detail-value">' . htmlspecialchars($user_data['email']) . '</span>
                    </div>
                    <div class="detail-row">
                        <span class="detail-label">Status:</span>
                        <span class="detail-value">✅ ' . htmlspecialchars(ucfirst($user_data['status'])) . '</span>
                    </div>
                </div>

                <div class="security-note">
                    <h4>🔐 Security Information</h4>
                    <ul>
                        <li>Your password has been set as provided during registration</li>
                        <li>Please log in and change your password if needed</li>
                        <li>Two-factor authentication (OTP) is enabled for your security</li>
                        <li>Never share your login credentials with anyone</li>
                    </ul>
                </div>

                <div style="text-align: center;">
                    <a href="' . url('login.php') . '" class="button">🚀 Access Your Account</a>
                </div>

                <h3>🎯 What\'s Next?</h3>
                <ul>
                    <li>✅ <strong>Log in</strong> to your account using your username and password</li>
                    <li>✅ <strong>Complete your profile</strong> by adding additional information</li>
                    <li>✅ <strong>Explore features</strong> like transfers, virtual cards, and more</li>
                    <li>✅ <strong>Contact support</strong> if you need any assistance</li>
                </ul>

                <div style="background: #e7f3ff; padding: 15px; border-radius: 5px; margin: 20px 0;">
                    <h4>📞 Need Help?</h4>
                    <p>Our support team is here to help you get started:</p>
                    <ul>
                        <li>📧 Email: support@' . strtolower(str_replace(' ', '', $bank_name)) . '.com</li>
                        <li>🌐 Visit: ' . url('') . '</li>
                        <li>💬 Live chat available 24/7</li>
                    </ul>
                </div>
            </div>

            <div class="footer">
                <p>Thank you for choosing ' . htmlspecialchars($bank_name) . '</p>
                <p>This email was sent to ' . htmlspecialchars($user_data['email']) . '</p>
                <p>&copy; ' . date('Y') . ' ' . htmlspecialchars($bank_name) . '. All rights reserved.</p>
            </div>
        </div>
    </body>
    </html>';

    return $html;
}

/**
 * Adjust color brightness
 */
function adjustBrightness($hex, $percent) {
    $hex = ltrim($hex, '#');
    $r = hexdec(substr($hex, 0, 2));
    $g = hexdec(substr($hex, 2, 2));
    $b = hexdec(substr($hex, 4, 2));

    $r = max(0, min(255, $r + ($r * $percent / 100)));
    $g = max(0, min(255, $g + ($g * $percent / 100)));
    $b = max(0, min(255, $b + ($b * $percent / 100)));

    return sprintf("#%02x%02x%02x", $r, $g, $b);
}

/**
 * Check if we're running on localhost
 */
function isLocalhost() {
    $localhost_indicators = [
        'localhost',
        '127.0.0.1',
        '::1',
        '0.0.0.0'
    ];

    $server_name = $_SERVER['SERVER_NAME'] ?? '';
    $http_host = $_SERVER['HTTP_HOST'] ?? '';

    foreach ($localhost_indicators as $indicator) {
        if (strpos($server_name, $indicator) !== false || strpos($http_host, $indicator) !== false) {
            return true;
        }
    }

    return false;
}

/**
 * Send OTP email
 */
function sendOTPEmail($email, $otp, $user_name) {
    try {
        // Include the new template system
        require_once __DIR__ . '/email_templates.php';

        // Create unique subject with timestamp to prevent email confusion
        $timestamp = date('H:i:s');
        $subject = "Login Verification Code - " . $timestamp;

        // Prepare user data for template
        $user_data = [
            'first_name' => $user_name,
            'last_name' => '',
            'email' => $email
        ];

        // Use the new template system
        $message = generateOTPEmailTemplate($user_data, $otp, 10);

        // Force SMTP email sending (bypass localhost detection)
        $result = sendEmailSMTP($email, $subject, $message, true);

        // Log email attempt for debugging
        error_log("OTP email attempt - Email: $email, Subject: $subject, Result: " . ($result ? 'SUCCESS' : 'FAILED'));

        return $result;

    } catch (Exception $e) {
        error_log("OTP email error: " . $e->getMessage());
        return false;
    }
}
?>
