<?php
/**
 * Super Admin Dashboard
 * Main dashboard for super administrators
 */

$page_title = 'Dashboard';
$page_subtitle = 'System overview and quick actions';

// Include header
include 'includes/header.php';

// Get dashboard statistics
$stats = getSuperAdminStats();

// Get recent audit logs (reduced to 5 for better dashboard performance)
$recent_logs = getSuperAdminAuditLogs(5);

// Log dashboard access
logSuperAdminAction('dashboard_access', 'Super admin accessed dashboard');
?>

<!-- Dashboard Stats Cards -->
<div class="row mb-4">
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-0 shadow-sm h-100">
            <div class="card-body">
                <div class="d-flex align-items-center">
                    <div class="flex-shrink-0">
                        <div class="bg-primary bg-gradient rounded-3 p-3">
                            <i class="fas fa-users text-white fa-2x"></i>
                        </div>
                    </div>
                    <div class="flex-grow-1 ms-3">
                        <h6 class="text-muted mb-1">Total Users</h6>
                        <h3 class="mb-0"><?php echo number_format($stats['total_users']); ?></h3>
                        <small class="text-success">
                            <i class="fas fa-arrow-up"></i> 
                            <?php echo number_format($stats['active_users']); ?> active
                        </small>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-0 shadow-sm h-100">
            <div class="card-body">
                <div class="d-flex align-items-center">
                    <div class="flex-shrink-0">
                        <div class="bg-success bg-gradient rounded-3 p-3">
                            <i class="fas fa-exchange-alt text-white fa-2x"></i>
                        </div>
                    </div>
                    <div class="flex-grow-1 ms-3">
                        <h6 class="text-muted mb-1">Transactions Today</h6>
                        <h3 class="mb-0"><?php echo number_format($stats['transactions_today']); ?></h3>
                        <small class="text-muted">
                            <i class="fas fa-calendar-day"></i> 
                            Last 24 hours
                        </small>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-0 shadow-sm h-100">
            <div class="card-body">
                <div class="d-flex align-items-center">
                    <div class="flex-shrink-0">
                        <div class="bg-warning bg-gradient rounded-3 p-3">
                            <i class="fas fa-users-cog text-white fa-2x"></i>
                        </div>
                    </div>
                    <div class="flex-grow-1 ms-3">
                        <h6 class="text-muted mb-1">Total Admins</h6>
                        <h3 class="mb-0"><?php echo number_format($stats['total_admins']); ?></h3>
                        <small class="text-muted">
                            <i class="fas fa-shield-alt"></i>
                            Admin Accounts
                        </small>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-0 shadow-sm h-100">
            <div class="card-body">
                <div class="d-flex align-items-center">
                    <div class="flex-shrink-0">
                        <div class="bg-info bg-gradient rounded-3 p-3">
                            <i class="fas fa-clipboard-list text-white fa-2x"></i>
                        </div>
                    </div>
                    <div class="flex-grow-1 ms-3">
                        <h6 class="text-muted mb-1">Audit Entries</h6>
                        <h3 class="mb-0"><?php echo number_format($stats['audit_entries_24h']); ?></h3>
                        <small class="text-muted">
                            <i class="fas fa-clock"></i> 
                            Last 24 hours
                        </small>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Quick Actions -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card border-0 shadow-sm">
            <div class="card-header bg-white border-0 py-3">
                <h5 class="card-title mb-0">
                    <i class="fas fa-bolt text-warning"></i> Quick Actions
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-lg-3 col-md-6 mb-3">
                        <a href="system-settings.php" class="btn btn-outline-primary w-100 h-100 d-flex flex-column align-items-center justify-content-center py-3">
                            <i class="fas fa-cogs fa-2x mb-2"></i>
                            <span>System Settings</span>
                        </a>
                    </div>
                    <div class="col-lg-3 col-md-6 mb-3">
                        <a href="appearance-settings.php" class="btn btn-outline-info w-100 h-100 d-flex flex-column align-items-center justify-content-center py-3">
                            <i class="fas fa-palette fa-2x mb-2"></i>
                            <span>Appearance Settings</span>
                        </a>
                    </div>
                    <div class="col-lg-3 col-md-6 mb-3">
                        <a href="email-templates.php" class="btn btn-outline-success w-100 h-100 d-flex flex-column align-items-center justify-content-center py-3">
                            <i class="fas fa-envelope fa-2x mb-2"></i>
                            <span>Email Templates</span>
                        </a>
                    </div>
                    <div class="col-lg-3 col-md-6 mb-3">
                        <a href="smtp-config.php" class="btn btn-outline-warning w-100 h-100 d-flex flex-column align-items-center justify-content-center py-3">
                            <i class="fas fa-server fa-2x mb-2"></i>
                            <span>SMTP Config</span>
                        </a>
                    </div>
                    <div class="col-lg-3 col-md-6 mb-3">
                        <a href="user-management.php" class="btn btn-outline-info w-100 h-100 d-flex flex-column align-items-center justify-content-center py-3">
                            <i class="fas fa-users fa-2x mb-2"></i>
                            <span>User Management</span>
                        </a>
                    </div>
                </div>
                <div class="row">
                    <div class="col-lg-3 col-md-6 mb-3">
                        <a href="setup-2fa.php" class="btn btn-outline-danger w-100 h-100 d-flex flex-column align-items-center justify-content-center py-3">
                            <i class="fas fa-mobile-alt fa-2x mb-2"></i>
                            <span>2FA Setup</span>
                            <?php
                            $is_2fa_enabled = false;
                            try {
                                if (function_exists('isSuperAdmin2FAEnabled')) {
                                    $is_2fa_enabled = isSuperAdmin2FAEnabled();
                                }
                            } catch (Exception $e) {
                                // 2FA system not set up yet
                            }
                            ?>
                            <?php if ($is_2fa_enabled): ?>
                                <small class="text-success mt-1"><i class="fas fa-check"></i> Enabled</small>
                            <?php else: ?>
                                <small class="text-warning mt-1"><i class="fas fa-exclamation-triangle"></i> Not Setup</small>
                            <?php endif; ?>
                        </a>
                    </div>
                    <div class="col-lg-3 col-md-6 mb-3">
                        <a href="security.php" class="btn btn-outline-secondary w-100 h-100 d-flex flex-column align-items-center justify-content-center py-3">
                            <i class="fas fa-shield-alt fa-2x mb-2"></i>
                            <span>Security Center</span>
                        </a>
                    </div>
                    <div class="col-lg-3 col-md-6 mb-3">
                        <a href="audit-logs.php" class="btn btn-outline-dark w-100 h-100 d-flex flex-column align-items-center justify-content-center py-3">
                            <i class="fas fa-clipboard-list fa-2x mb-2"></i>
                            <span>Audit Logs</span>
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Recent Activity and System Status -->
<div class="row">
    <!-- Recent Audit Logs -->
    <div class="col-lg-8 mb-4">
        <div class="card border-0 shadow-sm h-100">
            <div class="card-header bg-white border-0 py-3 d-flex justify-content-between align-items-center">
                <h5 class="card-title mb-0">
                    <i class="fas fa-history text-info"></i> Recent Activity
                </h5>
                <a href="audit-logs.php" class="btn btn-sm btn-outline-primary">
                    View All <i class="fas fa-arrow-right"></i>
                </a>
            </div>
            <div class="card-body p-0">
                <?php if (empty($recent_logs)): ?>
                    <div class="text-center py-4">
                        <i class="fas fa-clipboard-list fa-3x text-muted mb-3"></i>
                        <p class="text-muted">No recent activity found.</p>
                    </div>
                <?php else: ?>
                    <div class="list-group list-group-flush">
                        <?php foreach ($recent_logs as $log): ?>
                            <div class="list-group-item border-0 py-3">
                                <div class="d-flex align-items-start">
                                    <div class="flex-shrink-0 me-3">
                                        <?php
                                        $icon_class = 'fas fa-info-circle text-info';
                                        switch ($log['action']) {
                                            case 'login':
                                                $icon_class = 'fas fa-sign-in-alt text-success';
                                                break;
                                            case 'logout':
                                                $icon_class = 'fas fa-sign-out-alt text-warning';
                                                break;
                                            case 'login_failed':
                                                $icon_class = 'fas fa-exclamation-triangle text-danger';
                                                break;
                                            case 'settings_update':
                                                $icon_class = 'fas fa-cogs text-primary';
                                                break;
                                        }
                                        ?>
                                        <i class="<?php echo $icon_class; ?>"></i>
                                    </div>
                                    <div class="flex-grow-1">
                                        <h6 class="mb-1"><?php echo htmlspecialchars($log['description']); ?></h6>
                                        <p class="mb-1 text-muted small">
                                            <i class="fas fa-user"></i> <?php echo htmlspecialchars($log['username']); ?>
                                            <i class="fas fa-map-marker-alt ms-2"></i> <?php echo htmlspecialchars($log['ip_address']); ?>
                                        </p>
                                        <small class="text-muted">
                                            <i class="fas fa-clock"></i> 
                                            <?php echo date('M j, Y g:i A', strtotime($log['created_at'])); ?>
                                        </small>
                                    </div>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
    
    <!-- System Status -->
    <div class="col-lg-4 mb-4">
        <div class="card border-0 shadow-sm h-100">
            <div class="card-header bg-white border-0 py-3">
                <h5 class="card-title mb-0">
                    <i class="fas fa-heartbeat text-success"></i> System Status
                </h5>
            </div>
            <div class="card-body">
                <!-- Server Status -->
                <div class="d-flex justify-content-between align-items-center mb-3">
                    <span>Server Status</span>
                    <span class="badge bg-success">
                        <i class="fas fa-check"></i> Online
                    </span>
                </div>
                
                <!-- Database Status -->
                <div class="d-flex justify-content-between align-items-center mb-3">
                    <span>Database</span>
                    <span class="badge bg-success">
                        <i class="fas fa-check"></i> Connected
                    </span>
                </div>
                
                <!-- Email System -->
                <div class="d-flex justify-content-between align-items-center mb-3">
                    <span>Email System</span>
                    <span class="badge bg-success">
                        <i class="fas fa-check"></i> Operational
                    </span>
                </div>
                
                <!-- Session Info -->
                <hr>
                <h6 class="mb-3">Session Information</h6>
                <div class="small text-muted">
                    <div class="d-flex justify-content-between mb-2">
                        <span>Login Time:</span>
                        <span><?php echo date('g:i A', $session_info['login_time']); ?></span>
                    </div>
                    <div class="d-flex justify-content-between mb-2">
                        <span>Duration:</span>
                        <span><?php echo formatDuration($session_info['session_duration']); ?></span>
                    </div>
                    <div class="d-flex justify-content-between mb-2">
                        <span>IP Address:</span>
                        <span><?php echo htmlspecialchars($session_info['ip_address']); ?></span>
                    </div>
                </div>
                
                <!-- Quick System Actions -->
                <hr>
                <div class="d-grid gap-2">
                    <a href="security.php" class="btn btn-sm btn-outline-danger">
                        <i class="fas fa-shield-alt"></i> Security Center
                    </a>
                    <a href="audit-logs.php" class="btn btn-sm btn-outline-info">
                        <i class="fas fa-clipboard-list"></i> View Audit Logs
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- System Alerts (if any) -->
<?php
// Check for any system alerts or maintenance notices
$maintenance_mode = false; // This would come from settings
if ($maintenance_mode):
?>
<div class="row">
    <div class="col-12">
        <div class="alert alert-warning alert-dismissible fade show" role="alert">
            <i class="fas fa-exclamation-triangle"></i>
            <strong>Maintenance Mode Active:</strong> The system is currently in maintenance mode. Users cannot access the platform.
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    </div>
</div>
<?php endif; ?>

<?php
// Include footer
include 'includes/footer.php';
?>
