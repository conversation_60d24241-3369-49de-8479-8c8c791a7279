<?php
/**
 * Test Authentication Redirects
 * Verify that all authentication redirects point to the correct locations
 */

require_once '../../config/config.php';

$page_title = 'Authentication Redirect Test';

// Test results
$tests = [];

// Test 1: Check main index.php redirect logic
ob_start();
$test_session = [];
$test_session['user_id'] = 1;
$test_session['is_admin'] = false;

// Simulate the redirect logic from index.php
if (isset($test_session['user_id'])) {
    if (isset($test_session['is_admin']) && $test_session['is_admin']) {
        $redirect_url = url('admin/');
    } else {
        $redirect_url = url('user/dashboard/');
    }
} else {
    $redirect_url = url('login.php');
}
ob_end_clean();

$tests[] = [
    'name' => 'Main Index - Regular User',
    'expected' => url('user/dashboard/'),
    'actual' => $redirect_url,
    'status' => ($redirect_url === url('user/dashboard/')) ? 'PASS' : 'FAIL'
];

// Test 2: Check admin user redirect
ob_start();
$test_session_admin = [];
$test_session_admin['user_id'] = 1;
$test_session_admin['is_admin'] = true;

if (isset($test_session_admin['user_id'])) {
    if (isset($test_session_admin['is_admin']) && $test_session_admin['is_admin']) {
        $redirect_url_admin = url('admin/');
    } else {
        $redirect_url_admin = url('user/dashboard/');
    }
} else {
    $redirect_url_admin = url('login.php');
}
ob_end_clean();

$tests[] = [
    'name' => 'Main Index - Admin User',
    'expected' => url('admin/'),
    'actual' => $redirect_url_admin,
    'status' => ($redirect_url_admin === url('admin/')) ? 'PASS' : 'FAIL'
];

// Test 3: Check not logged in redirect
ob_start();
$test_session_none = [];

if (isset($test_session_none['user_id'])) {
    if (isset($test_session_none['is_admin']) && $test_session_none['is_admin']) {
        $redirect_url_none = url('admin/');
    } else {
        $redirect_url_none = url('user/dashboard/');
    }
} else {
    $redirect_url_none = url('login.php');
}
ob_end_clean();

$tests[] = [
    'name' => 'Main Index - Not Logged In',
    'expected' => url('login.php'),
    'actual' => $redirect_url_none,
    'status' => ($redirect_url_none === url('login.php')) ? 'PASS' : 'FAIL'
];

// Test 4: Check if old dashboard directory exists
$old_dashboard_exists = is_dir(__DIR__ . '/../../dashboard/');
$tests[] = [
    'name' => 'Old Dashboard Directory Check',
    'expected' => 'Should exist (for backward compatibility)',
    'actual' => $old_dashboard_exists ? 'Exists' : 'Does not exist',
    'status' => $old_dashboard_exists ? 'INFO' : 'WARNING'
];

// Test 5: Check if new user dashboard exists
$new_dashboard_exists = is_dir(__DIR__ . '/../../user/dashboard/');
$tests[] = [
    'name' => 'New User Dashboard Directory Check',
    'expected' => 'Should exist',
    'actual' => $new_dashboard_exists ? 'Exists' : 'Does not exist',
    'status' => $new_dashboard_exists ? 'PASS' : 'FAIL'
];

// Test 6: Check if redirect.php exists in old dashboard
$redirect_exists = file_exists(__DIR__ . '/../../dashboard/redirect.php');
$tests[] = [
    'name' => 'Dashboard Redirect File Check',
    'expected' => 'Should exist for backward compatibility',
    'actual' => $redirect_exists ? 'Exists' : 'Does not exist',
    'status' => $redirect_exists ? 'PASS' : 'WARNING'
];

?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $page_title; ?></title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        .test-pass { color: #28a745; }
        .test-fail { color: #dc3545; }
        .test-warning { color: #ffc107; }
        .test-info { color: #17a2b8; }
    </style>
</head>
<body>
    <div class="container mt-5">
        <div class="row">
            <div class="col-md-12">
                <h1><i class="fas fa-route"></i> Authentication Redirect Test</h1>
                <p class="lead">Testing authentication redirect logic after migration to user/dashboard/</p>
                
                <div class="card">
                    <div class="card-header">
                        <h5>Test Results</h5>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-striped">
                                <thead>
                                    <tr>
                                        <th>Test</th>
                                        <th>Expected</th>
                                        <th>Actual</th>
                                        <th>Status</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($tests as $test): ?>
                                    <tr>
                                        <td><?php echo htmlspecialchars($test['name']); ?></td>
                                        <td><code><?php echo htmlspecialchars($test['expected']); ?></code></td>
                                        <td><code><?php echo htmlspecialchars($test['actual']); ?></code></td>
                                        <td>
                                            <span class="test-<?php echo strtolower($test['status']); ?>">
                                                <i class="fas fa-<?php 
                                                    echo $test['status'] === 'PASS' ? 'check' : 
                                                        ($test['status'] === 'FAIL' ? 'times' : 
                                                        ($test['status'] === 'WARNING' ? 'exclamation-triangle' : 'info')); 
                                                ?>"></i>
                                                <?php echo $test['status']; ?>
                                            </span>
                                        </td>
                                    </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>

                <div class="card mt-4">
                    <div class="card-header">
                        <h5>Manual Test Links</h5>
                    </div>
                    <div class="card-body">
                        <p>Use these links to manually test the authentication flow:</p>
                        <div class="row">
                            <div class="col-md-6">
                                <h6>Authentication Pages</h6>
                                <ul class="list-group">
                                    <li class="list-group-item">
                                        <a href="<?php echo url('login.php'); ?>" target="_blank">
                                            <i class="fas fa-sign-in-alt"></i> Login Page
                                        </a>
                                    </li>
                                    <li class="list-group-item">
                                        <a href="<?php echo url('auth/verify-otp.php'); ?>" target="_blank">
                                            <i class="fas fa-key"></i> OTP Verification
                                        </a>
                                    </li>
                                    <li class="list-group-item">
                                        <a href="<?php echo url('index.php'); ?>" target="_blank">
                                            <i class="fas fa-home"></i> Main Index
                                        </a>
                                    </li>
                                </ul>
                            </div>
                            <div class="col-md-6">
                                <h6>Dashboard Pages</h6>
                                <ul class="list-group">
                                    <li class="list-group-item">
                                        <a href="<?php echo url('user/dashboard/'); ?>" target="_blank">
                                            <i class="fas fa-tachometer-alt"></i> New User Dashboard
                                        </a>
                                    </li>
                                    <li class="list-group-item">
                                        <a href="<?php echo url('dashboard/'); ?>" target="_blank">
                                            <i class="fas fa-redirect"></i> Old Dashboard (should redirect)
                                        </a>
                                    </li>
                                    <li class="list-group-item">
                                        <a href="<?php echo url('admin/'); ?>" target="_blank">
                                            <i class="fas fa-user-shield"></i> Admin Dashboard
                                        </a>
                                    </li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="alert alert-info mt-4">
                    <h6><i class="fas fa-info-circle"></i> Summary</h6>
                    <p><strong>Changes Made:</strong></p>
                    <ul>
                        <li>Updated <code>auth/includes/login_logic.php</code> - redirects to <code>user/dashboard/</code></li>
                        <li>Updated <code>auth/verify-otp.php</code> - redirects to <code>user/dashboard/</code></li>
                        <li>Updated <code>index.php</code> - redirects to <code>user/dashboard/</code></li>
                        <li>Updated production files with same changes</li>
                        <li>Old dashboard directory kept for backward compatibility with redirect</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
