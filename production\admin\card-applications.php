<?php
require_once '../config/config.php';
requireAdmin();

$page_title = 'Card Applications';

// Handle application approval/rejection
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action'])) {
    try {
        $db = getDB();
        $application_id = intval($_POST['application_id']);
        $action = $_POST['action'];
        $review_notes = trim($_POST['review_notes'] ?? '');
        
        if (!in_array($action, ['approve', 'reject'])) {
            throw new Exception("Invalid action.");
        }
        
        // Get application details
        $app_query = "SELECT ca.*, a.first_name, a.last_name, a.username 
                      FROM card_applications ca 
                      LEFT JOIN accounts a ON ca.account_id = a.id 
                      WHERE ca.application_id = ? AND ca.status = 'pending'";
        $app_result = $db->query($app_query, [$application_id]);
        $application = $app_result->fetch_assoc();
        
        if (!$application) {
            throw new Exception("Application not found or already processed.");
        }
        
        $db->query("START TRANSACTION");
        
        if ($action === 'approve') {
            // Generate card details
            $card_number = generateCardNumber();
            $cvv = str_pad(rand(100, 999), 3, '0', STR_PAD_LEFT);
            $expiry_date = date('Y-m-d', strtotime('+3 years'));
            $card_holder_name = strtoupper($application['first_name'] . ' ' . $application['last_name']);
            
            // Create virtual card
            $insert_card = "INSERT INTO virtual_cards (
                account_id, card_number, card_holder_name, expiry_date, cvv, 
                card_balance, daily_limit, monthly_limit, status, approved_by, approved_at
            ) VALUES (?, ?, ?, ?, ?, 0.00, ?, 50000.00, 'active', ?, NOW())";
            
            $card_id = $db->insert($insert_card, [
                $application['account_id'], $card_number, $card_holder_name, $expiry_date, $cvv,
                $application['requested_limit'], $_SESSION['user_id']
            ]);
            
            if (!$card_id) {
                throw new Exception("Failed to create virtual card.");
            }
            
            // Update application status
            $update_app = "UPDATE card_applications SET 
                          status = 'approved', reviewed_at = NOW(), reviewed_by = ?, 
                          review_notes = ?, card_id = ? 
                          WHERE application_id = ?";
            $db->query($update_app, [$_SESSION['user_id'], $review_notes, $card_id, $application_id]);
            
            $success = "Application approved successfully! Virtual card created with ID: $card_id";
            
        } else { // reject
            // Update application status
            $update_app = "UPDATE card_applications SET 
                          status = 'rejected', reviewed_at = NOW(), reviewed_by = ?, 
                          review_notes = ? 
                          WHERE application_id = ?";
            $db->query($update_app, [$_SESSION['user_id'], $review_notes, $application_id]);
            
            $success = "Application rejected successfully.";
        }
        
        $db->query("COMMIT");
        
    } catch (Exception $e) {
        if (isset($db)) {
            $db->query("ROLLBACK");
        }
        $error = $e->getMessage();
    }
}

// Get applications with pagination
$records_per_page = 25;
$current_page = intval($_GET['page'] ?? 1);
$offset = ($current_page - 1) * $records_per_page;

$filter_status = $_GET['status'] ?? '';
$where_conditions = [];
$params = [];

if (!empty($filter_status)) {
    $where_conditions[] = "ca.status = ?";
    $params[] = $filter_status;
}

$where_clause = !empty($where_conditions) ? "WHERE " . implode(" AND ", $where_conditions) : "";

try {
    $db = getDB();
    
    // Get total count
    $count_query = "SELECT COUNT(*) as total FROM card_applications ca $where_clause";
    $count_result = $db->query($count_query, $params);
    $total_records = $count_result->fetch_assoc()['total'];
    $total_pages = ceil($total_records / $records_per_page);
    
    // Get applications
    $apps_query = "SELECT ca.*, 
                   a.first_name, a.last_name, a.username, a.account_number,
                   admin.first_name as admin_first_name, admin.last_name as admin_last_name
                   FROM card_applications ca 
                   LEFT JOIN accounts a ON ca.account_id = a.id 
                   LEFT JOIN accounts admin ON ca.reviewed_by = admin.id 
                   $where_clause
                   ORDER BY ca.applied_at DESC 
                   LIMIT $records_per_page OFFSET $offset";
    
    $apps_result = $db->query($apps_query, $params);
    $applications = [];
    while ($row = $apps_result->fetch_assoc()) {
        $applications[] = $row;
    }
    
} catch (Exception $e) {
    $error = "Failed to load applications: " . $e->getMessage();
    $applications = [];
    $total_records = 0;
    $total_pages = 0;
}

// Helper function to generate card number
function generateCardNumber() {
    $prefix = '4000';
    $middle = '';
    for ($i = 0; $i < 8; $i++) {
        $middle .= rand(0, 9);
    }
    
    $number = $prefix . $middle;
    $sum = 0;
    $alternate = false;
    
    for ($i = strlen($number) - 1; $i >= 0; $i--) {
        $digit = intval($number[$i]);
        if ($alternate) {
            $digit *= 2;
            if ($digit > 9) {
                $digit = ($digit % 10) + 1;
            }
        }
        $sum += $digit;
        $alternate = !$alternate;
    }
    
    $checkDigit = (10 - ($sum % 10)) % 10;
    return $number . $checkDigit;
}

include 'includes/admin-header.php';
?>

<!-- Breadcrumb -->
<nav aria-label="breadcrumb" style="margin-bottom: 1rem;">
    <ol class="breadcrumb">
        <li class="breadcrumb-item"><a href="index.php">Admin</a></li>
        <li class="breadcrumb-item"><a href="virtual-cards.php">Virtual Cards</a></li>
        <li class="breadcrumb-item active" aria-current="page">Applications</li>
    </ol>
</nav>

<!-- Success/Error Messages -->
<?php if (isset($success)): ?>
<div class="alert alert-success" role="alert">
    <div class="d-flex">
        <div><i class="fas fa-check-circle me-2"></i></div>
        <div>
            <h4 class="alert-title">Success!</h4>
            <div class="text-muted"><?php echo htmlspecialchars($success); ?></div>
        </div>
    </div>
</div>
<?php endif; ?>

<?php if (isset($error)): ?>
<div class="alert alert-danger" role="alert">
    <div class="d-flex">
        <div><i class="fas fa-exclamation-circle me-2"></i></div>
        <div>
            <h4 class="alert-title">Error!</h4>
            <div class="text-muted"><?php echo htmlspecialchars($error); ?></div>
        </div>
    </div>
</div>
<?php endif; ?>

<!-- Filters -->
<div class="row row-cards mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h3 class="card-title">
                    <i class="fas fa-filter me-2"></i>
                    Application Filters
                </h3>
            </div>
            <div class="card-body">
                <form method="GET" action="" class="row g-2">
                    <div class="col-md-3">
                        <label class="form-label">Status</label>
                        <select name="status" class="form-select form-select-sm">
                            <option value="">All Status</option>
                            <option value="pending" <?php echo $filter_status === 'pending' ? 'selected' : ''; ?>>Pending</option>
                            <option value="approved" <?php echo $filter_status === 'approved' ? 'selected' : ''; ?>>Approved</option>
                            <option value="rejected" <?php echo $filter_status === 'rejected' ? 'selected' : ''; ?>>Rejected</option>
                            <option value="cancelled" <?php echo $filter_status === 'cancelled' ? 'selected' : ''; ?>>Cancelled</option>
                        </select>
                    </div>
                    
                    <div class="col-md-3">
                        <label class="form-label">&nbsp;</label>
                        <div class="btn-group d-block" role="group">
                            <button type="submit" class="btn btn-primary btn-sm">
                                <i class="fas fa-search me-1"></i>
                                Filter
                            </button>
                            <a href="card-applications.php" class="btn btn-outline-secondary btn-sm">
                                <i class="fas fa-times me-1"></i>
                                Clear
                            </a>
                        </div>
                    </div>
                    
                    <div class="col-md-6">
                        <label class="form-label">&nbsp;</label>
                        <div class="btn-group" role="group">
                            <a href="virtual-cards.php" class="btn btn-outline-primary btn-sm">
                                <i class="fas fa-credit-card me-1"></i>
                                All Cards
                            </a>
                            <a href="generate-card.php" class="btn btn-success btn-sm">
                                <i class="fas fa-plus me-1"></i>
                                Generate Card
                            </a>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- Applications Table -->
<div class="row row-cards">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h3 class="card-title">
                    <i class="fas fa-file-alt me-2"></i>
                    Card Applications
                    <span class="badge bg-secondary ms-2"><?php echo number_format($total_records); ?> total</span>
                </h3>
            </div>
            <div class="card-body p-0">
                <?php if (!empty($applications)): ?>
                <div class="table-responsive">
                    <table class="table table-vcenter card-table table-sm">
                        <thead>
                            <tr>
                                <th class="w-1">#</th>
                                <th>Applicant</th>
                                <th>Type</th>
                                <th>Requested Limit</th>
                                <th>Purpose</th>
                                <th>Status</th>
                                <th>Applied Date</th>
                                <th class="w-1">Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php
                            $row_number = ($current_page - 1) * $records_per_page + 1;
                            foreach ($applications as $app):
                            ?>
                            <tr>
                                <td>
                                    <span class="text-muted"><?php echo $row_number++; ?></span>
                                </td>

                                <td>
                                    <div class="d-flex align-items-center">
                                        <div class="avatar avatar-xs me-2">
                                            <?php echo strtoupper(substr($app['first_name'] ?? 'U', 0, 1) . substr($app['last_name'] ?? 'U', 0, 1)); ?>
                                        </div>
                                        <div>
                                            <div class="fw-bold" style="font-size: 0.85rem;"><?php echo htmlspecialchars(($app['first_name'] ?? 'Unknown') . ' ' . ($app['last_name'] ?? 'User')); ?></div>
                                            <small class="text-muted">@<?php echo htmlspecialchars($app['username'] ?? 'unknown'); ?></small>
                                        </div>
                                    </div>
                                </td>

                                <td>
                                    <span class="badge bg-primary badge-sm">
                                        <?php echo ucfirst($app['application_type']); ?>
                                    </span>
                                </td>

                                <td>
                                    <span class="fw-bold text-primary">
                                        <?php echo formatCurrency($app['requested_limit']); ?>
                                    </span>
                                </td>

                                <td>
                                    <div class="text-truncate" style="max-width: 150px;" title="<?php echo htmlspecialchars($app['purpose'] ?? 'Not specified'); ?>">
                                        <?php echo htmlspecialchars($app['purpose'] ?? 'Not specified'); ?>
                                    </div>
                                </td>

                                <td>
                                    <?php
                                    $status_colors = [
                                        'pending' => 'warning',
                                        'approved' => 'success',
                                        'rejected' => 'danger',
                                        'cancelled' => 'secondary'
                                    ];
                                    $status_color = $status_colors[$app['status']] ?? 'secondary';
                                    ?>
                                    <span class="badge bg-<?php echo $status_color; ?> badge-sm">
                                        <?php echo ucfirst($app['status']); ?>
                                    </span>
                                </td>

                                <td>
                                    <div class="d-flex flex-column">
                                        <div class="text-muted" style="font-size: 0.85rem;"><?php echo date('M j, Y', strtotime($app['applied_at'])); ?></div>
                                        <small class="text-muted"><?php echo date('g:i A', strtotime($app['applied_at'])); ?></small>
                                    </div>
                                </td>

                                <td>
                                    <div class="btn-list">
                                        <button type="button" class="btn btn-sm btn-outline-primary" onclick="viewApplication(<?php echo htmlspecialchars(json_encode($app)); ?>)" title="View Details">
                                            <i class="fas fa-eye"></i>
                                        </button>
                                        <?php if ($app['status'] === 'pending'): ?>
                                        <button type="button" class="btn btn-sm btn-outline-success" onclick="reviewApplication(<?php echo $app['application_id']; ?>, 'approve')" title="Approve">
                                            <i class="fas fa-check"></i>
                                        </button>
                                        <button type="button" class="btn btn-sm btn-outline-danger" onclick="reviewApplication(<?php echo $app['application_id']; ?>, 'reject')" title="Reject">
                                            <i class="fas fa-times"></i>
                                        </button>
                                        <?php endif; ?>
                                    </div>
                                </td>
                            </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
                <?php else: ?>
                <div class="empty">
                    <div class="empty-img">
                        <i class="fas fa-file-alt" style="font-size: 3rem; color: #6c757d;"></i>
                    </div>
                    <p class="empty-title">No applications found</p>
                    <p class="empty-subtitle text-muted">
                        No card applications have been submitted yet.
                    </p>
                </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<?php include 'includes/admin-footer.php'; ?>
