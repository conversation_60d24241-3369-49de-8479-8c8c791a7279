<?php
/**
 * Test Transfer Type Logic
 * Tests the beneficiary filtering and transfer type selection logic
 */

require_once __DIR__ . '/../config/config.php';

echo "<h2>🧪 Testing Transfer Type Logic</h2>";

try {
    $db = getDB();
    
    // Simulate a user (let's use user ID 1)
    $user_id = 1;
    
    echo "<h3>1. Testing Beneficiaries Query</h3>";
    
    // Get beneficiaries with internal user detection (same query as the actual pages)
    $beneficiaries_sql = "SELECT b.*, 
                                 a.id as internal_user_id,
                                 a.first_name as internal_first_name,
                                 a.last_name as internal_last_name
                          FROM beneficiaries b
                          LEFT JOIN accounts a ON b.account_number = a.account_number AND a.is_admin = 0
                          WHERE b.user_id = ? 
                          ORDER BY b.is_favorite DESC, b.name ASC";
    $beneficiaries_result = $db->query($beneficiaries_sql, [$user_id]);
    
    $beneficiaries = [];
    while ($row = $beneficiaries_result->fetch_assoc()) {
        $beneficiaries[] = $row;
    }
    
    echo "Found " . count($beneficiaries) . " beneficiaries for user {$user_id}<br><br>";
    
    if (empty($beneficiaries)) {
        echo "⚠️ No beneficiaries found. Let's create a test beneficiary with account ************<br>";
        
        // Create a test beneficiary
        $insert_sql = "INSERT INTO beneficiaries (user_id, name, account_number, bank_name, bank_code, country, currency, is_favorite) 
                       VALUES (?, ?, ?, ?, ?, ?, ?, ?)";
        $params = [$user_id, 'Demo User', '************', 'Internal Bank', 'INT001', 'USA', 'USD', 0];
        $db->query($insert_sql, $params);
        
        echo "✅ Created test beneficiary<br>";
        
        // Re-run the query
        $beneficiaries_result = $db->query($beneficiaries_sql, [$user_id]);
        $beneficiaries = [];
        while ($row = $beneficiaries_result->fetch_assoc()) {
            $beneficiaries[] = $row;
        }
        
        echo "Now found " . count($beneficiaries) . " beneficiaries<br><br>";
    }
    
    echo "<h3>2. Analyzing Beneficiaries</h3>";
    
    $internal_count = 0;
    $external_count = 0;
    
    foreach ($beneficiaries as $beneficiary) {
        echo "<h4>Beneficiary: {$beneficiary['name']}</h4>";
        echo "Account Number: {$beneficiary['account_number']}<br>";
        echo "Bank Name: {$beneficiary['bank_name']}<br>";
        echo "Internal User ID: " . ($beneficiary['internal_user_id'] ? $beneficiary['internal_user_id'] : 'None') . "<br>";
        
        if ($beneficiary['internal_user_id']) {
            echo "✅ <strong>INTERNAL USER</strong> - Should only show Inter-Bank transfer<br>";
            echo "Internal Name: {$beneficiary['internal_first_name']} {$beneficiary['internal_last_name']}<br>";
            $internal_count++;
        } else {
            echo "🏦 <strong>EXTERNAL USER</strong> - Should only show Local Bank transfer<br>";
            $external_count++;
        }
        echo "<br>";
    }
    
    echo "<h3>3. Transfer Type Logic Test</h3>";
    echo "Internal Beneficiaries: {$internal_count}<br>";
    echo "External Beneficiaries: {$external_count}<br><br>";
    
    echo "<h4>JavaScript Logic Simulation:</h4>";
    
    foreach ($beneficiaries as $beneficiary) {
        $transfer_type = $beneficiary['internal_user_id'] ? 'inter-bank' : 'local-bank';
        echo "Beneficiary '{$beneficiary['name']}' → Transfer Type: <strong>{$transfer_type}</strong><br>";
    }
    
    echo "<br><h3>4. Business Rules Validation</h3>";
    
    $issues = [];
    
    foreach ($beneficiaries as $beneficiary) {
        if ($beneficiary['internal_user_id']) {
            // Internal beneficiary - should only be available for inter-bank
            if ($beneficiary['bank_name'] && $beneficiary['bank_name'] !== 'Internal Bank') {
                $issues[] = "Internal beneficiary '{$beneficiary['name']}' has external bank name: {$beneficiary['bank_name']}";
            }
        } else {
            // External beneficiary - should only be available for local bank
            if (!$beneficiary['bank_name']) {
                $issues[] = "External beneficiary '{$beneficiary['name']}' missing bank name";
            }
        }
    }
    
    if (empty($issues)) {
        echo "✅ All beneficiaries follow correct business rules<br>";
    } else {
        echo "⚠️ Found issues:<br>";
        foreach ($issues as $issue) {
            echo "- {$issue}<br>";
        }
    }
    
    echo "<br><h3>5. Frontend Filter Logic Test</h3>";
    echo "Simulating how beneficiaries should be filtered for each transfer type:<br><br>";
    
    echo "<h4>For Inter-Bank Transfer:</h4>";
    $inter_bank_beneficiaries = array_filter($beneficiaries, function($b) {
        return $b['internal_user_id'] !== null;
    });
    
    if (empty($inter_bank_beneficiaries)) {
        echo "No internal beneficiaries available<br>";
    } else {
        foreach ($inter_bank_beneficiaries as $beneficiary) {
            echo "✅ {$beneficiary['name']} (Account: {$beneficiary['account_number']})<br>";
        }
    }
    
    echo "<br><h4>For Local Bank Transfer:</h4>";
    $local_bank_beneficiaries = array_filter($beneficiaries, function($b) {
        return $b['internal_user_id'] === null;
    });
    
    if (empty($local_bank_beneficiaries)) {
        echo "No external beneficiaries available<br>";
    } else {
        foreach ($local_bank_beneficiaries as $beneficiary) {
            echo "✅ {$beneficiary['name']} (Account: {$beneficiary['account_number']})<br>";
        }
    }
    
    echo "<br><h3>6. Recommendation</h3>";
    
    if ($internal_count > 0 && $external_count > 0) {
        echo "✅ Mixed beneficiaries detected. Transfer type selection should work correctly.<br>";
        echo "📋 Internal beneficiaries should auto-select Inter-Bank transfer<br>";
        echo "📋 External beneficiaries should auto-select Local Bank transfer<br>";
    } elseif ($internal_count > 0) {
        echo "ℹ️ Only internal beneficiaries found. Inter-Bank transfers should be preferred.<br>";
    } elseif ($external_count > 0) {
        echo "ℹ️ Only external beneficiaries found. Local Bank transfers should be used.<br>";
    } else {
        echo "⚠️ No beneficiaries found. User needs to add beneficiaries first.<br>";
    }
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "<br>";
    echo "Stack trace:<br><pre>" . $e->getTraceAsString() . "</pre>";
}
?>
