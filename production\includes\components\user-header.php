<?php
/**
 * User Header Component
 * Displays user avatar, name, account info and action buttons
 */

if (!isset($user) || !is_array($user)) {
    throw new Exception('User data is required for user header component');
}

// Calculate cheque indicator
$has_cheques = (!empty($cheque_deposits) || $cheque_documents_count > 0);
$cheque_count = count($cheque_deposits ?? []) + ($cheque_documents_count ?? 0);
?>

<!-- Breadcrumb -->
<nav aria-label="breadcrumb" style="margin-bottom: 1rem;">
    <ol class="breadcrumb">
        <li class="breadcrumb-item"><a href="index.php">Admin</a></li>
        <li class="breadcrumb-item"><a href="users.php">Users</a></li>
        <li class="breadcrumb-item active" aria-current="page">User Details</li>
    </ol>
</nav>

<!-- Page Header -->
<div class="page-header d-print-none">
    <div class="container-xl">
        <div class="row g-2 align-items-center">
            <div class="col">
                <div class="d-flex align-items-center">
                    <span class="avatar avatar-lg me-3 user-avatar-large">
                        <?php echo strtoupper(substr($user['first_name'], 0, 1) . substr($user['last_name'], 0, 1)); ?>
                    </span>
                    <div>
                        <h2 class="page-title mb-1">
                            <?php echo htmlspecialchars($user['first_name'] . ' ' . $user['last_name']); ?>
                        </h2>
                        <div class="text-muted">
                            <span class="me-3">@<?php echo htmlspecialchars($user['username']); ?></span>
                            <span class="me-3"><?php echo htmlspecialchars($user['account_number']); ?></span>
                            <span class="badge bg-<?php echo $user['status'] === 'active' ? 'green' : ($user['status'] === 'suspended' ? 'red' : 'gray'); ?>-lt">
                                <?php echo ucfirst($user['status']); ?>
                            </span>
                            <?php if ($has_cheques): ?>
                            <span class="badge bg-warning-lt ms-2">
                                <i class="fas fa-money-check me-1"></i>
                                Has Cheques (<?php echo $cheque_count; ?>)
                            </span>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-auto ms-auto d-print-none">
                <div class="btn-list">
                    <a href="edit-user.php?id=<?php echo $user['id']; ?>" class="btn btn-primary">
                        <i class="fas fa-edit me-2"></i>
                        Edit User
                    </a>
                    <a href="users.php" class="btn btn-secondary">
                        <i class="fas fa-arrow-left me-2"></i>
                        Back to Users
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>
