<?php
/**
 * Test Wire Transfer System
 * Simple test to verify database connection and basic functionality
 */

echo "<h1>🧪 Wire Transfer System Test</h1>";

try {
    // Test database connection
    require_once 'config/config.php';
    $db = getDB();
    echo "<p>✅ Database connection successful</p>";
    
    // Test if accounts table exists
    $result = $db->query("SHOW TABLES LIKE 'accounts'");
    if ($result->num_rows > 0) {
        echo "<p>✅ Accounts table exists</p>";
    } else {
        echo "<p>❌ Accounts table missing</p>";
    }
    
    // Test if transfers table exists
    $result = $db->query("SHOW TABLES LIKE 'transfers'");
    if ($result->num_rows > 0) {
        echo "<p>✅ Transfers table exists</p>";
        
        // Check if wire transfer columns exist
        $columns = $db->query("SHOW COLUMNS FROM transfers LIKE 'swift_code'");
        if ($columns->num_rows > 0) {
            echo "<p>✅ Wire transfer columns exist</p>";
        } else {
            echo "<p>⚠️ Wire transfer columns need to be added</p>";
        }
    } else {
        echo "<p>❌ Transfers table missing</p>";
    }
    
    // Test if billing code tables exist
    $billing_tables = ['user_billing_codes', 'billing_code_settings', 'wire_transfer_fields'];
    foreach ($billing_tables as $table) {
        $result = $db->query("SHOW TABLES LIKE '$table'");
        if ($result->num_rows > 0) {
            echo "<p>✅ Table '$table' exists</p>";
        } else {
            echo "<p>⚠️ Table '$table' needs to be created</p>";
        }
    }
    
    echo "<h2>📁 File Structure Check</h2>";
    
    // Check if wire transfer files exist
    $wire_files = [
        'user/wire-transfers/index.php',
        'user/wire-transfers/wire-transfers.css',
        'user/wire-transfers/wire-transfers.js',
        'user/wire-transfers/check-billing-codes.php',
        'user/wire-transfers/verify-billing-code.php',
        'user/wire-transfers/generate-wire-otp.php',
        'user/wire-transfers/process-wire-transfer.php'
    ];
    
    foreach ($wire_files as $file) {
        if (file_exists($file)) {
            echo "<p>✅ File '$file' exists</p>";
        } else {
            echo "<p>❌ File '$file' missing</p>";
        }
    }
    
    echo "<h2>🔗 System Links</h2>";
    echo "<ul>";
    echo "<li><a href='user/wire-transfers/' target='_blank'>Wire Transfer System</a></li>";
    echo "<li><a href='user/transfers/' target='_blank'>Main Transfer Page</a></li>";
    echo "<li><a href='admin/billing-code-settings.php' target='_blank'>Billing Code Settings</a></li>";
    echo "</ul>";
    
    echo "<h2>📋 Manual Database Setup</h2>";
    echo "<p>If tables are missing, you can create them manually by running these SQL commands:</p>";
    echo "<textarea style='width:100%; height:200px; font-family:monospace;'>";
    echo "-- Create billing_code_settings table
CREATE TABLE IF NOT EXISTS billing_code_settings (
    id INT AUTO_INCREMENT PRIMARY KEY,
    setting_key VARCHAR(100) NOT NULL UNIQUE,
    setting_value TEXT NOT NULL,
    setting_description VARCHAR(255) DEFAULT NULL,
    setting_type ENUM('text', 'boolean', 'number', 'json') DEFAULT 'text',
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    updated_by INT DEFAULT NULL
);

-- Create user_billing_codes table
CREATE TABLE IF NOT EXISTS user_billing_codes (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    billing_position TINYINT NOT NULL CHECK (billing_position BETWEEN 1 AND 4),
    billing_name VARCHAR(100) NOT NULL,
    billing_code VARCHAR(50) NOT NULL,
    billing_description TEXT DEFAULT NULL,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE KEY unique_user_position (user_id, billing_position)
);

-- Create wire_transfer_fields table
CREATE TABLE IF NOT EXISTS wire_transfer_fields (
    id INT AUTO_INCREMENT PRIMARY KEY,
    field_name VARCHAR(100) NOT NULL UNIQUE,
    field_label VARCHAR(150) NOT NULL,
    field_type ENUM('text', 'email', 'number', 'select', 'textarea', 'tel', 'url') DEFAULT 'text',
    field_placeholder VARCHAR(200) DEFAULT NULL,
    is_required BOOLEAN DEFAULT FALSE,
    is_active BOOLEAN DEFAULT TRUE,
    display_order INT DEFAULT 0,
    field_group VARCHAR(50) DEFAULT 'general',
    help_text TEXT DEFAULT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Add wire transfer columns to transfers table
ALTER TABLE transfers 
ADD COLUMN IF NOT EXISTS swift_code VARCHAR(20) DEFAULT NULL,
ADD COLUMN IF NOT EXISTS routing_code VARCHAR(50) DEFAULT NULL,
ADD COLUMN IF NOT EXISTS iban VARCHAR(50) DEFAULT NULL,
ADD COLUMN IF NOT EXISTS bank_name VARCHAR(150) DEFAULT NULL,
ADD COLUMN IF NOT EXISTS bank_address TEXT DEFAULT NULL,
ADD COLUMN IF NOT EXISTS bank_city VARCHAR(100) DEFAULT NULL,
ADD COLUMN IF NOT EXISTS bank_country VARCHAR(50) DEFAULT NULL,
ADD COLUMN IF NOT EXISTS beneficiary_address TEXT DEFAULT NULL,
ADD COLUMN IF NOT EXISTS purpose_of_payment VARCHAR(200) DEFAULT NULL,
ADD COLUMN IF NOT EXISTS billing_codes_verified BOOLEAN DEFAULT FALSE,
ADD COLUMN IF NOT EXISTS billing_verification_data JSON DEFAULT NULL,
ADD COLUMN IF NOT EXISTS wire_transfer_data JSON DEFAULT NULL,
ADD COLUMN IF NOT EXISTS processing_status ENUM('pending', 'processing', 'completed', 'failed', 'cancelled') DEFAULT 'pending';";
    echo "</textarea>";
    
} catch (Exception $e) {
    echo "<p>❌ Error: " . $e->getMessage() . "</p>";
}
?>
