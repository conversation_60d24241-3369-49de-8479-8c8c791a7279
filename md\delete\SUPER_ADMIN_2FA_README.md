# Super Admin Google Authenticator (2FA) Implementation

This document describes the implementation of Google Authenticator (Two-Factor Authentication) specifically for the super admin system in the online banking platform.

## 🔐 Overview

The Super Admin 2FA system provides an additional layer of security for super administrator accounts by requiring a time-based one-time password (TOTP) from Google Authenticator app in addition to the standard username/password authentication.

## 🚀 Features

### Core Features
- **Google Authenticator Integration**: Full TOTP support using Google2FA library
- **QR Code Generation**: Easy setup with mobile authenticator apps
- **Backup Codes**: Emergency access codes for account recovery
- **Account Lockout Protection**: Prevents brute force attacks on 2FA codes
- **Audit Logging**: Complete audit trail of all 2FA activities
- **Session Management**: Seamless integration with existing super admin authentication

### Security Features
- **Replay Attack Prevention**: Timestamp-based verification
- **Rate Limiting**: Maximum failed attempts with lockout duration
- **Secure Secret Storage**: Encrypted storage of 2FA secrets
- **Independent System**: Separate from regular user 2FA to prevent conflicts

## 📁 File Structure

```
super-admin/
├── includes/
│   ├── 2fa-functions.php          # Core 2FA functionality
│   └── auth.php                   # Updated authentication with 2FA support
├── 2fa-setup.php                  # 2FA setup and management interface
├── verify-2fa.php                 # 2FA verification page
├── login.php                      # Updated login with 2FA flow
└── dashboard.php                  # Updated dashboard with 2FA status

database/
└── create_super_admin_2fa_table.sql  # Database schema for 2FA

setup_super_admin_2fa.php             # Installation script
test_2fa_library.php                  # Library testing utility
```

## 🗄️ Database Schema

### super_admin_2fa_settings
Stores 2FA configuration for super admin accounts:
- `super_admin_username`: Super admin username
- `google_2fa_enabled`: Whether 2FA is enabled
- `google_2fa_secret`: Encrypted secret key
- `backup_codes`: JSON array of backup codes
- `failed_attempts`: Failed verification attempts
- `locked_until`: Account lockout timestamp

### super_admin_2fa_audit
Audit log for all 2FA activities:
- `super_admin_username`: Username
- `action`: Action performed (setup, verification, etc.)
- `details`: Additional details
- `ip_address`: Client IP address
- `success`: Whether action was successful

## 🔧 Installation

### Step 1: Run Database Setup
```bash
# Navigate to your web root
cd /path/to/online_banking

# Run the setup script via web browser
http://your-domain/setup_super_admin_2fa.php
```

### Step 2: Test Library (Optional)
```bash
# Test the Google2FA library functionality
http://your-domain/test_2fa_library.php
```

### Step 3: Access Super Admin Panel
```bash
# Login to super admin panel
http://your-domain/super-admin/login.php

# Credentials:
Username: superadmin
Password: admin123
```

### Step 4: Setup 2FA
1. Navigate to "2FA Setup" in the super admin menu
2. Click "Generate QR Code"
3. Scan QR code with Google Authenticator app
4. Enter verification code to complete setup
5. Save backup codes in a secure location

## 🔄 Authentication Flow

### Standard Login Flow (2FA Disabled)
1. User enters username/password
2. System validates credentials
3. User is logged in and redirected to dashboard

### Enhanced Login Flow (2FA Enabled)
1. User enters username/password
2. System validates credentials
3. User is redirected to 2FA verification page
4. User enters 6-digit code from Google Authenticator
5. System verifies TOTP code
6. User is logged in and redirected to dashboard

### Account Lockout Flow
1. User fails 2FA verification (default: 5 attempts)
2. Account is locked for specified duration (default: 30 minutes)
3. All verification attempts are blocked during lockout
4. Lockout automatically expires after duration

## 🛠️ Configuration

### System Settings
Configure 2FA behavior in `super_admin_settings` table:

```sql
-- Require 2FA for all super admin logins
UPDATE super_admin_settings SET setting_value = '1' 
WHERE setting_key = 'super_admin_2fa_required';

-- Maximum failed attempts before lockout
UPDATE super_admin_settings SET setting_value = '5' 
WHERE setting_key = 'super_admin_2fa_max_attempts';

-- Lockout duration in minutes
UPDATE super_admin_settings SET setting_value = '30' 
WHERE setting_key = 'super_admin_2fa_lockout_duration';

-- Number of backup codes to generate
UPDATE super_admin_settings SET setting_value = '10' 
WHERE setting_key = 'super_admin_2fa_backup_codes_count';
```

## 📱 Mobile App Setup

### Google Authenticator
1. **Download**: Install Google Authenticator from app store
2. **Setup**: Scan QR code or enter secret manually
3. **Verify**: Enter 6-digit code to confirm setup

### Alternative Apps
- Microsoft Authenticator
- Authy
- 1Password
- Bitwarden

## 🔍 Monitoring & Audit

### Audit Log Events
- `qr_generated`: QR code generated for setup
- `2fa_enabled`: 2FA enabled for account
- `2fa_disabled`: 2FA disabled for account
- `verification_success`: Successful code verification
- `verification_failed`: Failed code verification
- `account_locked`: Account locked due to failed attempts
- `backup_codes_regenerated`: New backup codes generated

### Monitoring Queries
```sql
-- Recent 2FA activity
SELECT * FROM super_admin_2fa_audit 
ORDER BY created_at DESC LIMIT 50;

-- Failed verification attempts
SELECT * FROM super_admin_2fa_audit 
WHERE action = 'verification_failed' 
AND created_at >= DATE_SUB(NOW(), INTERVAL 24 HOUR);

-- Current 2FA status
SELECT super_admin_username, google_2fa_enabled, failed_attempts, locked_until 
FROM super_admin_2fa_settings;
```

## 🚨 Security Considerations

### Best Practices
1. **Enable 2FA Requirement**: Set `super_admin_2fa_required = 1`
2. **Secure Backup Codes**: Store backup codes in secure location
3. **Regular Monitoring**: Review audit logs regularly
4. **Update Credentials**: Change default super admin password
5. **Network Security**: Use HTTPS for all super admin access

### Emergency Access
If 2FA device is lost:
1. Use backup codes for emergency access
2. Disable 2FA temporarily if needed
3. Setup new 2FA device
4. Generate new backup codes
5. Re-enable 2FA requirement

### Database Security
- 2FA secrets are stored securely
- Backup codes are hashed
- Audit logs include IP addresses
- Failed attempts are tracked and limited

## 🔧 Troubleshooting

### Common Issues

**Issue**: QR code not displaying
**Solution**: Check Google2FA library path and permissions

**Issue**: Verification codes not working
**Solution**: Ensure device time is synchronized

**Issue**: Account locked
**Solution**: Wait for lockout duration or manually reset in database

**Issue**: Backup codes not working
**Solution**: Verify codes haven't been used previously

### Manual Reset (Emergency)
```sql
-- Disable 2FA for emergency access
UPDATE super_admin_2fa_settings 
SET google_2fa_enabled = 0, failed_attempts = 0, locked_until = NULL 
WHERE super_admin_username = 'superadmin';

-- Clear lockout
UPDATE super_admin_2fa_settings 
SET failed_attempts = 0, locked_until = NULL 
WHERE super_admin_username = 'superadmin';
```

## 📞 Support

For technical support or questions about the 2FA implementation:
1. Check audit logs for error details
2. Verify Google2FA library is properly installed
3. Ensure database tables are created correctly
4. Test with the provided test utility

## 🔄 Updates & Maintenance

### Regular Tasks
- Monitor audit logs for suspicious activity
- Update backup codes periodically
- Review and update security settings
- Test emergency access procedures

### Version Compatibility
- PHP 7.4+
- MySQL 5.7+
- Google2FA library included
- Bootstrap 5.1.3
- Font Awesome 6.0.0

---

**Note**: This 2FA implementation is specifically designed for super admin accounts and operates independently from the regular user authentication system to ensure maximum security and prevent conflicts.
