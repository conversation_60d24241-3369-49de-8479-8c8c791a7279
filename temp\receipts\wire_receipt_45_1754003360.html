
    <!DOCTYPE html>
    <html>
    <head>
        <meta charset='UTF-8'>
        <title>Wire Transfer Receipt</title>
        <style>
            body { font-family: 'Courier New', monospace; margin: 20px; background: white; }
            .receipt-container { max-width: 800px; margin: 0 auto; border: 2px solid #000; }
            .header { background: #206bc4; color: white; padding: 20px; text-align: center; }
            .content { padding: 30px; }
            .section { margin-bottom: 25px; }
            .section-title { border-bottom: 1px solid #000; padding-bottom: 5px; margin-bottom: 10px; font-weight: bold; }
            .detail-row { display: flex; justify-content: space-between; padding: 3px 0; }
            .detail-label { width: 40%; }
            .detail-value { font-weight: bold; text-align: right; }
            .amount { font-size: 1.2em; color: #206bc4; }
        </style>
    </head>
    <body>
        <div class='receipt-container'>
            <div class='header'>
                <h1>WIRE TRANSFER RECEIPT</h1>
                <p>Reference: WTX20250731761196</p>
            </div>
            <div class='content'>
                <div class='section'>
                    <div class='section-title'>TRANSFER DETAILS</div>
                    <div class='detail-row'>
                        <span class='detail-label'>Amount:</span>
                        <span class='detail-value amount'>USD 901,334.00</span>
                    </div>
                    <div class='detail-row'>
                        <span class='detail-label'>Date:</span>
                        <span class='detail-value'>July 31, 2025 11:09 PM</span>
                    </div>
                    <div class='detail-row'>
                        <span class='detail-label'>Status:</span>
                        <span class='detail-value'>Pending</span>
                    </div>
                </div>

                <div class='section'>
                    <div class='section-title'>FROM ACCOUNT</div>
                    <div class='detail-row'>
                        <span class='detail-label'>Account Holder:</span>
                        <span class='detail-value'>james Bong</span>
                    </div>
                    <div class='detail-row'>
                        <span class='detail-label'>Account Number:</span>
                        <span class='detail-value'>****7588</span>
                    </div>
                </div>

                <div class='section'>
                    <div class='section-title'>BENEFICIARY DETAILS</div>
                    <div class='detail-row'>
                        <span class='detail-label'>Name:</span>
                        <span class='detail-value'>N/A</span>
                    </div>
                    <div class='detail-row'>
                        <span class='detail-label'>Account:</span>
                        <span class='detail-value'>N/A</span>
                    </div>
                    <div class='detail-row'>
                        <span class='detail-label'>Bank:</span>
                        <span class='detail-value'>Barclays Bank</span>
                    </div>
                    <div class='detail-row'>
                        <span class='detail-label'>SWIFT Code:</span>
                        <span class='detail-value'>N/A</span>
                    </div>
                </div>

                <div class='section' style='text-align: center; margin-top: 40px; border-top: 1px solid #000; padding-top: 20px;'>
                    <p><strong>*** COMPUTER GENERATED RECEIPT ***</strong></p>
                    <p>This receipt is valid without signature</p>
                    <p>Generated on: July 31, 2025 11:09 PM</p>
                </div>
            </div>
        </div>
    </body>
    </html>