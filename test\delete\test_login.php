<?php
/**
 * Simple login test script
 */

require_once 'config/config.php';

echo "<h2>Login Test</h2>";

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $email = $_POST['email'] ?? '';
    $password = $_POST['password'] ?? '';
    
    if ($email === '<EMAIL>' && $password === 'loving12') {
        try {
            $db = getDB();
            
            // Get user from database
            $sql = "SELECT id, username, password, first_name, last_name, email, account_number,
                          balance, status, is_admin, kyc_status
                    FROM accounts WHERE email = ?";
            $result = $db->query($sql, [$email]);
            
            if ($result && $result->num_rows === 1) {
                $user = $result->fetch_assoc();
                
                if (verifyPassword($password, $user['password'])) {
                    // Check OTP settings
                    $otp_check_sql = "SELECT otp_enabled FROM user_security_settings WHERE user_id = ?";
                    $otp_result = $db->query($otp_check_sql, [$user['id']]);
                    $otp_enabled = true;
                    
                    if ($otp_result && $otp_result->num_rows === 1) {
                        $otp_settings = $otp_result->fetch_assoc();
                        $otp_enabled = ($otp_settings['otp_enabled'] == 1);
                    }
                    
                    echo "<p style='color: green;'>✓ User authenticated successfully!</p>";
                    echo "<p>OTP Enabled: " . ($otp_enabled ? 'Yes' : 'No') . "</p>";
                    
                    if (!$otp_enabled) {
                        // Set session variables
                        $_SESSION['user_id'] = $user['id'];
                        $_SESSION['username'] = $user['username'];
                        $_SESSION['first_name'] = $user['first_name'];
                        $_SESSION['last_name'] = $user['last_name'];
                        $_SESSION['email'] = $user['email'];
                        $_SESSION['user_logged_in'] = true;
                        
                        echo "<p style='color: green;'>✓ Session variables set!</p>";
                        echo "<p>User ID: " . $_SESSION['user_id'] . "</p>";
                        echo "<p>Username: " . $_SESSION['username'] . "</p>";
                        echo "<p>Name: " . $_SESSION['first_name'] . " " . $_SESSION['last_name'] . "</p>";
                        
                        // Test redirect (commented out to see results)
                        echo "<p><strong>Would redirect to:</strong> " . url('dashboard/') . "</p>";
                        echo "<p><a href='" . url('dashboard/') . "'>Click here to test dashboard access</a></p>";
                        
                        // Uncomment to test actual redirect:
                        // redirect('dashboard/');
                    } else {
                        echo "<p style='color: orange;'>OTP is enabled - would redirect to OTP verification</p>";
                    }
                } else {
                    echo "<p style='color: red;'>✗ Invalid password</p>";
                }
            } else {
                echo "<p style='color: red;'>✗ User not found</p>";
            }
        } catch (Exception $e) {
            echo "<p style='color: red;'>✗ Error: " . $e->getMessage() . "</p>";
        }
    } else {
        echo "<p style='color: red;'>✗ Invalid credentials</p>";
    }
} else {
    // Show form
    ?>
    <form method="POST">
        <div style="margin: 10px 0;">
            <label>Email:</label><br>
            <input type="email" name="email" value="<EMAIL>" style="width: 300px; padding: 5px;">
        </div>
        <div style="margin: 10px 0;">
            <label>Password:</label><br>
            <input type="password" name="password" value="loving12" style="width: 300px; padding: 5px;">
        </div>
        <div style="margin: 10px 0;">
            <button type="submit" style="padding: 10px 20px;">Test Login</button>
        </div>
    </form>
    <?php
}
?>

<style>
body { font-family: Arial, sans-serif; margin: 20px; }
h2 { color: #333; }
</style>
