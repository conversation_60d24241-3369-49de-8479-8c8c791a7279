<?php
require_once '../config/config.php';
requireAdmin();

$page_title = 'Currency Management';

// Handle form submissions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $action = $_POST['action'] ?? '';
    
    if ($action === 'add_currency') {
        $code = strtoupper(sanitizeInput($_POST['code'] ?? ''));
        $name = sanitizeInput($_POST['name'] ?? '');
        $symbol = sanitizeInput($_POST['symbol'] ?? '');
        $status = sanitizeInput($_POST['status'] ?? 'active');
        
        if (strlen($code) === 3 && !empty($name) && !empty($symbol)) {
            try {
                $db = getDB();
                $result = $db->query("INSERT INTO currencies (code, name, symbol, status) VALUES (?, ?, ?, ?)", [$code, $name, $symbol, $status]);

                if ($result) {
                    setFlashMessage('success', 'Currency added successfully!');
                } else {
                    setFlashMessage('error', 'Failed to add currency. Code might already exist.');
                }
            } catch (Exception $e) {
                setFlashMessage('error', 'Error: ' . $e->getMessage());
            }
        } else {
            setFlashMessage('error', 'Please fill all fields correctly. Currency code must be 3 characters.');
        }
        
    } elseif ($action === 'update_currency') {
        $id = intval($_POST['id'] ?? 0);
        $code = strtoupper(sanitizeInput($_POST['code'] ?? ''));
        $name = sanitizeInput($_POST['name'] ?? '');
        $symbol = sanitizeInput($_POST['symbol'] ?? '');
        $status = sanitizeInput($_POST['status'] ?? 'active');
        
        if ($id > 0 && strlen($code) === 3 && !empty($name) && !empty($symbol)) {
            try {
                $db = getDB();
                $result = $db->query("UPDATE currencies SET code = ?, name = ?, symbol = ?, status = ? WHERE id = ?", [$code, $name, $symbol, $status, $id]);

                if ($result) {
                    setFlashMessage('success', 'Currency updated successfully!');
                } else {
                    setFlashMessage('error', 'Failed to update currency.');
                }
            } catch (Exception $e) {
                setFlashMessage('error', 'Error: ' . $e->getMessage());
            }
        } else {
            setFlashMessage('error', 'Invalid data provided.');
        }
        
    } elseif ($action === 'delete_currency') {
        $id = intval($_POST['id'] ?? 0);
        
        if ($id > 0) {
            try {
                $db = getDB();
                // Check if currency is in use
                $check_result = $db->query("SELECT COUNT(*) as count FROM accounts WHERE currency = (SELECT code FROM currencies WHERE id = ?)", [$id]);
                $result = $check_result->fetch_assoc();

                if ($result['count'] > 0) {
                    setFlashMessage('error', 'Cannot delete currency that is currently in use by accounts.');
                } else {
                    $delete_result = $db->query("DELETE FROM currencies WHERE id = ?", [$id]);

                    if ($delete_result) {
                        setFlashMessage('success', 'Currency deleted successfully!');
                    } else {
                        setFlashMessage('error', 'Failed to delete currency.');
                    }
                }
            } catch (Exception $e) {
                setFlashMessage('error', 'Error: ' . $e->getMessage());
            }
        }
    }
    
    redirect('currency-management.php');
}

// Get all currencies
$db = getDB();
$currencies_result = $db->query("SELECT * FROM currencies ORDER BY status DESC, code ASC");
$currencies = $currencies_result->fetch_all(MYSQLI_ASSOC);

// Get currency usage statistics
$usage_stats = [];
foreach ($currencies as $currency) {
    $result = $db->query("SELECT COUNT(*) as count FROM accounts WHERE currency = ?", [$currency['code']]);
    $row = $result->fetch_assoc();
    $usage_stats[$currency['code']] = $row['count'];
}

include 'includes/admin-header.php';
?>

<style>
.currency-symbol {
    background: var(--primary-color);
    color: white;
    padding: 0.25rem 0.5rem;
    border-radius: 6px;
    font-weight: 600;
    font-size: 0.9rem;
}
</style>

<!-- Breadcrumb -->
<nav aria-label="breadcrumb" style="margin-bottom: 1rem;">
    <ol class="breadcrumb">
        <li class="breadcrumb-item"><a href="index.php">Admin</a></li>
        <li class="breadcrumb-item active" aria-current="page">Currency Management</li>
    </ol>
</nav>

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h1 class="h3 mb-0">Currency Management</h1>
                <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addCurrencyModal">
                    <i class="fas fa-plus"></i> Add Currency
                </button>
            </div>
            
            <?php if ($flash = getFlashMessage('success')): ?>
                <div class="alert alert-success alert-dismissible fade show" role="alert">
                    <?php echo htmlspecialchars($flash); ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            <?php endif; ?>
            
            <?php if ($flash = getFlashMessage('error')): ?>
                <div class="alert alert-danger alert-dismissible fade show" role="alert">
                    <?php echo htmlspecialchars($flash); ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            <?php endif; ?>
            
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">Supported Currencies</h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-striped">
                            <thead>
                                <tr>
                                    <th>Code</th>
                                    <th>Name</th>
                                    <th>Symbol</th>
                                    <th>Status</th>
                                    <th>Accounts Using</th>
                                    <th>Created</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($currencies as $currency): ?>
                                <tr>
                                    <td><strong><?php echo htmlspecialchars($currency['code']); ?></strong></td>
                                    <td><?php echo htmlspecialchars($currency['name']); ?></td>
                                    <td><span class="currency-symbol"><?php echo htmlspecialchars($currency['symbol']); ?></span></td>
                                    <td>
                                        <span class="badge bg-<?php echo $currency['status'] === 'active' ? 'success' : 'warning'; ?>">
                                            <?php echo ucfirst($currency['status']); ?>
                                        </span>
                                    </td>
                                    <td>
                                        <span class="badge bg-info">
                                            <?php echo $usage_stats[$currency['code']] ?? 0; ?> accounts
                                        </span>
                                    </td>
                                    <td><?php echo date('M d, Y', strtotime($currency['created_at'])); ?></td>
                                    <td>
                                        <button type="button" class="btn btn-sm btn-outline-primary" 
                                                onclick="editCurrency(<?php echo htmlspecialchars(json_encode($currency)); ?>)">
                                            <i class="fas fa-edit"></i>
                                        </button>
                                        <?php if (($usage_stats[$currency['code']] ?? 0) === 0): ?>
                                        <button type="button" class="btn btn-sm btn-outline-danger" 
                                                onclick="deleteCurrency(<?php echo $currency['id']; ?>, '<?php echo htmlspecialchars($currency['code']); ?>')">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                        <?php endif; ?>
                                    </td>
                                </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Add Currency Modal -->
<div class="modal fade" id="addCurrencyModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Add New Currency</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form method="POST">
                <div class="modal-body">
                    <input type="hidden" name="action" value="add_currency">
                    
                    <div class="mb-3">
                        <label class="form-label">Currency Code</label>
                        <input type="text" name="code" class="form-control" maxlength="3" placeholder="USD" required>
                        <small class="form-text text-muted">3-letter ISO currency code</small>
                    </div>
                    
                    <div class="mb-3">
                        <label class="form-label">Currency Name</label>
                        <input type="text" name="name" class="form-control" placeholder="US Dollar" required>
                    </div>
                    
                    <div class="mb-3">
                        <label class="form-label">Currency Symbol</label>
                        <input type="text" name="symbol" class="form-control" placeholder="$" required>
                    </div>
                    
                    <div class="mb-3">
                        <label class="form-label">Status</label>
                        <select name="status" class="form-select">
                            <option value="active">Active</option>
                            <option value="inactive">Inactive</option>
                        </select>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary">Add Currency</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Edit Currency Modal -->
<div class="modal fade" id="editCurrencyModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Edit Currency</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form method="POST" id="editCurrencyForm">
                <div class="modal-body">
                    <input type="hidden" name="action" value="update_currency">
                    <input type="hidden" name="id" id="edit_id">
                    
                    <div class="mb-3">
                        <label class="form-label">Currency Code</label>
                        <input type="text" name="code" id="edit_code" class="form-control" maxlength="3" required>
                    </div>
                    
                    <div class="mb-3">
                        <label class="form-label">Currency Name</label>
                        <input type="text" name="name" id="edit_name" class="form-control" required>
                    </div>
                    
                    <div class="mb-3">
                        <label class="form-label">Currency Symbol</label>
                        <input type="text" name="symbol" id="edit_symbol" class="form-control" required>
                    </div>
                    
                    <div class="mb-3">
                        <label class="form-label">Status</label>
                        <select name="status" id="edit_status" class="form-select">
                            <option value="active">Active</option>
                            <option value="inactive">Inactive</option>
                        </select>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary">Update Currency</button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
function editCurrency(currency) {
    document.getElementById('edit_id').value = currency.id;
    document.getElementById('edit_code').value = currency.code;
    document.getElementById('edit_name').value = currency.name;
    document.getElementById('edit_symbol').value = currency.symbol;
    document.getElementById('edit_status').value = currency.status;
    
    new bootstrap.Modal(document.getElementById('editCurrencyModal')).show();
}

function deleteCurrency(id, code) {
    if (confirm(`Are you sure you want to delete currency ${code}?`)) {
        const form = document.createElement('form');
        form.method = 'POST';
        form.innerHTML = `
            <input type="hidden" name="action" value="delete_currency">
            <input type="hidden" name="id" value="${id}">
        `;
        document.body.appendChild(form);
        form.submit();
    }
}
</script>

<?php include 'includes/admin-footer.php'; ?>
