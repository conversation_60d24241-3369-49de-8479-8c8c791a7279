/**
 * Enhanced User Dashboard JavaScript
 * Professional Banking Interface Interactions
 */

document.addEventListener('DOMContentLoaded', function() {

    // Initialize dashboard components
    initializeVirtualCard();
    initializeTransactionTable();
    initializeBalanceCards();
    initializeQuickLinks();
    initializeTransactionModal();

    // Auto-refresh data every 5 minutes
    setInterval(refreshDashboardData, 300000);
});

/**
 * Initialize Transaction Modal
 */
function initializeTransactionModal() {
    // Add escape key handler
    document.addEventListener('keydown', function(e) {
        if (e.key === 'Escape') {
            closeTransactionModal();
        }
    });

    // Add backdrop click handler
    const modal = document.getElementById('transactionModal');
    if (modal) {
        modal.addEventListener('click', function(e) {
            if (e.target === modal || e.target.classList.contains('modal-overlay')) {
                closeTransactionModal();
            }
        });
    }
}

/**
 * Initialize Virtual Card Interactions
 */
function initializeVirtualCard() {
    const virtualCard = document.querySelector('.virtual-card');
    
    if (virtualCard) {
        // Add hover effect for card tilt
        virtualCard.addEventListener('mouseenter', function() {
            this.style.transform = 'rotateY(5deg) rotateX(5deg) scale(1.02)';
        });
        
        virtualCard.addEventListener('mouseleave', function() {
            this.style.transform = 'rotateY(0deg) rotateX(0deg) scale(1)';
        });
        
        // Add click to copy card number functionality
        const cardNumber = virtualCard.querySelector('.card-number');
        if (cardNumber) {
            cardNumber.addEventListener('click', function() {
                const cardNumberText = this.textContent.replace(/\s/g, '');
                copyToClipboard(cardNumberText, 'Card number copied to clipboard!');
            });
            
            // Add tooltip
            cardNumber.title = 'Click to copy card number';
            cardNumber.style.cursor = 'pointer';
        }
    }
}

/**
 * Initialize Transaction Table Features
 */
function initializeTransactionTable() {
    const transactionRows = document.querySelectorAll('.transactions-table tbody tr');

    transactionRows.forEach((row, index) => {
        // Add click to show modal functionality
        row.addEventListener('click', function() {
            showTransactionModal(this, index);
        });

        // Add hover effect
        row.addEventListener('mouseenter', function() {
            this.style.backgroundColor = 'rgba(var(--primary-color-rgb), 0.05)';
            this.style.cursor = 'pointer';
        });

        row.addEventListener('mouseleave', function() {
            this.style.backgroundColor = '';
        });
    });

    // Initialize transaction filtering
    initializeTransactionFilters();
}

/**
 * Initialize Balance Card Animations
 */
function initializeBalanceCards() {
    const balanceCards = document.querySelectorAll('.balance-card');
    
    balanceCards.forEach((card, index) => {
        // Animate balance amounts on load
        const balanceAmount = card.querySelector('.balance-amount');
        if (balanceAmount) {
            animateBalance(balanceAmount, index * 200);
        }
        
        // Add click to refresh functionality
        card.addEventListener('click', function() {
            refreshBalanceCard(this);
        });
    });
}

/**
 * Initialize Quick Links
 */
function initializeQuickLinks() {
    const quickLinks = document.querySelectorAll('.quick-link');
    
    quickLinks.forEach(link => {
        link.addEventListener('click', function(e) {
            // Add loading state
            const icon = this.querySelector('i');
            const originalClass = icon.className;
            
            icon.className = 'fas fa-spinner fa-spin';
            
            // Restore icon after a short delay (for visual feedback)
            setTimeout(() => {
                icon.className = originalClass;
            }, 1000);
        });
    });
}

/**
 * Toggle Transaction Details
 */
function toggleTransactionDetails(row) {
    const isExpanded = row.classList.contains('expanded');
    
    // Close all other expanded rows
    document.querySelectorAll('.transactions-table tbody tr.expanded').forEach(expandedRow => {
        if (expandedRow !== row) {
            expandedRow.classList.remove('expanded');
            const detailsRow = expandedRow.nextElementSibling;
            if (detailsRow && detailsRow.classList.contains('transaction-details')) {
                detailsRow.remove();
            }
        }
    });
    
    if (isExpanded) {
        // Collapse
        row.classList.remove('expanded');
        const detailsRow = row.nextElementSibling;
        if (detailsRow && detailsRow.classList.contains('transaction-details')) {
            detailsRow.remove();
        }
    } else {
        // Expand
        row.classList.add('expanded');
        const detailsRow = createTransactionDetailsRow(row);
        row.parentNode.insertBefore(detailsRow, row.nextSibling);
    }
}

/**
 * Create Transaction Details Row
 */
function createTransactionDetailsRow(row) {
    const detailsRow = document.createElement('tr');
    detailsRow.className = 'transaction-details';
    
    const detailsCell = document.createElement('td');
    detailsCell.colSpan = 7;
    detailsCell.innerHTML = `
        <div class="transaction-details-content">
            <div class="row">
                <div class="col-md-6">
                    <h6>Transaction Details</h6>
                    <p><strong>Reference:</strong> ${row.cells[2].querySelector('small')?.textContent || 'N/A'}</p>
                    <p><strong>Category:</strong> ${row.cells[3].textContent}</p>
                </div>
                <div class="col-md-6">
                    <h6>Additional Information</h6>
                    <p><strong>Processing Time:</strong> Instant</p>
                    <p><strong>Fee:</strong> $0.00</p>
                </div>
            </div>
        </div>
    `;
    
    detailsRow.appendChild(detailsCell);
    return detailsRow;
}

/**
 * Initialize Transaction Filters
 */
function initializeTransactionFilters() {
    // Add filter buttons if they exist
    const filterButtons = document.querySelectorAll('.transaction-filter');
    
    filterButtons.forEach(button => {
        button.addEventListener('click', function() {
            const filterType = this.dataset.filter;
            filterTransactions(filterType);
            
            // Update active state
            filterButtons.forEach(btn => btn.classList.remove('active'));
            this.classList.add('active');
        });
    });
}

/**
 * Filter Transactions
 */
function filterTransactions(filterType) {
    const rows = document.querySelectorAll('.transactions-table tbody tr:not(.transaction-details)');
    
    rows.forEach(row => {
        const typeCell = row.cells[3];
        const transactionType = typeCell.textContent.toLowerCase();
        
        let shouldShow = true;
        
        switch (filterType) {
            case 'admin':
                shouldShow = transactionType.includes('admin');
                break;
            case 'user':
                shouldShow = !transactionType.includes('admin');
                break;
            case 'credit':
                shouldShow = row.cells[4].textContent.includes('+');
                break;
            case 'debit':
                shouldShow = row.cells[4].textContent.includes('-');
                break;
            default:
                shouldShow = true;
        }
        
        row.style.display = shouldShow ? '' : 'none';
    });
}

/**
 * Animate Balance Amount
 */
function animateBalance(element, delay = 0) {
    const finalValue = parseFloat(element.textContent.replace(/[^0-9.-]+/g, ''));
    const currency = element.textContent.replace(/[0-9.-]/g, '').trim();
    
    setTimeout(() => {
        let currentValue = 0;
        const increment = finalValue / 50;
        const timer = setInterval(() => {
            currentValue += increment;
            if (currentValue >= finalValue) {
                currentValue = finalValue;
                clearInterval(timer);
            }
            element.textContent = currency + ' ' + currentValue.toLocaleString('en-US', {
                minimumFractionDigits: 2,
                maximumFractionDigits: 2
            });
        }, 20);
    }, delay);
}

/**
 * Refresh Balance Card
 */
function refreshBalanceCard(card) {
    const balanceAmount = card.querySelector('.balance-amount');
    const originalText = balanceAmount.textContent;
    
    // Show loading state
    balanceAmount.innerHTML = '<i class="fas fa-spinner fa-spin"></i>';
    
    // Simulate API call
    setTimeout(() => {
        balanceAmount.textContent = originalText;
        
        // Add success animation
        card.style.transform = 'scale(1.05)';
        setTimeout(() => {
            card.style.transform = 'scale(1)';
        }, 200);
    }, 1500);
}

/**
 * Refresh Dashboard Data
 */
function refreshDashboardData() {
    // This would typically make an AJAX call to refresh data
    console.log('Refreshing dashboard data...');
    
    // Show subtle loading indicator
    const indicator = document.createElement('div');
    indicator.className = 'refresh-indicator';
    indicator.innerHTML = '<i class="fas fa-sync-alt fa-spin"></i> Updating...';
    indicator.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        background: var(--primary-color);
        color: white;
        padding: 10px 15px;
        border-radius: 5px;
        font-size: 12px;
        z-index: 1000;
        opacity: 0.8;
    `;
    
    document.body.appendChild(indicator);
    
    setTimeout(() => {
        indicator.remove();
    }, 2000);
}

/**
 * Copy to Clipboard Utility
 */
function copyToClipboard(text, message = 'Copied to clipboard!') {
    if (navigator.clipboard) {
        navigator.clipboard.writeText(text).then(() => {
            showToast(message, 'success');
        });
    } else {
        // Fallback for older browsers
        const textArea = document.createElement('textarea');
        textArea.value = text;
        document.body.appendChild(textArea);
        textArea.select();
        document.execCommand('copy');
        document.body.removeChild(textArea);
        showToast(message, 'success');
    }
}

/**
 * Show Transaction Modal
 */
function showTransactionModal(row, index) {
    const cells = row.cells;

    // Extract transaction data from table row
    const transactionData = {
        number: cells[0].textContent.trim(),
        date: cells[1].querySelector('div').textContent + ' ' + cells[1].querySelector('small').textContent,
        description: cells[2].querySelector('.transaction-description').textContent.trim(),
        reference: cells[2].querySelector('small') ? cells[2].querySelector('small').textContent.replace('Ref: ', '') : 'N/A',
        type: cells[3].textContent.trim(),
        amount: cells[4].textContent.trim(),
        balance: cells[5].textContent.trim(),
        status: cells[6].textContent.trim()
    };

    // Generate receipt content
    const receiptContent = generateTransactionReceipt(transactionData);

    // Show modal
    const modal = document.getElementById('transactionModal');
    const content = document.getElementById('transactionModalContent');

    if (modal && content) {
        content.innerHTML = receiptContent;
        modal.style.display = 'block';
        document.body.style.overflow = 'hidden';
    }
}

/**
 * Close Transaction Modal
 */
function closeTransactionModal() {
    const modal = document.getElementById('transactionModal');
    if (modal) {
        modal.style.display = 'none';
        document.body.style.overflow = '';
    }
}

/**
 * Generate Transaction Receipt Content
 */
function generateTransactionReceipt(data) {
    const isCredit = data.amount.includes('+');
    const amountClass = isCredit ? 'credit' : 'debit';

    return `
        <div class="receipt-section">
            <h6>Transaction Information</h6>
            <div class="receipt-row">
                <span class="receipt-label">Transaction #:</span>
                <span class="receipt-value">${data.number}</span>
            </div>
            <div class="receipt-row">
                <span class="receipt-label">Date & Time:</span>
                <span class="receipt-value">${data.date}</span>
            </div>
            <div class="receipt-row">
                <span class="receipt-label">Reference:</span>
                <span class="receipt-value">${data.reference}</span>
            </div>
            <div class="receipt-row">
                <span class="receipt-label">Status:</span>
                <span class="receipt-value">${data.status}</span>
            </div>
        </div>

        <div class="receipt-section">
            <h6>Transaction Details</h6>
            <div class="receipt-row">
                <span class="receipt-label">Description:</span>
                <span class="receipt-value">${data.description}</span>
            </div>
            <div class="receipt-row">
                <span class="receipt-label">Transaction Type:</span>
                <span class="receipt-value">${data.type}</span>
            </div>
        </div>

        <div class="receipt-section">
            <h6>Amount Details</h6>
            <div class="receipt-row">
                <span class="receipt-label">Amount:</span>
                <span class="receipt-value receipt-amount ${amountClass}">${data.amount}</span>
            </div>
            <div class="receipt-row">
                <span class="receipt-label">Balance After:</span>
                <span class="receipt-value">${data.balance}</span>
            </div>
        </div>

        <div class="receipt-section">
            <h6>Banking Information</h6>
            <div class="receipt-row">
                <span class="receipt-label">Bank:</span>
                <span class="receipt-value">PremierBank Pro</span>
            </div>
            <div class="receipt-row">
                <span class="receipt-label">Processing Time:</span>
                <span class="receipt-value">Instant</span>
            </div>
            <div class="receipt-row">
                <span class="receipt-label">Transaction Fee:</span>
                <span class="receipt-value">$0.00</span>
            </div>
        </div>
    `;
}

/**
 * Print Receipt
 */
function printReceipt() {
    const content = document.getElementById('transactionModalContent').innerHTML;
    const printWindow = window.open('', '_blank');

    printWindow.document.write(`
        <!DOCTYPE html>
        <html>
        <head>
            <title>Transaction Receipt - PremierBank Pro</title>
            <style>
                body { font-family: 'Courier New', monospace; margin: 20px; }
                .receipt-section { margin-bottom: 20px; padding-bottom: 15px; border-bottom: 1px dashed #ccc; }
                .receipt-section:last-child { border-bottom: none; }
                .receipt-section h6 { font-weight: bold; margin-bottom: 10px; text-transform: uppercase; }
                .receipt-row { display: flex; justify-content: space-between; margin-bottom: 5px; }
                .receipt-amount.credit { color: #28a745; }
                .receipt-amount.debit { color: #dc3545; }
                @media print { body { margin: 0; } }
            </style>
        </head>
        <body>
            <h2 style="text-align: center; margin-bottom: 30px;">PremierBank Pro - Transaction Receipt</h2>
            ${content}
            <div style="text-align: center; margin-top: 30px; font-size: 12px; color: #666;">
                Generated on ${new Date().toLocaleString()}
            </div>
        </body>
        </html>
    `);

    printWindow.document.close();
    printWindow.print();
}

/**
 * Show Toast Notification
 */
function showToast(message, type = 'info') {
    const toast = document.createElement('div');
    toast.className = `toast toast-${type}`;
    toast.textContent = message;
    toast.style.cssText = `
        position: fixed;
        bottom: 20px;
        right: 20px;
        background: ${type === 'success' ? '#28a745' : type === 'error' ? '#dc3545' : '#17a2b8'};
        color: white;
        padding: 12px 20px;
        border-radius: 5px;
        font-size: 14px;
        z-index: 1000;
        opacity: 0;
        transform: translateY(20px);
        transition: all 0.3s ease;
    `;

    document.body.appendChild(toast);

    // Animate in
    setTimeout(() => {
        toast.style.opacity = '1';
        toast.style.transform = 'translateY(0)';
    }, 100);

    // Remove after 3 seconds
    setTimeout(() => {
        toast.style.opacity = '0';
        toast.style.transform = 'translateY(20px)';
        setTimeout(() => {
            toast.remove();
        }, 300);
    }, 3000);
}

/**
 * Toggle wallet address display and copy functionality
 */
function toggleWalletAddress(element) {
    const fullAddress = element.getAttribute('data-full-address');
    const currentText = element.textContent.trim();

    // If currently showing truncated, show full address
    if (currentText.includes('...')) {
        element.textContent = fullAddress;
        element.title = 'Click to copy and hide';

        // Copy to clipboard
        copyToClipboard(fullAddress);

        // Auto-hide after 3 seconds
        setTimeout(() => {
            if (element.textContent === fullAddress) {
                const truncated = fullAddress.length > 20 ?
                    fullAddress.substr(0, 8) + '...' + fullAddress.substr(-8) :
                    fullAddress;
                element.textContent = truncated;
                element.title = 'Click to copy full address';
            }
        }, 3000);
    } else {
        // Copy to clipboard and show truncated
        copyToClipboard(fullAddress);
        const truncated = fullAddress.length > 20 ?
            fullAddress.substr(0, 8) + '...' + fullAddress.substr(-8) :
            fullAddress;
        element.textContent = truncated;
        element.title = 'Click to copy full address';
    }
}

/**
 * Copy text to clipboard with user feedback
 */
function copyToClipboard(text) {
    if (navigator.clipboard && window.isSecureContext) {
        // Use modern clipboard API
        navigator.clipboard.writeText(text).then(() => {
            showToast('Address copied to clipboard!', 'success');
        }).catch(() => {
            fallbackCopyToClipboard(text);
        });
    } else {
        // Fallback for older browsers
        fallbackCopyToClipboard(text);
    }
}

/**
 * Fallback copy method for older browsers
 */
function fallbackCopyToClipboard(text) {
    const textArea = document.createElement('textarea');
    textArea.value = text;
    textArea.style.position = 'fixed';
    textArea.style.left = '-999999px';
    textArea.style.top = '-999999px';
    document.body.appendChild(textArea);
    textArea.focus();
    textArea.select();

    try {
        document.execCommand('copy');
        showToast('Address copied to clipboard!', 'success');
    } catch (err) {
        showToast('Failed to copy address', 'error');
    }

    document.body.removeChild(textArea);
}
