<?php
require_once '../../config/config.php';
requireAdmin();

header('Content-Type: application/json');

try {
    if (!isset($_GET['user_id']) || empty($_GET['user_id'])) {
        throw new Exception("User ID is required");
    }
    
    $user_id = intval($_GET['user_id']);
    $db = getDB();
    
    // Get user information
    $user_query = "SELECT id, first_name, last_name, username, email FROM accounts WHERE id = ? AND is_admin = 0";
    $user_result = $db->query($user_query, [$user_id]);
    $user = $user_result ? $user_result->fetch_assoc() : null;
    
    if (!$user) {
        throw new Exception("User not found");
    }
    
    // Get user security settings
    $security_query = "SELECT * FROM user_security_settings WHERE user_id = ?";
    $security_result = $db->query($security_query, [$user_id]);
    $security_settings = $security_result ? $security_result->fetch_assoc() : null;
    
    if (!$security_settings) {
        // Create default security settings if they don't exist
        $create_settings = "INSERT INTO user_security_settings (
            user_id, otp_enabled, google_2fa_enabled, require_2fa, allow_remember_device,
            login_attempts_limit, lockout_duration, otp_expiry_minutes, created_by, updated_by
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";
        
        $db->query($create_settings, [
            $user_id, 1, 0, 1, 0, 5, 30, 10, $_SESSION['user_id'], $_SESSION['user_id']
        ]);
        
        // Fetch the newly created settings
        $security_result = $db->query($security_query, [$user_id]);
        $security_settings = $security_result ? $security_result->fetch_assoc() : null;
    }
    
    echo json_encode([
        'success' => true,
        'user' => $user,
        'settings' => $security_settings
    ]);
    
} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'error' => $e->getMessage()
    ]);
}
?>
