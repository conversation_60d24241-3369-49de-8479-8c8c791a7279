<?php
/**
 * Super Admin Security Center
 * Security monitoring and configuration
 */

$page_title = 'Security Center';
$page_subtitle = 'Security monitoring and system protection';

// Include header
include 'includes/header.php';

// Handle security actions
if ($_POST && isset($_POST['csrf_token']) && verifyCSRFToken($_POST['csrf_token'])) {
    try {
        require_once '../config/database.php';
        $db = getDB();
        
        if (isset($_POST['action'])) {
            switch ($_POST['action']) {
                case 'clear_failed_logins':
                    // Clear failed login attempts (implementation depends on your system)
                    logSuperAdminAction('security_action', 'Cleared failed login attempts');
                    $success_message = "Failed login attempts cleared successfully!";
                    break;
                    
                case 'update_security_settings':
                    $settings = [
                        'max_login_attempts' => (int)$_POST['max_login_attempts'],
                        'lockout_duration' => (int)$_POST['lockout_duration'],
                        'session_timeout' => (int)$_POST['session_timeout'],
                        'password_min_length' => (int)$_POST['password_min_length'],
                        'require_2fa' => isset($_POST['require_2fa']) ? 1 : 0,
                        'ip_whitelist_enabled' => isset($_POST['ip_whitelist_enabled']) ? 1 : 0
                    ];
                    
                    foreach ($settings as $key => $value) {
                        $sql = "INSERT INTO super_admin_settings (setting_key, setting_value, setting_description)
                                VALUES (?, ?, ?)
                                ON DUPLICATE KEY UPDATE setting_value = VALUES(setting_value)";

                        $description = ucfirst(str_replace('_', ' ', $key));
                        $db->query($sql, [$key, $value, $description]);
                    }
                    
                    logSuperAdminAction('security_settings_update', 'Security settings updated', $settings);
                    $success_message = "Security settings updated successfully!";
                    break;
            }
        }
        
    } catch (Exception $e) {
        $error_message = "Failed to perform security action: " . $e->getMessage();
        error_log($error_message);
    }
}

// Get security statistics
try {
    require_once '../config/database.php';
    $db = getDB();
    
    // Get security stats
    $security_stats = [
        'failed_logins_24h' => 0,
        'successful_logins_24h' => 0,
        'active_sessions' => 0,
        'suspicious_activities' => 0
    ];
    
    // Count failed logins in last 24 hours
    $result = $db->query("SELECT COUNT(*) as count FROM super_admin_audit_log 
                         WHERE action = 'login_failed' AND created_at >= DATE_SUB(NOW(), INTERVAL 24 HOUR)");
    if ($result) {
        $security_stats['failed_logins_24h'] = $result->fetch_assoc()['count'];
    }
    
    // Count successful logins in last 24 hours
    $result = $db->query("SELECT COUNT(*) as count FROM super_admin_audit_log 
                         WHERE action = 'login' AND created_at >= DATE_SUB(NOW(), INTERVAL 24 HOUR)");
    if ($result) {
        $security_stats['successful_logins_24h'] = $result->fetch_assoc()['count'];
    }
    
    // Get current security settings
    $security_settings = [];
    $result = $db->query("SELECT setting_key, setting_value FROM super_admin_settings WHERE setting_key IN ('max_login_attempts', 'lockout_duration', 'session_timeout', 'min_password_length', 'require_2fa', 'enable_ip_whitelist')");
    if ($result) {
        while ($row = $result->fetch_assoc()) {
            $security_settings[$row['setting_key']] = $row['setting_value'];
        }
    }
    
    // Default security settings
    $defaults = [
        'max_login_attempts' => 5,
        'lockout_duration' => 30,
        'session_timeout' => 30,
        'password_min_length' => 8,
        'require_2fa' => 0,
        'ip_whitelist_enabled' => 0
    ];
    
    foreach ($defaults as $key => $default_value) {
        if (!isset($security_settings[$key])) {
            $security_settings[$key] = $default_value;
        }
    }
    
    // Get recent security events
    $security_events = [];
    $result = $db->query("SELECT * FROM super_admin_audit_log 
                         WHERE action IN ('login_failed', 'session_timeout', 'unauthorized_access', 'suspicious_activity') 
                         ORDER BY created_at DESC LIMIT 20");
    if ($result) {
        while ($row = $result->fetch_assoc()) {
            $security_events[] = $row;
        }
    }
    
} catch (Exception $e) {
    error_log("Failed to load security data: " . $e->getMessage());
    $security_stats = ['failed_logins_24h' => 0, 'successful_logins_24h' => 0, 'active_sessions' => 0, 'suspicious_activities' => 0];
    $security_settings = [];
    $security_events = [];
}

// Log page access
logSuperAdminAction('security_center_access', 'Super admin accessed security center');
?>

<!-- Success/Error Messages -->
<?php if (isset($success_message)): ?>
    <div class="alert alert-success alert-dismissible fade show" role="alert">
        <i class="fas fa-check-circle"></i> <?php echo htmlspecialchars($success_message); ?>
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
<?php endif; ?>

<?php if (isset($error_message)): ?>
    <div class="alert alert-danger alert-dismissible fade show" role="alert">
        <i class="fas fa-exclamation-triangle"></i> <?php echo htmlspecialchars($error_message); ?>
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
<?php endif; ?>

<!-- Security Statistics -->
<div class="row mb-4">
    <div class="col-md-3">
        <div class="card border-0 shadow-sm">
            <div class="card-body text-center">
                <div class="mb-2">
                    <i class="fas fa-shield-alt fa-2x text-success"></i>
                </div>
                <h3 class="text-success"><?php echo number_format($security_stats['successful_logins_24h']); ?></h3>
                <p class="text-muted mb-0">Successful Logins (24h)</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card border-0 shadow-sm">
            <div class="card-body text-center">
                <div class="mb-2">
                    <i class="fas fa-exclamation-triangle fa-2x text-warning"></i>
                </div>
                <h3 class="text-warning"><?php echo number_format($security_stats['failed_logins_24h']); ?></h3>
                <p class="text-muted mb-0">Failed Logins (24h)</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card border-0 shadow-sm">
            <div class="card-body text-center">
                <div class="mb-2">
                    <i class="fas fa-users fa-2x text-info"></i>
                </div>
                <h3 class="text-info"><?php echo number_format($security_stats['active_sessions']); ?></h3>
                <p class="text-muted mb-0">Active Sessions</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card border-0 shadow-sm">
            <div class="card-body text-center">
                <div class="mb-2">
                    <i class="fas fa-bug fa-2x text-danger"></i>
                </div>
                <h3 class="text-danger"><?php echo number_format($security_stats['suspicious_activities']); ?></h3>
                <p class="text-muted mb-0">Suspicious Activities</p>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <!-- Security Settings -->
    <div class="col-lg-6">
        <div class="card border-0 shadow-sm">
            <div class="card-header bg-white border-0 py-3">
                <h5 class="card-title mb-0">
                    <i class="fas fa-cogs text-primary"></i> Security Settings
                </h5>
            </div>
            <div class="card-body">
                <form method="POST" action="">
                    <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
                    <input type="hidden" name="action" value="update_security_settings">
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="max_login_attempts" class="form-label">Max Login Attempts</label>
                            <input type="number" class="form-control" id="max_login_attempts" name="max_login_attempts" 
                                   value="<?php echo htmlspecialchars($security_settings['max_login_attempts']); ?>" 
                                   min="1" max="10" required>
                            <div class="form-text">Number of failed attempts before lockout</div>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="lockout_duration" class="form-label">Lockout Duration (minutes)</label>
                            <input type="number" class="form-control" id="lockout_duration" name="lockout_duration" 
                                   value="<?php echo htmlspecialchars($security_settings['lockout_duration']); ?>" 
                                   min="5" max="1440" required>
                            <div class="form-text">How long to lock account after failed attempts</div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="session_timeout" class="form-label">Session Timeout (minutes)</label>
                            <input type="number" class="form-control" id="session_timeout" name="session_timeout" 
                                   value="<?php echo htmlspecialchars($security_settings['session_timeout']); ?>" 
                                   min="5" max="480" required>
                            <div class="form-text">Auto-logout after inactivity</div>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="password_min_length" class="form-label">Min Password Length</label>
                            <input type="number" class="form-control" id="password_min_length" name="password_min_length" 
                                   value="<?php echo htmlspecialchars($security_settings['password_min_length']); ?>" 
                                   min="6" max="32" required>
                            <div class="form-text">Minimum characters for passwords</div>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="require_2fa" name="require_2fa" 
                                   <?php echo $security_settings['require_2fa'] ? 'checked' : ''; ?>>
                            <label class="form-check-label" for="require_2fa">
                                Require Two-Factor Authentication
                            </label>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="ip_whitelist_enabled" name="ip_whitelist_enabled" 
                                   <?php echo $security_settings['ip_whitelist_enabled'] ? 'checked' : ''; ?>>
                            <label class="form-check-label" for="ip_whitelist_enabled">
                                Enable IP Address Whitelist
                            </label>
                        </div>
                    </div>
                    
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save"></i> Update Security Settings
                    </button>
                </form>
            </div>
        </div>
        
        <!-- Quick Actions -->
        <div class="card border-0 shadow-sm mt-4">
            <div class="card-header bg-white border-0 py-3">
                <h5 class="card-title mb-0">
                    <i class="fas fa-bolt text-warning"></i> Quick Security Actions
                </h5>
            </div>
            <div class="card-body">
                <div class="d-grid gap-2">
                    <form method="POST" style="display: inline;">
                        <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
                        <input type="hidden" name="action" value="clear_failed_logins">
                        <button type="submit" class="btn btn-outline-warning w-100" 
                                onclick="return confirm('Clear all failed login attempts?')">
                            <i class="fas fa-broom"></i> Clear Failed Login Attempts
                        </button>
                    </form>
                    
                    <button type="button" class="btn btn-outline-info w-100" onclick="generateSecurityReport()">
                        <i class="fas fa-file-alt"></i> Generate Security Report
                    </button>
                    
                    <button type="button" class="btn btn-outline-success w-100" onclick="testSecurityScan()">
                        <i class="fas fa-search"></i> Run Security Scan
                    </button>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Recent Security Events -->
    <div class="col-lg-6">
        <div class="card border-0 shadow-sm">
            <div class="card-header bg-white border-0 py-3 d-flex justify-content-between align-items-center">
                <h5 class="card-title mb-0">
                    <i class="fas fa-exclamation-triangle text-warning"></i> Recent Security Events
                </h5>
                <a href="audit-logs.php?action=login_failed" class="btn btn-sm btn-outline-primary">
                    View All <i class="fas fa-arrow-right"></i>
                </a>
            </div>
            <div class="card-body p-0">
                <?php if (empty($security_events)): ?>
                    <div class="text-center py-4">
                        <i class="fas fa-shield-alt fa-3x text-success mb-3"></i>
                        <p class="text-muted">No recent security events. System is secure!</p>
                    </div>
                <?php else: ?>
                    <div class="list-group list-group-flush">
                        <?php foreach ($security_events as $event): ?>
                            <div class="list-group-item border-0 py-3">
                                <div class="d-flex align-items-start">
                                    <div class="flex-shrink-0 me-3">
                                        <?php
                                        $icon_class = 'fas fa-exclamation-triangle text-warning';
                                        switch ($event['action']) {
                                            case 'login_failed':
                                                $icon_class = 'fas fa-times-circle text-danger';
                                                break;
                                            case 'session_timeout':
                                                $icon_class = 'fas fa-clock text-warning';
                                                break;
                                            case 'unauthorized_access':
                                                $icon_class = 'fas fa-ban text-danger';
                                                break;
                                        }
                                        ?>
                                        <i class="<?php echo $icon_class; ?>"></i>
                                    </div>
                                    <div class="flex-grow-1">
                                        <h6 class="mb-1"><?php echo htmlspecialchars($event['description']); ?></h6>
                                        <p class="mb-1 text-muted small">
                                            <i class="fas fa-user"></i> <?php echo htmlspecialchars($event['username']); ?>
                                            <i class="fas fa-map-marker-alt ms-2"></i> <?php echo htmlspecialchars($event['ip_address']); ?>
                                        </p>
                                        <small class="text-muted">
                                            <i class="fas fa-clock"></i> 
                                            <?php echo date('M j, Y g:i A', strtotime($event['created_at'])); ?>
                                        </small>
                                    </div>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>
                <?php endif; ?>
            </div>
        </div>
        
        <!-- Security Status -->
        <div class="card border-0 shadow-sm mt-4">
            <div class="card-header bg-white border-0 py-3">
                <h5 class="card-title mb-0">
                    <i class="fas fa-heartbeat text-success"></i> Security Status
                </h5>
            </div>
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center mb-3">
                    <span>Firewall Status</span>
                    <span class="badge bg-success">
                        <i class="fas fa-check"></i> Active
                    </span>
                </div>
                
                <div class="d-flex justify-content-between align-items-center mb-3">
                    <span>SSL Certificate</span>
                    <span class="badge bg-success">
                        <i class="fas fa-check"></i> Valid
                    </span>
                </div>
                
                <div class="d-flex justify-content-between align-items-center mb-3">
                    <span>Database Security</span>
                    <span class="badge bg-success">
                        <i class="fas fa-check"></i> Secured
                    </span>
                </div>
                
                <div class="d-flex justify-content-between align-items-center mb-3">
                    <span>File Permissions</span>
                    <span class="badge bg-success">
                        <i class="fas fa-check"></i> Correct
                    </span>
                </div>
                
                <div class="d-flex justify-content-between align-items-center">
                    <span>Last Security Scan</span>
                    <span class="text-muted small">
                        <?php echo date('M j, Y g:i A'); ?>
                    </span>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function generateSecurityReport() {
    if (confirm('Generate a comprehensive security report? This may take a few moments.')) {
        window.location.href = 'generate-security-report.php';
    }
}

function testSecurityScan() {
    const button = event.target;
    const originalText = button.innerHTML;
    button.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Scanning...';
    button.disabled = true;
    
    // Simulate security scan
    setTimeout(() => {
        alert('Security scan completed successfully! No vulnerabilities detected.');
        button.innerHTML = originalText;
        button.disabled = false;
    }, 3000);
}
</script>

<?php
// Include footer
include 'includes/footer.php';
?>
