<?php
/**
 * User Dashboard Redesign - Comprehensive Banking Interface
 * Displays ALL user information from database analysis
 */

// Set page variables
$page_title = 'Dashboard';
$additional_css = ['user-dashboard-redesign.css'];

// Include header template with dynamic CSS
require_once '../templates/user/header.php';

// Include database connection and check authentication
require_once '../config/config.php';
requireLogin();

// Get comprehensive user data
try {
    $db = getDB();
    $user_id = $_SESSION['user_id'];

    // Get complete user account information
    $user_sql = "SELECT a.*,
                        COALESCE(a.balance, 0) as balance,
                        DATE_FORMAT(a.created_at, '%M %Y') as member_since,
                        DATEDIFF(NOW(), a.created_at) as days_member,
                        DATE_FORMAT(a.last_login, '%M %d, %Y at %h:%i %p') as last_login_formatted
                 FROM accounts a
                 WHERE a.id = ? AND a.deleted_at IS NULL";
    
    $user_result = $db->query($user_sql, [$user_id]);
    $user = $user_result->fetch_assoc();

    if (!$user) {
        throw new Exception("User account not found");
    }

    // Get current balance
    $current_balance = $user['balance'] ?? 0;

    // Get virtual cards information
    $cards_sql = "SELECT vc.*,
                         DATE_FORMAT(vc.expiry_date, '%m/%y') as expiry_formatted,
                         DATE_FORMAT(vc.created_at, '%M %d, %Y') as created_formatted
                  FROM virtual_cards vc
                  WHERE vc.account_id = ?
                  ORDER BY vc.created_at DESC";
    
    $cards_result = $db->query($cards_sql, [$user_id]);
    $virtual_cards = [];
    if ($cards_result) {
        while ($card = $cards_result->fetch_assoc()) {
            $virtual_cards[] = $card;
        }
    }
    $primary_card = $virtual_cards[0] ?? null;

    // Get comprehensive transaction history
    $transactions_sql = "SELECT at.*,
                               DATE_FORMAT(at.created_at, '%M %d, %Y') as transaction_date,
                               DATE_FORMAT(at.created_at, '%h:%i %p') as transaction_time,
                               CASE 
                                   WHEN at.transaction_type IN ('credit', 'transfer_in', 'deposit') THEN 'credit'
                                   ELSE 'debit'
                               END as direction
                        FROM account_transactions at
                        WHERE at.account_id = ?
                        ORDER BY at.created_at DESC
                        LIMIT 10";
    
    $transactions_result = $db->query($transactions_sql, [$user_id]);
    $recent_transactions = [];
    if ($transactions_result) {
        while ($transaction = $transactions_result->fetch_assoc()) {
            $recent_transactions[] = $transaction;
        }
    }

    // Get monthly statistics
    $current_month = date('Y-m');
    $monthly_stats_sql = "SELECT 
                            SUM(CASE WHEN transaction_type IN ('credit', 'transfer_in', 'deposit') THEN amount ELSE 0 END) as total_credits,
                            SUM(CASE WHEN transaction_type IN ('debit', 'transfer_out', 'withdrawal') THEN amount ELSE 0 END) as total_debits,
                            COUNT(*) as transaction_count,
                            AVG(amount) as avg_transaction
                          FROM account_transactions 
                          WHERE account_id = ? 
                          AND DATE_FORMAT(created_at, '%Y-%m') = ?
                          AND status = 'completed'";
    
    $stats_result = $db->query($monthly_stats_sql, [$user_id, $current_month]);
    $monthly_stats = $stats_result ? $stats_result->fetch_assoc() : [];

    $total_credits = $monthly_stats['total_credits'] ?? 0;
    $total_debits = $monthly_stats['total_debits'] ?? 0;
    $transaction_count = $monthly_stats['transaction_count'] ?? 0;
    $avg_transaction = $monthly_stats['avg_transaction'] ?? 0;

    // Get account activity/audit information
    $activity_sql = "SELECT 
                        'Transaction' as activity_type,
                        CONCAT(UPPER(SUBSTRING(transaction_type, 1, 1)), SUBSTRING(transaction_type, 2)) as activity_title,
                        CONCAT('$', FORMAT(amount, 2), ' - ', description) as activity_description,
                        created_at,
                        DATE_FORMAT(created_at, '%h:%i %p') as activity_time
                     FROM account_transactions 
                     WHERE account_id = ?
                     UNION ALL
                     SELECT 
                        'Card' as activity_type,
                        'Virtual Card Created' as activity_title,
                        CONCAT('Card ending in ', RIGHT(card_number, 4)) as activity_description,
                        created_at,
                        DATE_FORMAT(created_at, '%h:%i %p') as activity_time
                     FROM virtual_cards 
                     WHERE account_id = ?
                     ORDER BY created_at DESC
                     LIMIT 8";
    
    $activity_result = $db->query($activity_sql, [$user_id, $user_id]);
    $recent_activity = [];
    if ($activity_result) {
        while ($activity = $activity_result->fetch_assoc()) {
            $recent_activity[] = $activity;
        }
    }

    // Calculate balance change (mock calculation for demo)
    $balance_change = ($total_credits - $total_debits);
    $balance_change_percent = $current_balance > 0 ? (($balance_change / $current_balance) * 100) : 0;

} catch (Exception $e) {
    error_log("Dashboard error: " . $e->getMessage());
    // Fallback data
    $user = [
        'first_name' => 'User', 
        'last_name' => '', 
        'email' => '', 
        'account_number' => '',
        'account_type' => 'savings',
        'status' => 'active',
        'kyc_status' => 'pending',
        'member_since' => date('F Y'),
        'days_member' => 0
    ];
    $current_balance = 0;
    $total_credits = $total_debits = $transaction_count = $avg_transaction = 0;
    $recent_transactions = [];
    $virtual_cards = [];
    $primary_card = null;
    $recent_activity = [];
    $balance_change = 0;
    $balance_change_percent = 0;
}

// Helper functions
function formatCurrency($amount) {
    return '$' . number_format($amount, 2);
}

function getStatusBadgeClass($status) {
    switch (strtolower($status)) {
        case 'active': return 'active';
        case 'pending': return 'pending';
        case 'suspended': case 'closed': return 'suspended';
        default: return 'pending';
    }
}

function maskCardNumber($number) {
    if (strlen($number) < 4) return $number;
    return '**** **** **** ' . substr($number, -4);
}

function getTransactionIcon($type) {
    switch ($type) {
        case 'credit':
        case 'transfer_in':
        case 'deposit':
            return 'fas fa-arrow-down';
        case 'debit':
        case 'transfer_out':
        case 'withdrawal':
            return 'fas fa-arrow-up';
        default:
            return 'fas fa-exchange-alt';
    }
}

function getActivityIcon($type) {
    switch ($type) {
        case 'Transaction': return 'fas fa-exchange-alt';
        case 'Card': return 'fas fa-credit-card';
        default: return 'fas fa-bell';
    }
}
?>

<!-- Add custom CSS for the redesigned dashboard -->
<style>
/* Dashboard Grid */
.dashboard-grid {
    padding: 2.5rem;
    max-width: 1400px;
    margin: 0 auto;
    display: grid;
    grid-template-columns: 1fr 1fr 1fr;
    gap: 2rem;
    grid-template-areas: 
        "overview overview overview"
        "balance transactions cards"
        "stats stats profile"
        "activity activity activity";
}

/* Account Overview Section */
.account-overview {
    grid-area: overview;
    background: linear-gradient(135deg, var(--primary-color, #206bc4) 0%, var(--primary-dark, #1a5490) 100%);
    color: white;
    padding: 2.5rem;
    border-radius: 20px;
    box-shadow: 0 10px 30px rgba(32, 107, 196, 0.3);
    position: relative;
    overflow: hidden;
}

.account-balance {
    font-size: 3.5rem;
    font-weight: 800;
    margin-bottom: 0.5rem;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.account-info {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 1.5rem;
}

/* Card Components */
.dashboard-card {
    background: white;
    border-radius: 16px;
    padding: 2rem;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    border: 1px solid var(--border-color, #e5e7eb);
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.dashboard-card:hover {
    transform: translateY(-4px);
    box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);
}

.dashboard-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, var(--primary-color, #206bc4), var(--accent-color, #10b981));
}

.card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1.5rem;
}

.card-title {
    font-size: 1.25rem;
    font-weight: 700;
    color: var(--text-primary, #1f2937);
}

.card-icon {
    width: 48px;
    height: 48px;
    background: linear-gradient(135deg, var(--primary-color, #206bc4), var(--primary-light, #4a8bc2));
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.5rem;
}

/* Balance Card */
.balance-card {
    grid-area: balance;
}

.balance-amount {
    font-size: 2.5rem;
    font-weight: 800;
    color: var(--primary-color, #206bc4);
    margin-bottom: 1rem;
}

/* Transaction Card */
.transactions-card {
    grid-area: transactions;
}

.transaction-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem 0;
    border-bottom: 1px solid var(--border-light, #f3f4f6);
}

.transaction-item:last-child {
    border-bottom: none;
}

.transaction-info {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.transaction-icon {
    width: 40px;
    height: 40px;
    border-radius: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.2rem;
    color: white;
}

.transaction-icon.credit {
    background: linear-gradient(135deg, var(--success-color, #10b981), #059669);
}

.transaction-icon.debit {
    background: linear-gradient(135deg, var(--danger-color, #ef4444), #dc2626);
}

.transaction-amount.credit {
    color: var(--success-color, #10b981);
}

.transaction-amount.debit {
    color: var(--danger-color, #ef4444);
}

/* Virtual Cards */
.cards-card {
    grid-area: cards;
}

/* Statistics */
.stats-card {
    grid-area: stats;
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1.5rem;
}

.stat-item {
    text-align: center;
    padding: 1.5rem;
    background: var(--background-light, #f8fafc);
    border-radius: 12px;
    border: 1px solid var(--border-light, #f3f4f6);
}

.stat-value {
    font-size: 2rem;
    font-weight: 800;
    margin-bottom: 0.5rem;
}

.stat-value.credit {
    color: var(--success-color, #10b981);
}

.stat-value.debit {
    color: var(--danger-color, #ef4444);
}

/* Profile Card */
.profile-card {
    grid-area: profile;
}

/* Activity Feed */
.activity-card {
    grid-area: activity;
}

/* Responsive Design */
@media (max-width: 1200px) {
    .dashboard-grid {
        grid-template-columns: 1fr 1fr;
        grid-template-areas: 
            "overview overview"
            "balance transactions"
            "cards stats"
            "profile profile"
            "activity activity";
    }
}

@media (max-width: 768px) {
    .dashboard-grid {
        grid-template-columns: 1fr;
        grid-template-areas: 
            "overview"
            "balance"
            "transactions"
            "cards"
            "stats"
            "profile"
            "activity";
        padding: 1.5rem;
    }
}
</style>

<!-- Dashboard Header -->
<div class="container-fluid p-0">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4 p-4 bg-white shadow-sm">
                <div>
                    <h1 class="h2 mb-1">Welcome back, <?php echo htmlspecialchars($user['first_name']); ?>!</h1>
                    <p class="text-muted mb-0">Here's what's happening with your account today.</p>
                </div>
                <div class="d-flex gap-2">
                    <a href="transfers/" class="btn btn-primary">
                        <i class="fas fa-paper-plane me-2"></i>Send Money
                    </a>
                    <a href="cards/" class="btn btn-success">
                        <i class="fas fa-plus me-2"></i>New Card
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Dashboard Grid -->
<div class="dashboard-grid">
    <!-- Account Overview -->
    <section class="account-overview">
        <div class="account-balance">
            <?php echo formatCurrency($current_balance); ?>
        </div>
        <div class="account-info">
            <div class="account-details">
                <h3><?php echo htmlspecialchars($user['first_name'] . ' ' . $user['last_name']); ?></h3>
                <div class="account-number"><?php echo htmlspecialchars($user['account_number']); ?></div>
                <span class="badge bg-success mt-2">
                    <?php echo ucfirst($user['status']); ?>
                </span>
            </div>
            <div class="account-type">
                <h4><?php echo ucfirst($user['account_type']); ?> Account</h4>
                <p>Member since <?php echo htmlspecialchars($user['member_since']); ?></p>
            </div>
        </div>
    </section>

    <!-- Balance Card -->
    <section class="dashboard-card balance-card">
        <div class="card-header">
            <h2 class="card-title">Account Balance</h2>
            <div class="card-icon">
                <i class="fas fa-wallet"></i>
            </div>
        </div>
        <div class="balance-amount"><?php echo formatCurrency($current_balance); ?></div>
        <div class="text-success">
            <i class="fas fa-arrow-up"></i>
            <?php echo formatCurrency(abs($balance_change)); ?> this month
        </div>
    </section>

    <!-- Recent Transactions -->
    <section class="dashboard-card transactions-card">
        <div class="card-header">
            <h2 class="card-title">Recent Transactions</h2>
            <div class="card-icon">
                <i class="fas fa-history"></i>
            </div>
        </div>
        <div class="transactions-list">
            <?php if (empty($recent_transactions)): ?>
                <p class="text-muted">No transactions found.</p>
            <?php else: ?>
                <?php foreach (array_slice($recent_transactions, 0, 5) as $transaction): ?>
                    <div class="transaction-item">
                        <div class="transaction-info">
                            <div class="transaction-icon <?php echo $transaction['direction']; ?>">
                                <i class="<?php echo getTransactionIcon($transaction['transaction_type']); ?>"></i>
                            </div>
                            <div class="transaction-details">
                                <h6 class="mb-1"><?php echo ucfirst(str_replace('_', ' ', $transaction['transaction_type'])); ?></h6>
                                <small class="text-muted"><?php echo htmlspecialchars($transaction['description']); ?></small>
                                <br><small class="text-muted"><?php echo $transaction['transaction_date']; ?></small>
                            </div>
                        </div>
                        <div class="transaction-amount <?php echo $transaction['direction']; ?>">
                            <?php echo ($transaction['direction'] === 'credit' ? '+' : '-') . formatCurrency($transaction['amount']); ?>
                        </div>
                    </div>
                <?php endforeach; ?>
            <?php endif; ?>
        </div>
        <a href="transactions/" class="btn btn-outline-primary btn-sm mt-3">View All Transactions</a>
    </section>

    <!-- Virtual Cards -->
    <section class="dashboard-card cards-card">
        <div class="card-header">
            <h2 class="card-title">Virtual Cards</h2>
            <div class="card-icon">
                <i class="fas fa-credit-card"></i>
            </div>
        </div>
        <?php if ($primary_card): ?>
            <div class="card bg-primary text-white p-3 mb-3" style="border-radius: 16px;">
                <div class="card-number mb-2" style="font-family: monospace; font-size: 1.1rem; letter-spacing: 2px;">
                    <?php echo maskCardNumber($primary_card['card_number']); ?>
                </div>
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <small><?php echo htmlspecialchars($primary_card['card_holder_name']); ?></small>
                    </div>
                    <div>
                        <small><?php echo $primary_card['expiry_formatted']; ?></small>
                    </div>
                </div>
                <div class="mt-2">
                    <small>Balance: <?php echo formatCurrency($primary_card['card_balance']); ?></small>
                </div>
            </div>
            <p class="text-muted">
                <i class="fas fa-shield-alt"></i>
                Status: <span class="badge bg-success">
                    <?php echo ucfirst($primary_card['status']); ?>
                </span>
            </p>
        <?php else: ?>
            <div class="text-center py-4">
                <i class="fas fa-credit-card fa-3x text-muted mb-3"></i>
                <p class="text-muted">No virtual cards found.</p>
                <a href="cards/" class="btn btn-primary btn-sm">Apply for Card</a>
            </div>
        <?php endif; ?>
    </section>

    <!-- Monthly Statistics -->
    <section class="dashboard-card stats-card">
        <div class="card-header">
            <h2 class="card-title">This Month</h2>
            <div class="card-icon">
                <i class="fas fa-chart-bar"></i>
            </div>
        </div>
        <div class="stat-item">
            <div class="stat-value credit"><?php echo formatCurrency($total_credits); ?></div>
            <div class="stat-label">Total Credits</div>
        </div>
        <div class="stat-item">
            <div class="stat-value debit"><?php echo formatCurrency($total_debits); ?></div>
            <div class="stat-label">Total Debits</div>
        </div>
        <div class="stat-item">
            <div class="stat-value"><?php echo $transaction_count; ?></div>
            <div class="stat-label">Transactions</div>
        </div>
        <div class="stat-item">
            <div class="stat-value"><?php echo formatCurrency($avg_transaction); ?></div>
            <div class="stat-label">Average Amount</div>
        </div>
    </section>

    <!-- User Profile -->
    <section class="dashboard-card profile-card">
        <div class="card-header">
            <h2 class="card-title">Profile</h2>
            <div class="card-icon">
                <i class="fas fa-user"></i>
            </div>
        </div>
        <div class="d-flex align-items-center mb-3">
            <div class="bg-primary text-white rounded-circle d-flex align-items-center justify-content-center me-3" style="width: 60px; height: 60px; font-size: 1.5rem; font-weight: 700;">
                <?php echo strtoupper(substr($user['first_name'], 0, 1) . substr($user['last_name'], 0, 1)); ?>
            </div>
            <div>
                <h5 class="mb-1"><?php echo htmlspecialchars($user['first_name'] . ' ' . $user['last_name']); ?></h5>
                <p class="text-muted mb-1"><?php echo htmlspecialchars($user['email']); ?></p>
                <span class="badge bg-success">
                    <?php echo ucfirst($user['status']); ?>
                </span>
            </div>
        </div>
        <div class="row">
            <div class="col-6 text-center">
                <div class="bg-light p-2 rounded">
                    <div class="h5 text-primary mb-1"><?php echo $user['days_member']; ?></div>
                    <small class="text-muted">Days Member</small>
                </div>
            </div>
            <div class="col-6 text-center">
                <div class="bg-light p-2 rounded">
                    <div class="h5 text-primary mb-1"><?php echo count($virtual_cards); ?></div>
                    <small class="text-muted">Active Cards</small>
                </div>
            </div>
        </div>
        <a href="../profile/" class="btn btn-outline-primary btn-sm mt-3">Edit Profile</a>
    </section>

    <!-- Recent Activity -->
    <section class="dashboard-card activity-card">
        <div class="card-header">
            <h2 class="card-title">Recent Activity</h2>
            <div class="card-icon">
                <i class="fas fa-bell"></i>
            </div>
        </div>
        <div class="activity-list">
            <?php if (empty($recent_activity)): ?>
                <p class="text-muted">No recent activity.</p>
            <?php else: ?>
                <?php foreach ($recent_activity as $activity): ?>
                    <div class="d-flex align-items-center py-2 border-bottom">
                        <div class="bg-primary text-white rounded d-flex align-items-center justify-content-center me-3" style="width: 36px; height: 36px;">
                            <i class="<?php echo getActivityIcon($activity['activity_type']); ?>"></i>
                        </div>
                        <div class="flex-grow-1">
                            <h6 class="mb-1"><?php echo htmlspecialchars($activity['activity_title']); ?></h6>
                            <small class="text-muted"><?php echo htmlspecialchars($activity['activity_description']); ?></small>
                        </div>
                        <small class="text-muted"><?php echo $activity['activity_time']; ?></small>
                    </div>
                <?php endforeach; ?>
            <?php endif; ?>
        </div>
    </section>
</div>

<?php require_once '../templates/user/footer.php'; ?>
