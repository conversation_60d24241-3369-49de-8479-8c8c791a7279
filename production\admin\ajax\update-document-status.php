<?php
require_once '../../config/config.php';
requireAdmin();

header('Content-Type: application/json');

try {
    // Get JSON input
    $input = json_decode(file_get_contents('php://input'), true);
    
    if (!$input) {
        throw new Exception('Invalid JSON input');
    }
    
    $document_id = intval($input['document_id'] ?? 0);
    $status = trim($input['status'] ?? '');
    $reason = trim($input['reason'] ?? '');
    
    // Validate inputs
    if ($document_id <= 0) {
        throw new Exception('Invalid document ID');
    }
    
    $valid_statuses = ['pending', 'approved', 'rejected', 'expired'];
    if (!in_array($status, $valid_statuses)) {
        throw new Exception('Invalid status');
    }
    
    $db = getDB();
    
    // Check if document exists
    $check_query = "SELECT id, user_id, document_type, document_name FROM user_documents WHERE id = ?";
    $check_result = $db->query($check_query, [$document_id]);
    
    if (!$check_result || $check_result->num_rows === 0) {
        throw new Exception('Document not found');
    }
    
    $document = $check_result->fetch_assoc();
    
    // Update document status
    $update_query = "UPDATE user_documents SET 
                     verification_status = ?, 
                     verified_by = ?, 
                     verified_at = NOW(), 
                     rejection_reason = ?,
                     updated_at = NOW()
                     WHERE id = ?";
    
    $db->query($update_query, [
        $status,
        $_SESSION['user_id'],
        $status === 'rejected' ? $reason : null,
        $document_id
    ]);
    
    // Add to verification history
    $history_query = "INSERT INTO document_verification_history 
                      (document_id, action, performed_by, reason, notes) 
                      VALUES (?, ?, ?, ?, ?)";
    
    $action_reason = $status === 'approved' ? 'Document approved by admin' : 
                    ($status === 'rejected' ? 'Document rejected by admin' : 
                     'Document status updated by admin');
    
    $db->query($history_query, [
        $document_id,
        $status,
        $_SESSION['user_id'],
        $action_reason,
        $reason
    ]);
    
    // Log the action
    error_log("Document status updated: Document ID {$document_id}, Status: {$status}, Admin: {$_SESSION['user_id']}");
    
    echo json_encode([
        'success' => true,
        'message' => 'Document status updated successfully',
        'document_id' => $document_id,
        'new_status' => $status
    ]);
    
} catch (Exception $e) {
    error_log("Document status update error: " . $e->getMessage());
    
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ]);
}
?>
