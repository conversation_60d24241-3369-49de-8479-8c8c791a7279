<?php
/**
 * Comprehensive Transfer System Test
 * Tests database structure, OTP system, and transfer functionality
 */

// Enable error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Include required files
require_once 'config/config.php';

// Start session for testing
session_start();

// Test results storage
$test_results = [];
$overall_success = true;

/**
 * Log test result
 */
function logTest($test_name, $success, $message = '', $details = '') {
    global $test_results, $overall_success;
    
    $test_results[] = [
        'name' => $test_name,
        'success' => $success,
        'message' => $message,
        'details' => $details,
        'timestamp' => date('Y-m-d H:i:s')
    ];
    
    if (!$success) {
        $overall_success = false;
    }
    
    $status = $success ? '✅ PASS' : '❌ FAIL';
    echo "<div style='margin: 10px 0; padding: 10px; border-left: 4px solid " . ($success ? '#28a745' : '#dc3545') . "; background: " . ($success ? '#d4edda' : '#f8d7da') . ";'>";
    echo "<strong>$status: $test_name</strong><br>";
    if ($message) echo "Message: $message<br>";
    if ($details) echo "Details: <code>$details</code>";
    echo "</div>";
}

/**
 * Test database connection
 */
function testDatabaseConnection() {
    try {
        $db = getDB();
        $connection = $db->getConnection();
        
        if ($connection && !$connection->connect_error) {
            logTest('Database Connection', true, 'Successfully connected to database');
            return $db;
        } else {
            logTest('Database Connection', false, 'Failed to connect to database', $connection->connect_error ?? 'Unknown error');
            return null;
        }
    } catch (Exception $e) {
        logTest('Database Connection', false, 'Exception during connection', $e->getMessage());
        return null;
    }
}

/**
 * Test table structure
 */
function testTableStructure($db) {
    $tables_to_check = ['accounts', 'transfers', 'user_otps', 'beneficiaries'];
    
    foreach ($tables_to_check as $table) {
        try {
            $result = $db->query("DESCRIBE $table");
            if ($result && $result->num_rows > 0) {
                $columns = [];
                while ($row = $result->fetch_assoc()) {
                    $columns[] = $row['Field'];
                }
                logTest("Table Structure: $table", true, "Found " . count($columns) . " columns", implode(', ', $columns));
            } else {
                logTest("Table Structure: $table", false, "Table not found or empty");
            }
        } catch (Exception $e) {
            logTest("Table Structure: $table", false, "Error checking table", $e->getMessage());
        }
    }
}

/**
 * Test database methods
 */
function testDatabaseMethods($db) {
    try {
        // Test beginTransaction method
        if (method_exists($db, 'beginTransaction')) {
            logTest('Database Method: beginTransaction', true, 'Method exists');
        } else {
            logTest('Database Method: beginTransaction', false, 'Method does not exist');
        }
        
        // Test insert method
        if (method_exists($db, 'insert')) {
            logTest('Database Method: insert', true, 'Method exists');
        } else {
            logTest('Database Method: insert', false, 'Method does not exist');
        }
        
        // Test getConnection method
        if (method_exists($db, 'getConnection')) {
            logTest('Database Method: getConnection', true, 'Method exists');
        } else {
            logTest('Database Method: getConnection', false, 'Method does not exist');
        }
        
        // Test insert_id property access
        $connection = $db->getConnection();
        if (property_exists($connection, 'insert_id')) {
            logTest('Database Property: insert_id', true, 'Property accessible via getConnection()');
        } else {
            logTest('Database Property: insert_id', false, 'Property not accessible');
        }
        
    } catch (Exception $e) {
        logTest('Database Methods Test', false, 'Exception during method testing', $e->getMessage());
    }
}

/**
 * Test user accounts
 */
function testUserAccounts($db) {
    try {
        // Find test users
        $result = $db->query("SELECT id, account_number, first_name, last_name, email, is_admin, status, balance FROM accounts WHERE status = 'active' LIMIT 5");
        
        if ($result && $result->num_rows > 0) {
            $users = [];
            while ($row = $result->fetch_assoc()) {
                $users[] = $row;
            }
            
            logTest('User Accounts', true, "Found " . count($users) . " active users", 
                   "Sample: " . $users[0]['first_name'] . " " . $users[0]['last_name'] . " (" . $users[0]['account_number'] . ")");
            
            return $users;
        } else {
            logTest('User Accounts', false, 'No active users found');
            return [];
        }
    } catch (Exception $e) {
        logTest('User Accounts', false, 'Error fetching users', $e->getMessage());
        return [];
    }
}

/**
 * Test OTP system
 */
function testOTPSystem($db, $users) {
    if (empty($users)) {
        logTest('OTP System', false, 'No users available for testing');
        return;
    }
    
    $test_user = $users[0];
    $user_id = $test_user['id'];
    
    try {
        // Generate test OTP
        $otp_code = sprintf('%06d', mt_rand(100000, 999999));
        
        // Insert OTP using correct column names
        $insert_otp_sql = "INSERT INTO user_otps (user_id, otp_code, expires_at, source, used, created_at) 
                          VALUES (?, ?, DATE_ADD(NOW(), INTERVAL 10 MINUTE), 'transfer', 0, NOW())";
        
        $db->query($insert_otp_sql, [$user_id, $otp_code]);
        
        // Verify OTP was inserted
        $verify_otp_sql = "SELECT * FROM user_otps WHERE user_id = ? AND otp_code = ? AND expires_at > NOW() AND used = 0";
        $result = $db->query($verify_otp_sql, [$user_id, $otp_code]);
        
        if ($result && $result->num_rows > 0) {
            logTest('OTP System: Insert & Verify', true, "OTP generated and verified successfully", "OTP: $otp_code");
            
            // Test OTP marking as used
            $mark_used_sql = "UPDATE user_otps SET used = 1, used_at = NOW() WHERE user_id = ? AND otp_code = ?";
            $db->query($mark_used_sql, [$user_id, $otp_code]);
            
            // Verify OTP is now marked as used
            $check_used_sql = "SELECT used FROM user_otps WHERE user_id = ? AND otp_code = ?";
            $used_result = $db->query($check_used_sql, [$user_id, $otp_code]);
            $used_row = $used_result->fetch_assoc();
            
            if ($used_row && $used_row['used'] == 1) {
                logTest('OTP System: Mark as Used', true, "OTP successfully marked as used");
            } else {
                logTest('OTP System: Mark as Used', false, "Failed to mark OTP as used");
            }
            
        } else {
            logTest('OTP System: Insert & Verify', false, "Failed to verify generated OTP");
        }
        
    } catch (Exception $e) {
        logTest('OTP System', false, 'Exception during OTP testing', $e->getMessage());
    }
}

/**
 * Test transfer table structure
 */
function testTransferTableStructure($db) {
    try {
        // Get actual transfers table structure
        $result = $db->query("DESCRIBE transfers");
        $actual_columns = [];
        
        if ($result) {
            while ($row = $result->fetch_assoc()) {
                $actual_columns[] = $row['Field'];
            }
        }
        
        // Expected columns based on schema
        $expected_columns = ['id', 'transaction_id', 'sender_id', 'recipient_id', 'recipient_account', 
                           'recipient_name', 'amount', 'currency', 'transfer_type', 'status', 
                           'description', 'fee', 'exchange_rate', 'created_at', 'completed_at'];
        
        $missing_columns = array_diff($expected_columns, $actual_columns);
        $extra_columns = array_diff($actual_columns, $expected_columns);
        
        if (empty($missing_columns) && empty($extra_columns)) {
            logTest('Transfer Table Structure', true, "All expected columns present", implode(', ', $actual_columns));
        } else {
            $details = '';
            if (!empty($missing_columns)) {
                $details .= "Missing: " . implode(', ', $missing_columns) . "; ";
            }
            if (!empty($extra_columns)) {
                $details .= "Extra: " . implode(', ', $extra_columns);
            }
            logTest('Transfer Table Structure', false, "Column mismatch detected", $details);
        }
        
        return $actual_columns;
        
    } catch (Exception $e) {
        logTest('Transfer Table Structure', false, 'Error checking transfer table', $e->getMessage());
        return [];
    }
}

// Start testing
echo "<!DOCTYPE html><html><head><title>Transfer System Test Results</title>";
echo "<style>body{font-family:Arial,sans-serif;margin:20px;} code{background:#f4f4f4;padding:2px 4px;border-radius:3px;}</style>";
echo "</head><body>";
echo "<h1>🧪 Transfer System Comprehensive Test</h1>";
echo "<p><strong>Test Started:</strong> " . date('Y-m-d H:i:s') . "</p>";

// Run tests
echo "<h2>📋 Test Results</h2>";

$db = testDatabaseConnection();
if ($db) {
    testTableStructure($db);
    testDatabaseMethods($db);
    $users = testUserAccounts($db);
    testOTPSystem($db, $users);
    testTransferTableStructure($db);
}

// Summary
echo "<h2>📊 Test Summary</h2>";
$total_tests = count($test_results);
$passed_tests = array_filter($test_results, function($test) { return $test['success']; });
$passed_count = count($passed_tests);
$failed_count = $total_tests - $passed_count;

echo "<div style='padding: 15px; background: " . ($overall_success ? '#d4edda' : '#f8d7da') . "; border: 1px solid " . ($overall_success ? '#c3e6cb' : '#f5c6cb') . "; border-radius: 5px;'>";
echo "<strong>Overall Result: " . ($overall_success ? '✅ ALL TESTS PASSED' : '❌ SOME TESTS FAILED') . "</strong><br>";
echo "Total Tests: $total_tests | Passed: $passed_count | Failed: $failed_count";
echo "</div>";

if (!$overall_success) {
    echo "<h3>❌ Failed Tests:</h3>";
    foreach ($test_results as $test) {
        if (!$test['success']) {
            echo "<div style='margin: 5px 0; padding: 8px; background: #f8d7da; border-left: 4px solid #dc3545;'>";
            echo "<strong>{$test['name']}</strong>: {$test['message']}";
            if ($test['details']) echo "<br>Details: <code>{$test['details']}</code>";
            echo "</div>";
        }
    }
}

echo "<h3>🔧 Next Steps:</h3>";
if ($overall_success) {
    echo "<p>✅ All tests passed! Ready to implement the corrected transfer system.</p>";
} else {
    echo "<p>❌ Fix the failed tests before implementing the transfer system.</p>";
}

echo "</body></html>";
?>
