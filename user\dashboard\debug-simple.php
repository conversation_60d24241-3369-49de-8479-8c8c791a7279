<?php
/**
 * Simple Debug for Dashboard Issues
 */

// Enable error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h1>Dashboard Debug</h1>";

// Test 1: Session
session_start();
echo "<h2>1. Session Test</h2>";
if (isset($_SESSION['user_id'])) {
    echo "✅ User logged in: " . $_SESSION['user_id'] . "<br>";
} else {
    echo "❌ User not logged in<br>";
    echo "<a href='../../auth/login.php'>Go to Login</a><br>";
}

// Test 2: Config file
echo "<h2>2. Config File Test</h2>";
if (file_exists('../../config/config.php')) {
    echo "✅ Config file exists<br>";
    try {
        require_once '../../config/config.php';
        echo "✅ Config loaded successfully<br>";
    } catch (Exception $e) {
        echo "❌ Config error: " . $e->getMessage() . "<br>";
    }
} else {
    echo "❌ Config file not found<br>";
}

// Test 3: Database connection
echo "<h2>3. Database Test</h2>";
try {
    $db = getDB();
    echo "✅ Database connection successful<br>";
    
    // Test query
    $result = $db->query("SELECT COUNT(*) as count FROM accounts");
    $count = $result->fetch_assoc();
    echo "✅ Database query successful - " . $count['count'] . " accounts found<br>";
} catch (Exception $e) {
    echo "❌ Database error: " . $e->getMessage() . "<br>";
}

// Test 4: Shared files
echo "<h2>4. Shared Files Test</h2>";
$shared_files = [
    '../shared/header.php',
    '../shared/sidebar.php',
    '../shared/user_header.php',
    '../shared/user_footer.php',
    '../shared/footer.php'
];

foreach ($shared_files as $file) {
    if (file_exists($file)) {
        echo "✅ $file exists<br>";
    } else {
        echo "❌ $file missing<br>";
    }
}

// Test 5: CSS and JS files
echo "<h2>5. Asset Files Test</h2>";
$asset_files = [
    'dashboard.css',
    'dashboard.js'
];

foreach ($asset_files as $file) {
    if (file_exists($file)) {
        echo "✅ $file exists<br>";
    } else {
        echo "❌ $file missing<br>";
    }
}

// Test 6: Dynamic CSS
echo "<h2>6. Dynamic CSS Test</h2>";
if (file_exists('../../config/dynamic-css.php')) {
    echo "✅ Dynamic CSS file exists<br>";
    try {
        require_once '../../config/dynamic-css.php';
        echo "✅ Dynamic CSS loaded<br>";
    } catch (Exception $e) {
        echo "❌ Dynamic CSS error: " . $e->getMessage() . "<br>";
    }
} else {
    echo "❌ Dynamic CSS file not found<br>";
}

// Test 7: User data
if (isset($_SESSION['user_id'])) {
    echo "<h2>7. User Data Test</h2>";
    try {
        $user_id = $_SESSION['user_id'];
        $user_query = "SELECT * FROM accounts WHERE id = ?";
        $user_result = $db->query($user_query, [$user_id]);
        
        if ($user_result->num_rows > 0) {
            $user = $user_result->fetch_assoc();
            echo "✅ User data found: " . htmlspecialchars($user['first_name'] . ' ' . $user['last_name']) . "<br>";
            echo "✅ Account balance: " . $user['balance'] . "<br>";
        } else {
            echo "❌ User data not found<br>";
        }
    } catch (Exception $e) {
        echo "❌ User data error: " . $e->getMessage() . "<br>";
    }
}

echo "<h2>8. Test Links</h2>";
echo "<a href='index.php'>Try Main Dashboard</a><br>";
echo "<a href='debug.php'>Try Original Debug</a><br>";
echo "<a href='index-simple.php'>Try Simple Dashboard</a><br>";

?>
