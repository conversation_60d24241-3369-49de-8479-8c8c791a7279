<?php
// Basic test to see if <PERSON><PERSON> is working
echo "<h1>Basic PHP Test</h1>";
echo "<p>Current time: " . date('Y-m-d H:i:s') . "</p>";
echo "<p>PHP Version: " . phpversion() . "</p>";

// Check if files exist
echo "<h2>File Check</h2>";
$files_to_check = [
    'config/config.php',
    'config/database.php', 
    'config/email.php',
    'vendor/phpmailer/phpmailer/src/PHPMailer.php'
];

foreach ($files_to_check as $file) {
    if (file_exists($file)) {
        echo "✅ $file exists<br>";
    } else {
        echo "❌ $file NOT FOUND<br>";
    }
}

// Try to include email config
echo "<h2>Email Config Test</h2>";
try {
    require_once 'config/email.php';
    echo "✅ Email config loaded<br>";
    
    if (defined('SMTP_HOST')) {
        echo "✅ SMTP_HOST: " . SMTP_HOST . "<br>";
        echo "✅ SMTP_PORT: " . SMTP_PORT . "<br>";
        echo "✅ FROM_EMAIL: " . FROM_EMAIL . "<br>";
    } else {
        echo "❌ SMTP constants not defined<br>";
    }
} catch (Exception $e) {
    echo "❌ Error loading email config: " . $e->getMessage() . "<br>";
}

echo "<p>If you can see this, PHP is working!</p>";
?>
