<?php
/**
 * Simple Email <NAME_EMAIL>
 */

// Basic email test using PHP mail() function
$to = '<EMAIL>';
$subject = '🏦 Online Banking System - Email Test ' . date('Y-m-d H:i:s');
$message = '
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>Email Test</title>
</head>
<body style="font-family: Arial, sans-serif; margin: 0; padding: 20px; background-color: #f5f5f5;">
    <div style="max-width: 600px; margin: 0 auto; background-color: white; border-radius: 10px; overflow: hidden; box-shadow: 0 4px 6px rgba(0,0,0,0.1);">
        <div style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 30px; text-align: center;">
            <h1>🏦 Online Banking System</h1>
            <h2>Email Functionality Test</h2>
        </div>
        
        <div style="padding: 30px;">
            <div style="background: #d4edda; border: 1px solid #c3e6cb; color: #155724; padding: 20px; border-radius: 5px; margin: 20px 0;">
                <h3>✅ Email System Working!</h3>
                <p>This email confirms that the Online Banking System email functionality is working correctly.</p>
            </div>
            
            <h3>📋 Test Details:</h3>
            <div style="background: #e2e3e5; border: 1px solid #d6d8db; color: #383d41; padding: 15px; border-radius: 5px; margin: 15px 0;">
                <strong>Test Email:</strong> ' . $to . '<br>
                <strong>Test Date:</strong> ' . date('Y-m-d H:i:s') . '<br>
                <strong>Server:</strong> localhost<br>
                <strong>Test Type:</strong> Simple PHP mail() function
            </div>
            
            <h3>🎯 What This Means:</h3>
            <ul>
                <li>✅ PHP mail() function is working</li>
                <li>✅ Email templates are rendering correctly</li>
                <li>✅ Server can send emails</li>
                <li>✅ HTML email formatting is functional</li>
            </ul>
            
            <h3>🔧 Next Steps:</h3>
            <ol>
                <li>Verify this email was received in your inbox</li>
                <li>Check spam/junk folder if not in inbox</li>
                <li>Test the full banking system email features</li>
                <li>Test OTP email functionality</li>
            </ol>
        </div>
        
        <div style="background: #f8f9fa; padding: 20px; text-align: center; color: #6c757d; font-size: 14px;">
            <p>This is an automated test email from the Online Banking System.</p>
            <p>&copy; ' . date('Y') . ' Online Banking System. All rights reserved.</p>
        </div>
    </div>
</body>
</html>';

// Set headers for HTML email
$headers = array();
$headers[] = 'MIME-Version: 1.0';
$headers[] = 'Content-type: text/html; charset=UTF-8';
$headers[] = 'From: Online Banking System <<EMAIL>>';
$headers[] = 'Reply-To: <EMAIL>';
$headers[] = 'X-Mailer: PHP/' . phpversion();

// Send the email
$result = mail($to, $subject, $message, implode("\r\n", $headers));

// Display result
echo "<h1>📧 Simple Email Test Result</h1>";
echo "<p><strong>Target Email:</strong> $to</p>";
echo "<p><strong>Subject:</strong> $subject</p>";
echo "<p><strong>Test Time:</strong> " . date('Y-m-d H:i:s') . "</p>";

if ($result) {
    echo "<div style='background: #d4edda; border: 1px solid #c3e6cb; color: #155724; padding: 20px; border-radius: 5px; margin: 20px 0;'>";
    echo "<h2>✅ EMAIL SENT SUCCESSFULLY!</h2>";
    echo "<p>The email has been sent to <strong>$to</strong></p>";
    echo "<p>Please check your inbox (and spam folder) for the test email.</p>";
    echo "</div>";
} else {
    echo "<div style='background: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; padding: 20px; border-radius: 5px; margin: 20px 0;'>";
    echo "<h2>❌ EMAIL SENDING FAILED</h2>";
    echo "<p>The email could not be sent to <strong>$to</strong></p>";
    echo "<p>This might be due to server configuration or SMTP settings.</p>";
    echo "</div>";
}

// Log the attempt
$log_message = "=== SIMPLE EMAIL TEST ===\n";
$log_message .= "To: $to\n";
$log_message .= "Subject: $subject\n";
$log_message .= "Time: " . date('Y-m-d H:i:s') . "\n";
$log_message .= "Result: " . ($result ? 'SUCCESS' : 'FAILED') . "\n";
$log_message .= "========================\n\n";

// Create logs directory if it doesn't exist
if (!is_dir('logs')) {
    mkdir('logs', 0755, true);
}

file_put_contents('logs/simple_email_test.log', $log_message, FILE_APPEND | LOCK_EX);

echo "<h2>📄 Log Information</h2>";
echo "<p>Test result has been logged to: <code>logs/simple_email_test.log</code></p>";

// Show recent log entries
if (file_exists('logs/simple_email_test.log')) {
    $log_content = file_get_contents('logs/simple_email_test.log');
    $log_lines = explode("\n", $log_content);
    $recent_lines = array_slice($log_lines, -10);
    
    echo "<h3>Recent Log Entries:</h3>";
    echo "<pre style='background: #f8f9fa; padding: 15px; border-radius: 5px; font-size: 12px; max-height: 200px; overflow-y: auto;'>";
    echo implode("\n", $recent_lines);
    echo "</pre>";
}

echo "<div style='background: #e7f3ff; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
echo "<h3>🔍 What to Check:</h3>";
echo "<ol>";
echo "<li>Check your email inbox at <strong><EMAIL></strong></li>";
echo "<li>Look for email with subject: <em>$subject</em></li>";
echo "<li>Check spam/junk folder if not in inbox</li>";
echo "<li>Email delivery may take a few minutes</li>";
echo "</ol>";
echo "</div>";
?>
