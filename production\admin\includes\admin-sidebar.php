<?php
// admin_sidebar.php

function isActivePage($page) {
    return basename($_SERVER['PHP_SELF']) == $page ? 'active' : '';
}
?>

<div class="admin-sidebar">
    <ul class="admin-nav">
        <li class="admin-nav-item">
            <a class="admin-nav-link <?php echo isActivePage('dashboard.php'); ?>" href="dashboard.php">
                <i class="fas fa-tachometer-alt admin-nav-icon"></i>
                Dashboard
            </a>
        </li>
        <li class="admin-nav-item">
            <a class="admin-nav-link <?php echo isActivePage('users.php'); ?>" href="users.php">
                <i class="fas fa-users admin-nav-icon"></i>
                Users
            </a>
        </li>
        <li class="admin-nav-item">
            <a class="admin-nav-link <?php echo isActivePage('settings.php'); ?>" href="settings.php">
                <i class="fas fa-cog admin-nav-icon"></i>
                Settings
            </a>
        </li>
        <li class="admin-nav-item">
            <a class="admin-nav-link <?php echo isActivePage('security-settings.php'); ?>" href="security-settings.php">
                <i class="fas fa-cog admin-nav-icon"></i>
                Global Security
            </a>
        </li>
        <li class="admin-nav-item">
            <a class="admin-nav-link <?php echo isActivePage('configure-2fa.php'); ?>" href="configure-2fa.php">
                <i class="fas fa-shield-alt admin-nav-icon"></i>
                Configure 2FA
            </a>
        </li>
    </ul>
</div>