<?php
// Set page variables
$page_title = 'Transaction History';
$additional_css = ['transactions.css'];
$additional_js = ['transactions.js'];

// Include header template
require_once '../../templates/user/header.php';

// Include database connection
require_once '../../config/config.php';
requireLogin();

// Get user transactions
try {
    $db = getDB();
    $user_id = $_SESSION['user_id'];
    
    // Pagination
    $page = isset($_GET['page']) ? max(1, intval($_GET['page'])) : 1;
    $limit = 20;
    $offset = ($page - 1) * $limit;
    
    // Get total count
    $count_sql = "SELECT COUNT(*) as total FROM transactions WHERE user_id = ?";
    $count_result = $db->query($count_sql, [$user_id]);
    $total_transactions = $count_result->fetch_assoc()['total'];
    $total_pages = ceil($total_transactions / $limit);
    
    // Get transactions
    $transactions_sql = "SELECT * FROM transactions WHERE user_id = ? ORDER BY created_at DESC LIMIT ? OFFSET ?";
    $transactions_result = $db->query($transactions_sql, [$user_id, $limit, $offset]);
    $transactions = [];
    while ($row = $transactions_result->fetch_assoc()) {
        $transactions[] = $row;
    }
    
    // Get account info for currency
    $account_sql = "SELECT currency FROM accounts WHERE id = ?";
    $account_result = $db->query($account_sql, [$user_id]);
    $currency = $account_result->fetch_assoc()['currency'] ?? 'USD';
    
} catch (Exception $e) {
    error_log("Transactions page error: " . $e->getMessage());
    $transactions = [];
    $total_transactions = 0;
    $total_pages = 1;
    $currency = 'USD';
}

?>

<!-- Include Sidebar -->
<?php require_once '../../templates/user/sidebar.php'; ?>

<div class="main-content">
    <div class="banking-container">
        <div class="banking-dashboard">
            <!-- Page Header -->
            <div class="page-header">
                <div class="page-title">
                    <h1>Transaction History</h1>
                    <p>View all your account transactions</p>
                </div>
                <div class="page-actions">
                    <a href="../statements/" class="btn-outline">
                        <svg width="16" height="16" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M4 4a2 2 0 012-2h4.586A2 2 0 0112 2.586L15.414 6A2 2 0 0116 7.414V16a2 2 0 01-2 2H6a2 2 0 01-2-2V4zm2 6a1 1 0 011-1h6a1 1 0 110 2H7a1 1 0 01-1-1zm1 3a1 1 0 100 2h6a1 1 0 100-2H7z" clip-rule="evenodd"/>
                        </svg>
                        Download Statement
                    </a>
                    <a href="../transfers/" class="btn-primary">
                        <svg width="16" height="16" fill="currentColor" viewBox="0 0 20 20">
                            <path d="M8 5a1 1 0 100 2h5.586l-1.293 1.293a1 1 0 001.414 1.414l3-3a1 1 0 000-1.414l-3-3a1 1 0 10-1.414 1.414L13.586 5H8z"/>
                        </svg>
                        New Transfer
                    </a>
                </div>
            </div>

            <!-- Transaction Filters -->
            <div class="filters-section">
                <div class="filter-card">
                    <form method="GET" style="display: flex; gap: 1rem; align-items: end; flex-wrap: wrap;">
                        <div class="filter-group">
                            <label>Date Range</label>
                            <select name="period" class="form-control">
                                <option value="all">All Time</option>
                                <option value="today">Today</option>
                                <option value="week">This Week</option>
                                <option value="month">This Month</option>
                                <option value="quarter">This Quarter</option>
                                <option value="year">This Year</option>
                            </select>
                        </div>
                        <div class="filter-group">
                            <label>Transaction Type</label>
                            <select name="type" class="form-control">
                                <option value="all">All Types</option>
                                <option value="credit">Credits</option>
                                <option value="debit">Debits</option>
                                <option value="transfer">Transfers</option>
                                <option value="payment">Payments</option>
                            </select>
                        </div>
                        <div class="filter-group">
                            <label>Status</label>
                            <select name="status" class="form-control">
                                <option value="all">All Status</option>
                                <option value="completed">Completed</option>
                                <option value="pending">Pending</option>
                                <option value="failed">Failed</option>
                            </select>
                        </div>
                        <button type="submit" class="btn-primary">Filter</button>
                    </form>
                </div>
            </div>

            <!-- Transactions List -->
            <div class="transaction-history-section full-width">
                <div class="section-header">
                    <div class="section-title">
                        <h3>All Transactions</h3>
                        <p>Showing <?php echo count($transactions); ?> of <?php echo $total_transactions; ?> transactions</p>
                    </div>
                    <div class="section-actions">
                        <button class="btn-outline">
                            <svg width="16" height="16" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M3 17a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm3.293-7.707a1 1 0 011.414 0L9 10.586V3a1 1 0 112 0v7.586l1.293-1.293a1 1 0 111.414 1.414l-3 3a1 1 0 01-1.414 0l-3-3a1 1 0 010-1.414z" clip-rule="evenodd"/>
                            </svg>
                            Export CSV
                        </button>
                    </div>
                </div>

                <div class="transaction-list-container">
                    <?php if (!empty($transactions)): ?>
                        <div class="transaction-list">
                            <?php foreach ($transactions as $transaction): ?>
                                <div class="enhanced-transaction-item">
                                    <div class="transaction-main">
                                        <div class="transaction-avatar">
                                            <div class="avatar-icon <?php echo $transaction['transaction_type'] === 'credit' ? 'received' : 'sent'; ?>">
                                                <?php if ($transaction['transaction_type'] === 'credit'): ?>
                                                    <svg width="20" height="20" fill="currentColor" viewBox="0 0 20 20">
                                                        <path fill-rule="evenodd" d="M3 17a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm3.293-7.707a1 1 0 011.414 0L9 10.586V3a1 1 0 112 0v7.586l1.293-1.293a1 1 0 111.414 1.414l-3 3a1 1 0 01-1.414 0l-3-3a1 1 0 010-1.414z" clip-rule="evenodd"/>
                                                    </svg>
                                                <?php else: ?>
                                                    <svg width="20" height="20" fill="currentColor" viewBox="0 0 20 20">
                                                        <path fill-rule="evenodd" d="M3 3a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm3.293 7.707a1 1 0 011.414 0L9 9.414V17a1 1 0 11-2 0V9.414L5.707 10.707a1 1 0 01-1.414-1.414l3-3a1 1 0 011.414 0l3 3a1 1 0 01-1.414 1.414z" clip-rule="evenodd"/>
                                                    </svg>
                                                <?php endif; ?>
                                            </div>
                                        </div>
                                        <div class="transaction-details">
                                            <div class="transaction-primary">
                                                <h4><?php echo htmlspecialchars($transaction['description'] ?? 'Transaction'); ?></h4>
                                                <span class="transaction-type"><?php echo ucfirst($transaction['transaction_type']); ?></span>
                                            </div>
                                            <div class="transaction-secondary">
                                                <span class="transaction-description">
                                                    <?php echo htmlspecialchars($transaction['reference'] ?? ''); ?>
                                                </span>
                                                <span class="transaction-id">ID: <?php echo htmlspecialchars($transaction['id']); ?></span>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="transaction-amount-section">
                                        <div class="amount <?php echo $transaction['transaction_type'] === 'credit' ? 'positive' : 'negative'; ?>">
                                            <?php echo $transaction['transaction_type'] === 'credit' ? '+' : '-'; ?>
                                            <?php echo formatCurrency($transaction['amount'], $currency); ?>
                                        </div>
                                        <div class="transaction-time">
                                            <?php echo date('M j, Y g:i A', strtotime($transaction['created_at'])); ?>
                                        </div>
                                        <div class="transaction-status">
                                            <span class="status-badge <?php echo strtolower($transaction['status'] ?? 'completed'); ?>">
                                                <?php echo ucfirst($transaction['status'] ?? 'Completed'); ?>
                                            </span>
                                        </div>
                                    </div>
                                    <div class="transaction-expand-icon">
                                        <svg width="16" height="16" fill="currentColor" viewBox="0 0 20 20">
                                            <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd"/>
                                        </svg>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        </div>
                    <?php else: ?>
                        <div class="empty-transactions">
                            <svg class="empty-icon" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M3 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1z" clip-rule="evenodd"/>
                            </svg>
                            <h4>No transactions found</h4>
                            <p>You haven't made any transactions yet. Start by making a transfer or payment.</p>
                            <div class="empty-actions">
                                <a href="../transfers/" class="btn-primary">Make Transfer</a>
                                <a href="../payments/" class="btn-outline">Pay Bills</a>
                            </div>
                        </div>
                    <?php endif; ?>
                </div>

                <?php if ($total_pages > 1): ?>
                    <div class="transaction-footer">
                        <div class="transaction-summary">
                            Page <?php echo $page; ?> of <?php echo $total_pages; ?>
                        </div>
                        <div class="pagination">
                            <?php if ($page > 1): ?>
                                <a href="?page=<?php echo $page - 1; ?>" class="pagination-btn">Previous</a>
                            <?php endif; ?>
                            
                            <?php for ($i = max(1, $page - 2); $i <= min($total_pages, $page + 2); $i++): ?>
                                <a href="?page=<?php echo $i; ?>" class="pagination-btn <?php echo $i === $page ? 'active' : ''; ?>">
                                    <?php echo $i; ?>
                                </a>
                            <?php endfor; ?>
                            
                            <?php if ($page < $total_pages): ?>
                                <a href="?page=<?php echo $page + 1; ?>" class="pagination-btn">Next</a>
                            <?php endif; ?>
                        </div>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<?php
// Include footer template
require_once '../../templates/user/footer.php';
?>
