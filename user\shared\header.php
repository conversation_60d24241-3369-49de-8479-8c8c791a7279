<?php
// Start session if not already started
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    header('Location: ../../auth/login.php');
    exit();
}

// Get site configuration
$site_name = 'PremierBank Pro';
$current_page = basename($_SERVER['PHP_SELF'], '.php');
$current_dir = basename(dirname($_SERVER['PHP_SELF']));

// Get the base URL for proper linking
$protocol = isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? 'https' : 'http';
$host = $_SERVER['HTTP_HOST'];
$base_url = $protocol . '://' . $host . '/online_banking';
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo isset($page_title) ? htmlspecialchars($page_title) . ' - ' : ''; ?><?php echo htmlspecialchars($site_name); ?></title>
    
    <!-- Favicon -->
    <?php
    try {
        // Get site settings for favicon
        require_once __DIR__ . '/../../config/database.php';
        $db = getDB();
        $favicon_result = $db->query("SELECT setting_value FROM super_admin_settings WHERE setting_key = 'site_favicon'");
        $favicon_path = '';
        if ($favicon_result && $favicon_result->num_rows > 0) {
            $favicon_row = $favicon_result->fetch_assoc();
            $favicon_path = $favicon_row['setting_value'];
        }

        if (!empty($favicon_path) && file_exists(__DIR__ . '/../../' . $favicon_path)) {
            echo '<link rel="icon" type="image/x-icon" href="' . $base_url . '/' . htmlspecialchars($favicon_path) . '">';
        } else {
            echo '<link rel="icon" type="image/x-icon" href="' . $base_url . '/assets/favicon.ico">';
        }
    } catch (Exception $e) {
        echo '<link rel="icon" type="image/x-icon" href="' . $base_url . '/assets/favicon.ico">';
    }
    ?>

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">

    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

    <!-- CSS Files -->
    <link rel="stylesheet" href="<?php echo $base_url; ?>/assets/css/user-dashboard.css">
    <link rel="stylesheet" href="<?php echo $base_url; ?>/assets/css/components.css">
    <link rel="stylesheet" href="<?php echo $base_url; ?>/assets/css/universal-layout.css">

    <!-- Dynamic CSS Variables -->
    <style>
        <?php
        if (file_exists(__DIR__ . '/../../config/dynamic-css.php')) {
            require_once __DIR__ . '/../../config/dynamic-css.php';
            if (function_exists('getInlineDynamicCSS')) {
                echo getInlineDynamicCSS();
            }
        }
        ?>
    </style>
    
    <!-- Additional CSS if specified -->
    <?php if (isset($additional_css)): ?>
        <?php foreach ($additional_css as $css_file): ?>
            <link rel="stylesheet" href="<?php echo $base_url; ?>/assets/css/<?php echo $css_file; ?>">
        <?php endforeach; ?>
    <?php endif; ?>
    
    <!-- Custom page styles -->
    <?php if (isset($custom_styles)): ?>
        <style><?php echo $custom_styles; ?></style>
    <?php endif; ?>
</head>
<body class="user-dashboard">
    <div class="dashboard-wrapper">
