<?php
/**
 * Transfer OTP Generation API
 * Generates and sends OTP for transfer verification
 */

// Enable error reporting for debugging
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Start session and check authentication
session_start();
if (!isset($_SESSION['user_id'])) {
    http_response_code(401);
    echo json_encode(['error' => 'Unauthorized']);
    exit();
}

// Include database connection and email system
require_once '../../config/config.php';
require_once '../../config/email.php';
require_once '../../config/email_templates.php';

// Set JSON response header
header('Content-Type: application/json');

try {
    // Get database connection
    $db = getDB();
    $user_id = $_SESSION['user_id'];
    
    // Get user information
    $user_query = "SELECT * FROM accounts WHERE id = ?";
    $user_result = $db->query($user_query, [$user_id]);
    $user = $user_result->fetch_assoc();
    
    if (!$user) {
        throw new Exception('User not found');
    }
    
    // Get JSON input
    $input = json_decode(file_get_contents('php://input'), true);
    $transfer_data = $input['transfer_data'] ?? [];
    
    if (empty($transfer_data)) {
        throw new Exception('Transfer data is required');
    }
    
    // Check if OTP is enabled for this specific user (per-user setting)
    $user_otp_query = "SELECT COALESCE(uss.otp_enabled, 1) as otp_enabled
                       FROM accounts a
                       LEFT JOIN user_security_settings uss ON a.id = uss.user_id
                       WHERE a.id = ?";
    $user_otp_result = $db->query($user_otp_query, [$user_id]);
    $otp_enabled = true; // Default to enabled

    if ($user_otp_result && $user_otp_result->num_rows > 0) {
        $setting = $user_otp_result->fetch_assoc();
        $otp_enabled = ($setting['otp_enabled'] == 1);
    }

    if (!$otp_enabled) {
        // OTP is disabled for this user
        echo json_encode([
            'success' => true,
            'otp_required' => false,
            'message' => 'OTP verification is disabled for your account'
        ]);
        exit();
    }
    
    // Generate 6-digit OTP
    $otp_code = sprintf('%06d', mt_rand(100000, 999999));

    // Store OTP in database using MySQL-based expiration to avoid timezone issues
    $insert_otp_sql = "INSERT INTO user_otps (user_id, otp_code, purpose, expires_at, created_at)
                       VALUES (?, ?, 'transfer', DATE_ADD(NOW(), INTERVAL 10 MINUTE), NOW())
                       ON DUPLICATE KEY UPDATE
                       otp_code = VALUES(otp_code),
                       expires_at = DATE_ADD(NOW(), INTERVAL 10 MINUTE),
                       created_at = NOW(),
                       is_used = 0";

    $db->query($insert_otp_sql, [$user_id, $otp_code]);
    
    // Prepare email content using the proper template system
    $transfer_amount = formatCurrency($transfer_data['amount'], $transfer_data['currency']);
    $beneficiary_name = htmlspecialchars($transfer_data['beneficiary_name']);
    $transfer_type = ucwords(str_replace('-', ' ', $transfer_data['transfer_type']));

    $email_subject = "Transfer Verification Code - {$transfer_amount}";

    // Create transfer OTP email template
    $email_body = generateTransferOTPEmailTemplate([
        'first_name' => $user['first_name'],
        'last_name' => $user['last_name'],
        'email' => $user['email']
    ], $otp_code, [
        'amount' => $transfer_amount,
        'beneficiary_name' => $beneficiary_name,
        'beneficiary_account' => $transfer_data['beneficiary_account'],
        'transfer_type' => $transfer_type
    ]);
    
    // Send email using the existing email system
    $email_sent = false;

    // Log the email attempt
    error_log("Transfer OTP email attempt - User: {$user_id}, Email: {$user['email']}, Subject: {$email_subject}");

    // Use the same email sending method as login OTP (force SMTP)
    if (function_exists('sendEmailSMTP')) {
        // Force SMTP email sending (bypass localhost detection) - same as login OTP
        $email_sent = sendEmailSMTP($user['email'], $email_subject, $email_body, true);

        // Log the result
        error_log("Transfer OTP email result (SMTP) - User: {$user_id}, Success: " . ($email_sent ? 'YES' : 'NO'));

    } elseif (function_exists('sendEmail')) {
        $email_sent = sendEmail($user['email'], $email_subject, $email_body, true);

        // Log the result
        error_log("Transfer OTP email result (sendEmail) - User: {$user_id}, Success: " . ($email_sent ? 'YES' : 'NO'));

    } else {
        error_log("Transfer OTP email error - No email functions available");

        // Fallback: Use PHP mail function with proper headers
        $headers = [
            'MIME-Version: 1.0',
            'Content-type: text/html; charset=UTF-8',
            'From: ' . (defined('FROM_NAME') ? FROM_NAME : 'Online Banking') . ' <' . (defined('FROM_EMAIL') ? FROM_EMAIL : '<EMAIL>') . '>',
            'Reply-To: ' . (defined('FROM_EMAIL') ? FROM_EMAIL : '<EMAIL>'),
            'X-Mailer: PHP/' . phpversion(),
            'X-Priority: 1',
            'X-MSMail-Priority: High'
        ];

        $email_sent = mail($user['email'], $email_subject, $email_body, implode("\r\n", $headers));

        // Log the fallback result
        error_log("Transfer OTP email fallback result - User: {$user_id}, Success: " . ($email_sent ? 'YES' : 'NO'));
    }
    
    if ($email_sent) {
        // Log the OTP generation
        error_log("Transfer OTP generated for user {$user_id}: {$transfer_amount} to {$beneficiary_name}");
        
        echo json_encode([
            'success' => true,
            'otp_required' => true,
            'message' => 'Verification code sent to your email',
            'expires_in' => 600 // 10 minutes in seconds
        ]);
    } else {
        throw new Exception('Failed to send verification email');
    }
    
} catch (Exception $e) {
    error_log("Transfer OTP generation error: " . $e->getMessage());
    
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'error' => 'OTP generation failed',
        'message' => $e->getMessage()
    ]);
}
?>
