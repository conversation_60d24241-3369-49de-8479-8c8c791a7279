<?php
/**
 * Verify Billing Code API
 * Verifies individual billing codes during wire transfer
 */

// Start session and check authentication
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

if (!isset($_SESSION['user_id'])) {
    http_response_code(401);
    echo json_encode(['error' => 'Unauthorized']);
    exit();
}

// Include database connection
require_once '../../config/config.php';

// Set JSON response header
header('Content-Type: application/json');

try {
    $db = getDB();
    $user_id = $_SESSION['user_id'];
    
    // Get JSON input
    $input = json_decode(file_get_contents('php://input'), true);
    
    if (!$input) {
        throw new Exception('Invalid request data');
    }
    
    $billing_position = intval($input['billing_position'] ?? 0);
    $entered_code = sanitizeInput($input['billing_code'] ?? '');
    
    if ($billing_position < 1 || $billing_position > 4) {
        throw new Exception('Invalid billing position');
    }
    
    if (empty($entered_code)) {
        throw new Exception('Billing code is required');
    }
    
    // Get the expected billing code for this position
    $query = "SELECT billing_code, billing_name 
              FROM user_billing_codes 
              WHERE user_id = ? AND billing_position = ? AND is_active = 1";
    
    $result = $db->query($query, [$user_id, $billing_position]);
    $billing_code_data = $result->fetch_assoc();
    
    if (!$billing_code_data) {
        throw new Exception('Billing code not found for this position');
    }
    
    // Verify the code
    $is_correct = ($entered_code === $billing_code_data['billing_code']);
    
    if ($is_correct) {
        // Log successful verification
        $log_query = "INSERT INTO billing_code_verifications 
                      (user_id, billing_position, billing_code_entered, billing_code_expected, is_verified, ip_address, user_agent) 
                      VALUES (?, ?, ?, ?, 1, ?, ?)";
        
        $db->query($log_query, [
            $user_id,
            $billing_position,
            $entered_code,
            $billing_code_data['billing_code'],
            $_SERVER['REMOTE_ADDR'] ?? '',
            $_SERVER['HTTP_USER_AGENT'] ?? ''
        ]);
        
        // Store verification in session for later use
        if (!isset($_SESSION['verified_billing_codes'])) {
            $_SESSION['verified_billing_codes'] = [];
        }
        $_SESSION['verified_billing_codes'][$billing_position] = true;
        
        echo json_encode([
            'success' => true,
            'message' => 'Billing code verified successfully',
            'billing_name' => $billing_code_data['billing_name']
        ]);
        
    } else {
        // Log failed verification
        $log_query = "INSERT INTO billing_code_verifications 
                      (user_id, billing_position, billing_code_entered, billing_code_expected, is_verified, ip_address, user_agent) 
                      VALUES (?, ?, ?, ?, 0, ?, ?)";
        
        $db->query($log_query, [
            $user_id,
            $billing_position,
            $entered_code,
            $billing_code_data['billing_code'],
            $_SERVER['REMOTE_ADDR'] ?? '',
            $_SERVER['HTTP_USER_AGENT'] ?? ''
        ]);
        
        echo json_encode([
            'success' => false,
            'error' => 'Invalid billing code. Please check and try again.'
        ]);
    }
    
} catch (Exception $e) {
    error_log("Verify billing code error: " . $e->getMessage());
    echo json_encode([
        'success' => false,
        'error' => $e->getMessage()
    ]);
}
?>
