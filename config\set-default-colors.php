<?php
/**
 * Set Default Colors in Database
 * Run this script once to set #206bc4 as the default primary color
 */

require_once __DIR__ . '/config.php';

try {
    require_once __DIR__ . '/database.php';
    $db = getDB();

    // Set default colors
    $default_colors = [
        'theme_color' => '#206bc4',
        'secondary_color' => '#6c757d',
        'accent_color' => '#10b981',
        'success_color' => '#10b981',
        'warning_color' => '#f59e0b',
        'danger_color' => '#ef4444'
    ];

    foreach ($default_colors as $key => $value) {
        // Check if setting already exists
        $check_result = $db->query("SELECT id FROM super_admin_settings WHERE setting_key = '$key'");

        if ($check_result->num_rows == 0) {
            // Insert new setting
            $description = ucfirst(str_replace('_', ' ', $key));
            $db->query("INSERT INTO super_admin_settings (setting_key, setting_value, setting_description, setting_type) VALUES ('$key', '$value', '$description', 'text')");
            echo "✅ Set {$key} to {$value}\n";
        } else {
            echo "ℹ️  {$key} already exists in database\n";
        }
    }
    
    echo "\n🎨 Default colors have been set successfully!\n";
    echo "Primary color is now: #206bc4\n";
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
}
?>
