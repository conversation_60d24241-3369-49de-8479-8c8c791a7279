<?php
/**
 * Database Connection and Dynamic CSS Tester
 */

// Enable error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h2>🔍 Database and Dynamic CSS Test</h2>";

// Test 1: Basic file includes
echo "<h3>1. Testing File Includes</h3>";
try {
    echo "✅ Testing config.php include...<br>";
    require_once __DIR__ . '/config.php';
    echo "✅ config.php loaded successfully<br>";
    
    echo "✅ Testing database.php include...<br>";
    require_once __DIR__ . '/database.php';
    echo "✅ database.php loaded successfully<br>";
    
} catch (Exception $e) {
    echo "❌ Error loading files: " . $e->getMessage() . "<br>";
}

// Test 2: Database connection
echo "<h3>2. Testing Database Connection</h3>";
try {
    $db = getDB();
    echo "✅ Database connection successful<br>";
    
    // Test query
    $result = $db->query("SELECT COUNT(*) as count FROM super_admin_settings");
    if ($result) {
        $row = $result->fetch_assoc();
        echo "✅ Database query successful - Found " . $row['count'] . " settings<br>";
    } else {
        echo "❌ Database query failed<br>";
    }
    
} catch (Exception $e) {
    echo "❌ Database error: " . $e->getMessage() . "<br>";
}

// Test 3: Dynamic CSS system
echo "<h3>3. Testing Dynamic CSS System</h3>";
try {
    echo "✅ Testing dynamic-css.php include...<br>";
    require_once __DIR__ . '/dynamic-css.php';
    echo "✅ dynamic-css.php loaded successfully<br>";
    
    echo "✅ Testing CSS generation...<br>";
    $css = generateDynamicCSS();
    echo "✅ CSS generated successfully (" . strlen($css) . " characters)<br>";
    
    echo "<h4>Generated CSS Preview:</h4>";
    echo "<pre style='background: #f5f5f5; padding: 10px; border-radius: 4px; max-height: 200px; overflow-y: auto;'>";
    echo htmlspecialchars(substr($css, 0, 500)) . "...";
    echo "</pre>";
    
} catch (Exception $e) {
    echo "❌ Dynamic CSS error: " . $e->getMessage() . "<br>";
}

// Test 4: Color settings from database
echo "<h3>4. Testing Color Settings</h3>";
try {
    $db = getDB();
    $color_result = $db->query("SELECT setting_key, setting_value FROM super_admin_settings WHERE setting_key IN ('theme_color', 'secondary_color')");
    
    echo "<table border='1' style='border-collapse: collapse; margin: 10px 0;'>";
    echo "<tr><th style='padding: 8px;'>Setting Key</th><th style='padding: 8px;'>Value</th></tr>";
    
    while ($row = $color_result->fetch_assoc()) {
        echo "<tr>";
        echo "<td style='padding: 8px;'>" . htmlspecialchars($row['setting_key']) . "</td>";
        echo "<td style='padding: 8px;'>";
        echo "<span style='background: " . htmlspecialchars($row['setting_value']) . "; width: 20px; height: 20px; display: inline-block; margin-right: 10px; border: 1px solid #ccc;'></span>";
        echo htmlspecialchars($row['setting_value']);
        echo "</td>";
        echo "</tr>";
    }
    echo "</table>";
    
} catch (Exception $e) {
    echo "❌ Color settings error: " . $e->getMessage() . "<br>";
}

// Test 5: File permissions and paths
echo "<h3>5. Testing File Permissions and Paths</h3>";
$files_to_check = [
    __DIR__ . '/config.php',
    __DIR__ . '/database.php', 
    __DIR__ . '/dynamic-css.php',
    __DIR__ . '/../admin/includes/admin-header.php',
    __DIR__ . '/../logs'
];

foreach ($files_to_check as $file) {
    if (file_exists($file)) {
        $perms = substr(sprintf('%o', fileperms($file)), -4);
        echo "✅ " . basename($file) . " exists (permissions: {$perms})<br>";
    } else {
        echo "❌ " . basename($file) . " NOT FOUND<br>";
    }
}

echo "<h3>6. System Information</h3>";
echo "PHP Version: " . PHP_VERSION . "<br>";
echo "Memory Limit: " . ini_get('memory_limit') . "<br>";
echo "Max Execution Time: " . ini_get('max_execution_time') . "<br>";
echo "Error Reporting Level: " . error_reporting() . "<br>";
echo "Display Errors: " . (ini_get('display_errors') ? 'ON' : 'OFF') . "<br>";

echo "<h3>✅ Test Complete</h3>";
echo "<p>If you see this message, the basic systems are working. Check above for any ❌ errors.</p>";
?>
