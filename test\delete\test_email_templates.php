<?php
/**
 * Comprehensive Email Template System Test
 * Tests all email <NAME_EMAIL>
 */

// Enable error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Include required files
require_once 'config/config.php';
require_once 'config/email.php';
require_once 'config/email_templates.php';

echo "<!DOCTYPE html>";
echo "<html><head><title>Email Template System Test</title>";
echo "<style>
body { font-family: Arial, sans-serif; margin: 20px; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); min-height: 100vh; }
.container { max-width: 1000px; margin: 0 auto; background: white; padding: 30px; border-radius: 15px; box-shadow: 0 8px 32px rgba(0,0,0,0.3); }
.header { background: linear-gradient(135deg, #1e40af 0%, #3b82f6 100%); color: white; padding: 30px; text-align: center; border-radius: 10px; margin-bottom: 20px; }
.success { background: linear-gradient(135deg, #059669 0%, #10b981 100%); color: white; padding: 15px; border-radius: 8px; margin: 10px 0; }
.error { background: linear-gradient(135deg, #dc2626 0%, #ef4444 100%); color: white; padding: 15px; border-radius: 8px; margin: 10px 0; }
.info { background: #e7f3ff; border: 1px solid #b3d9ff; color: #0c5460; padding: 15px; border-radius: 8px; margin: 10px 0; }
.template-section { background: #f8f9fa; padding: 20px; border-radius: 8px; margin: 20px 0; border-left: 4px solid #1e40af; }
.btn { background: #1e40af; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; display: inline-block; margin: 5px; }
.btn:hover { background: #1d4ed8; }
</style>";
echo "</head><body>";

echo "<div class='container'>";
echo "<div class='header'>";
echo "<h1>🏦 Email Template System Test</h1>";
echo "<p>Testing all email templates with the new centralized system</p>";
echo "</div>";

$test_email = '<EMAIL>';
$test_results = [];

// Sample user data
$user_data = [
    'first_name' => 'Demo',
    'last_name' => 'Developer',
    'username' => 'demothedev',
    'email' => $test_email,
    'account_number' => '**********',
    'account_type' => 'savings',
    'currency' => 'USD',
    'balance' => 5000.00,
    'status' => 'active'
];

echo "<div class='info'>";
echo "<h3>📧 Test Configuration</h3>";
echo "<p><strong>Target Email:</strong> $test_email</p>";
echo "<p><strong>SMTP Host:</strong> " . SMTP_HOST . "</p>";
echo "<p><strong>SMTP Port:</strong> " . SMTP_PORT . " (SSL)</p>";
echo "<p><strong>Test User:</strong> {$user_data['first_name']} {$user_data['last_name']}</p>";
echo "</div>";

// Test 1: Welcome Email
echo "<div class='template-section'>";
echo "<h2>1. 👋 Welcome Email Template</h2>";
try {
    $welcome_html = generateWelcomeEmailTemplate($user_data);
    $subject = "Welcome to Online Banking System - Your Account is Ready!";
    $result = sendEmailSMTP($test_email, $subject, $welcome_html, true);
    
    if ($result) {
        echo "<div class='success'>✅ Welcome email sent successfully</div>";
        $test_results['welcome'] = 'SUCCESS';
    } else {
        echo "<div class='error'>❌ Welcome email failed to send</div>";
        $test_results['welcome'] = 'FAILED';
    }
} catch (Exception $e) {
    echo "<div class='error'>❌ Welcome email error: " . $e->getMessage() . "</div>";
    $test_results['welcome'] = 'ERROR';
}
echo "</div>";

// Test 2: OTP Email
echo "<div class='template-section'>";
echo "<h2>2. 🔐 OTP Verification Email</h2>";
try {
    $otp_code = generateOTP();
    $otp_html = generateOTPEmailTemplate($user_data, $otp_code, 10);
    $subject = "Your Online Banking System Login Verification Code";
    $result = sendEmailSMTP($test_email, $subject, $otp_html, true);
    
    if ($result) {
        echo "<div class='success'>✅ OTP email sent successfully</div>";
        echo "<div class='info'>🔢 <strong>OTP Code:</strong> $otp_code</div>";
        $test_results['otp'] = 'SUCCESS';
    } else {
        echo "<div class='error'>❌ OTP email failed to send</div>";
        $test_results['otp'] = 'FAILED';
    }
} catch (Exception $e) {
    echo "<div class='error'>❌ OTP email error: " . $e->getMessage() . "</div>";
    $test_results['otp'] = 'ERROR';
}
echo "</div>";

// Test 3: Credit Alert
echo "<div class='template-section'>";
echo "<h2>3. 💰 Credit Alert Email</h2>";
try {
    $transaction_data = [
        'transaction_id' => 'TXN' . time(),
        'amount' => 1500.00,
        'currency' => 'USD',
        'date_time' => date('F j, Y \a\t g:i A'),
        'sender_name' => 'John Smith',
        'reference' => 'Salary Payment',
        'new_balance' => 6500.00
    ];
    
    $credit_html = generateCreditAlertEmailTemplate($user_data, $transaction_data);
    $subject = "Credit Alert - Money Received";
    $result = sendEmailSMTP($test_email, $subject, $credit_html, true);
    
    if ($result) {
        echo "<div class='success'>✅ Credit alert email sent successfully</div>";
        echo "<div class='info'>💰 <strong>Amount:</strong> +$" . number_format($transaction_data['amount'], 2) . "</div>";
        $test_results['credit'] = 'SUCCESS';
    } else {
        echo "<div class='error'>❌ Credit alert email failed to send</div>";
        $test_results['credit'] = 'FAILED';
    }
} catch (Exception $e) {
    echo "<div class='error'>❌ Credit alert email error: " . $e->getMessage() . "</div>";
    $test_results['credit'] = 'ERROR';
}
echo "</div>";

// Test 4: Debit Alert
echo "<div class='template-section'>";
echo "<h2>4. 💸 Debit Alert Email</h2>";
try {
    $debit_transaction = [
        'transaction_id' => 'TXN' . (time() + 1),
        'amount' => 250.00,
        'currency' => 'USD',
        'date_time' => date('F j, Y \a\t g:i A'),
        'recipient_name' => 'Amazon.com',
        'reference' => 'Online Purchase',
        'new_balance' => 6250.00
    ];
    
    $debit_html = generateDebitAlertEmailTemplate($user_data, $debit_transaction);
    $subject = "Debit Alert - Money Sent";
    $result = sendEmailSMTP($test_email, $subject, $debit_html, true);
    
    if ($result) {
        echo "<div class='success'>✅ Debit alert email sent successfully</div>";
        echo "<div class='info'>💸 <strong>Amount:</strong> -$" . number_format($debit_transaction['amount'], 2) . "</div>";
        $test_results['debit'] = 'SUCCESS';
    } else {
        echo "<div class='error'>❌ Debit alert email failed to send</div>";
        $test_results['debit'] = 'FAILED';
    }
} catch (Exception $e) {
    echo "<div class='error'>❌ Debit alert email error: " . $e->getMessage() . "</div>";
    $test_results['debit'] = 'ERROR';
}
echo "</div>";

// Test 5: Security Alert
echo "<div class='template-section'>";
echo "<h2>5. 🔐 Login Alert Email</h2>";
try {
    $login_details = [
        'timestamp' => date('F j, Y \a\t g:i A'),
        'ip_address' => $_SERVER['REMOTE_ADDR'] ?? '*************',
        'device' => 'Windows PC',
        'location' => 'New York, USA',
        'browser' => 'Chrome 120.0'
    ];
    
    $login_html = generateLoginAlertEmailTemplate($user_data, $login_details);
    $subject = "New Account Login Detected";
    $result = sendEmailSMTP($test_email, $subject, $login_html, true);
    
    if ($result) {
        echo "<div class='success'>✅ Login alert email sent successfully</div>";
        echo "<div class='info'>🔐 <strong>Location:</strong> {$login_details['location']}</div>";
        $test_results['login'] = 'SUCCESS';
    } else {
        echo "<div class='error'>❌ Login alert email failed to send</div>";
        $test_results['login'] = 'FAILED';
    }
} catch (Exception $e) {
    echo "<div class='error'>❌ Login alert email error: " . $e->getMessage() . "</div>";
    $test_results['login'] = 'ERROR';
}
echo "</div>";

// Test 6: KYC Status Update
echo "<div class='template-section'>";
echo "<h2>6. ✅ KYC Verification Email</h2>";
try {
    $kyc_html = generateKYCStatusEmailTemplate($user_data, 'verified');
    $subject = "KYC Verification Complete - Account Verified";
    $result = sendEmailSMTP($test_email, $subject, $kyc_html, true);
    
    if ($result) {
        echo "<div class='success'>✅ KYC verification email sent successfully</div>";
        echo "<div class='info'>✅ <strong>Status:</strong> Verified</div>";
        $test_results['kyc'] = 'SUCCESS';
    } else {
        echo "<div class='error'>❌ KYC verification email failed to send</div>";
        $test_results['kyc'] = 'FAILED';
    }
} catch (Exception $e) {
    echo "<div class='error'>❌ KYC verification email error: " . $e->getMessage() . "</div>";
    $test_results['kyc'] = 'ERROR';
}
echo "</div>";

// Test 7: Account Suspension
echo "<div class='template-section'>";
echo "<h2>7. ⚠️ Account Suspension Email</h2>";
try {
    $suspension_html = generateAccountSuspensionEmailTemplate($user_data, 'Suspicious activity detected');
    $subject = "Account Security Alert - Temporary Suspension";
    $result = sendEmailSMTP($test_email, $subject, $suspension_html, true);
    
    if ($result) {
        echo "<div class='success'>✅ Account suspension email sent successfully</div>";
        echo "<div class='info'>⚠️ <strong>Reason:</strong> Suspicious activity detected</div>";
        $test_results['suspension'] = 'SUCCESS';
    } else {
        echo "<div class='error'>❌ Account suspension email failed to send</div>";
        $test_results['suspension'] = 'FAILED';
    }
} catch (Exception $e) {
    echo "<div class='error'>❌ Account suspension email error: " . $e->getMessage() . "</div>";
    $test_results['suspension'] = 'ERROR';
}
echo "</div>";

// Test Summary
echo "<hr>";
echo "<h2>📊 Test Summary</h2>";

$total_tests = count($test_results);
$successful_tests = count(array_filter($test_results, function($result) { return $result === 'SUCCESS'; }));

echo "<div class='info'>";
echo "<h3>🎯 Results: $successful_tests/$total_tests tests passed</h3>";
echo "<table style='width: 100%; border-collapse: collapse; margin: 20px 0;'>";
echo "<tr style='background: #f8f9fa;'>";
echo "<th style='padding: 12px; border: 1px solid #dee2e6; text-align: left;'>Template</th>";
echo "<th style='padding: 12px; border: 1px solid #dee2e6; text-align: left;'>Status</th>";
echo "<th style='padding: 12px; border: 1px solid #dee2e6; text-align: left;'>Description</th>";
echo "</tr>";

$template_descriptions = [
    'welcome' => '👋 Welcome Email - New account creation',
    'otp' => '🔐 OTP Email - Login verification',
    'credit' => '💰 Credit Alert - Money received',
    'debit' => '💸 Debit Alert - Money sent',
    'login' => '🔐 Login Alert - Security notification',
    'kyc' => '✅ KYC Email - Verification status',
    'suspension' => '⚠️ Suspension Email - Security alert'
];

foreach ($test_results as $template => $status) {
    $status_color = $status === 'SUCCESS' ? '#059669' : '#dc2626';
    $status_icon = $status === 'SUCCESS' ? '✅' : '❌';
    
    echo "<tr>";
    echo "<td style='padding: 12px; border: 1px solid #dee2e6;'>" . ucfirst($template) . "</td>";
    echo "<td style='padding: 12px; border: 1px solid #dee2e6; color: $status_color; font-weight: bold;'>$status_icon $status</td>";
    echo "<td style='padding: 12px; border: 1px solid #dee2e6;'>{$template_descriptions[$template]}</td>";
    echo "</tr>";
}

echo "</table>";
echo "</div>";

if ($successful_tests >= 5) {
    echo "<div class='success'>";
    echo "<h3>🎉 EMAIL TEMPLATE SYSTEM WORKING!</h3>";
    echo "<p>The majority of email templates are working correctly. Check your inbox at <strong>$test_email</strong> for all the test emails.</p>";
    echo "<p>✅ Centralized template system is operational</p>";
    echo "<p>✅ Consistent branding across all emails</p>";
    echo "<p>✅ Mobile-responsive design</p>";
    echo "<p>✅ Professional banking appearance</p>";
    echo "</div>";
} else {
    echo "<div class='error'>";
    echo "<h3>⚠️ SOME TEMPLATES FAILED</h3>";
    echo "<p>Some email templates failed to send. Check the error messages above for details.</p>";
    echo "</div>";
}

echo "<div class='info'>";
echo "<h3>📧 What to Check in Your Email:</h3>";
echo "<ol>";
echo "<li><strong>Welcome Email</strong> - Account creation with full details</li>";
echo "<li><strong>OTP Email</strong> - Login verification with code: " . (isset($otp_code) ? $otp_code : 'N/A') . "</li>";
echo "<li><strong>Credit Alert</strong> - Money received notification</li>";
echo "<li><strong>Debit Alert</strong> - Money sent notification</li>";
echo "<li><strong>Login Alert</strong> - Security notification</li>";
echo "<li><strong>KYC Email</strong> - Verification status update</li>";
echo "<li><strong>Suspension Email</strong> - Security alert</li>";
echo "</ol>";
echo "<p><strong>Check spam/junk folder if emails are not in inbox!</strong></p>";
echo "</div>";

echo "<p><strong>Test completed at:</strong> " . date('Y-m-d H:i:s') . "</p>";
echo "</div>";
echo "</body></html>";
?>
