<?php
/**
 * Setup Super Admin Database Tables
 * Run this script once to create the necessary tables for super admin settings
 */

// Enable error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Include database configuration
require_once '../config/database.php';

echo "<!DOCTYPE html>";
echo "<html><head><title>Super Admin Database Setup</title>";
echo "<style>
body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }
.container { max-width: 800px; margin: 0 auto; background: white; padding: 30px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
.success { background: #d4edda; color: #155724; padding: 15px; border-radius: 5px; margin: 10px 0; }
.error { background: #f8d7da; color: #721c24; padding: 15px; border-radius: 5px; margin: 10px 0; }
.info { background: #d1ecf1; color: #0c5460; padding: 15px; border-radius: 5px; margin: 10px 0; }
.step { background: #f8f9fa; padding: 20px; border-radius: 5px; margin: 20px 0; border-left: 4px solid #007bff; }
</style>";
echo "</head><body>";

echo "<div class='container'>";
echo "<h1>🔧 Super Admin Database Setup</h1>";
echo "<p>This script will create the necessary database tables for the super admin settings system.</p>";

try {
    $db = getDB();
    
    echo "<div class='step'>";
    echo "<h3>Step 1: Creating super_admin_settings table</h3>";
    
    // Create super_admin_settings table
    $sql = "CREATE TABLE IF NOT EXISTS `super_admin_settings` (
      `id` int(11) NOT NULL AUTO_INCREMENT,
      `setting_key` varchar(100) NOT NULL UNIQUE,
      `setting_value` text NOT NULL,
      `setting_description` varchar(255) DEFAULT NULL,
      `setting_type` enum('text','email','phone','url','password','number','boolean') DEFAULT 'text',
      `is_encrypted` tinyint(1) DEFAULT 0,
      `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
      `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
      `created_by` int(11) DEFAULT NULL,
      `updated_by` int(11) DEFAULT NULL,
      PRIMARY KEY (`id`),
      KEY `idx_setting_key` (`setting_key`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
    
    $db->query($sql);
    echo "<div class='success'>✅ super_admin_settings table created successfully</div>";
    echo "</div>";
    
    echo "<div class='step'>";
    echo "<h3>Step 2: Creating audit log table</h3>";
    
    // Create audit log table
    $sql = "CREATE TABLE IF NOT EXISTS `super_admin_settings_audit` (
      `id` int(11) NOT NULL AUTO_INCREMENT,
      `setting_key` varchar(100) NOT NULL,
      `old_value` text,
      `new_value` text NOT NULL,
      `changed_by` int(11) NOT NULL,
      `change_reason` varchar(255) DEFAULT NULL,
      `ip_address` varchar(45) DEFAULT NULL,
      `user_agent` text DEFAULT NULL,
      `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
      PRIMARY KEY (`id`),
      KEY `idx_setting_key` (`setting_key`),
      KEY `idx_changed_by` (`changed_by`),
      KEY `idx_created_at` (`created_at`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
    
    $db->query($sql);
    echo "<div class='success'>✅ super_admin_settings_audit table created successfully</div>";
    echo "</div>";
    
    echo "<div class='step'>";
    echo "<h3>Step 3: Adding super admin columns to accounts table</h3>";
    
    // Add super admin columns to accounts table
    try {
        $db->query("ALTER TABLE `accounts` ADD COLUMN `is_super_admin` tinyint(1) DEFAULT 0 AFTER `is_admin`");
        echo "<div class='success'>✅ Added is_super_admin column</div>";
    } catch (Exception $e) {
        if (strpos($e->getMessage(), 'Duplicate column name') !== false) {
            echo "<div class='info'>ℹ️ is_super_admin column already exists</div>";
        } else {
            throw $e;
        }
    }
    
    try {
        $db->query("ALTER TABLE `accounts` ADD COLUMN `role` enum('user','admin','super_admin') DEFAULT 'user' AFTER `is_super_admin`");
        echo "<div class='success'>✅ Added role column</div>";
    } catch (Exception $e) {
        if (strpos($e->getMessage(), 'Duplicate column name') !== false) {
            echo "<div class='info'>ℹ️ role column already exists</div>";
        } else {
            throw $e;
        }
    }
    echo "</div>";
    
    echo "<div class='step'>";
    echo "<h3>Step 4: Setting existing admins as super admins</h3>";
    
    $db->query("UPDATE `accounts` SET `is_super_admin` = 1, `role` = 'super_admin' WHERE `is_admin` = 1");

    // Get count of super admins
    $count_result = $db->query("SELECT COUNT(*) as count FROM `accounts` WHERE `is_super_admin` = 1");
    $affected_rows = $count_result ? $count_result->fetch_assoc()['count'] : 0;
    echo "<div class='success'>✅ Updated $affected_rows admin accounts to super admin status</div>";
    echo "</div>";
    
    echo "<div class='step'>";
    echo "<h3>Step 5: Inserting default super admin settings</h3>";
    
    $default_settings = [
        ['site_name', 'Online Banking System', 'The name of the banking platform displayed in emails and throughout the system', 'text'],
        ['site_url', 'http://localhost/online_banking', 'The base URL of the banking platform', 'url'],
        ['support_email', '<EMAIL>', 'Primary support email address for customer inquiries', 'email'],
        ['support_phone', '1-800-BANKING', 'Primary support phone number for customer service', 'phone'],
        ['security_email', '<EMAIL>', 'Security team email for fraud and security issues', 'email'],
        ['security_phone', '1-800-SECURITY', 'Security team phone number for urgent security matters', 'phone'],
        ['noreply_email', '<EMAIL>', 'No-reply email address for automated system emails', 'email'],
        ['admin_email', '<EMAIL>', 'Administrator email for system notifications', 'email'],
        ['company_address', '123 Banking Street, Financial District, NY 10001', 'Physical address of the banking institution', 'text'],
        ['company_phone', '1-800-MAIN-BANK', 'Main company phone number', 'phone'],
        ['smtp_host', 'smtp.hostinger.com', 'SMTP server hostname for email delivery', 'text'],
        ['smtp_port', '465', 'SMTP server port (usually 587 for TLS or 465 for SSL)', 'number'],
        ['smtp_username', '<EMAIL>', 'SMTP authentication username', 'email'],
        ['smtp_password', 'Money2025@Demo#', 'SMTP authentication password', 'password'],
        ['smtp_encryption', 'ssl', 'SMTP encryption method (tls, ssl, or none)', 'text'],
        ['smtp_from_email', '<EMAIL>', 'Default from email address for outgoing emails', 'email'],
        ['smtp_from_name', 'Online Banking System', 'Default from name for outgoing emails', 'text'],
        ['email_footer_text', 'Your trusted financial partner since 2024', 'Footer text displayed in all emails', 'text'],
        ['privacy_policy_url', 'http://localhost/online_banking/privacy-policy.php', 'URL to privacy policy page', 'url'],
        ['terms_of_service_url', 'http://localhost/online_banking/terms-of-service.php', 'URL to terms of service page', 'url'],
        ['help_center_url', 'http://localhost/online_banking/help-center.php', 'URL to help center or FAQ page', 'url'],
        ['max_login_attempts', '5', 'Maximum number of failed login attempts before account lockout', 'number'],
        ['session_timeout', '30', 'Session timeout in minutes for inactive users', 'number'],
        ['otp_expiry_minutes', '10', 'OTP code expiry time in minutes', 'number'],
        ['maintenance_mode', '0', 'Enable/disable maintenance mode (1 = enabled, 0 = disabled)', 'boolean'],
        ['maintenance_message', 'We are currently performing scheduled maintenance. Please check back soon.', 'Message displayed during maintenance mode', 'text']
    ];
    
    $inserted_count = 0;
    foreach ($default_settings as $setting) {
        try {
            $sql = "INSERT IGNORE INTO `super_admin_settings` (`setting_key`, `setting_value`, `setting_description`, `setting_type`) VALUES (?, ?, ?, ?)";
            $db->query($sql, $setting);

            // Check if the setting was inserted by checking if it exists
            $check_result = $db->query("SELECT COUNT(*) as count FROM `super_admin_settings` WHERE `setting_key` = ?", [$setting[0]]);
            if ($check_result && $check_result->fetch_assoc()['count'] > 0) {
                $inserted_count++;
            }
        } catch (Exception $e) {
            echo "<div class='error'>❌ Error inserting {$setting[0]}: " . $e->getMessage() . "</div>";
        }
    }
    
    echo "<div class='success'>✅ Inserted $inserted_count default settings</div>";
    echo "</div>";
    
    echo "<div class='step'>";
    echo "<h3>Step 6: Creating indexes for performance</h3>";
    
    try {
        $db->query("CREATE INDEX IF NOT EXISTS `idx_role` ON `accounts` (`role`)");
        echo "<div class='success'>✅ Created role index</div>";
    } catch (Exception $e) {
        echo "<div class='info'>ℹ️ Role index already exists or not needed</div>";
    }
    
    try {
        $db->query("CREATE INDEX IF NOT EXISTS `idx_is_super_admin` ON `accounts` (`is_super_admin`)");
        echo "<div class='success'>✅ Created is_super_admin index</div>";
    } catch (Exception $e) {
        echo "<div class='info'>ℹ️ is_super_admin index already exists or not needed</div>";
    }
    echo "</div>";
    
    echo "<div class='success'>";
    echo "<h2>🎉 Setup Complete!</h2>";
    echo "<p>The super admin settings system has been successfully set up. You can now:</p>";
    echo "<ul>";
    echo "<li>Access <strong>Super Admin Settings</strong> from the admin panel</li>";
    echo "<li>Configure SMTP settings separately for security</li>";
    echo "<li>Manage all system-wide contact information</li>";
    echo "<li>View audit logs of all setting changes</li>";
    echo "</ul>";
    echo "<p><strong>Next Steps:</strong></p>";
    echo "<ol>";
    echo "<li>Login to the admin panel as a super admin</li>";
    echo "<li>Go to Super Admin Settings to configure your system</li>";
    echo "<li>Test the email templates to ensure everything works</li>";
    echo "</ol>";
    echo "</div>";
    
    echo "<div class='info'>";
    echo "<h3>📊 Database Summary</h3>";
    echo "<ul>";
    echo "<li><strong>Tables Created:</strong> super_admin_settings, super_admin_settings_audit</li>";
    echo "<li><strong>Columns Added:</strong> is_super_admin, role (to accounts table)</li>";
    echo "<li><strong>Default Settings:</strong> $inserted_count settings inserted</li>";
    echo "<li><strong>Super Admins:</strong> $affected_rows accounts upgraded</li>";
    echo "</ul>";
    echo "</div>";
    
} catch (Exception $e) {
    echo "<div class='error'>";
    echo "<h3>❌ Setup Failed</h3>";
    echo "<p>Error: " . htmlspecialchars($e->getMessage()) . "</p>";
    echo "<p>Please check your database configuration and try again.</p>";
    echo "</div>";
}

echo "<p><strong>Setup completed at:</strong> " . date('Y-m-d H:i:s') . "</p>";
echo "</div>";
echo "</body></html>";
?>
