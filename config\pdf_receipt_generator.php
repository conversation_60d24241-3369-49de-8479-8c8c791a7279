<?php
/**
 * PDF Receipt Generator Helper Functions
 * Generates HTML content for PDF receipts
 */

/**
 * Generate PDF Receipt HTML Content
 * @param array $user_data User information
 * @param array $transfer_data Transfer information
 * @return string HTML content for PDF
 */
function generatePDFReceiptHTML($user_data, $transfer_data) {
    $sender_name = trim($user_data['first_name'] . ' ' . $user_data['last_name']);
    $transfer_date = formatDate($transfer_data['created_at'], 'F j, Y g:i A');
    $transfer_type_display = ucwords(str_replace('-', ' ', $transfer_data['transfer_type']));
    
    $html = '<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>Transfer Receipt - ' . htmlspecialchars($transfer_data['reference_number']) . '</title>
    <style>
        body {
            font-family: "Arial", sans-serif;
            margin: 0;
            padding: 20px;
            background: white;
            color: #333;
            line-height: 1.4;
        }
        
        .receipt-container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border: 2px solid #2563eb;
            border-radius: 8px;
            overflow: hidden;
        }
        
        .receipt-header {
            background: linear-gradient(135deg, #2563eb 0%, #1d4ed8 100%);
            color: white;
            padding: 30px;
            text-align: center;
            position: relative;
        }
        
        .bank-logo {
            font-size: 28px;
            font-weight: bold;
            margin-bottom: 5px;
            letter-spacing: 1px;
        }
        
        .receipt-title {
            font-size: 18px;
            margin: 0;
            opacity: 0.9;
        }
        
        .receipt-body {
            padding: 30px;
        }
        
        .account-info {
            background: #f8fafc;
            border: 1px solid #e2e8f0;
            border-radius: 6px;
            padding: 20px;
            margin-bottom: 25px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .account-details h3 {
            margin: 0 0 8px 0;
            color: #1e40af;
            font-size: 16px;
        }
        
        .account-number {
            font-family: "Courier New", monospace;
            font-size: 14px;
            color: #64748b;
            font-weight: bold;
        }
        
        .section {
            margin-bottom: 25px;
        }
        
        .section-title {
            background: #1e40af;
            color: white;
            padding: 12px 20px;
            margin: 0 0 15px 0;
            font-size: 14px;
            font-weight: bold;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }
        
        .info-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px;
            margin-bottom: 15px;
        }
        
        .info-item {
            display: flex;
            justify-content: space-between;
            padding: 8px 0;
            border-bottom: 1px solid #e2e8f0;
        }
        
        .info-label {
            font-weight: 600;
            color: #374151;
        }
        
        .info-value {
            color: #1f2937;
            font-family: "Courier New", monospace;
        }
        
        .amount-section {
            background: #f0f9ff;
            border: 2px solid #0ea5e9;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
        }
        
        .amount-row {
            display: flex;
            justify-content: space-between;
            padding: 8px 0;
            font-size: 16px;
        }
        
        .amount-total {
            border-top: 2px solid #0ea5e9;
            padding-top: 12px;
            margin-top: 12px;
            font-weight: bold;
            font-size: 18px;
            color: #0c4a6e;
        }
        
        .status-badge {
            display: inline-block;
            background: #dcfce7;
            color: #166534;
            padding: 6px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: bold;
            text-transform: uppercase;
        }
        
        .receipt-footer {
            background: #f8fafc;
            border-top: 1px solid #e2e8f0;
            padding: 20px 30px;
            text-align: center;
            font-size: 12px;
            color: #64748b;
        }
        
        .footer-note {
            margin: 5px 0;
        }
        
        .reference-number {
            font-family: "Courier New", monospace;
            font-weight: bold;
            color: #1e40af;
            font-size: 14px;
        }
        
        @media print {
            body { margin: 0; }
            .receipt-container { border: none; box-shadow: none; }
        }
    </style>
</head>
<body>
    <div class="receipt-container">
        <!-- Header -->
        <div class="receipt-header">
            <div class="bank-logo">🏦 PREMIERBANK PRO</div>
            <div class="receipt-title">TRANSFER RECEIPT</div>
        </div>
        
        <!-- Account Information -->
        <div class="receipt-body">
            <div class="account-info">
                <div class="account-details">
                    <h3>' . htmlspecialchars($sender_name) . '</h3>
                    <div class="account-number">Account: ****' . substr($user_data['account_number'], -4) . '</div>
                </div>
                <div class="status-badge">✅ COMPLETED</div>
            </div>
            
            <!-- Transaction Details -->
            <div class="section">
                <h2 class="section-title">📋 Transaction Details</h2>
                <div class="info-grid">
                    <div class="info-item">
                        <span class="info-label">Reference Number:</span>
                        <span class="info-value reference-number">' . htmlspecialchars($transfer_data['reference_number']) . '</span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">Transfer Type:</span>
                        <span class="info-value">' . htmlspecialchars($transfer_type_display) . '</span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">Date & Time:</span>
                        <span class="info-value">' . htmlspecialchars($transfer_date) . '</span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">Status:</span>
                        <span class="info-value" style="color: #059669; font-weight: bold;">COMPLETED</span>
                    </div>
                </div>
            </div>
            
            <!-- Recipient Information -->
            <div class="section">
                <h2 class="section-title">🎯 Recipient Information</h2>
                <div class="info-grid">
                    <div class="info-item">
                        <span class="info-label">Name:</span>
                        <span class="info-value">' . htmlspecialchars($transfer_data['recipient_name']) . '</span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">Account:</span>
                        <span class="info-value">****' . substr($transfer_data['recipient_account'], -4) . '</span>
                    </div>';
                    
    if (!empty($transfer_data['bank_name'])) {
        $html .= '
                    <div class="info-item">
                        <span class="info-label">Bank:</span>
                        <span class="info-value">' . htmlspecialchars($transfer_data['bank_name']) . '</span>
                    </div>';
    }
    
    $html .= '
                </div>
            </div>
            
            <!-- Amount Details -->
            <div class="amount-section">
                <h2 class="section-title" style="background: #0ea5e9; margin: -20px -20px 15px -20px;">💰 Amount Details</h2>
                <div class="amount-row">
                    <span>Transfer Amount:</span>
                    <span class="info-value">' . htmlspecialchars($transfer_data['currency']) . ' ' . number_format($transfer_data['amount'], 2) . '</span>
                </div>';
                
    if ($transfer_data['fee'] > 0) {
        $html .= '
                <div class="amount-row">
                    <span>Transfer Fee:</span>
                    <span class="info-value">' . htmlspecialchars($transfer_data['currency']) . ' ' . number_format($transfer_data['fee'], 2) . '</span>
                </div>
                <div class="amount-row amount-total">
                    <span>Total Debited:</span>
                    <span class="info-value">' . htmlspecialchars($transfer_data['currency']) . ' ' . number_format($transfer_data['amount'] + $transfer_data['fee'], 2) . '</span>
                </div>';
    } else {
        $html .= '
                <div class="amount-row">
                    <span>Transfer Fee:</span>
                    <span class="info-value" style="color: #059669; font-weight: bold;">FREE</span>
                </div>';
    }
    
    $html .= '
            </div>';
            
    if (!empty($transfer_data['description'])) {
        $html .= '
            <!-- Purpose -->
            <div class="section">
                <h2 class="section-title">📝 Purpose</h2>
                <div style="background: #f9fafb; border: 1px solid #e5e7eb; border-radius: 6px; padding: 15px;">
                    <em>"' . htmlspecialchars($transfer_data['description']) . '"</em>
                </div>
            </div>';
    }
    
    $html .= '
        </div>
        
        <!-- Footer -->
        <div class="receipt-footer">
            <div class="footer-note"><strong>*** COMPUTER GENERATED RECEIPT ***</strong></div>
            <div class="footer-note">This receipt is valid without signature</div>
            <div class="footer-note">For inquiries: <EMAIL> | Phone: +****************</div>
            <div class="footer-note">Transaction ID: ' . htmlspecialchars($transfer_data['reference_number']) . '</div>
            <div class="footer-note">Generated on: ' . date('F j, Y g:i A') . '</div>
        </div>
    </div>
    
    <script>
        // Auto-print when opened directly
        if (window.location.search.includes("print=1")) {
            window.onload = function() {
                window.print();
            };
        }
    </script>
</body>
</html>';

    return $html;
}
?>
