<?php
/**
 * Test Session Debug - Check session state after login
 */

require_once 'config/config.php';

echo "<h2>🔍 Session Debug Test</h2>";

echo "<h3>Current Session State</h3>";
echo "<table border='1' cellpadding='5' cellspacing='0'>";
echo "<tr><th>Session Variable</th><th>Value</th><th>Type</th></tr>";

if (empty($_SESSION)) {
    echo "<tr><td colspan='3' style='color: red;'><strong>❌ No session data found</strong></td></tr>";
} else {
    foreach ($_SESSION as $key => $value) {
        $type = gettype($value);
        $display_value = is_bool($value) ? ($value ? 'true' : 'false') : htmlspecialchars(print_r($value, true));
        echo "<tr><td><strong>$key</strong></td><td>$display_value</td><td>$type</td></tr>";
    }
}
echo "</table>";

echo "<h3>Session Configuration</h3>";
echo "<table border='1' cellpadding='5' cellspacing='0'>";
echo "<tr><th>Setting</th><th>Value</th></tr>";
echo "<tr><td>Session ID</td><td>" . session_id() . "</td></tr>";
echo "<tr><td>Session Status</td><td>" . session_status() . " (" . 
    (session_status() === PHP_SESSION_ACTIVE ? 'ACTIVE' : 
     (session_status() === PHP_SESSION_NONE ? 'NONE' : 'DISABLED')) . ")</td></tr>";
echo "<tr><td>Session Name</td><td>" . session_name() . "</td></tr>";
echo "<tr><td>Session Save Path</td><td>" . session_save_path() . "</td></tr>";
echo "<tr><td>Session Cookie Lifetime</td><td>" . ini_get('session.cookie_lifetime') . "</td></tr>";
echo "<tr><td>Session GC Maxlifetime</td><td>" . ini_get('session.gc_maxlifetime') . "</td></tr>";
echo "</table>";

echo "<h3>Login Status Check</h3>";
$is_logged_in = isLoggedIn();
echo "<p><strong>isLoggedIn() result:</strong> " . ($is_logged_in ? '✅ TRUE' : '❌ FALSE') . "</p>";

if (!$is_logged_in) {
    echo "<h4>Why isLoggedIn() returned FALSE:</h4>";
    echo "<ul>";
    
    if (!isset($_SESSION['user_id'])) {
        echo "<li style='color: red;'>❌ \$_SESSION['user_id'] is not set</li>";
    } else {
        echo "<li style='color: green;'>✅ \$_SESSION['user_id'] is set: " . $_SESSION['user_id'] . "</li>";
    }
    
    if (!isset($_SESSION['username'])) {
        echo "<li style='color: red;'>❌ \$_SESSION['username'] is not set</li>";
    } else {
        echo "<li style='color: green;'>✅ \$_SESSION['username'] is set: " . htmlspecialchars($_SESSION['username']) . "</li>";
    }
    
    if (isset($_SESSION['is_admin_session'])) {
        echo "<li style='color: red;'>❌ \$_SESSION['is_admin_session'] is set (this causes isLoggedIn() to return false for regular users)</li>";
        echo "<li>Value: " . ($_SESSION['is_admin_session'] ? 'true' : 'false') . "</li>";
    } else {
        echo "<li style='color: green;'>✅ \$_SESSION['is_admin_session'] is not set (good for regular users)</li>";
    }
    
    echo "</ul>";
}

echo "<h3>🧪 Manual Login Test</h3>";
echo "<div style='background-color: #fff3cd; padding: 15px; border: 1px solid #ffeaa7; border-radius: 5px;'>";
echo "<p>Let's manually set session variables and test:</p>";

// Manually set session for testing
$_SESSION['user_id'] = 5;
$_SESSION['username'] = '<EMAIL>';
$_SESSION['first_name'] = 'james';
$_SESSION['last_name'] = 'Bong';
$_SESSION['is_admin'] = false;

// Don't set is_admin_session for regular users
if (isset($_SESSION['is_admin_session'])) {
    unset($_SESSION['is_admin_session']);
}

echo "<p>✅ Set session variables manually</p>";
$is_logged_in_after = isLoggedIn();
echo "<p><strong>isLoggedIn() after manual setup:</strong> " . ($is_logged_in_after ? '✅ TRUE' : '❌ FALSE') . "</p>";

if ($is_logged_in_after) {
    echo "<p style='color: green;'>🎉 Session is working! The issue might be in the login process itself.</p>";
} else {
    echo "<p style='color: red;'>❌ Still not working. There might be a deeper issue.</p>";
}
echo "</div>";

echo "<h3>🔧 Recommended Actions</h3>";
echo "<div style='background-color: #d1ecf1; padding: 15px; border: 1px solid #bee5eb; border-radius: 5px;'>";
echo "<ol>";
echo "<li><strong>Check if session is being saved:</strong> The session might not be persisting between requests</li>";
echo "<li><strong>Remove admin session flag:</strong> Don't set \$_SESSION['is_admin_session'] for regular users in login logic</li>";
echo "<li><strong>Check session configuration:</strong> Ensure session.save_path is writable</li>";
echo "<li><strong>Clear browser cache:</strong> Old session data might be interfering</li>";
echo "</ol>";
echo "</div>";

echo "<hr>";
echo "<p><a href='login.php'>Test Login Again</a> | <a href='dashboard/'>Try Dashboard</a></p>";
?>
