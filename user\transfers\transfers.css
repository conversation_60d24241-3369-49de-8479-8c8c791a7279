/**
 * Transfer Page CSS
 * Dedicated styles for money transfer page
 * Extends dashboard patterns with transfer-specific styling
 */

/* Import dashboard base styles */
@import url('../dashboard/dashboard.css');

/* Quick Beneficiaries Section */
.quick-beneficiaries-section .card {
    border: 1px solid var(--border-color);
    border-radius: 12px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.08);
}

.quick-beneficiaries-section .card-header {
    background: var(--background-light);
    border-bottom: 1px solid var(--border-color);
    border-radius: 12px 12px 0 0;
}

.beneficiaries-quick-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
    gap: 1rem;
}

.beneficiary-quick-card {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1rem;
    border: 1px solid var(--border-color);
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s ease;
    background: var(--background-white);
}

.beneficiary-quick-card:hover {
    border-color: var(--primary-color);
    background: rgba(var(--primary-rgb), 0.05);
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
}

.beneficiary-quick-card.view-all {
    border-style: dashed;
    color: var(--text-secondary);
}

.beneficiary-quick-card .beneficiary-avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-weight: 600;
    font-size: 0.9rem;
    flex-shrink: 0;
}

.beneficiary-quick-card.view-all .beneficiary-avatar {
    background: var(--background-light);
    color: var(--text-secondary);
}

.beneficiary-quick-card .beneficiary-info {
    flex: 1;
    min-width: 0;
}

.beneficiary-quick-card .beneficiary-name {
    font-weight: 500;
    color: var(--text-primary);
    margin-bottom: 0.25rem;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.beneficiary-quick-card .beneficiary-account {
    font-family: 'Courier New', monospace;
    color: var(--text-secondary);
    font-size: 0.8rem;
    margin-bottom: 0.25rem;
}

.beneficiary-quick-card .internal-badge {
    background: linear-gradient(135deg, #10b981 0%, #059669 100%);
    color: white;
    padding: 0.1rem 0.4rem;
    border-radius: 4px;
    font-size: 0.65rem;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.3px;
}

.beneficiary-quick-card .quick-transfer-btn {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    background: var(--primary-color);
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.8rem;
    flex-shrink: 0;
    transition: all 0.3s ease;
}

.beneficiary-quick-card:hover .quick-transfer-btn {
    background: var(--primary-dark);
    transform: scale(1.1);
}

/* Transfer Hero Section */
.transfer-hero {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
    border-radius: 16px;
    padding: 1.5rem 2rem;
    color: white;
    margin-bottom: 2rem;
    box-shadow: 0 8px 25px rgba(0,0,0,0.15);
    position: relative;
    overflow: hidden;
}

.transfer-hero::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, rgba(255,255,255,0.1) 0%, transparent 50%);
    pointer-events: none;
}

.transfer-hero .hero-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    position: relative;
    z-index: 1;
}

.transfer-hero .hero-main {
    flex: 1;
}

.transfer-hero .hero-title {
    font-size: 1.75rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
    text-shadow: 0 2px 4px rgba(0,0,0,0.3);
}

.transfer-hero .hero-subtitle {
    font-size: 1rem;
    opacity: 0.9;
    margin-bottom: 0.5rem;
}

.transfer-hero .hero-stats {
    font-size: 0.9rem;
    opacity: 0.8;
    font-weight: 500;
}

.transfer-hero .hero-actions {
    display: flex;
    gap: 0.75rem;
}

/* Transfer Types Section */
.transfer-types-section {
    background: var(--background-white);
    border-radius: 12px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.08);
    border: 1px solid var(--border-color);
    padding: 1.5rem;
}

.transfer-types-header {
    text-align: center;
    margin-bottom: 2rem;
}

.transfer-types-header h3 {
    color: var(--text-primary);
    margin-bottom: 0.5rem;
}

.transfer-types-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 1.5rem;
}

/* Transfer Type Card */
.transfer-type-card {
    background: var(--background-white);
    border: 2px solid var(--border-color);
    border-radius: 12px;
    padding: 1.5rem;
    text-align: center;
    transition: all 0.3s ease;
    cursor: pointer;
    position: relative;
    overflow: hidden;
}

.transfer-type-card:hover:not(.disabled) {
    transform: translateY(-4px);
    box-shadow: 0 12px 30px rgba(0,0,0,0.15);
    border-color: var(--primary-color);
}

.transfer-type-card.disabled {
    opacity: 0.6;
    cursor: not-allowed;
    background: var(--background-light);
}

.transfer-type-card.selected {
    border-color: var(--primary-color);
    background: rgba(var(--primary-rgb), 0.05);
}

.transfer-type-icon {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1rem;
    color: white;
    font-size: 1.5rem;
}

.transfer-type-card.disabled .transfer-type-icon {
    background: var(--text-muted);
}

.transfer-type-info h5 {
    color: var(--text-primary);
    margin-bottom: 0.5rem;
    font-weight: 600;
}

.transfer-type-info p {
    color: var(--text-secondary);
    margin-bottom: 1rem;
    font-size: 0.9rem;
}

.transfer-type-features {
    display: flex;
    justify-content: center;
    gap: 0.5rem;
    margin-bottom: 1.5rem;
    flex-wrap: wrap;
}

.feature-badge {
    background: var(--background-light);
    color: var(--text-secondary);
    padding: 0.25rem 0.75rem;
    border-radius: 20px;
    font-size: 0.75rem;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.transfer-type-card.selected .feature-badge {
    background: rgba(var(--primary-rgb), 0.1);
    color: var(--primary-color);
}

/* Transfer Form Section */
.transfer-form-section {
    background: var(--background-white);
    border-radius: 12px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.08);
    border: 1px solid var(--border-color);
    overflow: hidden;
}

.transfer-form-header {
    background: var(--background-light);
    padding: 1rem 1.5rem;
    border-bottom: 1px solid var(--border-color);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.transfer-form-header h3 {
    margin: 0;
    color: var(--text-primary);
    font-size: 1.25rem;
    font-weight: 600;
}

.transfer-form {
    padding: 1.5rem;
}

/* Form Sections */
.form-section {
    margin-bottom: 2rem;
    padding-bottom: 1.5rem;
    border-bottom: 1px solid var(--border-color);
}

.form-section:last-child {
    border-bottom: none;
    margin-bottom: 0;
}

.form-section h5 {
    color: var(--text-primary);
    margin-bottom: 1rem;
    font-weight: 600;
    display: flex;
    align-items: center;
}

/* Source Account Selection */
.source-accounts {
    display: grid;
    gap: 1rem;
}

.source-account-option {
    position: relative;
}

.source-account-option input[type="radio"] {
    position: absolute;
    opacity: 0;
    pointer-events: none;
}

.source-account-label {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem;
    border: 2px solid var(--border-color);
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s ease;
    background: var(--background-white);
}

.source-account-option input[type="radio"]:checked + .source-account-label {
    border-color: var(--primary-color);
    background: rgba(var(--primary-rgb), 0.05);
}

.account-info {
    flex: 1;
}

.account-name {
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 0.25rem;
}

.account-number {
    font-family: 'Courier New', monospace;
    color: var(--text-secondary);
    font-size: 0.9rem;
}

.account-balance {
    text-align: right;
}

.balance-label {
    font-size: 0.8rem;
    color: var(--text-muted);
    margin-bottom: 0.25rem;
}

.balance-amount {
    font-weight: 600;
    color: var(--primary-color);
    font-size: 1.1rem;
}

/* Quick Beneficiaries */
.quick-beneficiaries {
    margin-bottom: 1.5rem;
}

.beneficiaries-list {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    gap: 1rem;
    margin-top: 0.5rem;
}

.beneficiary-quick-option {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 0.75rem;
    border: 1px solid var(--border-color);
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s ease;
    background: var(--background-white);
}

.beneficiary-quick-option:hover {
    border-color: var(--primary-color);
    background: rgba(var(--primary-rgb), 0.05);
}

.beneficiary-quick-option.view-all {
    border-style: dashed;
    color: var(--text-secondary);
}

.beneficiary-avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-weight: 600;
    font-size: 0.9rem;
    flex-shrink: 0;
}

.beneficiary-quick-option.view-all .beneficiary-avatar {
    background: var(--background-light);
    color: var(--text-secondary);
}

.beneficiary-info {
    flex: 1;
    min-width: 0;
}

.beneficiary-name {
    font-weight: 500;
    color: var(--text-primary);
    margin-bottom: 0.25rem;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.beneficiary-account {
    font-family: 'Courier New', monospace;
    color: var(--text-secondary);
    font-size: 0.8rem;
}

.internal-indicator {
    color: var(--success-color);
    font-size: 0.9rem;
}

/* Transfer Summary */
.transfer-summary {
    background: var(--background-light);
    border-radius: 8px;
    padding: 1.25rem;
}

.transfer-summary h5 {
    margin-bottom: 1rem;
    color: var(--text-primary);
}

.summary-details {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
}

.summary-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.summary-row.total {
    padding-top: 0.75rem;
    border-top: 1px solid var(--border-color);
    font-weight: 600;
    font-size: 1.1rem;
}

.summary-label {
    color: var(--text-secondary);
}

.summary-value {
    color: var(--text-primary);
    font-weight: 500;
}

.summary-row.total .summary-value {
    color: var(--primary-color);
}

/* Form Actions */
.form-actions {
    display: flex;
    justify-content: flex-end;
    gap: 1rem;
    margin-top: 2rem;
    padding-top: 1.5rem;
    border-top: 1px solid var(--border-color);
}

/* Modal Enhancements */
.modal-content {
    border: none;
    border-radius: 12px;
    box-shadow: 0 20px 60px rgba(0,0,0,0.2);
}

.otp-verification {
    padding: 1rem;
}

.otp-icon {
    color: var(--primary-color);
}

.otp-input-group input {
    text-align: center;
    font-size: 1.2rem;
    letter-spacing: 0.5rem;
    padding: 0.75rem;
}

.success-animation {
    animation: successPulse 0.6s ease-out;
}

@keyframes successPulse {
    0% { transform: scale(0.8); opacity: 0; }
    50% { transform: scale(1.1); }
    100% { transform: scale(1); opacity: 1; }
}

/* Responsive Design */
@media (max-width: 768px) {
    .transfer-hero .hero-content {
        flex-direction: column;
        gap: 1rem;
        text-align: center;
    }
    
    .transfer-types-grid {
        grid-template-columns: 1fr;
    }
    
    .transfer-form-header {
        flex-direction: column;
        gap: 1rem;
        text-align: center;
    }
    
    .source-account-label {
        flex-direction: column;
        gap: 1rem;
        text-align: center;
    }
    
    .beneficiaries-list {
        grid-template-columns: 1fr;
    }
    
    .form-actions {
        flex-direction: column;
    }
}

@media (max-width: 576px) {
    .transfer-hero {
        padding: 1rem;
    }
    
    .transfer-hero .hero-title {
        font-size: 1.5rem;
    }
    
    .transfer-types-section,
    .transfer-form {
        padding: 1rem;
    }
}
