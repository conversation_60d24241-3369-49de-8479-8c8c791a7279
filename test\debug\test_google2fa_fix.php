<?php
/**
 * Test script to verify Google2FA fixes
 */

// Enable error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);
ini_set('log_errors', 1);
ini_set('error_log', __DIR__ . '/../../logs/error.log');

echo "<h1>Google2FA Fix Test</h1>";

try {
    echo "<h2>1. Testing Config Include</h2>";
    require_once '../../config/config.php';
    echo "✅ Config included successfully<br>";
    
    echo "<h2>2. Testing Google2FA Class</h2>";
    $google2fa = getGoogle2FA();
    echo "✅ Google2FA instance created successfully<br>";
    
    echo "<h2>3. Testing Secret Generation</h2>";
    $secret = $google2fa->generateSecretKey();
    echo "✅ Secret generated: " . substr($secret, 0, 8) . "...<br>";
    
    echo "<h2>4. Testing QR Code URL Generation</h2>";
    $qrUrl = $google2fa->getQRCodeUrl('Test Company', '<EMAIL>', $secret);
    echo "✅ QR Code URL generated successfully<br>";
    
    echo "<h2>5. Testing OTP Generation</h2>";
    $otp = $google2fa->getCurrentOtp($secret);
    echo "✅ Current OTP: " . $otp . "<br>";
    
    echo "<h2>6. Testing OTP Verification</h2>";
    $isValid = $google2fa->verifyKey($secret, $otp);
    echo "✅ OTP verification: " . ($isValid ? 'Valid' : 'Invalid') . "<br>";
    
    echo "<h2>7. Testing Admin Configure 2FA Page</h2>";
    echo "<a href='../../admin/configure-2fa.php' target='_blank'>Test Admin Configure 2FA</a><br>";
    
    echo "<h2>8. Testing Super Admin Login</h2>";
    echo "<a href='../../super-admin/login.php' target='_blank'>Test Super Admin Login</a><br>";
    
    echo "<h2>✅ All tests passed! Google2FA is working correctly.</h2>";
    
} catch (Exception $e) {
    echo "<h2>❌ Error occurred:</h2>";
    echo "<p><strong>Message:</strong> " . $e->getMessage() . "</p>";
    echo "<p><strong>File:</strong> " . $e->getFile() . "</p>";
    echo "<p><strong>Line:</strong> " . $e->getLine() . "</p>";
    echo "<p><strong>Trace:</strong></p>";
    echo "<pre>" . $e->getTraceAsString() . "</pre>";
} catch (Error $e) {
    echo "<h2>❌ Fatal error occurred:</h2>";
    echo "<p><strong>Message:</strong> " . $e->getMessage() . "</p>";
    echo "<p><strong>File:</strong> " . $e->getFile() . "</p>";
    echo "<p><strong>Line:</strong> " . $e->getLine() . "</p>";
    echo "<p><strong>Trace:</strong></p>";
    echo "<pre>" . $e->getTraceAsString() . "</pre>";
}

echo "<hr>";
echo "<p><strong>Log file location:</strong> " . __DIR__ . '/../../logs/error.log</p>';
echo "<p><strong>Check the error log for detailed information.</strong></p>";
?>
