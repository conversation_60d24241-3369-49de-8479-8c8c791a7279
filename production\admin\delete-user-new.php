<?php
/**
 * Delete User Functionality - Clean Implementation
 * Online Banking System
 * 
 * This file handles the complete deletion of a user account and all related data.
 * It includes proper transaction handling, logging, and security checks.
 */

require_once '../config/config.php';

// Security: Ensure only admin users can access this functionality
requireAdmin();

// Initialize response array for JSON responses
$response = [
    'success' => false,
    'message' => '',
    'data' => null
];

// Check if this is a valid AJAX POST request with required parameters
if ($_SERVER['REQUEST_METHOD'] !== 'POST' || !isset($_GET['id']) || !isset($_GET['ajax'])) {
    $response['message'] = 'Invalid request. This endpoint requires a POST request with user ID and AJAX flag.';
    sendJsonResponse($response);
}

// Validate and sanitize the user ID
$user_id = filter_var($_GET['id'], FILTER_VALIDATE_INT);
if (!$user_id || $user_id <= 0) {
    $response['message'] = 'Invalid user ID provided.';
    sendJsonResponse($response);
}

try {
    // Get database connection
    $db = getDB();
    
    // First, retrieve and validate the user to be deleted
    $user = getUserForDeletion($db, $user_id);
    
    // Perform security checks
    performSecurityChecks($user, $user_id);
    
    // Execute the deletion process with transaction handling
    $deletionResult = executeUserDeletion($db, $user_id, $user);
    
    if ($deletionResult['success']) {
        // Log the successful deletion
        logUserDeletion($user, $_SESSION['user_id']);
        
        $response['success'] = true;
        $response['message'] = "User '{$user['username']}' has been permanently deleted.";
        $response['data'] = [
            'deleted_user_id' => $user_id,
            'deleted_username' => $user['username'],
            'deletion_timestamp' => date('Y-m-d H:i:s')
        ];
    } else {
        $response['message'] = $deletionResult['message'];
    }
    
} catch (Exception $e) {
    // Log the error for debugging
    error_log("Delete user error for ID $user_id: " . $e->getMessage());
    
    $response['message'] = 'An unexpected error occurred while deleting the user. Please try again or contact support.';
    
    // In development, you might want to show the actual error
    if (defined('DEVELOPMENT_MODE') && DEVELOPMENT_MODE) {
        $response['debug_error'] = $e->getMessage();
    }
}

// Send the JSON response
sendJsonResponse($response);

/**
 * Retrieve user data for deletion with validation
 */
function getUserForDeletion($db, $user_id) {
    $query = "SELECT id, username, email, first_name, last_name, account_number, is_admin, status 
              FROM accounts 
              WHERE id = ? AND is_admin = 0";
    
    $result = $db->query($query, [$user_id]);
    
    if ($result->num_rows === 0) {
        throw new Exception('User not found or cannot delete admin users.');
    }
    
    return $result->fetch_assoc();
}

/**
 * Perform security checks before deletion
 */
function performSecurityChecks($user, $user_id) {
    // Prevent users from deleting themselves
    if ($user_id == $_SESSION['user_id']) {
        throw new Exception('You cannot delete your own account.');
    }
    
    // Additional check: Ensure user is not an admin (double safety)
    if ($user['is_admin']) {
        throw new Exception('Admin accounts cannot be deleted through this interface.');
    }
    
    // Optional: Check if user has pending transactions or critical data
    // You can add additional business logic checks here
}

/**
 * Execute the complete user deletion process
 */
function executeUserDeletion($db, $user_id, $user) {
    try {
        // Start database transaction
        $db->query("START TRANSACTION");
        
        $deletionStats = [
            'user_otps' => 0,
            'transfers_sent' => 0,
            'transfers_received' => 0,
            'beneficiaries' => 0,
            'tickets' => 0,
            'audit_logs' => 0
        ];
        
        // Step 1: Delete OTP records
        $deletionStats['user_otps'] = $db->delete(
            "DELETE FROM user_otps WHERE user_id = ?", 
            [$user_id]
        );
        
        // Step 2: Delete transfer records where user is sender
        $deletionStats['transfers_sent'] = $db->delete(
            "DELETE FROM transfers WHERE sender_id = ?", 
            [$user_id]
        );
        
        // Step 3: Delete transfer records where user is recipient
        $deletionStats['transfers_received'] = $db->delete(
            "DELETE FROM transfers WHERE recipient_id = ?", 
            [$user_id]
        );
        
        // Step 4: Delete beneficiaries
        $deletionStats['beneficiaries'] = $db->delete(
            "DELETE FROM beneficiaries WHERE user_id = ?", 
            [$user_id]
        );
        
        // Step 5: Delete support tickets
        $deletionStats['tickets'] = $db->delete(
            "DELETE FROM tickets WHERE user_id = ?", 
            [$user_id]
        );
        
        // Step 6: Clean up audit logs related to this user (optional)
        // You might want to keep audit logs for compliance, so this is commented out
        // $deletionStats['audit_logs'] = $db->delete(
        //     "DELETE FROM audit_logs WHERE user_id = ?", 
        //     [$user_id]
        // );
        
        // Step 7: Finally, delete the main user account
        $userDeletionResult = $db->delete(
            "DELETE FROM accounts WHERE id = ? AND is_admin = 0", 
            [$user_id]
        );
        
        if ($userDeletionResult <= 0) {
            throw new Exception('Failed to delete user account. User may have been deleted already.');
        }
        
        // Commit the transaction
        $db->query("COMMIT");
        
        return [
            'success' => true,
            'message' => 'User successfully deleted',
            'stats' => $deletionStats
        ];
        
    } catch (Exception $e) {
        // Rollback transaction on any error
        $db->query("ROLLBACK");
        
        return [
            'success' => false,
            'message' => 'Failed to delete user: ' . $e->getMessage()
        ];
    }
}

/**
 * Log the user deletion for audit purposes
 */
function logUserDeletion($user, $admin_id) {
    try {
        $logMessage = "Admin (ID: $admin_id) deleted user: {$user['username']} " . 
                     "(ID: {$user['id']}, Email: {$user['email']}, " . 
                     "Account: {$user['account_number']})";
        
        // Log to system logs
        error_log("USER_DELETION: $logMessage");
        
        // If you have an admin_logs table, log there too
        $db = getDB();
        $logQuery = "INSERT INTO audit_logs (user_id, action, table_name, record_id, details, created_at) 
                     VALUES (?, 'DELETE', 'accounts', ?, ?, NOW())";
        
        $logDetails = json_encode([
            'deleted_user' => [
                'id' => $user['id'],
                'username' => $user['username'],
                'email' => $user['email'],
                'account_number' => $user['account_number']
            ],
            'deleted_by_admin_id' => $admin_id,
            'deletion_timestamp' => date('Y-m-d H:i:s')
        ]);
        
        $db->query($logQuery, [$admin_id, $user['id'], $logDetails]);
        
    } catch (Exception $e) {
        // Don't fail the deletion if logging fails, just log the error
        error_log("Failed to log user deletion: " . $e->getMessage());
    }
}

/**
 * Send JSON response and exit
 */
function sendJsonResponse($response) {
    header('Content-Type: application/json');
    header('Cache-Control: no-cache, must-revalidate');
    echo json_encode($response, JSON_UNESCAPED_UNICODE);
    exit;
}

?>
