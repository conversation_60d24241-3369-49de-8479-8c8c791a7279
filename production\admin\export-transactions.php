<?php
require_once '../config/config.php';
requireAdmin();

// Check if export is requested
if (!isset($_GET['export']) || $_GET['export'] !== 'csv') {
    redirect('transactions.php');
}

// Get the same filter parameters as the main page
$filter_type = $_GET['type'] ?? '';
$filter_status = $_GET['status'] ?? '';
$filter_user = $_GET['user'] ?? '';
$filter_date_from = $_GET['date_from'] ?? '';
$filter_date_to = $_GET['date_to'] ?? '';
$filter_amount_min = floatval($_GET['amount_min'] ?? 0);
$filter_amount_max = floatval($_GET['amount_max'] ?? 0);

// Build WHERE clause for filters (same as transactions.php)
$where_conditions = [];
$params = [];

if (!empty($filter_type)) {
    $where_conditions[] = "at.transaction_type = ?";
    $params[] = $filter_type;
}

if (!empty($filter_status)) {
    $where_conditions[] = "at.status = ?";
    $params[] = $filter_status;
}

if (!empty($filter_user)) {
    $where_conditions[] = "(a.first_name LIKE ? OR a.last_name LIKE ? OR a.username LIKE ?)";
    $search_term = "%$filter_user%";
    $params[] = $search_term;
    $params[] = $search_term;
    $params[] = $search_term;
}

if (!empty($filter_date_from)) {
    $where_conditions[] = "DATE(at.created_at) >= ?";
    $params[] = $filter_date_from;
}

if (!empty($filter_date_to)) {
    $where_conditions[] = "DATE(at.created_at) <= ?";
    $params[] = $filter_date_to;
}

if ($filter_amount_min > 0) {
    $where_conditions[] = "at.amount >= ?";
    $params[] = $filter_amount_min;
}

if ($filter_amount_max > 0) {
    $where_conditions[] = "at.amount <= ?";
    $params[] = $filter_amount_max;
}

$where_clause = !empty($where_conditions) ? "WHERE " . implode(" AND ", $where_conditions) : "";

try {
    $db = getDB();
    
    // Get all transactions matching the filters (no pagination for export)
    $transactions_query = "SELECT at.*, 
                          a.first_name, a.last_name, a.username, a.account_number,
                          admin.first_name as admin_first_name, admin.last_name as admin_last_name
                          FROM account_transactions at 
                          LEFT JOIN accounts a ON at.account_id = a.id 
                          LEFT JOIN accounts admin ON at.processed_by = admin.id 
                          $where_clause
                          ORDER BY at.created_at DESC";
    
    $transactions_result = $db->query($transactions_query, $params);
    
    // Set headers for CSV download
    $filename = 'transactions_' . date('Y-m-d_H-i-s') . '.csv';
    header('Content-Type: text/csv');
    header('Content-Disposition: attachment; filename="' . $filename . '"');
    header('Cache-Control: no-cache, must-revalidate');
    header('Expires: Sat, 26 Jul 1997 05:00:00 GMT');
    
    // Create file pointer connected to the output stream
    $output = fopen('php://output', 'w');
    
    // Add CSV headers
    $headers = [
        'Transaction ID',
        'Reference Number',
        'User Name',
        'Username',
        'Account Number',
        'Transaction Type',
        'Amount',
        'Currency',
        'Description',
        'Category',
        'Status',
        'Processed By',
        'Created Date',
        'Created Time',
        'Updated Date',
        'Updated Time'
    ];
    
    fputcsv($output, $headers);
    
    // Add transaction data
    while ($transaction = $transactions_result->fetch_assoc()) {
        $row = [
            $transaction['id'],
            $transaction['reference_number'],
            ($transaction['first_name'] ?? 'Unknown') . ' ' . ($transaction['last_name'] ?? 'User'),
            $transaction['username'] ?? 'unknown',
            $transaction['account_number'] ?? 'N/A',
            ucfirst(str_replace('_', ' ', $transaction['transaction_type'])),
            number_format($transaction['amount'], 2),
            $transaction['currency'],
            $transaction['description'],
            $transaction['category'],
            ucfirst($transaction['status']),
            $transaction['admin_first_name'] ? 
                $transaction['admin_first_name'] . ' ' . $transaction['admin_last_name'] : 'System',
            date('Y-m-d', strtotime($transaction['created_at'])),
            date('H:i:s', strtotime($transaction['created_at'])),
            date('Y-m-d', strtotime($transaction['updated_at'])),
            date('H:i:s', strtotime($transaction['updated_at']))
        ];
        
        fputcsv($output, $row);
    }
    
    fclose($output);
    exit;
    
} catch (Exception $e) {
    // If there's an error, redirect back to transactions page with error
    $_SESSION['export_error'] = 'Failed to export transactions: ' . $e->getMessage();
    redirect('transactions.php');
}
?>
