<?php
/**
 * Super Admin Logout
 * Handles super admin logout and session cleanup
 */

session_start();

// Include authentication functions
require_once 'includes/auth.php';

// Log logout action if logged in
if (isSuperAdminLoggedIn()) {
    logSuperAdminAction('logout', 'Super admin logged out successfully');
}

// Destroy super admin session
destroySuperAdminSession();

// Redirect to login page with logout message
header('Location: login.php?logout=1');
exit;
?>
