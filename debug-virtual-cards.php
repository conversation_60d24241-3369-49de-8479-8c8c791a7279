<?php
session_start();
require_once 'config/config.php';

echo "<h2>Debug Virtual Cards for Wire Transfer</h2>";

try {
    $db = getDB();
    
    // Check current user session
    echo "<h3>Current User Session</h3>";
    echo "User ID: " . ($_SESSION['user_id'] ?? 'Not set') . "<br>";
    echo "Username: " . ($_SESSION['username'] ?? 'Not set') . "<br>";
    echo "Is Admin: " . ($_SESSION['is_admin'] ?? 'Not set') . "<br>";
    
    // Get current user details
    if (isset($_SESSION['user_id'])) {
        $user_id = $_SESSION['user_id'];
        $user_query = "SELECT * FROM accounts WHERE id = ?";
        $user_result = $db->query($user_query, [$user_id]);
        if ($user = $user_result->fetch_assoc()) {
            echo "<h3>Current User Details</h3>";
            echo "ID: " . $user['id'] . "<br>";
            echo "Account Number: " . $user['account_number'] . "<br>";
            echo "Name: " . $user['first_name'] . " " . $user['last_name'] . "<br>";
            echo "Balance: $" . number_format($user['balance'], 2) . "<br>";
        }
    }
    
    // Check all virtual cards in database
    echo "<h3>All Virtual Cards in Database</h3>";
    $all_cards_query = "SELECT vc.*, a.first_name, a.last_name, a.username 
                        FROM virtual_cards vc 
                        LEFT JOIN accounts a ON vc.account_id = a.id 
                        ORDER BY vc.card_id";
    $all_cards_result = $db->query($all_cards_query);
    
    if ($all_cards_result && $all_cards_result->num_rows > 0) {
        echo "<table border='1' style='border-collapse: collapse;'>";
        echo "<tr><th>Card ID</th><th>Account ID</th><th>Card Number</th><th>Balance</th><th>Status</th><th>Owner</th></tr>";
        while ($card = $all_cards_result->fetch_assoc()) {
            echo "<tr>";
            echo "<td>" . $card['card_id'] . "</td>";
            echo "<td>" . $card['account_id'] . "</td>";
            echo "<td>****" . substr($card['card_number'], -4) . "</td>";
            echo "<td>$" . number_format($card['card_balance'], 2) . "</td>";
            echo "<td>" . $card['status'] . "</td>";
            echo "<td>" . $card['first_name'] . " " . $card['last_name'] . " (@" . $card['username'] . ")</td>";
            echo "</tr>";
        }
        echo "</table>";
    } else {
        echo "No virtual cards found in database.";
    }
    
    // Check virtual cards for current user
    if (isset($_SESSION['user_id'])) {
        echo "<h3>Virtual Cards for Current User (ID: $user_id)</h3>";
        $user_cards_query = "SELECT card_id, card_number, card_holder_name, card_balance, currency, card_type
                            FROM virtual_cards
                            WHERE account_id = ? AND status = 'active' AND card_balance > 0
                            ORDER BY card_balance DESC";
        $user_cards_result = $db->query($user_cards_query, [$user_id]);
        
        if ($user_cards_result && $user_cards_result->num_rows > 0) {
            echo "<table border='1' style='border-collapse: collapse;'>";
            echo "<tr><th>Card ID</th><th>Card Number</th><th>Holder Name</th><th>Balance</th><th>Currency</th><th>Type</th></tr>";
            while ($card = $user_cards_result->fetch_assoc()) {
                echo "<tr>";
                echo "<td>" . $card['card_id'] . "</td>";
                echo "<td>****" . substr($card['card_number'], -4) . "</td>";
                echo "<td>" . $card['card_holder_name'] . "</td>";
                echo "<td>$" . number_format($card['card_balance'], 2) . "</td>";
                echo "<td>" . $card['currency'] . "</td>";
                echo "<td>" . $card['card_type'] . "</td>";
                echo "</tr>";
            }
            echo "</table>";
        } else {
            echo "No virtual cards found for current user with balance > 0.";
        }
        
        // Check all virtual cards for current user (including zero balance)
        echo "<h3>All Virtual Cards for Current User (including zero balance)</h3>";
        $all_user_cards_query = "SELECT card_id, card_number, card_holder_name, card_balance, currency, card_type, status
                                FROM virtual_cards
                                WHERE account_id = ?
                                ORDER BY card_balance DESC";
        $all_user_cards_result = $db->query($all_user_cards_query, [$user_id]);
        
        if ($all_user_cards_result && $all_user_cards_result->num_rows > 0) {
            echo "<table border='1' style='border-collapse: collapse;'>";
            echo "<tr><th>Card ID</th><th>Card Number</th><th>Holder Name</th><th>Balance</th><th>Currency</th><th>Type</th><th>Status</th></tr>";
            while ($card = $all_user_cards_result->fetch_assoc()) {
                echo "<tr>";
                echo "<td>" . $card['card_id'] . "</td>";
                echo "<td>****" . substr($card['card_number'], -4) . "</td>";
                echo "<td>" . $card['card_holder_name'] . "</td>";
                echo "<td>$" . number_format($card['card_balance'], 2) . "</td>";
                echo "<td>" . $card['currency'] . "</td>";
                echo "<td>" . $card['card_type'] . "</td>";
                echo "<td>" . $card['status'] . "</td>";
                echo "</tr>";
            }
            echo "</table>";
        } else {
            echo "No virtual cards found for current user.";
        }
    }
    
} catch (Exception $e) {
    echo "Error: " . $e->getMessage();
}
?>
