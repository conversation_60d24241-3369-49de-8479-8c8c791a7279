<?php
require_once '../config/config.php';
requireAdmin();

$page_title = 'Virtual Card Transactions';

// Pagination settings
$records_per_page = 25;
$current_page = intval($_GET['page'] ?? 1);
$offset = ($current_page - 1) * $records_per_page;

// Filter parameters
$filter_type = $_GET['type'] ?? '';
$filter_status = $_GET['status'] ?? '';
$filter_card = $_GET['card'] ?? '';
$filter_date_from = $_GET['date_from'] ?? '';
$filter_date_to = $_GET['date_to'] ?? '';
$filter_amount_min = floatval($_GET['amount_min'] ?? 0);
$filter_amount_max = floatval($_GET['amount_max'] ?? 0);

// Build WHERE clause for filters
$where_conditions = [];
$params = [];

if (!empty($filter_type)) {
    $where_conditions[] = "vct.transaction_type = ?";
    $params[] = $filter_type;
}

if (!empty($filter_status)) {
    $where_conditions[] = "vct.status = ?";
    $params[] = $filter_status;
}

if (!empty($filter_card)) {
    $where_conditions[] = "(vc.card_number LIKE ? OR vc.card_holder_name LIKE ?)";
    $search_term = "%$filter_card%";
    $params[] = $search_term;
    $params[] = $search_term;
}

if (!empty($filter_date_from)) {
    $where_conditions[] = "DATE(vct.created_at) >= ?";
    $params[] = $filter_date_from;
}

if (!empty($filter_date_to)) {
    $where_conditions[] = "DATE(vct.created_at) <= ?";
    $params[] = $filter_date_to;
}

if ($filter_amount_min > 0) {
    $where_conditions[] = "vct.amount >= ?";
    $params[] = $filter_amount_min;
}

if ($filter_amount_max > 0) {
    $where_conditions[] = "vct.amount <= ?";
    $params[] = $filter_amount_max;
}

$where_clause = !empty($where_conditions) ? "WHERE " . implode(" AND ", $where_conditions) : "";

try {
    $db = getDB();
    
    // Get total count for pagination
    $count_query = "SELECT COUNT(*) as total 
                    FROM virtual_card_transactions vct 
                    LEFT JOIN virtual_cards vc ON vct.card_id = vc.card_id 
                    LEFT JOIN accounts a ON vct.account_id = a.id 
                    $where_clause";
    $count_result = $db->query($count_query, $params);
    $total_records = $count_result->fetch_assoc()['total'];
    $total_pages = ceil($total_records / $records_per_page);
    
    // Get transactions with pagination
    $transactions_query = "SELECT vct.*, 
                          vc.card_number, vc.card_holder_name,
                          a.first_name, a.last_name, a.username, a.account_number,
                          admin.first_name as admin_first_name, admin.last_name as admin_last_name
                          FROM virtual_card_transactions vct 
                          LEFT JOIN virtual_cards vc ON vct.card_id = vc.card_id 
                          LEFT JOIN accounts a ON vct.account_id = a.id 
                          LEFT JOIN accounts admin ON vct.processed_by = admin.id 
                          $where_clause
                          ORDER BY vct.created_at DESC 
                          LIMIT $records_per_page OFFSET $offset";
    
    $transactions_result = $db->query($transactions_query, $params);
    $transactions = [];
    while ($row = $transactions_result->fetch_assoc()) {
        $transactions[] = $row;
    }
    
} catch (Exception $e) {
    $error = "Failed to load card transactions: " . $e->getMessage();
    $transactions = [];
    $total_records = 0;
    $total_pages = 0;
}

include 'includes/admin-header.php';
?>

<!-- Breadcrumb -->
<nav aria-label="breadcrumb" style="margin-bottom: 1rem;">
    <ol class="breadcrumb">
        <li class="breadcrumb-item"><a href="index.php">Admin</a></li>
        <li class="breadcrumb-item"><a href="virtual-cards.php">Virtual Cards</a></li>
        <li class="breadcrumb-item active" aria-current="page">Card Transactions</li>
    </ol>
</nav>

<!-- Error Message -->
<?php if (isset($error)): ?>
<div class="alert alert-danger" role="alert">
    <div class="d-flex">
        <div><i class="fas fa-exclamation-circle me-2"></i></div>
        <div>
            <h4 class="alert-title">Error!</h4>
            <div class="text-muted"><?php echo htmlspecialchars($error); ?></div>
        </div>
    </div>
</div>
<?php endif; ?>

<!-- Filters Card -->
<div class="row row-cards mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h3 class="card-title">
                    <i class="fas fa-filter me-2"></i>
                    Transaction Filters
                </h3>
                <div class="card-actions">
                    <a href="card-transactions.php" class="btn btn-outline-secondary btn-sm">
                        <i class="fas fa-times me-2"></i>
                        Clear Filters
                    </a>
                </div>
            </div>
            <div class="card-body">
                <form method="GET" action="" class="row g-2">
                    <div class="col-md-2">
                        <label class="form-label">Type</label>
                        <select name="type" class="form-select form-select-sm">
                            <option value="">All Types</option>
                            <option value="credit" <?php echo $filter_type === 'credit' ? 'selected' : ''; ?>>Credit</option>
                            <option value="debit" <?php echo $filter_type === 'debit' ? 'selected' : ''; ?>>Debit</option>
                            <option value="purchase" <?php echo $filter_type === 'purchase' ? 'selected' : ''; ?>>Purchase</option>
                            <option value="refund" <?php echo $filter_type === 'refund' ? 'selected' : ''; ?>>Refund</option>
                            <option value="transfer_in" <?php echo $filter_type === 'transfer_in' ? 'selected' : ''; ?>>Transfer In</option>
                            <option value="transfer_out" <?php echo $filter_type === 'transfer_out' ? 'selected' : ''; ?>>Transfer Out</option>
                            <option value="fee" <?php echo $filter_type === 'fee' ? 'selected' : ''; ?>>Fee</option>
                        </select>
                    </div>
                    
                    <div class="col-md-2">
                        <label class="form-label">Status</label>
                        <select name="status" class="form-select form-select-sm">
                            <option value="">All Status</option>
                            <option value="pending" <?php echo $filter_status === 'pending' ? 'selected' : ''; ?>>Pending</option>
                            <option value="processing" <?php echo $filter_status === 'processing' ? 'selected' : ''; ?>>Processing</option>
                            <option value="completed" <?php echo $filter_status === 'completed' ? 'selected' : ''; ?>>Completed</option>
                            <option value="cancelled" <?php echo $filter_status === 'cancelled' ? 'selected' : ''; ?>>Cancelled</option>
                            <option value="failed" <?php echo $filter_status === 'failed' ? 'selected' : ''; ?>>Failed</option>
                        </select>
                    </div>
                    
                    <div class="col-md-2">
                        <label class="form-label">Card</label>
                        <input type="text" name="card" class="form-control form-control-sm" placeholder="Card number or holder..." value="<?php echo htmlspecialchars($filter_card); ?>">
                    </div>
                    
                    <div class="col-md-2">
                        <label class="form-label">Date From</label>
                        <input type="date" name="date_from" class="form-control form-control-sm" value="<?php echo htmlspecialchars($filter_date_from); ?>">
                    </div>
                    
                    <div class="col-md-2">
                        <label class="form-label">Date To</label>
                        <input type="date" name="date_to" class="form-control form-control-sm" value="<?php echo htmlspecialchars($filter_date_to); ?>">
                    </div>
                    
                    <div class="col-md-2">
                        <label class="form-label">Amount Range</label>
                        <div class="input-group input-group-sm">
                            <input type="number" name="amount_min" class="form-control" step="0.01" placeholder="Min" value="<?php echo $filter_amount_min > 0 ? $filter_amount_min : ''; ?>">
                            <span class="input-group-text">-</span>
                            <input type="number" name="amount_max" class="form-control" step="0.01" placeholder="Max" value="<?php echo $filter_amount_max > 0 ? $filter_amount_max : ''; ?>">
                        </div>
                    </div>
                    
                    <div class="col-12 mt-3">
                        <div class="btn-group" role="group">
                            <button type="submit" class="btn btn-primary btn-sm">
                                <i class="fas fa-search me-1"></i>
                                Filter
                            </button>
                            <a href="card-transactions.php" class="btn btn-outline-secondary btn-sm">
                                <i class="fas fa-times me-1"></i>
                                Clear
                            </a>
                        </div>
                        
                        <div class="btn-group ms-2" role="group">
                            <a href="card-operations.php" class="btn btn-success btn-sm">
                                <i class="fas fa-plus me-1"></i>
                                New Operation
                            </a>
                            <a href="virtual-cards.php" class="btn btn-outline-primary btn-sm">
                                <i class="fas fa-credit-card me-1"></i>
                                All Cards
                            </a>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- Card Transactions Table -->
<div class="row row-cards">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h3 class="card-title">
                    <i class="fas fa-list me-2"></i>
                    Virtual Card Transactions
                    <span class="badge bg-secondary ms-2"><?php echo number_format($total_records); ?> total</span>
                </h3>
                <div class="card-actions">
                    <span class="text-muted">
                        Showing <?php echo number_format(($current_page - 1) * $records_per_page + 1); ?> -
                        <?php echo number_format(min($current_page * $records_per_page, $total_records)); ?>
                        of <?php echo number_format($total_records); ?> transactions
                    </span>
                </div>
            </div>
            <div class="card-body p-0">
                <?php if (!empty($transactions)): ?>
                <div class="table-responsive">
                    <table class="table table-vcenter card-table table-sm">
                        <thead>
                            <tr>
                                <th class="w-1">#</th>
                                <th>Transaction ID</th>
                                <th>Card</th>
                                <th>Type</th>
                                <th>Amount</th>
                                <th>Description</th>
                                <th>Status</th>
                                <th>Date</th>
                                <th class="w-1">Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php
                            $row_number = ($current_page - 1) * $records_per_page + 1;
                            foreach ($transactions as $transaction):
                            ?>
                            <tr>
                                <!-- Row Number -->
                                <td>
                                    <span class="text-muted"><?php echo $row_number++; ?></span>
                                </td>

                                <!-- Transaction ID -->
                                <td>
                                    <div class="d-flex flex-column">
                                        <div class="fw-bold">#<?php echo $transaction['id']; ?></div>
                                        <small class="text-muted"><?php echo htmlspecialchars(substr($transaction['reference_number'], -8)); ?></small>
                                    </div>
                                </td>

                                <!-- Card -->
                                <td>
                                    <div class="d-flex flex-column">
                                        <div class="fw-bold font-monospace">**** **** **** <?php echo substr($transaction['card_number'], -4); ?></div>
                                        <small class="text-muted"><?php echo htmlspecialchars($transaction['card_holder_name']); ?></small>
                                    </div>
                                </td>

                                <!-- Type -->
                                <td>
                                    <?php
                                    $type_colors = [
                                        'credit' => 'success',
                                        'debit' => 'danger',
                                        'purchase' => 'info',
                                        'refund' => 'warning',
                                        'transfer_in' => 'primary',
                                        'transfer_out' => 'secondary',
                                        'fee' => 'dark'
                                    ];
                                    $color = $type_colors[$transaction['transaction_type']] ?? 'secondary';
                                    ?>
                                    <span class="badge bg-<?php echo $color; ?> badge-sm">
                                        <?php echo ucfirst(str_replace('_', ' ', $transaction['transaction_type'])); ?>
                                    </span>
                                </td>

                                <!-- Amount -->
                                <td>
                                    <span class="fw-bold <?php echo in_array($transaction['transaction_type'], ['credit', 'refund', 'transfer_in']) ? 'text-success' : 'text-danger'; ?>">
                                        <?php echo in_array($transaction['transaction_type'], ['credit', 'refund', 'transfer_in']) ? '+' : '-'; ?>
                                        <?php echo formatCurrency($transaction['amount'], $transaction['currency']); ?>
                                    </span>
                                </td>

                                <!-- Description -->
                                <td>
                                    <div class="text-truncate" style="max-width: 200px;" title="<?php echo htmlspecialchars($transaction['description']); ?>">
                                        <?php echo htmlspecialchars($transaction['description']); ?>
                                    </div>
                                    <?php if ($transaction['merchant_name']): ?>
                                    <small class="text-muted d-block">Merchant: <?php echo htmlspecialchars($transaction['merchant_name']); ?></small>
                                    <?php endif; ?>
                                </td>

                                <!-- Status -->
                                <td>
                                    <?php
                                    $status_colors = [
                                        'pending' => 'warning',
                                        'processing' => 'info',
                                        'completed' => 'success',
                                        'cancelled' => 'secondary',
                                        'failed' => 'danger'
                                    ];
                                    $status_color = $status_colors[$transaction['status']] ?? 'secondary';
                                    ?>
                                    <span class="badge bg-<?php echo $status_color; ?> badge-sm">
                                        <?php echo ucfirst($transaction['status']); ?>
                                    </span>
                                </td>

                                <!-- Date -->
                                <td>
                                    <div class="d-flex flex-column">
                                        <div class="text-muted" style="font-size: 0.85rem;"><?php echo date('M j, Y', strtotime($transaction['created_at'])); ?></div>
                                        <small class="text-muted"><?php echo date('g:i A', strtotime($transaction['created_at'])); ?></small>
                                    </div>
                                </td>

                                <!-- Actions -->
                                <td>
                                    <div class="btn-list">
                                        <button type="button" class="btn btn-sm btn-outline-primary" onclick="viewCardTransaction(<?php echo htmlspecialchars(json_encode($transaction)); ?>)" title="View Details">
                                            <i class="fas fa-eye"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
                <?php else: ?>
                <div class="empty">
                    <div class="empty-img">
                        <i class="fas fa-search" style="font-size: 3rem; color: #6c757d;"></i>
                    </div>
                    <p class="empty-title">No card transactions found</p>
                    <p class="empty-subtitle text-muted">
                        <?php if (!empty(array_filter([$filter_type, $filter_status, $filter_card, $filter_date_from, $filter_date_to]))): ?>
                        Try adjusting your filters to see more results.
                        <?php else: ?>
                        No card transactions have been recorded yet.
                        <?php endif; ?>
                    </p>
                    <div class="empty-action">
                        <a href="card-operations.php" class="btn btn-primary">
                            <i class="fas fa-plus me-2"></i>
                            Create First Transaction
                        </a>
                    </div>
                </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<?php include 'includes/admin-footer.php'; ?>
