<?php
require_once '../config/config.php';

// Require admin authentication
requireAdmin();

$page_title = 'Add New User';

// Define page actions
$page_actions = [
    [
        'url' => 'users.php',
        'label' => 'Back to Users',
        'icon' => 'fas fa-arrow-left'
    ]
];

$errors = [];
$success = '';

// Initialize database connection
$db = getDB();

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // Sanitize and validate input
    $username = sanitizeInput($_POST['username'] ?? '');
    $email = sanitizeInput($_POST['email'] ?? '');
    $first_name = sanitizeInput($_POST['first_name'] ?? '');
    $last_name = sanitizeInput($_POST['last_name'] ?? '');
    $phone = sanitizeInput($_POST['phone'] ?? '');
    $address = sanitizeInput($_POST['address'] ?? '');
    $date_of_birth = sanitizeInput($_POST['date_of_birth'] ?? '');
    $occupation = sanitizeInput($_POST['occupation'] ?? '');
    $marital_status = sanitizeInput($_POST['marital_status'] ?? 'single');
    $gender = sanitizeInput($_POST['gender'] ?? 'male');
    $currency = sanitizeInput($_POST['currency'] ?? 'USD');
    $account_type = sanitizeInput($_POST['account_type'] ?? 'savings');
    $initial_balance = floatval($_POST['initial_balance'] ?? 0);
    $password = $_POST['password'] ?? '';
    $confirm_password = $_POST['confirm_password'] ?? '';
    $kyc_status = sanitizeInput($_POST['kyc_status'] ?? 'pending');
    $status = sanitizeInput($_POST['status'] ?? 'active');
    
    // Validation
    if (empty($username)) {
        $errors[] = 'Username is required.';
    } elseif (strlen($username) < 3) {
        $errors[] = 'Username must be at least 3 characters long.';
    }
    
    if (empty($email)) {
        $errors[] = 'Email is required.';
    } elseif (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
        $errors[] = 'Please enter a valid email address.';
    }
    
    if (empty($first_name)) {
        $errors[] = 'First name is required.';
    }
    
    if (empty($last_name)) {
        $errors[] = 'Last name is required.';
    }
    
    if (empty($password)) {
        $errors[] = 'Password is required.';
    } elseif (strlen($password) < 6) {
        $errors[] = 'Password must be at least 6 characters long.';
    }
    
    if ($password !== $confirm_password) {
        $errors[] = 'Passwords do not match.';
    }
    
    if (!empty($date_of_birth)) {
        $dob = DateTime::createFromFormat('Y-m-d', $date_of_birth);
        if (!$dob || $dob->format('Y-m-d') !== $date_of_birth) {
            $errors[] = 'Please enter a valid date of birth.';
        }
    }
    
    if ($initial_balance < 0) {
        $errors[] = 'Initial balance cannot be negative.';
    }
    
    // Check for existing username and email
    if (empty($errors)) {
        try {
            $db = getDB();
            
            // Check username
            $check_username = $db->query("SELECT id FROM accounts WHERE username = ?", [$username]);
            if ($check_username->num_rows > 0) {
                $errors[] = 'Username already exists.';
            }
            
            // Check email
            $check_email = $db->query("SELECT id FROM accounts WHERE email = ?", [$email]);
            if ($check_email->num_rows > 0) {
                $errors[] = 'Email address already exists.';
            }
            
        } catch (Exception $e) {
            $errors[] = 'Database error occurred. Please try again.';
            error_log("Add user validation error: " . $e->getMessage());
        }
    }
    
    // Create user if no errors
    if (empty($errors)) {
        try {
            $db = getDB();
            $db->beginTransaction();
            
            // Generate account number
            $account_number = generateAccountNumber();
            
            // Hash password
            $hashed_password = hashPassword($password);
            
            // Insert user
            $sql = "INSERT INTO accounts (
                        account_number, username, password, email, first_name, last_name,
                        phone, address, date_of_birth, occupation, marital_status, gender,
                        currency, account_type, balance, status, kyc_status, is_admin
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, 0)";

            $params = [
                $account_number, $username, $hashed_password, $email, $first_name, $last_name,
                $phone, $address, $date_of_birth ?: null, $occupation, $marital_status, $gender,
                $currency, $account_type, $initial_balance, $status, $kyc_status
            ];
            
            $user_id = $db->insert($sql, $params);
            
            // Log the activity
            logActivity($_SESSION['user_id'], 'Admin created new user', 'accounts', $user_id, null, [
                'username' => $username,
                'email' => $email,
                'account_number' => $account_number
            ]);

            $db->commit();

            // Prepare user data for welcome email
            $user_data = [
                'first_name' => $first_name,
                'last_name' => $last_name,
                'username' => $username,
                'email' => $email,
                'account_number' => $account_number,
                'account_type' => $account_type,
                'currency' => $currency,
                'balance' => $initial_balance,
                'status' => $status
            ];

            // Send welcome email
            $emailSent = sendWelcomeEmail($email, $user_data);

            if ($emailSent) {
                $success = "User created successfully! Account Number: $account_number. Welcome email sent to $email.";
            } else {
                $success = "User created successfully! Account Number: $account_number. Note: Welcome email could not be sent.";
            }
            
            // Clear form data
            $username = $email = $first_name = $last_name = $phone = $address = $date_of_birth = $occupation = '';
            $initial_balance = 0;
            $account_type = 'savings';
            $marital_status = 'single';
            $gender = 'male';
            $currency = 'USD';
            $kyc_status = 'pending';
            $status = 'active';
            
        } catch (Exception $e) {
            $db->rollback();
            $errors[] = 'Failed to create user. Please try again.';
            error_log("Add user error: " . $e->getMessage());
        }
    }
}

include 'includes/admin-header.php';
?>

<!-- Breadcrumb -->
<nav aria-label="breadcrumb" style="margin-bottom: 1rem;">
    <ol class="breadcrumb">
        <li class="breadcrumb-item"><a href="index.php">Admin</a></li>
        <li class="breadcrumb-item"><a href="users.php">Users</a></li>
        <li class="breadcrumb-item active" aria-current="page">Add User</li>
    </ol>
</nav>
<div class="row row-cards">
    <div class="col-12">
        <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">User Information</h3>
                        </div>
                        <div class="card-body">
                            <?php if (!empty($errors)): ?>
                            <div class="alert alert-danger" role="alert">
                                <div class="d-flex">
                                    <div>
                                        <svg xmlns="http://www.w3.org/2000/svg" class="icon alert-icon" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
                                            <path stroke="none" d="M0 0h24v24H0z" fill="none"/>
                                            <circle cx="12" cy="12" r="9"/>
                                            <line x1="15" y1="9" x2="9" y2="15"/>
                                            <line x1="9" y1="9" x2="15" y2="15"/>
                                        </svg>
                                    </div>
                                    <div>
                                        <h4 class="alert-title">Error!</h4>
                                        <ul class="mb-0">
                                            <?php foreach ($errors as $error): ?>
                                            <li><?php echo htmlspecialchars($error); ?></li>
                                            <?php endforeach; ?>
                                        </ul>
                                    </div>
                                </div>
                            </div>
                            <?php endif; ?>
                            
                            <?php if (!empty($success)): ?>
                            <div class="alert alert-success" role="alert">
                                <div class="d-flex">
                                    <div>
                                        <svg xmlns="http://www.w3.org/2000/svg" class="icon alert-icon" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
                                            <path stroke="none" d="M0 0h24v24H0z" fill="none"/>
                                            <path d="M5 12l5 5l10 -10"/>
                                        </svg>
                                    </div>
                                    <div>
                                        <h4 class="alert-title">Success!</h4>
                                        <div class="text-muted"><?php echo htmlspecialchars($success); ?></div>
                                    </div>
                                </div>
                            </div>
                            <?php endif; ?>
                            
                            <form method="POST" action="">
                                <!-- Account Number Display -->
                                <div class="row">
                                    <div class="col-12">
                                        <div class="mb-3">
                                            <label class="form-label">Account Number</label>
                                            <div class="input-group">
                                                <input type="text" id="account_number_display" class="form-control" value="<?php echo isset($account_number) ? htmlspecialchars($account_number) : 'Will be auto-generated'; ?>" readonly>
                                                <button type="button" class="btn btn-outline-secondary" onclick="generateAccountNumber()">
                                                    <i class="fas fa-refresh me-1"></i>
                                                    Generate
                                                </button>
                                            </div>
                                            <small class="form-hint">Account number will be automatically generated when user is created.</small>
                                        </div>
                                    </div>
                                </div>

                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label required">Username</label>
                                            <input type="text" name="username" class="form-control" value="<?php echo htmlspecialchars($username ?? ''); ?>" required>
                                            <small class="form-hint">Must be unique and at least 3 characters long.</small>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label required">Email Address</label>
                                            <input type="email" name="email" class="form-control" value="<?php echo htmlspecialchars($email ?? ''); ?>" required>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label required">First Name</label>
                                            <input type="text" name="first_name" class="form-control" value="<?php echo htmlspecialchars($first_name ?? ''); ?>" required>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label required">Last Name</label>
                                            <input type="text" name="last_name" class="form-control" value="<?php echo htmlspecialchars($last_name ?? ''); ?>" required>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label">Phone Number</label>
                                            <input type="tel" name="phone" class="form-control" placeholder="Type without + (e.g., 2341234567786)" value="<?php echo htmlspecialchars($phone ?? ''); ?>">
                                            <small class="form-hint">Type without + (e.g., 2341234567786)</small>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label">Date of Birth</label>
                                            <input type="date" name="date_of_birth" class="form-control" value="<?php echo htmlspecialchars($date_of_birth ?? ''); ?>">
                                        </div>
                                    </div>
                                </div>

                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label">Occupation</label>
                                            <input type="text" name="occupation" class="form-control" value="<?php echo htmlspecialchars($occupation ?? ''); ?>">
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label">Marital Status</label>
                                            <select name="marital_status" class="form-select">
                                                <option value="single" <?php echo ($marital_status ?? 'single') === 'single' ? 'selected' : ''; ?>>Single</option>
                                                <option value="married" <?php echo ($marital_status ?? '') === 'married' ? 'selected' : ''; ?>>Married</option>
                                                <option value="divorced" <?php echo ($marital_status ?? '') === 'divorced' ? 'selected' : ''; ?>>Divorced</option>
                                                <option value="widowed" <?php echo ($marital_status ?? '') === 'widowed' ? 'selected' : ''; ?>>Widowed</option>
                                            </select>
                                        </div>
                                    </div>
                                </div>

                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label">Gender</label>
                                            <select name="gender" class="form-select">
                                                <option value="male" <?php echo ($gender ?? 'male') === 'male' ? 'selected' : ''; ?>>Male</option>
                                                <option value="female" <?php echo ($gender ?? '') === 'female' ? 'selected' : ''; ?>>Female</option>
                                                <option value="other" <?php echo ($gender ?? '') === 'other' ? 'selected' : ''; ?>>Other</option>
                                            </select>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label">Currency</label>
                                            <select name="currency" class="form-select">
                                                <?php
                                                // Get active currencies from database
                                                $currencies_result = $db->query("SELECT code, name, symbol FROM currencies WHERE status = 'active' ORDER BY code ASC");
                                                while ($curr = $currencies_result->fetch_assoc()) {
                                                    $selected = ($currency ?? 'USD') === $curr['code'] ? 'selected' : '';
                                                    echo "<option value=\"{$curr['code']}\" {$selected}>{$curr['code']} - {$curr['name']} ({$curr['symbol']})</option>";
                                                }
                                                ?>
                                            </select>
                                            <small class="form-text text-muted">
                                                <a href="currency-management.php" target="_blank">Manage Currencies</a>
                                            </small>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="mb-3">
                                    <label class="form-label">Address</label>
                                    <textarea name="address" class="form-control" rows="3"><?php echo htmlspecialchars($address ?? ''); ?></textarea>
                                </div>
                                
                                <div class="row">
                                    <div class="col-md-4">
                                        <div class="mb-3">
                                            <label class="form-label">Account Type</label>
                                            <select name="account_type" class="form-select">
                                                <option value="savings" <?php echo ($account_type ?? 'savings') === 'savings' ? 'selected' : ''; ?>>Savings</option>
                                                <option value="checking" <?php echo ($account_type ?? '') === 'checking' ? 'selected' : ''; ?>>Checking</option>
                                                <option value="business" <?php echo ($account_type ?? '') === 'business' ? 'selected' : ''; ?>>Business</option>
                                            </select>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="mb-3">
                                            <label class="form-label">Initial Balance</label>
                                            <div class="input-group">
                                                <span class="input-group-text">$</span>
                                                <input type="number" name="initial_balance" class="form-control" step="0.01" min="0" value="<?php echo htmlspecialchars($initial_balance ?? '0'); ?>">
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="mb-3">
                                            <label class="form-label">Registration Date</label>
                                            <input type="date" name="registration_date" class="form-control" value="<?php echo date('Y-m-d'); ?>">
                                            <small class="form-hint">Leave as today's date or select a different date</small>
                                        </div>
                                    </div>
                                </div>

                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label">Account Status</label>
                                            <select name="status" class="form-select">
                                                <option value="active" <?php echo ($status ?? 'active') === 'active' ? 'selected' : ''; ?>>Active</option>
                                                <option value="suspended" <?php echo ($status ?? '') === 'suspended' ? 'selected' : ''; ?>>Suspended</option>
                                            </select>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label">KYC Status</label>
                                            <select name="kyc_status" class="form-select">
                                                <option value="pending" <?php echo ($kyc_status ?? 'pending') === 'pending' ? 'selected' : ''; ?>>Pending</option>
                                                <option value="verified" <?php echo ($kyc_status ?? '') === 'verified' ? 'selected' : ''; ?>>Verified</option>
                                                <option value="rejected" <?php echo ($kyc_status ?? '') === 'rejected' ? 'selected' : ''; ?>>Rejected</option>
                                            </select>
                                        </div>
                                    </div>
                                </div>
                                

                                
                                <hr>
                                
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label required">Password</label>
                                            <input type="password" name="password" class="form-control" required>
                                            <small class="form-hint">Minimum 6 characters.</small>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label required">Confirm Password</label>
                                            <input type="password" name="confirm_password" class="form-control" required>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="form-footer">
                                    <div class="btn-list">
                                        <a href="users.php" class="btn">
                                            Cancel
                                        </a>
                                        <button type="submit" class="btn btn-primary" data-original-text="Create User">
                                            <svg xmlns="http://www.w3.org/2000/svg" class="icon" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
                                                <path stroke="none" d="M0 0h24v24H0z" fill="none"/>
                                                <line x1="12" y1="5" x2="12" y2="19"/>
                                                <line x1="5" y1="12" x2="19" y2="12"/>
                                            </svg>
                                            Create User
                                        </button>
                                    </div>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function generateAccountNumber() {
    const timestamp = Date.now().toString().slice(-6);
    const random = Math.floor(Math.random() * 9999).toString().padStart(4, '0');
    const accountNumber = '10' + timestamp + random;

    const accountInput = document.getElementById('account_number_display');
    if (accountInput) {
        accountInput.value = accountNumber;
    }

    return accountNumber;
}
</script>

<?php include 'includes/admin-footer.php'; ?>
