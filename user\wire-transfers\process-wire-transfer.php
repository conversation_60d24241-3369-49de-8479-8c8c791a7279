<?php
/**
 * Process Wire Transfer
 * Handles wire transfer processing with billing code and OTP verification
 */

// Start session and check authentication
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

if (!isset($_SESSION['user_id'])) {
    http_response_code(401);
    echo json_encode(['error' => 'Unauthorized']);
    exit();
}

// Enable error reporting for debugging
error_reporting(E_ALL);
ini_set('display_errors', 1); // Show errors for debugging
ini_set('log_errors', 1);

// Add detailed error logging function
function logWireTransferError($message, $data = null) {
    $log_message = "WIRE_TRANSFER_ERROR: " . $message;
    if ($data) {
        $log_message .= " | Data: " . json_encode($data);
    }
    error_log($log_message);
}

// Add detailed logging for debugging
function logWireTransferDebug($message) {
    error_log("WIRE_TRANSFER_DEBUG: " . $message);
}

// Include required files
require_once '../../config/config.php';
require_once '../../config/email.php';
require_once '../../config/email_templates.php';

/**
 * Generate PDF receipt for wire transfer
 */
function generateWireTransferPDFReceipt($transfer_id, $user_data, $transfer_data, $wire_transfer_data) {
    try {
        // Create temporary directory for PDFs if it doesn't exist
        $pdf_dir = __DIR__ . '/../../temp/receipts/';
        if (!is_dir($pdf_dir)) {
            mkdir($pdf_dir, 0755, true);
        }

        // Generate unique filename for text receipt
        $filename = 'wire_receipt_' . $transfer_id . '_' . time() . '.txt';
        $txt_path = $pdf_dir . $filename;

        // Generate text content for email attachment
        $txt_content = generateWireReceiptText($transfer_id, $user_data, $transfer_data, $wire_transfer_data);

        // Save as text file (more reliable for email attachments)
        file_put_contents($txt_path, $txt_content);

        // Log for debugging
        error_log("Wire transfer receipt generated: " . $txt_path);
        error_log("Text content length: " . strlen($txt_content));

        return $txt_path;

    } catch (Exception $e) {
        error_log("PDF generation error: " . $e->getMessage());
        return null;
    }
}

/**
 * Generate HTML content for wire transfer receipt
 */
function generateWireReceiptHTML($transfer_id, $user_data, $transfer_data, $wire_transfer_data) {
    $sender_name = $user_data['first_name'] . ' ' . $user_data['last_name'];
    $transfer_date = date('F j, Y g:i A');

    // Use the wire transfer data passed separately
    $wire_data = $wire_transfer_data;

    $html = "
    <!DOCTYPE html>
    <html>
    <head>
        <meta charset='UTF-8'>
        <title>Wire Transfer Receipt</title>
        <style>
            body { font-family: 'Courier New', monospace; margin: 20px; background: white; }
            .receipt-container { max-width: 800px; margin: 0 auto; border: 2px solid #000; }
            .header { background: #206bc4; color: white; padding: 20px; text-align: center; }
            .content { padding: 30px; }
            .section { margin-bottom: 25px; }
            .section-title { border-bottom: 1px solid #000; padding-bottom: 5px; margin-bottom: 10px; font-weight: bold; }
            .detail-row { display: flex; justify-content: space-between; padding: 3px 0; }
            .detail-label { width: 40%; }
            .detail-value { font-weight: bold; text-align: right; }
            .amount { font-size: 1.2em; color: #206bc4; }
        </style>
    </head>
    <body>
        <div class='receipt-container'>
            <div class='header'>
                <h1>WIRE TRANSFER RECEIPT</h1>
                <p>Reference: {$transfer_data['reference_number']}</p>
            </div>
            <div class='content'>
                <div class='section'>
                    <div class='section-title'>TRANSFER DETAILS</div>
                    <div class='detail-row'>
                        <span class='detail-label'>Amount:</span>
                        <span class='detail-value amount'>" . ($transfer_data['currency'] ?? 'USD') . " " . number_format($transfer_data['amount'] ?? 0, 2) . "</span>
                    </div>
                    <div class='detail-row'>
                        <span class='detail-label'>Date:</span>
                        <span class='detail-value'>{$transfer_date}</span>
                    </div>
                    <div class='detail-row'>
                        <span class='detail-label'>Status:</span>
                        <span class='detail-value'>" . ucfirst($transfer_data['status'] ?? 'pending') . "</span>
                    </div>
                    <div class='detail-row'>
                        <span class='detail-label'>Reference:</span>
                        <span class='detail-value'>" . ($transfer_data['reference_number'] ?? 'N/A') . "</span>
                    </div>
                </div>

                <div class='section'>
                    <div class='section-title'>FROM ACCOUNT</div>
                    <div class='detail-row'>
                        <span class='detail-label'>Account Holder:</span>
                        <span class='detail-value'>{$sender_name}</span>
                    </div>
                    <div class='detail-row'>
                        <span class='detail-label'>Account Number:</span>
                        <span class='detail-value'>****" . substr($user_data['account_number'], -4) . "</span>
                    </div>
                </div>

                <div class='section'>
                    <div class='section-title'>BENEFICIARY DETAILS</div>
                    <div class='detail-row'>
                        <span class='detail-label'>Name:</span>
                        <span class='detail-value'>" . ($wire_data['beneficiary_account_name'] ?? 'N/A') . "</span>
                    </div>
                    <div class='detail-row'>
                        <span class='detail-label'>Account:</span>
                        <span class='detail-value'>" . ($wire_data['beneficiary_account_number'] ?? 'N/A') . "</span>
                    </div>
                    <div class='detail-row'>
                        <span class='detail-label'>Bank:</span>
                        <span class='detail-value'>" . ($wire_data['bank_name'] ?? 'N/A') . "</span>
                    </div>
                    <div class='detail-row'>
                        <span class='detail-label'>SWIFT Code:</span>
                        <span class='detail-value'>" . ($wire_data['swift_code'] ?? 'N/A') . "</span>
                    </div>
                </div>

                <div class='section' style='text-align: center; margin-top: 40px; border-top: 1px solid #000; padding-top: 20px;'>
                    <p><strong>*** COMPUTER GENERATED RECEIPT ***</strong></p>
                    <p>This receipt is valid without signature</p>
                    <p>Generated on: " . date('F j, Y g:i A') . "</p>
                </div>
            </div>
        </div>
    </body>
    </html>";

    return $html;
}

/**
 * Generate text content for wire transfer receipt (for email attachment)
 */
function generateWireReceiptText($transfer_id, $user_data, $transfer_data, $wire_transfer_data) {
    $sender_name = $user_data['first_name'] . ' ' . $user_data['last_name'];
    $transfer_date = date('F j, Y g:i A');

    // Use the wire transfer data passed separately
    $wire_data = $wire_transfer_data;

    $text = "
================================================================================
                           WIRE TRANSFER RECEIPT
================================================================================

Reference Number: " . ($transfer_data['reference_number'] ?? 'N/A') . "
Generated: " . date('F j, Y g:i A') . "

================================================================================
                            TRANSFER DETAILS
================================================================================

Amount:           " . ($transfer_data['currency'] ?? 'USD') . " " . number_format($transfer_data['amount'] ?? 0, 2) . "
Date:             {$transfer_date}
Status:           " . ucfirst($transfer_data['status'] ?? 'pending') . "
Reference:        " . ($transfer_data['reference_number'] ?? 'N/A') . "

================================================================================
                            FROM ACCOUNT
================================================================================

Account Holder:   {$sender_name}
Account Number:   ****" . substr($user_data['account_number'], -4) . "

================================================================================
                          BENEFICIARY DETAILS
================================================================================

Name:             " . ($wire_data['beneficiary_account_name'] ?? 'N/A') . "
Account:          " . ($wire_data['beneficiary_account_number'] ?? 'N/A') . "
Bank:             " . ($wire_data['bank_name'] ?? 'N/A') . "
SWIFT Code:       " . ($wire_data['swift_code'] ?? 'N/A') . "
Country:          " . ($wire_data['beneficiary_country'] ?? $wire_data['bank_country'] ?? 'N/A') . "

================================================================================
                              IMPORTANT NOTICE
================================================================================

*** COMPUTER GENERATED RECEIPT ***
This receipt is valid without signature.

International wire transfers typically take 1-3 business days to process.
You will receive email updates when your transfer status changes.

================================================================================
";

    return $text;
}


// Set JSON response header and ensure clean output
header('Content-Type: application/json');
if (ob_get_level()) {
    ob_clean(); // Clear any previous output only if buffering is active
}

try {
    logWireTransferDebug("Starting wire transfer processing");
    logWireTransferError("=== STARTING WIRE TRANSFER PROCESSING ===");

    $db = getDB();
    $user_id = $_SESSION['user_id'];

    logWireTransferDebug("User ID: " . $user_id);
    logWireTransferError("Database connection successful, user_id: " . $user_id);

    // Get user information
    $user_query = "SELECT * FROM accounts WHERE id = ?";
    $user_result = $db->query($user_query, [$user_id]);
    $user = $user_result->fetch_assoc();

    if (!$user) {
        throw new Exception('User not found');
    }

    // Get and validate input
    $raw_input = file_get_contents('php://input');
    logWireTransferDebug("Raw input: " . $raw_input);

    $input = json_decode($raw_input, true);

    if (!$input) {
        logWireTransferDebug("JSON decode failed: " . json_last_error_msg());
        throw new Exception('Invalid request data');
    }

    logWireTransferDebug("Parsed input: " . json_encode($input));
    
    // Verify OTP using database (same as working transfer system)
    $entered_otp = sanitizeInput($input['otp_code'] ?? '');

    logWireTransferDebug("OTP Verification - Entered: " . $entered_otp);

    if (empty($entered_otp)) {
        logWireTransferError("OTP verification failed - missing OTP");
        throw new Exception('OTP is required');
    }

    // Check OTP in database
    $verify_otp_sql = "SELECT * FROM user_otps
                      WHERE user_id = ? AND otp_code = ?
                      AND (purpose = 'wire_transfer' OR source = 'wire_transfer')
                      AND expires_at > NOW()
                      AND (is_used = 0 OR used = 0)";
    $otp_result = $db->query($verify_otp_sql, [$user_id, $entered_otp]);

    if ($otp_result->num_rows === 0) {
        logWireTransferError("OTP verification failed - invalid or expired OTP: " . $entered_otp);
        throw new Exception('Invalid or expired verification code');
    }

    // Mark OTP as used
    $mark_otp_used_sql = "UPDATE user_otps SET is_used = 1, used = 1, used_at = NOW() WHERE user_id = ? AND otp_code = ?";
    $db->query($mark_otp_used_sql, [$user_id, $entered_otp]);

    logWireTransferDebug("OTP verification successful and marked as used");
    
    // Extract and validate transfer data
    $amount = floatval($input['amount'] ?? 0);
    $transfer_currency = sanitizeInput($input['transfer_currency'] ?? $user['currency']);
    $purpose_of_payment = sanitizeInput($input['purpose_of_payment'] ?? '');
    $narration = sanitizeInput($input['narration'] ?? 'Wire Transfer');
    $source_account = sanitizeInput($input['source_account'] ?? 'main_account');

    if ($amount <= 0) {
        throw new Exception('Invalid transfer amount');
    }

    // Validate funding source and check balance
    $funding_source = null;
    $available_balance = 0;

    if ($source_account === 'main_account') {
        $funding_source = 'main_account';
        $available_balance = $user['balance'];
    } elseif (strpos($source_account, 'virtual_card_') === 0) {
        $card_id = intval(str_replace('virtual_card_', '', $source_account));

        // Get virtual card details
        $card_query = "SELECT * FROM virtual_cards WHERE card_id = ? AND account_id = ? AND status = 'active'";
        $card_result = $db->query($card_query, [$card_id, $user_id]);

        if ($card_result->num_rows === 0) {
            throw new Exception('Selected virtual card not found or inactive');
        }

        $virtual_card = $card_result->fetch_assoc();
        $funding_source = 'virtual_card';
        $available_balance = $virtual_card['card_balance'];
    } else {
        throw new Exception('Invalid funding source selected');
    }

    if ($amount > $available_balance) {
        throw new Exception('Insufficient balance in selected funding source');
    }
    
    if (empty($purpose_of_payment)) {
        throw new Exception('Purpose of payment is required');
    }
    
    // Check if user has verified all required billing codes
    $billing_codes_query = "SELECT COUNT(*) as total_codes FROM user_billing_codes WHERE user_id = ? AND is_active = 1";
    $billing_codes_result = $db->query($billing_codes_query, [$user_id]);
    $total_billing_codes = $billing_codes_result->fetch_assoc()['total_codes'];
    
    $verified_codes = $_SESSION['verified_billing_codes'] ?? [];
    
    if ($total_billing_codes > 0 && count($verified_codes) < $total_billing_codes) {
        throw new Exception('All billing codes must be verified before processing');
    }
    
    // Start database transaction
    $db->beginTransaction();
    
    try {
        // Generate unique transfer reference
        $transfer_reference = 'WTX' . date('Ymd') . rand(100000, 999999);
        
        // Prepare wire transfer data
        $wire_transfer_data = [];
        $billing_verification_data = [];
        
        // Get all configured wire transfer fields dynamically from database
        $fields_sql = "SELECT field_name FROM wire_transfer_fields WHERE is_active = 1";
        $fields_result = $db->query($fields_sql, []);
        $configured_fields = [];
        while ($field_row = $fields_result->fetch_assoc()) {
            $configured_fields[] = $field_row['field_name'];
        }

        // Standard wire transfer fields (always include these)
        $standard_fields = [
            'beneficiary_account_number', 'beneficiary_account_name', 'beneficiary_address',
            'bank_name', 'bank_address', 'bank_city', 'bank_country',
            'swift_code', 'routing_code', 'iban'
        ];

        // Merge standard fields with configured custom fields
        $all_wire_fields = array_unique(array_merge($standard_fields, $configured_fields));

        // Collect all wire transfer specific fields
        foreach ($all_wire_fields as $field) {
            if (isset($input[$field]) && !empty($input[$field])) {
                $wire_transfer_data[$field] = sanitizeInput($input[$field]);
            }
        }
        
        // Prepare billing verification data
        if ($total_billing_codes > 0) {
            $billing_verification_data = [
                'total_codes_required' => $total_billing_codes,
                'codes_verified' => count($verified_codes),
                'verification_timestamp' => date('Y-m-d H:i:s'),
                'verified_positions' => array_keys($verified_codes)
            ];
        }
        
        // Insert transfer record with pending status
        $insert_query = "INSERT INTO transfers (
            sender_id, transfer_type, transaction_id, amount, currency,
            recipient_account, recipient_name, description, status,
            bank_name, swift_code, routing_code, iban, bank_address, bank_city, bank_country,
            beneficiary_address, purpose_of_payment, wire_transfer_data, processing_status,
            created_at
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW())";

        $transfer_description = "Wire Transfer to " . ($wire_transfer_data['beneficiary_account_name'] ?? 'International Beneficiary') .
                               " - " . ($wire_transfer_data['transfer_description'] ?? 'International Wire Transfer');

        $db->query($insert_query, [
            $user_id,
            'international',
            $transfer_reference,
            $amount,
            $transfer_currency,
            $wire_transfer_data['beneficiary_account_number'] ?? '',
            $wire_transfer_data['beneficiary_account_name'] ?? '',
            $transfer_description,
            'pending',
            // Wire transfer specific fields
            $wire_transfer_data['bank_name'] ?? '',
            $wire_transfer_data['swift_code'] ?? '',
            $wire_transfer_data['routing_code'] ?? '',
            $wire_transfer_data['iban'] ?? '',
            $wire_transfer_data['bank_address'] ?? '',
            $wire_transfer_data['bank_city'] ?? '',
            $wire_transfer_data['bank_country'] ?? '',
            $wire_transfer_data['beneficiary_address'] ?? '',
            $wire_transfer_data['purpose_of_payment'] ?? '',
            json_encode($wire_transfer_data),
            'pending'
        ]);
        
        $transfer_id = $db->lastInsertId();
        
        // Deduct amount from selected funding source
        if ($funding_source === 'main_account') {
            $new_balance = $user['balance'] - $amount;
            $update_balance_query = "UPDATE accounts SET balance = ? WHERE id = ?";
            $db->query($update_balance_query, [$new_balance, $user_id]);
            $balance_after = $new_balance;
        } elseif ($funding_source === 'virtual_card') {
            $new_card_balance = $virtual_card['card_balance'] - $amount;
            $update_card_query = "UPDATE virtual_cards SET card_balance = ?, updated_at = NOW() WHERE card_id = ?";
            $db->query($update_card_query, [$new_card_balance, $virtual_card['card_id']]);

            // Record virtual card transaction
            $card_transaction_query = "INSERT INTO virtual_card_transactions (
                card_id, account_id, transaction_type, amount, currency, description,
                reference_number, status, created_at, updated_at
            ) VALUES (?, ?, 'debit', ?, ?, ?, ?, 'completed', NOW(), NOW())";

            $db->query($card_transaction_query, [
                $virtual_card['card_id'],
                $user_id,
                $amount,
                $transfer_currency,
                "Wire Transfer - " . ($wire_transfer_data['beneficiary_account_name'] ?? 'International Transfer'),
                $transfer_reference
            ]);

            $balance_after = $new_card_balance;
        }
        
        // Record transaction in transactions table
        $transaction_query = "INSERT INTO transactions (
            user_id, transaction_type, amount, currency, description,
            reference_number, category, status, created_at
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, NOW())";

        $funding_description = $funding_source === 'virtual_card'
            ? "Wire Transfer from Virtual Card to " . ($wire_transfer_data['beneficiary_account_name'] ?? 'Beneficiary')
            : "Wire Transfer to " . ($wire_transfer_data['beneficiary_account_name'] ?? 'Beneficiary');

        $db->query($transaction_query, [
            $user_id,
            'debit',
            $amount,
            $transfer_currency,
            $funding_description,
            $transfer_reference,
            'transfer',
            'pending'
        ]);
        
        // Commit transaction
        $db->commit();
        
        // Clear OTP and billing verification from session
        unset($_SESSION['wire_transfer_otp']);
        unset($_SESSION['wire_transfer_otp_expires']);
        unset($_SESSION['wire_transfer_data']);
        unset($_SESSION['verified_billing_codes']);
        
        // Send confirmation email using template system
        try {
            // Prepare user data for email template
            $user_data = [
                'first_name' => $user['first_name'],
                'last_name' => $user['last_name'],
                'account_number' => $user['account_number'],
                'email' => $user['email']
            ];

            // Get the site domain for receipt URL
            $site_domain = $_SERVER['HTTP_HOST'] ?? 'localhost';
            $protocol = isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? 'https' : 'http';
            $base_url = $protocol . '://' . $site_domain;

            // Prepare transfer data for email template
            $transfer_data = [
                'reference_number' => $transfer_reference,
                'transfer_type' => 'international-wire',
                'amount' => $amount,
                'currency' => $transfer_currency,
                'fee' => 0, // Wire transfer fees can be added here if needed
                'recipient_name' => $wire_transfer_data['beneficiary_account_name'] ?? 'International Beneficiary',
                'recipient_account' => $wire_transfer_data['beneficiary_account_number'] ?? '',
                'bank_name' => $wire_transfer_data['bank_name'] ?? '',
                'description' => $wire_transfer_data['transfer_description'] ?? 'International Wire Transfer',
                'status' => 'pending',
                'created_at' => date('Y-m-d H:i:s'),
                'receipt_url' => $base_url . '/online_banking/user/wire-transfers/generate-wire-receipt.php?id=' . $transfer_id
            ];

            // Generate email using template
            $email_subject = 'Wire Transfer Confirmation - ' . $transfer_reference;
            $email_body = generateWireTransferReceiptEmailTemplate($user_data, $transfer_data);

            // Generate PDF receipt for attachment
            $pdf_attachment_path = generateWireTransferPDFReceipt($transfer_id, $user_data, $transfer_data, $wire_transfer_data);

            // Send email with receipt attachment
            if ($pdf_attachment_path && file_exists($pdf_attachment_path)) {
                sendEmailSMTPWithAttachment(
                    $user['email'],
                    $email_subject,
                    $email_body,
                    true,
                    $pdf_attachment_path,
                    'Wire_Transfer_Receipt_' . $transfer_reference . '.txt'
                );
                error_log("Wire transfer email sent with receipt attachment: " . $pdf_attachment_path);
            } else {
                // Fallback to email without attachment
                sendEmailSMTP($user['email'], $email_subject, $email_body);
                error_log("Wire transfer email sent without attachment (receipt generation failed)");
            }

        } catch (Exception $e) {
            error_log("Wire transfer confirmation email error: " . $e->getMessage());
        }
        
        echo json_encode([
            'success' => true,
            'message' => 'Wire transfer submitted successfully and is pending review',
            'transfer_reference' => $transfer_reference,
            'amount' => $amount,
            'currency' => $transfer_currency,
            'new_balance' => $balance_after,
            'status' => 'pending',
            'pdf_receipt_url' => 'generate-wire-receipt.php?id=' . $transfer_id,
            'transfer_data' => $wire_transfer_data
        ]);
        exit(); // Ensure no additional output
        
    } catch (Exception $e) {
        if (isset($db)) {
            $db->rollback();
        }
        logWireTransferError("Transaction rollback due to error: " . $e->getMessage());
        throw $e;
    }

} catch (Exception $e) {
    // Clean up any output that might have been started
    if (ob_get_level()) {
        ob_clean();
    }

    logWireTransferError("Process wire transfer error: " . $e->getMessage());
    logWireTransferDebug("Session data at error: " . json_encode([
        'wire_transfer_otp' => $_SESSION['wire_transfer_otp'] ?? 'not set',
        'wire_transfer_otp_expires' => $_SESSION['wire_transfer_otp_expires'] ?? 'not set'
    ]));

    error_log("Process wire transfer error: " . $e->getMessage());

    http_response_code(400);
    $response = [
        'success' => false,
        'error' => $e->getMessage()
    ];

    logWireTransferDebug("Sending error response: " . json_encode($response));
    echo json_encode($response);
}

exit(); // Ensure no additional output


