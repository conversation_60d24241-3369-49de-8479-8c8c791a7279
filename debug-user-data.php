<?php
/**
 * Debug User Data - Verify database information
 */

require_once 'config/database.php';

try {
    $db = getDB();
    
    // Check user data
    echo "=== USER DATA VERIFICATION ===\n";
    $user_result = $db->query("SELECT * FROM accounts WHERE username = 'jane_smith'");
    if ($user_result && $user_result->num_rows > 0) {
        $user = $user_result->fetch_assoc();
        echo "✅ User found: " . $user['first_name'] . " " . $user['last_name'] . "\n";
        echo "   Account ID: " . $user['id'] . "\n";
        echo "   Balance: $" . number_format($user['balance'], 2) . "\n";
        echo "   Status: " . $user['status'] . "\n";
        echo "   Email: " . $user['email'] . "\n";
        echo "   Account Number: " . $user['account_number'] . "\n";
        
        $user_id = $user['id'];
        
        // Check transactions
        echo "\n=== TRANSACTION DATA ===\n";
        $trans_result = $db->query("SELECT COUNT(*) as count, SUM(amount) as total FROM account_transactions WHERE account_id = $user_id");
        if ($trans_result) {
            $trans_data = $trans_result->fetch_assoc();
            echo "✅ Transactions found: " . $trans_data['count'] . "\n";
            echo "   Total amount: $" . number_format($trans_data['total'], 2) . "\n";
            
            // Get recent transactions
            $recent_trans = $db->query("SELECT * FROM account_transactions WHERE account_id = $user_id ORDER BY created_at DESC LIMIT 5");
            if ($recent_trans) {
                echo "   Recent transactions:\n";
                while ($trans = $recent_trans->fetch_assoc()) {
                    echo "   - " . $trans['transaction_type'] . ": $" . $trans['amount'] . " (" . $trans['status'] . ")\n";
                }
            }
        }
        
        // Check virtual cards
        echo "\n=== VIRTUAL CARDS DATA ===\n";
        $cards_result = $db->query("SELECT * FROM virtual_cards WHERE account_id = $user_id");
        if ($cards_result) {
            echo "✅ Virtual cards found: " . $cards_result->num_rows . "\n";
            if ($cards_result->num_rows > 0) {
                while ($card = $cards_result->fetch_assoc()) {
                    echo "   - Card: ****" . substr($card['card_number'], -4) . " (Balance: $" . $card['card_balance'] . ", Status: " . $card['status'] . ")\n";
                }
            }
        }
        
        // Check recent OTP
        echo "\n=== OTP DATA ===\n";
        $otp_result = $db->query("SELECT * FROM user_otps WHERE user_id = $user_id ORDER BY created_at DESC LIMIT 3");
        if ($otp_result && $otp_result->num_rows > 0) {
            echo "✅ Recent OTPs:\n";
            while ($otp = $otp_result->fetch_assoc()) {
                echo "   OTP: " . $otp['otp_code'] . " (Created: " . $otp['created_at'] . ", Expires: " . $otp['expires_at'] . ")\n";
            }
        } else {
            echo "❌ No OTP found\n";
        }
        
        // Test monthly stats query
        echo "\n=== MONTHLY STATS TEST ===\n";
        $current_month = date('Y-m');
        $monthly_stats_sql = "SELECT 
                                SUM(CASE WHEN transaction_type IN ('credit', 'transfer_in', 'deposit') THEN amount ELSE 0 END) as total_credits,
                                SUM(CASE WHEN transaction_type IN ('debit', 'transfer_out', 'withdrawal') THEN amount ELSE 0 END) as total_debits,
                                COUNT(*) as transaction_count,
                                AVG(amount) as avg_transaction
                              FROM account_transactions 
                              WHERE account_id = $user_id 
                              AND DATE_FORMAT(created_at, '%Y-%m') = '$current_month'
                              AND status = 'completed'";
        
        $stats_result = $db->query($monthly_stats_sql);
        if ($stats_result) {
            $monthly_stats = $stats_result->fetch_assoc();
            echo "✅ Monthly stats for $current_month:\n";
            echo "   Credits: $" . number_format($monthly_stats['total_credits'] ?? 0, 2) . "\n";
            echo "   Debits: $" . number_format($monthly_stats['total_debits'] ?? 0, 2) . "\n";
            echo "   Transaction count: " . ($monthly_stats['transaction_count'] ?? 0) . "\n";
            echo "   Average: $" . number_format($monthly_stats['avg_transaction'] ?? 0, 2) . "\n";
        }
        
    } else {
        echo "❌ User not found\n";
    }
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
    echo "Stack trace: " . $e->getTraceAsString() . "\n";
}
?>
