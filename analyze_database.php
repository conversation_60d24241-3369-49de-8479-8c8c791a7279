<?php
require_once 'config/config.php';

try {
    $db = getDB();
    
    echo "=== DATABASE ANALYSIS ===\n\n";
    
    // Check if billing code tables exist
    echo "1. CHECKING BILLING CODE TABLES:\n";
    $tables = ['billing_code_settings', 'user_billing_codes', 'wire_transfer_fields'];
    
    foreach ($tables as $table) {
        $result = $db->query("SHOW TABLES LIKE '$table'");
        if ($result->num_rows > 0) {
            echo "✅ $table - EXISTS\n";
            
            // Show table structure
            $structure = $db->query("DESCRIBE $table");
            echo "   Columns: ";
            $columns = [];
            while ($row = $structure->fetch_assoc()) {
                $columns[] = $row['Field'];
            }
            echo implode(', ', $columns) . "\n";
            
            // Show row count
            $count = $db->query("SELECT COUNT(*) as count FROM $table")->fetch_assoc();
            echo "   Rows: " . $count['count'] . "\n\n";
        } else {
            echo "❌ $table - MISSING\n\n";
        }
    }
    
    // Check transfers table for wire transfer columns
    echo "2. CHECKING TRANSFERS TABLE FOR WIRE TRANSFER COLUMNS:\n";
    $transfers_structure = $db->query("DESCRIBE transfers");
    $wire_columns = ['swift_code', 'routing_code', 'iban', 'bank_name', 'billing_codes_verified', 'processing_status'];
    $existing_columns = [];
    
    while ($row = $transfers_structure->fetch_assoc()) {
        $existing_columns[] = $row['Field'];
    }
    
    foreach ($wire_columns as $column) {
        if (in_array($column, $existing_columns)) {
            echo "✅ $column - EXISTS\n";
        } else {
            echo "❌ $column - MISSING\n";
        }
    }
    
    // Show sample billing code data
    echo "\n3. SAMPLE BILLING CODE DATA:\n";
    $billing_codes = $db->query("SELECT * FROM user_billing_codes LIMIT 5");
    if ($billing_codes->num_rows > 0) {
        while ($row = $billing_codes->fetch_assoc()) {
            echo "User {$row['user_id']}: Position {$row['billing_position']} - {$row['billing_name']} ({$row['billing_code']})\n";
        }
    } else {
        echo "No billing codes found\n";
    }
    
    // Show wire transfer fields
    echo "\n4. WIRE TRANSFER FIELDS:\n";
    $fields = $db->query("SELECT field_name, field_label, is_required, is_active FROM wire_transfer_fields ORDER BY display_order");
    if ($fields->num_rows > 0) {
        while ($row = $fields->fetch_assoc()) {
            $status = $row['is_active'] ? 'ACTIVE' : 'INACTIVE';
            $required = $row['is_required'] ? 'REQUIRED' : 'OPTIONAL';
            echo "{$row['field_name']}: {$row['field_label']} ($required, $status)\n";
        }
    } else {
        echo "No wire transfer fields found\n";
    }
    
} catch (Exception $e) {
    echo "ERROR: " . $e->getMessage() . "\n";
}
?>
