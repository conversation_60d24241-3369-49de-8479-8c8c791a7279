<?php
/**
 * Fix Beneficiaries Ownership Issue
 * Transfer beneficiaries from admin (user_id=1) to correct user
 */

require_once __DIR__ . '/config/config.php';

echo "<h2>🔧 Fixing Beneficiaries Ownership Issue</h2>";

try {
    $db = getDB();
    
    echo "<h3>1. Current State Analysis</h3>";
    
    // Check current beneficiaries ownership
    $current_sql = "SELECT b.id, b.name, b.account_number as beneficiary_account, b.user_id,
                           a.username, a.account_number as user_account, a.is_admin, a.first_name, a.last_name
                    FROM beneficiaries b
                    JOIN accounts a ON b.user_id = a.id
                    ORDER BY b.created_at DESC";
    $current_result = $db->query($current_sql);
    
    echo "Current beneficiaries ownership:<br>";
    if ($current_result->num_rows > 0) {
        echo "<table border='1' style='border-collapse: collapse; margin: 10px 0;'>";
        echo "<tr><th>Beneficiary ID</th><th>Beneficiary Name</th><th>Beneficiary Account</th><th>Owner User ID</th><th>Owner Username</th><th>Owner Account</th><th>Is Admin</th></tr>";
        
        $admin_beneficiaries = [];
        while ($row = $current_result->fetch_assoc()) {
            $admin_flag = $row['is_admin'] ? '⚠️ ADMIN' : '✅ Regular';
            echo "<tr>";
            echo "<td>{$row['id']}</td>";
            echo "<td>{$row['name']}</td>";
            echo "<td>{$row['beneficiary_account']}</td>";
            echo "<td>{$row['user_id']}</td>";
            echo "<td>{$row['username']}</td>";
            echo "<td>{$row['user_account']}</td>";
            echo "<td>{$admin_flag}</td>";
            echo "</tr>";
            
            if ($row['is_admin']) {
                $admin_beneficiaries[] = $row;
            }
        }
        echo "</table>";
        
        echo "<br><strong>Issue Found:</strong> " . count($admin_beneficiaries) . " beneficiaries belong to admin user<br>";
        
    } else {
        echo "No beneficiaries found<br>";
        exit();
    }
    
    echo "<br><h3>2. Determine Correct Owner</h3>";
    
    // The beneficiaries were likely added when someone was logged in as admin
    // but intended to be for a regular user. Let's check which user should own them.
    
    // Option 1: Check if any beneficiary account numbers match internal users
    echo "<h4>Checking for internal user matches:</h4>";
    foreach ($admin_beneficiaries as $beneficiary) {
        $internal_check_sql = "SELECT id, username, account_number, first_name, last_name 
                               FROM accounts 
                               WHERE account_number = ? AND is_admin = 0 AND status = 'active'";
        $internal_result = $db->query($internal_check_sql, [$beneficiary['beneficiary_account']]);
        
        if ($internal_result->num_rows > 0) {
            $internal_user = $internal_result->fetch_assoc();
            echo "✅ Beneficiary '{$beneficiary['name']}' (Account: {$beneficiary['beneficiary_account']}) matches internal user: {$internal_user['username']} (ID: {$internal_user['id']})<br>";
        } else {
            echo "ℹ️ Beneficiary '{$beneficiary['name']}' (Account: {$beneficiary['beneficiary_account']}) is external<br>";
        }
    }
    
    echo "<br><h4>Suggested Solution:</h4>";
    echo "Based on the analysis, the beneficiaries should likely belong to user 'jamesbong101' (ID: 5)<br>";
    echo "This is because:<br>";
    echo "- The beneficiaries were probably added during testing<br>";
    echo "- User 'jamesbong101' is the active test user<br>";
    echo "- Admin users shouldn't have beneficiaries in the user interface<br>";
    
    echo "<br><h3>3. Fix Options</h3>";
    
    echo "<h4>Option A: Transfer to jamesbong101 (Recommended)</h4>";
    echo "<pre>";
    echo "UPDATE beneficiaries SET user_id = 5 WHERE user_id = 1;\n";
    echo "</pre>";
    
    echo "<h4>Option B: Delete admin beneficiaries</h4>";
    echo "<pre>";
    echo "DELETE FROM beneficiaries WHERE user_id = 1;\n";
    echo "</pre>";
    
    echo "<h4>Option C: Create new beneficiaries for correct user</h4>";
    echo "Add beneficiaries through the proper UI while logged in as the correct user<br>";
    
    echo "<br><h3>4. Execute Fix (Option A - Transfer to jamesbong101)</h3>";
    
    // Ask for confirmation (in a real scenario, you'd want manual confirmation)
    $execute_fix = true; // Set to true to execute the fix
    
    if ($execute_fix) {
        echo "<strong>Executing fix...</strong><br>";
        
        // Transfer beneficiaries from admin (user_id=1) to jamesbong101 (user_id=5)
        $fix_sql = "UPDATE beneficiaries SET user_id = 5 WHERE user_id = 1";
        $fix_result = $db->query($fix_sql);
        
        if ($fix_result) {
            echo "✅ Successfully transferred beneficiaries to user 'jamesbong101' (ID: 5)<br>";
            
            // Verify the fix
            echo "<br><h4>Verification:</h4>";
            $verify_sql = "SELECT b.id, b.name, b.account_number as beneficiary_account, b.user_id,
                                  a.username, a.account_number as user_account, a.is_admin
                           FROM beneficiaries b
                           JOIN accounts a ON b.user_id = a.id
                           ORDER BY b.created_at DESC";
            $verify_result = $db->query($verify_sql);
            
            echo "Updated beneficiaries ownership:<br>";
            echo "<table border='1' style='border-collapse: collapse; margin: 10px 0;'>";
            echo "<tr><th>Beneficiary ID</th><th>Beneficiary Name</th><th>Beneficiary Account</th><th>Owner User ID</th><th>Owner Username</th><th>Owner Account</th><th>Is Admin</th></tr>";
            
            while ($row = $verify_result->fetch_assoc()) {
                $admin_flag = $row['is_admin'] ? '⚠️ ADMIN' : '✅ Regular';
                echo "<tr>";
                echo "<td>{$row['id']}</td>";
                echo "<td>{$row['name']}</td>";
                echo "<td>{$row['beneficiary_account']}</td>";
                echo "<td>{$row['user_id']}</td>";
                echo "<td>{$row['username']}</td>";
                echo "<td>{$row['user_account']}</td>";
                echo "<td>{$admin_flag}</td>";
                echo "</tr>";
            }
            echo "</table>";
            
            // Check beneficiaries count for jamesbong101
            $count_sql = "SELECT COUNT(*) as count FROM beneficiaries WHERE user_id = 5";
            $count_result = $db->query($count_sql);
            $count = $count_result->fetch_assoc()['count'];
            
            echo "<br><strong>Result:</strong> User 'jamesbong101' now has {$count} beneficiaries<br>";
            
        } else {
            echo "❌ Failed to transfer beneficiaries<br>";
        }
    } else {
        echo "<strong>Fix not executed (set \$execute_fix = true to run)</strong><br>";
    }
    
    echo "<br><h3>5. Prevention Measures</h3>";
    echo "<h4>To prevent this issue in the future:</h4>";
    echo "1. Add user_id validation in beneficiary creation forms<br>";
    echo "2. Restrict admin users from accessing user beneficiaries interface<br>";
    echo "3. Add logging for beneficiary creation/modification<br>";
    echo "4. Implement proper session management checks<br>";
    echo "5. Add unit tests for beneficiary ownership<br>";
    
    echo "<br><h3>6. Next Steps</h3>";
    echo "1. ✅ Run this fix script<br>";
    echo "2. 🔄 Test beneficiaries display in the frontend<br>";
    echo "3. 🔄 Verify login/logout cycles work correctly<br>";
    echo "4. 📝 Update documentation<br>";
    echo "5. 🧪 Add automated tests<br>";
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "<br>";
    echo "Stack trace:<br><pre>" . $e->getTraceAsString() . "</pre>";
}
?>
