<?php
/**
 * Admin Transfer Management Page
 * Comprehensive transfer management with editing capabilities
 */

// Enable error reporting for debugging
error_reporting(E_ALL);
ini_set('display_errors', 1);

require_once '../config/config.php';

// Check if user is logged in and is admin
if (!isset($_SESSION['user_id']) || !isset($_SESSION['is_admin']) || !$_SESSION['is_admin']) {
    redirect('../auth/login.php');
}

// Get database connection
$db = getDB();

// Handle form submissions
$success_message = '';
$error_message = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    try {
        if (isset($_POST['action'])) {
            switch ($_POST['action']) {
                case 'update_transfer_status':
                    $transfer_id = (int)$_POST['transfer_id'];
                    $new_status = sanitizeInput($_POST['status']);

                    // Validate status
                    $valid_statuses = ['pending', 'completed', 'failed', 'cancelled'];
                    if (!in_array($new_status, $valid_statuses)) {
                        throw new Exception('Invalid status selected');
                    }

                    // Update transfer status
                    $update_sql = "UPDATE transfers SET status = ? WHERE id = ?";
                    $db->query($update_sql, [$new_status, $transfer_id]);

                    $success_message = "Transfer status updated successfully";
                    break;

                case 'update_transfer_details':
                    $transfer_id = (int)$_POST['transfer_id'];
                    $amount = floatval($_POST['amount']);
                    $status = sanitizeInput($_POST['status']);
                    $recipient_name = sanitizeInput($_POST['recipient_name']);
                    $recipient_account = sanitizeInput($_POST['recipient_account']);
                    $description = sanitizeInput($_POST['description']);

                    // Validate amount
                    if ($amount <= 0) {
                        throw new Exception('Amount must be greater than 0');
                    }

                    // Validate status
                    $valid_statuses = ['pending', 'completed', 'failed', 'cancelled'];
                    if (!in_array($status, $valid_statuses)) {
                        throw new Exception('Invalid status provided');
                    }

                    // Update transfer details including status
                    $update_sql = "UPDATE transfers SET
                                   amount = ?, status = ?, recipient_name = ?, recipient_account = ?, description = ?
                                   WHERE id = ?";
                    $db->query($update_sql, [$amount, $status, $recipient_name, $recipient_account, $description, $transfer_id]);

                    $success_message = "Transfer details updated successfully";
                    break;

                case 'toggle_otp_setting':
                    $otp_enabled = isset($_POST['otp_enabled']) ? '1' : '0';

                    // Update or insert OTP setting
                    $setting_sql = "INSERT INTO admin_settings (setting_key, value, updated_at)
                                   VALUES ('transfer_otp_enabled', ?, NOW())
                                   ON DUPLICATE KEY UPDATE value = VALUES(value), updated_at = NOW()";
                    $db->query($setting_sql, [$otp_enabled]);

                    $success_message = "OTP setting updated successfully";
                    break;
            }
        }
    } catch (Exception $e) {
        $error_message = $e->getMessage();
    }
}

// Get transfer statistics (excluding wire transfers)
$stats_sql = "SELECT
                COUNT(*) as total_transfers,
                COUNT(CASE WHEN status = 'completed' THEN 1 END) as completed_transfers,
                COUNT(CASE WHEN status = 'pending' THEN 1 END) as pending_transfers,
                COUNT(CASE WHEN status = 'failed' THEN 1 END) as failed_transfers,
                COALESCE(SUM(CASE WHEN status = 'completed' THEN amount ELSE 0 END), 0) as total_amount,
                COUNT(CASE WHEN transfer_type = 'inter-bank' THEN 1 END) as inter_bank_count,
                COUNT(CASE WHEN transfer_type = 'local-bank' THEN 1 END) as local_bank_count
              FROM transfers WHERE transfer_type != 'wire-international'";
$stats_result = $db->query($stats_sql);
$stats = $stats_result->fetch_assoc();

// Get current OTP setting
$otp_setting_sql = "SELECT value FROM admin_settings WHERE setting_key = 'transfer_otp_enabled'";
$otp_setting_result = $db->query($otp_setting_sql);
$otp_enabled = true; // Default to enabled
if ($otp_setting_result && $otp_setting_result->num_rows > 0) {
    $setting = $otp_setting_result->fetch_assoc();
    $otp_enabled = ($setting['value'] === '1');
}

// Pagination setup
$page = isset($_GET['page']) ? max(1, intval($_GET['page'])) : 1;
$per_page = 20;
$offset = ($page - 1) * $per_page;

// Filter setup
$status_filter = sanitizeInput($_GET['status'] ?? '');
$type_filter = sanitizeInput($_GET['type'] ?? '');
$search_query = sanitizeInput($_GET['search'] ?? '');

// Build WHERE clause (exclude wire transfers)
$where_conditions = ["t.transfer_type != 'wire-international'"];
$params = [];

if (!empty($status_filter)) {
    $where_conditions[] = "t.status = ?";
    $params[] = $status_filter;
}

if (!empty($type_filter)) {
    $where_conditions[] = "t.transfer_type = ?";
    $params[] = $type_filter;
}

if (!empty($search_query)) {
    $where_conditions[] = "(t.transaction_id LIKE ? OR t.recipient_name LIKE ? OR u.first_name LIKE ? OR u.last_name LIKE ?)";
    $search_param = "%{$search_query}%";
    $params = array_merge($params, [$search_param, $search_param, $search_param, $search_param]);
}

$where_clause = !empty($where_conditions) ? "WHERE " . implode(" AND ", $where_conditions) : "";

$page_title = 'Transfer Management';

// Define page actions
$page_actions = [
    [
        'url' => 'index.php',
        'label' => 'Back to Dashboard',
        'icon' => 'fas fa-arrow-left'
    ]
];

include 'includes/admin-header.php';
?>

<!-- Breadcrumb -->
<nav aria-label="breadcrumb" style="margin-bottom: 1rem;">
    <ol class="breadcrumb">
        <li class="breadcrumb-item"><a href="index.php">Admin</a></li>
        <li class="breadcrumb-item active" aria-current="page">Transfer Management</li>
    </ol>
</nav>

<!-- Wire Transfer Notice -->
<div class="alert alert-info mb-4">
    <div class="d-flex align-items-center">
        <i class="fas fa-info-circle me-2"></i>
        <div class="flex-grow-1">
            <strong>Note:</strong> This page manages Inter-Bank and Local Bank transfers only.
            Wire transfers (international) are managed separately for better organization and specialized features.
        </div>
        <a href="wire-transfers.php" class="btn btn-outline-primary btn-sm">
            <i class="fas fa-globe me-1"></i>Manage Wire Transfers
        </a>
    </div>
</div>

<!-- Success/Error Messages -->
<?php if (!empty($success_message)): ?>
<div class="alert alert-success alert-dismissible fade show" role="alert">
    <i class="fas fa-check-circle me-2"></i><?php echo htmlspecialchars($success_message); ?>
    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
</div>
<?php endif; ?>

<?php if (!empty($error_message)): ?>
<div class="alert alert-danger alert-dismissible fade show" role="alert">
    <i class="fas fa-exclamation-circle me-2"></i><?php echo htmlspecialchars($error_message); ?>
    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
</div>
<?php endif; ?>

<!-- Statistics Cards -->
<div class="row row-cards mb-4">
    <div class="col-sm-6 col-lg-3">
        <div class="card card-sm">
            <div class="card-body">
                <div class="row align-items-center">
                    <div class="col-auto">
                        <span class="bg-primary text-white avatar">
                            <i class="fas fa-paper-plane"></i>
                        </span>
                    </div>
                    <div class="col">
                        <div class="font-weight-medium"><?php echo number_format($stats['total_transfers']); ?></div>
                        <div class="text-muted">Total Transfers</div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-sm-6 col-lg-3">
        <div class="card card-sm">
            <div class="card-body">
                <div class="row align-items-center">
                    <div class="col-auto">
                        <span class="bg-success text-white avatar">
                            <i class="fas fa-check-circle"></i>
                        </span>
                    </div>
                    <div class="col">
                        <div class="font-weight-medium"><?php echo number_format($stats['completed_transfers']); ?></div>
                        <div class="text-muted">Completed</div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-sm-6 col-lg-3">
        <div class="card card-sm">
            <div class="card-body">
                <div class="row align-items-center">
                    <div class="col-auto">
                        <span class="bg-warning text-white avatar">
                            <i class="fas fa-clock"></i>
                        </span>
                    </div>
                    <div class="col">
                        <div class="font-weight-medium"><?php echo number_format($stats['pending_transfers']); ?></div>
                        <div class="text-muted">Pending</div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-sm-6 col-lg-3">
        <div class="card card-sm">
            <div class="card-body">
                <div class="row align-items-center">
                    <div class="col-auto">
                        <span class="bg-info text-white avatar">
                            <i class="fas fa-dollar-sign"></i>
                        </span>
                    </div>
                    <div class="col">
                        <div class="font-weight-medium"><?php echo formatCurrency($stats['total_amount'], 'USD'); ?></div>
                        <div class="text-muted">Total Amount</div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Settings Card -->
<div class="row row-cards mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h3 class="card-title">
                    <i class="fas fa-cog me-2"></i>Transfer Settings
                </h3>
            </div>
            <div class="card-body">
                <form method="POST" class="row">
                    <input type="hidden" name="action" value="toggle_otp_setting">
                    <div class="col-md-6">
                        <div class="form-check form-switch">
                            <input class="form-check-input" type="checkbox" name="otp_enabled" id="otp_enabled"
                                   <?php echo $otp_enabled ? 'checked' : ''; ?>>
                            <label class="form-check-label" for="otp_enabled">
                                <strong>Require OTP for Local Bank Transfers</strong>
                            </label>
                            <div class="form-text">
                                When enabled, users must verify OTP via email for local bank transfers.
                                Inter-bank transfers are always processed instantly without OTP.
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save me-2"></i>Update Global OTP Setting
                        </button>
                        <a href="user-otp-settings.php" class="btn btn-outline-info ms-2">
                            <i class="fas fa-users-cog me-2"></i>Per-User OTP Settings
                        </a>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- Transfers Table -->
<div class="row row-cards">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h3 class="card-title">
                    <i class="fas fa-list me-2"></i>All Transfers
                </h3>
                <div class="card-actions">
                    <!-- Filters -->
                    <form method="GET" class="d-flex gap-2">
                        <select name="status" class="form-select form-select-sm" onchange="this.form.submit()">
                            <option value="">All Status</option>
                            <option value="pending" <?php echo $status_filter === 'pending' ? 'selected' : ''; ?>>Pending</option>
                            <option value="completed" <?php echo $status_filter === 'completed' ? 'selected' : ''; ?>>Completed</option>
                            <option value="failed" <?php echo $status_filter === 'failed' ? 'selected' : ''; ?>>Failed</option>
                            <option value="cancelled" <?php echo $status_filter === 'cancelled' ? 'selected' : ''; ?>>Cancelled</option>

                        </select>

                        <select name="type" class="form-select form-select-sm" onchange="this.form.submit()">
                            <option value="">All Types</option>
                            <option value="inter-bank" <?php echo $type_filter === 'inter-bank' ? 'selected' : ''; ?>>Inter-Bank</option>
                            <option value="local-bank" <?php echo $type_filter === 'local-bank' ? 'selected' : ''; ?>>Local Bank</option>
                        </select>

                        <input type="text" name="search" class="form-control form-control-sm"
                               placeholder="Search..." value="<?php echo htmlspecialchars($search_query); ?>">
                        <button type="submit" class="btn btn-sm btn-primary">
                            <i class="fas fa-search"></i>
                        </button>
                        <?php if (!empty($status_filter) || !empty($type_filter) || !empty($search_query)): ?>
                        <a href="transfers.php" class="btn btn-sm btn-outline-secondary">
                            <i class="fas fa-times"></i>
                        </a>
                        <?php endif; ?>
                    </form>
                </div>
            </div>

            <?php
            // Get transfers with pagination
            $transfers_sql = "SELECT t.*,
                                     u.first_name, u.last_name, u.email, u.account_number as sender_account,
                                     r.first_name as recipient_first_name, r.last_name as recipient_last_name
                              FROM transfers t
                              LEFT JOIN accounts u ON t.sender_id = u.id
                              LEFT JOIN accounts r ON t.recipient_id = r.id
                              {$where_clause}
                              ORDER BY t.created_at DESC
                              LIMIT {$per_page} OFFSET {$offset}";

            $transfers_result = $db->query($transfers_sql, $params);
            $transfers = [];
            while ($row = $transfers_result->fetch_assoc()) {
                $transfers[] = $row;
            }

            // Get total count for pagination
            $count_sql = "SELECT COUNT(*) as total FROM transfers t LEFT JOIN accounts u ON t.sender_id = u.id {$where_clause}";
            $count_result = $db->query($count_sql, $params);
            $total_transfers = $count_result->fetch_assoc()['total'];
            $total_pages = ceil($total_transfers / $per_page);
            ?>

            <?php if (!empty($transfers)): ?>
            <div class="table-responsive">
                <table class="table table-vcenter card-table">
                    <thead>
                        <tr>
                            <th>#</th>
                            <th>Reference</th>
                            <th>Sender</th>
                            <th>Recipient</th>
                            <th>Type</th>
                            <th>Amount</th>
                            <th>Status</th>
                            <th>Date</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php
                        $row_number = ($page - 1) * $per_page + 1;
                        foreach ($transfers as $transfer):
                        ?>
                        <tr>
                            <td>
                                <div class="text-muted"><?php echo $row_number++; ?></div>
                            </td>
                            <td>
                                <div class="text-muted"><?php echo htmlspecialchars($transfer['transaction_id']); ?></div>
                            </td>
                            <td>
                                <div class="d-flex py-1 align-items-center">
                                    <div class="avatar avatar-sm me-2" style="background-image: url(https://ui-avatars.com/api/?name=<?php echo urlencode($transfer['first_name'] . ' ' . $transfer['last_name']); ?>&background=random)"></div>
                                    <div class="flex-fill">
                                        <div class="font-weight-medium"><?php echo htmlspecialchars($transfer['first_name'] . ' ' . $transfer['last_name']); ?></div>
                                        <div class="text-muted"><small><?php echo htmlspecialchars($transfer['sender_account']); ?></small></div>
                                    </div>
                                </div>
                            </td>
                            <td>
                                <div class="font-weight-medium"><?php echo htmlspecialchars($transfer['recipient_name'] ?? 'Unknown'); ?></div>
                                <div class="text-muted"><small>****<?php echo $transfer['recipient_account'] ? substr($transfer['recipient_account'], -4) : '****'; ?></small></div>
                                <?php if (!empty($transfer['recipient_first_name']) && !empty($transfer['recipient_last_name'])): ?>
                                <div class="text-muted"><small><?php echo htmlspecialchars($transfer['recipient_first_name'] . ' ' . $transfer['recipient_last_name']); ?></small></div>
                                <?php endif; ?>
                            </td>
                            <td>
                                <?php
                                $type_badges = [
                                    'local' => '<span class="badge bg-info">Local Bank</span>',
                                    'international' => '<span class="badge bg-warning">International</span>',
                                    'bitcoin' => '<span class="badge bg-dark">Bitcoin</span>',
                                    'inter-bank' => '<span class="badge bg-primary">Inter-Bank</span>',
                                    'local-bank' => '<span class="badge bg-info">Local Bank</span>'
                                ];
                                echo $type_badges[$transfer['transfer_type']] ?? '<span class="badge bg-secondary">Unknown</span>';
                                ?>
                            </td>
                            <td>
                                <div class="font-weight-medium"><?php echo formatCurrency($transfer['amount'], $transfer['currency']); ?></div>
                                <?php if (isset($transfer['fee']) && $transfer['fee'] > 0): ?>
                                <div class="text-muted"><small>Fee: <?php echo formatCurrency($transfer['fee'], $transfer['currency']); ?></small></div>
                                <?php endif; ?>
                            </td>
                            <td>
                                <?php
                                $status_badges = [
                                    'completed' => '<span class="badge bg-success">Completed</span>',
                                    'pending' => '<span class="badge bg-warning">Pending</span>',
                                    'failed' => '<span class="badge bg-danger">Failed</span>',
                                    'cancelled' => '<span class="badge bg-secondary">Cancelled</span>'
                                ];
                                echo $status_badges[$transfer['status']] ?? '<span class="badge bg-secondary">Unknown</span>';
                                ?>
                            </td>
                            <td>
                                <div class="text-muted"><?php echo formatDate($transfer['created_at'], 'M j, Y'); ?></div>
                                <div class="text-muted"><small><?php echo formatDate($transfer['created_at'], 'g:i A'); ?></small></div>
                            </td>
                            <td>
                                <div class="btn-group" role="group">
                                    <button type="button" class="btn btn-sm btn-outline-primary"
                                            data-transfer='<?php echo json_encode($transfer, JSON_HEX_APOS | JSON_HEX_QUOT); ?>'
                                            onclick="editTransferFromData(this)">
                                        <i class="fas fa-edit"></i>
                                    </button>
                                    <button type="button" class="btn btn-sm btn-outline-info"
                                            data-transfer='<?php echo json_encode($transfer, JSON_HEX_APOS | JSON_HEX_QUOT); ?>'
                                            onclick="viewTransferDetailsFromData(this)">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                </div>
                            </td>
                        </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>

            <!-- Pagination -->
            <?php if ($total_pages > 1): ?>
            <div class="card-footer d-flex align-items-center">
                <p class="m-0 text-muted">
                    Showing <?php echo $offset + 1; ?> to <?php echo min($offset + $per_page, $total_transfers); ?>
                    of <?php echo $total_transfers; ?> transfers
                </p>
                <ul class="pagination m-0 ms-auto">
                    <?php if ($page > 1): ?>
                    <li class="page-item">
                        <a class="page-link" href="?page=<?php echo $page - 1; ?>&status=<?php echo $status_filter; ?>&type=<?php echo $type_filter; ?>&search=<?php echo urlencode($search_query); ?>">
                            <i class="fas fa-chevron-left"></i> prev
                        </a>
                    </li>
                    <?php endif; ?>

                    <?php for ($i = max(1, $page - 2); $i <= min($total_pages, $page + 2); $i++): ?>
                    <li class="page-item <?php echo $i === $page ? 'active' : ''; ?>">
                        <a class="page-link" href="?page=<?php echo $i; ?>&status=<?php echo $status_filter; ?>&type=<?php echo $type_filter; ?>&search=<?php echo urlencode($search_query); ?>">
                            <?php echo $i; ?>
                        </a>
                    </li>
                    <?php endfor; ?>

                    <?php if ($page < $total_pages): ?>
                    <li class="page-item">
                        <a class="page-link" href="?page=<?php echo $page + 1; ?>&status=<?php echo $status_filter; ?>&type=<?php echo $type_filter; ?>&search=<?php echo urlencode($search_query); ?>">
                            next <i class="fas fa-chevron-right"></i>
                        </a>
                    </li>
                    <?php endif; ?>
                </ul>
            </div>
            <?php endif; ?>

            <?php else: ?>
            <div class="card-body">
                <div class="empty">
                    <div class="empty-img">
                        <i class="fas fa-paper-plane" style="font-size: 3rem; color: #6c757d;"></i>
                    </div>
                    <p class="empty-title">No transfers found</p>
                    <p class="empty-subtitle text-muted">
                        <?php if (!empty($status_filter) || !empty($type_filter) || !empty($search_query)): ?>
                        No transfers match your current filters. Try adjusting your search criteria.
                        <?php else: ?>
                        No transfers have been made yet. Transfers will appear here once users start making transactions.
                        <?php endif; ?>
                    </p>
                    <?php if (!empty($status_filter) || !empty($type_filter) || !empty($search_query)): ?>
                    <div class="empty-action">
                        <a href="transfers.php" class="btn btn-primary">
                            <i class="fas fa-times me-2"></i>Clear Filters
                        </a>
                    </div>
                    <?php endif; ?>
                </div>
            </div>
            <?php endif; ?>
        </div>
    </div>
</div>

<!-- Edit Transfer Modal -->
<div class="modal fade" id="editTransferModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-edit me-2"></i>Edit Transfer
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form method="POST" id="editTransferForm">
                <input type="hidden" name="action" value="update_transfer_details">
                <input type="hidden" name="transfer_id" id="editTransferId">
                <div class="modal-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">Reference Number</label>
                                <input type="text" class="form-control" id="editReferenceNumber" readonly>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">Transfer Type</label>
                                <input type="text" class="form-control" id="editTransferType" readonly>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">Amount <span class="text-danger">*</span></label>
                                <input type="number" name="amount" class="form-control" step="0.01" min="1"
                                       id="editAmount" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">Status</label>
                                <select name="status" class="form-control" id="editStatus">
                                    <option value="pending">Pending</option>
                                    <option value="completed">Completed</option>
                                    <option value="failed">Failed</option>
                                    <option value="cancelled">Cancelled</option>
                                </select>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">Recipient Name <span class="text-danger">*</span></label>
                                <input type="text" name="recipient_name" class="form-control"
                                       id="editRecipientName" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">Recipient Account <span class="text-danger">*</span></label>
                                <input type="text" name="recipient_account" class="form-control"
                                       id="editRecipientAccount" required>
                            </div>
                        </div>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">Description/Purpose</label>
                        <textarea name="description" class="form-control" rows="3" id="editDescription"></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save me-2"></i>Update Transfer
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- View Transfer Details Modal -->
<div class="modal fade" id="viewTransferModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-eye me-2"></i>Transfer Details
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body" id="transferDetailsContent">
                <!-- Content will be populated by JavaScript -->
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                <button type="button" class="btn btn-primary" onclick="printTransferDetails()">
                    <i class="fas fa-print me-2"></i>Print Details
                </button>
            </div>
        </div>
    </div>
</div>

<script>
// Simple working functions
function editTransferFromData(button) {
    try {
        var transfer = JSON.parse(button.getAttribute('data-transfer'));
        editTransfer(transfer);
    } catch (e) {
        alert('Error loading transfer data');
    }
}

function viewTransferDetailsFromData(button) {
    try {
        var transfer = JSON.parse(button.getAttribute('data-transfer'));
        viewTransferDetails(transfer);
    } catch (e) {
        alert('Error loading transfer data');
    }
}

function editTransfer(transfer) {
    try {
        document.getElementById('editTransferId').value = transfer.id || '';
        document.getElementById('editReferenceNumber').value = transfer.transaction_id || '';
        document.getElementById('editTransferType').value = transfer.transfer_type || '';
        document.getElementById('editAmount').value = transfer.amount || '';
        document.getElementById('editStatus').value = transfer.status || '';
        document.getElementById('editRecipientName').value = transfer.recipient_name || '';
        document.getElementById('editRecipientAccount').value = transfer.recipient_account || '';
        document.getElementById('editDescription').value = transfer.description || '';

        var modal = new bootstrap.Modal(document.getElementById('editTransferModal'));
        modal.show();
    } catch (e) {
        alert('Error opening edit modal');
    }
}

function viewTransferDetails(transfer) {
    try {
        var content = '<div class="row"><div class="col-12"><h6>Transfer Information</h6><table class="table table-sm">';
        content += '<tr><td><strong>Reference:</strong></td><td>' + (transfer.transaction_id || 'N/A') + '</td></tr>';
        content += '<tr><td><strong>Type:</strong></td><td>' + (transfer.transfer_type || 'N/A') + '</td></tr>';
        content += '<tr><td><strong>Status:</strong></td><td><span class="badge bg-success">' + (transfer.status || 'N/A') + '</span></td></tr>';
        content += '<tr><td><strong>Amount:</strong></td><td>$' + parseFloat(transfer.amount || 0).toFixed(2) + '</td></tr>';
        content += '<tr><td><strong>Sender:</strong></td><td>' + (transfer.first_name || '') + ' ' + (transfer.last_name || '') + '</td></tr>';
        content += '<tr><td><strong>Recipient:</strong></td><td>' + (transfer.recipient_name || 'Unknown') + '</td></tr>';
        content += '</table></div></div>';

        document.getElementById('transferDetailsContent').innerHTML = content;
        var modal = new bootstrap.Modal(document.getElementById('viewTransferModal'));
        modal.show();
    } catch (e) {
        alert('Error opening view modal');
    }
}

function printTransferDetails() {
    try {
        var content = document.getElementById('transferDetailsContent').innerHTML;
        var printWindow = window.open('', '_blank');
        printWindow.document.write('<html><head><title>Transfer Details</title></head><body><h3>Transfer Details</h3>' + content + '</body></html>');
        printWindow.document.close();
        printWindow.print();
    } catch (e) {
        alert('Error printing transfer details');
    }
}
</script>

<?php include 'includes/admin-footer.php'; ?>
