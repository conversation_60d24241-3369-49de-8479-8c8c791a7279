<?php
/**
 * User Virtual Cards & Crypto Section Component
 * Displays virtual cards and crypto accounts side by side
 */

if (!isset($virtual_cards)) {
    $virtual_cards = [];
}
if (!isset($crypto_accounts)) {
    $crypto_accounts = [];
}
?>

<!-- Virtual Cards & Crypto Accounts -->
<div class="row row-cards mt-4">
    <!-- Virtual Cards -->
    <div class="col-lg-6">
        <div class="card">
            <div class="card-header">
                <h3 class="card-title">
                    <i class="fas fa-credit-card me-2"></i>
                    Virtual Cards
                </h3>
                <div class="card-actions">
                    <span class="badge bg-primary"><?php echo count($virtual_cards); ?></span>
                </div>
            </div>
            <div class="card-body virtual-cards-section">
                <?php if (!empty($virtual_cards)): ?>
                    <?php foreach ($virtual_cards as $card): ?>
                    <div class="col-12 mb-3">
                        <!-- Credit Card Design -->
                        <div class="credit-card <?php echo strtolower($card['card_type'] ?? 'visa'); ?>-card">
                            <div class="d-flex justify-content-between align-items-start">
                                <div class="card-logo">
                                    <span class="<?php echo strtolower($card['card_type'] ?? 'visa'); ?>-logo">
                                        <?php echo strtoupper($card['card_type'] ?? 'VISA'); ?>
                                    </span>
                                </div>
                                <div class="card-status">
                                    <span class="badge bg-<?php echo $card['status'] === 'active' ? 'success' : ($card['status'] === 'blocked' ? 'danger' : 'warning'); ?>">
                                        <?php echo ucfirst($card['status']); ?>
                                    </span>
                                </div>
                            </div>
                            
                            <div class="card-number">
                                <?php echo chunk_split($card['card_number'], 4, ' '); ?>
                            </div>
                            
                            <div class="d-flex justify-content-between align-items-end">
                                <div>
                                    <div class="card-label">Card Holder</div>
                                    <div class="card-holder-name">
                                        <?php echo htmlspecialchars($card['card_holder_name']); ?>
                                    </div>
                                </div>
                                <div>
                                    <div class="card-label">Expires</div>
                                    <div class="card-expiry-date">
                                        <?php echo sprintf('%02d/%02d', $card['expiry_month'], $card['expiry_year']); ?>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="card-balance-section">
                                <div class="row">
                                    <div class="col-6">
                                        <div class="card-label">Balance</div>
                                        <div class="fw-bold">
                                            <?php echo formatCurrency($card['current_balance'] ?? 0, 'USD'); ?>
                                        </div>
                                    </div>
                                    <div class="col-6">
                                        <div class="card-label">Limit</div>
                                        <div class="fw-bold">
                                            <?php echo formatCurrency($card['spending_limit'] ?? 0, 'USD'); ?>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <?php endforeach; ?>
                <?php else: ?>
                <div class="text-center text-muted py-4">
                    <i class="fas fa-credit-card mb-3" style="font-size: 3rem; opacity: 0.3;"></i>
                    <h4>No Virtual Cards</h4>
                    <p>This user has no virtual cards yet.</p>
                </div>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <!-- Crypto Accounts -->
    <div class="col-lg-6">
        <div class="card">
            <div class="card-header">
                <h3 class="card-title">
                    <i class="fab fa-bitcoin me-2"></i>
                    Cryptocurrency Wallets
                </h3>
                <div class="card-actions">
                    <span class="badge bg-warning"><?php echo count($crypto_accounts); ?></span>
                </div>
            </div>
            <div class="card-body crypto-section">
                <?php if (!empty($crypto_accounts)): ?>
                <div class="list-group list-group-flush">
                    <?php foreach ($crypto_accounts as $crypto): ?>
                    <div class="list-group-item d-flex justify-content-between align-items-start">
                        <div class="ms-2 me-auto">
                            <div class="fw-bold d-flex align-items-center">
                                <i class="fab fa-<?php echo strtolower($crypto['cryptocurrency'] ?? $crypto['crypto_type'] ?? 'bitcoin'); ?> me-2"></i>
                                <?php echo strtoupper($crypto['cryptocurrency'] ?? $crypto['crypto_type'] ?? 'BTC'); ?>
                            </div>
                            <div class="text-muted">
                                Balance: <strong><?php echo number_format($crypto['balance'] ?? 0, 8); ?></strong>
                            </div>
                            <?php if (!empty($crypto['wallet_address'])): ?>
                            <small class="font-monospace text-muted" title="Click to copy" 
                                   onclick="copyToClipboard('<?php echo htmlspecialchars($crypto['wallet_address']); ?>', this)">
                                <?php echo substr($crypto['wallet_address'], 0, 20) . '...'; ?>
                            </small>
                            <?php endif; ?>
                        </div>
                        <div class="text-end">
                            <div class="fw-bold text-success">
                                <?php echo formatCurrency($crypto['usd_equivalent'] ?? 0, 'USD'); ?>
                            </div>
                            <span class="badge bg-<?php echo ($crypto['status'] ?? 'active') === 'active' ? 'success' : 'danger'; ?>-lt">
                                <?php echo ucfirst($crypto['status'] ?? 'active'); ?>
                            </span>
                        </div>
                    </div>
                    <?php endforeach; ?>
                </div>
                <?php else: ?>
                <div class="text-center text-muted py-4">
                    <i class="fab fa-bitcoin mb-3" style="font-size: 3rem; opacity: 0.3;"></i>
                    <h4>No Crypto Wallets</h4>
                    <p>This user has no cryptocurrency wallets yet.</p>
                </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>
