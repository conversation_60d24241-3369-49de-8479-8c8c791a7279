-- Create Super Admin 2FA Settings Table
-- This table stores Google Authenticator settings specifically for super admin accounts

CREATE TABLE IF NOT EXISTS `super_admin_2fa_settings` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `super_admin_username` varchar(50) NOT NULL,
  `google_2fa_enabled` tinyint(1) DEFAULT 0,
  `google_2fa_secret` varchar(255) DEFAULT NULL,
  `backup_codes` text DEFAULT NULL,
  `last_used_timestamp` int(11) DEFAULT NULL,
  `failed_attempts` int(11) DEFAULT 0,
  `locked_until` datetime DEFAULT NULL,
  `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `unique_super_admin` (`super_admin_username`),
  KEY `idx_enabled` (`google_2fa_enabled`),
  KEY `idx_locked_until` (`locked_until`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Create Super Admin 2FA Audit Log
CREATE TABLE IF NOT EXISTS `super_admin_2fa_audit` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `super_admin_username` varchar(50) NOT NULL,
  `action` varchar(100) NOT NULL,
  `details` text DEFAULT NULL,
  `ip_address` varchar(45) DEFAULT NULL,
  `user_agent` text DEFAULT NULL,
  `success` tinyint(1) DEFAULT 0,
  `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_username` (`super_admin_username`),
  KEY `idx_action` (`action`),
  KEY `idx_created_at` (`created_at`),
  KEY `idx_success` (`success`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Insert default 2FA settings for super admin
INSERT INTO `super_admin_2fa_settings` (`super_admin_username`, `google_2fa_enabled`) 
VALUES ('superadmin', 0) 
ON DUPLICATE KEY UPDATE `super_admin_username` = VALUES(`super_admin_username`);

-- Add 2FA related settings to super_admin_settings table
INSERT INTO `super_admin_settings` (`setting_key`, `setting_value`, `setting_description`, `setting_type`) VALUES
('super_admin_2fa_required', '0', 'Require Google Authenticator for super admin login', 'boolean'),
('super_admin_2fa_backup_codes_count', '10', 'Number of backup codes to generate', 'number'),
('super_admin_2fa_max_attempts', '5', 'Maximum failed 2FA attempts before lockout', 'number'),
('super_admin_2fa_lockout_duration', '30', 'Lockout duration in minutes after max failed attempts', 'number')
ON DUPLICATE KEY UPDATE 
`setting_value` = VALUES(`setting_value`),
`setting_description` = VALUES(`setting_description`);
