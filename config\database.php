<?php
/**
 * Database Configuration for Online Banking System
 * Using MySQLi for shared hosting compatibility
 */

// Database configuration
define('DB_HOST', 'localhost');
define('DB_USERNAME', 'root');
define('DB_PASSWORD', 'root');
define('DB_NAME', 'online_banking');
define('DB_CHARSET', 'utf8mb4');

class Database {
    private $connection;
    private static $instance = null;
    
    private function __construct() {
        $this->connect();
    }
    
    public static function getInstance() {
        if (self::$instance === null) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    private function connect() {
        try {
            $this->connection = new mysqli(DB_HOST, DB_USERNAME, DB_PASSWORD, DB_NAME);
            
            if ($this->connection->connect_error) {
                throw new Exception("Connection failed: " . $this->connection->connect_error);
            }
            
            // Set charset
            $this->connection->set_charset(DB_CHARSET);
            
        } catch (Exception $e) {
            error_log("Database connection error: " . $e->getMessage());
            die("Database connection failed. Please try again later.");
        }
    }
    
    public function getConnection() {
        return $this->connection;
    }
    
    public function query($sql, $params = []) {
        try {
            if (empty($params)) {
                $result = $this->connection->query($sql);
                if ($result === false) {
                    throw new Exception("Query failed: " . $this->connection->error);
                }
                return $result;
            }
            
            $stmt = $this->connection->prepare($sql);
            if ($stmt === false) {
                throw new Exception("Prepare failed: " . $this->connection->error);
            }
            
            if (!empty($params)) {
                $types = '';
                foreach ($params as $param) {
                    if (is_int($param)) {
                        $types .= 'i';
                    } elseif (is_float($param)) {
                        $types .= 'd';
                    } else {
                        $types .= 's';
                    }
                }
                $stmt->bind_param($types, ...$params);
            }
            
            $stmt->execute();

            // For SELECT statements, get the result set
            // For INSERT, UPDATE, DELETE statements, get_result() is not needed
            if (stripos(trim($sql), 'SELECT') === 0) {
                $result = $stmt->get_result();
                $stmt->close();
                return $result;
            } else {
                // For non-SELECT statements, just return true on success
                $stmt->close();
                return true;
            }
            
        } catch (Exception $e) {
            error_log("Database query error: " . $e->getMessage());
            throw $e;
        }
    }
    
    public function insert($sql, $params = []) {
        try {
            $this->query($sql, $params);
            return $this->connection->insert_id;
        } catch (Exception $e) {
            error_log("Database insert error: " . $e->getMessage());
            throw $e;
        }
    }

    public function lastInsertId() {
        return $this->connection->insert_id;
    }
    
    public function update($sql, $params = []) {
        try {
            $this->query($sql, $params);
            return $this->connection->affected_rows;
        } catch (Exception $e) {
            error_log("Database update error: " . $e->getMessage());
            throw $e;
        }
    }
    
    public function delete($sql, $params = []) {
        try {
            $this->query($sql, $params);
            return $this->connection->affected_rows;
        } catch (Exception $e) {
            error_log("Database delete error: " . $e->getMessage());
            throw $e;
        }
    }
    
    public function escape($string) {
        return $this->connection->real_escape_string($string);
    }
    
    public function beginTransaction() {
        $this->connection->autocommit(false);
    }
    
    public function commit() {
        $this->connection->commit();
        $this->connection->autocommit(true);
    }
    
    public function rollback() {
        $this->connection->rollback();
        $this->connection->autocommit(true);
    }
    
    public function __destruct() {
        if ($this->connection) {
            $this->connection->close();
        }
    }
}

// Global database instance
function getDB() {
    return Database::getInstance();
}
?>
