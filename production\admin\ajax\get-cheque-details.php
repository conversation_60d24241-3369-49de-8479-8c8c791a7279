<?php
require_once '../../config/config.php';
requireAdmin();

header('Content-Type: application/json');

try {
    $cheque_id = intval($_GET['id'] ?? 0);
    
    if ($cheque_id <= 0) {
        throw new Exception('Invalid cheque ID');
    }
    
    $db = getDB();
    
    // Get cheque details with user information
    $query = "SELECT cd.*, 
              a.first_name, a.last_name, a.username, a.account_number,
              admin.first_name as admin_first_name, admin.last_name as admin_last_name
              FROM cheque_deposits cd 
              LEFT JOIN accounts a ON cd.account_id = a.id 
              LEFT JOIN accounts admin ON cd.created_by = admin.id 
              WHERE cd.id = ?";
    
    $result = $db->query($query, [$cheque_id]);
    
    if (!$result || $result->num_rows === 0) {
        throw new Exception('Cheque not found');
    }
    
    $cheque = $result->fetch_assoc();
    
    // Log the view action
    error_log("Cheque details viewed: Cheque ID {$cheque_id}, Admin: {$_SESSION['user_id']}");
    
    echo json_encode([
        'success' => true,
        'cheque' => $cheque
    ]);
    
} catch (Exception $e) {
    error_log("Cheque details error: " . $e->getMessage());
    
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ]);
}
?>
