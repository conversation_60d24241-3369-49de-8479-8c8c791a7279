<?php
/**
 * Working Email Test - Simple test that should work
 */

// Enable error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h1>🏦 Working Email Test</h1>";
echo "<p>Testing email functionality step by step...</p>";

// Include email config
require_once 'config/email.php';

echo "<h2>✅ Email Configuration Loaded</h2>";
echo "SMTP Host: " . SMTP_HOST . "<br>";
echo "SMTP Port: " . SMTP_PORT . "<br>";
echo "From Email: " . FROM_EMAIL . "<br>";
echo "From Name: " . FROM_NAME . "<br>";

// Load PHPMailer manually
echo "<h2>📧 Loading PHPMailer</h2>";
try {
    require_once 'vendor/phpmailer/phpmailer/src/Exception.php';
    require_once 'vendor/phpmailer/phpmailer/src/SMTP.php';
    require_once 'vendor/phpmailer/phpmailer/src/PHPMailer.php';
    echo "✅ PHPMailer loaded successfully<br>";
    
    // Create PHPMailer instance
    $mail = new PHPMailer\PHPMailer\PHPMailer(true);
    echo "✅ PHPMailer instance created<br>";
    
    // Configure SMTP
    $mail->isSMTP();
    $mail->Host = SMTP_HOST;
    $mail->SMTPAuth = true;
    $mail->Username = SMTP_USERNAME;
    $mail->Password = SMTP_PASSWORD;
    $mail->SMTPSecure = PHPMailer\PHPMailer\PHPMailer::ENCRYPTION_SMTPS; // SSL for port 465
    $mail->Port = SMTP_PORT;
    $mail->SMTPDebug = 0;
    
    echo "✅ SMTP configured<br>";
    
    // Set email details
    $test_email = '<EMAIL>';
    $mail->setFrom(FROM_EMAIL, FROM_NAME);
    $mail->addAddress($test_email, 'Demo Developer');
    $mail->isHTML(true);
    $mail->Subject = '🏦 Working Email Test - ' . date('H:i:s');
    $mail->Body = '
    <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px; background: #f5f5f5;">
        <div style="background: white; padding: 30px; border-radius: 10px; box-shadow: 0 4px 6px rgba(0,0,0,0.1);">
            <div style="background: linear-gradient(135deg, #28a745 0%, #20c997 100%); color: white; padding: 20px; text-align: center; border-radius: 8px; margin-bottom: 20px;">
                <h1 style="margin: 0;">🎉 Email Test Successful!</h1>
                <p style="margin: 10px 0 0 0;">Banking system email is working perfectly!</p>
            </div>
            
            <h2 style="color: #2c3e50;">📋 Test Details</h2>
            <div style="background: #f8f9fa; padding: 15px; border-radius: 5px; border-left: 4px solid #28a745;">
                <ul style="margin: 0; padding-left: 20px;">
                    <li><strong>SMTP Host:</strong> ' . SMTP_HOST . '</li>
                    <li><strong>SMTP Port:</strong> ' . SMTP_PORT . ' (SSL)</li>
                    <li><strong>Test Time:</strong> ' . date('Y-m-d H:i:s') . '</li>
                    <li><strong>Recipient:</strong> ' . $test_email . '</li>
                    <li><strong>From:</strong> ' . FROM_EMAIL . '</li>
                </ul>
            </div>
            
            <h2 style="color: #2c3e50;">🚀 What This Means</h2>
            <div style="background: #d1ecf1; padding: 15px; border-radius: 5px; border-left: 4px solid #17a2b8;">
                <ul style="margin: 0; padding-left: 20px; color: #0c5460;">
                    <li>✅ <strong>SMTP Connection:</strong> Working perfectly</li>
                    <li>✅ <strong>Email Delivery:</strong> Functional</li>
                    <li>✅ <strong>Banking System:</strong> Ready for user creation</li>
                    <li>✅ <strong>Welcome Emails:</strong> Will be sent automatically</li>
                    <li>✅ <strong>OTP Emails:</strong> Will work for login verification</li>
                </ul>
            </div>
            
            <div style="text-align: center; margin-top: 20px;">
                <p style="color: #28a745; font-weight: bold; font-size: 18px;">🎯 Banking System Email Ready!</p>
            </div>
        </div>
    </div>';
    
    echo "<h2>📤 Sending Email</h2>";
    echo "Sending to: <strong>$test_email</strong><br>";
    
    $result = $mail->send();
    
    if ($result) {
        echo "<div style='background: #d4edda; padding: 20px; border-radius: 5px; margin: 20px 0; color: #155724; border: 1px solid #c3e6cb;'>";
        echo "<h2>🎉 EMAIL SENT SUCCESSFULLY!</h2>";
        echo "<p>✅ Email delivered to: <strong>$test_email</strong></p>";
        echo "<p>✅ Subject: Working Email Test</p>";
        echo "<p>✅ Time: " . date('Y-m-d H:i:s') . "</p>";
        echo "<p><strong>Check your <NAME_EMAIL>!</strong></p>";
        echo "</div>";
        
        echo "<h2>🏦 Now Test User Creation</h2>";
        echo "<div style='background: #e7f3ff; padding: 20px; border-radius: 5px; margin: 20px 0; color: #0c5460; border: 1px solid #b3d9ff;'>";
        echo "<p>Since email is working, you can now:</p>";
        echo "<ol>";
        echo "<li>Go to Admin Panel → Add User</li>";
        echo "<li>Create <NAME_EMAIL></li>";
        echo "<li>Welcome email will be sent automatically</li>";
        echo "<li>User can login and receive OTP emails</li>";
        echo "</ol>";
        echo "<p><a href='admin/add-user.php' style='background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>🚀 Go to Add User</a></p>";
        echo "</div>";
        
    } else {
        echo "<div style='background: #f8d7da; padding: 20px; border-radius: 5px; margin: 20px 0; color: #721c24; border: 1px solid #f5c6cb;'>";
        echo "<h2>❌ EMAIL SENDING FAILED</h2>";
        echo "<p>There was an issue sending the email.</p>";
        echo "</div>";
    }
    
} catch (Exception $e) {
    echo "<div style='background: #f8d7da; padding: 20px; border-radius: 5px; margin: 20px 0; color: #721c24; border: 1px solid #f5c6cb;'>";
    echo "<h2>❌ ERROR</h2>";
    echo "<p>Error: " . htmlspecialchars($e->getMessage()) . "</p>";
    echo "</div>";
}

echo "<hr>";
echo "<p><strong>Test completed at:</strong> " . date('Y-m-d H:i:s') . "</p>";
?>
