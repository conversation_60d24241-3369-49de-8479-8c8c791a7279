[{"id": "TC001", "title": "User Registration with OTP Verification", "description": "Verify that a new user can successfully register and verify their account using OTP/2FA.", "category": "functional", "priority": "High", "steps": [{"type": "action", "description": "Navigate to the registration page"}, {"type": "action", "description": "Fill in valid registration details (username, email, password)"}, {"type": "action", "description": "Submit the registration form"}, {"type": "assertion", "description": "Check that an OTP is sent to the user's email or phone"}, {"type": "action", "description": "Input the correct OTP received"}, {"type": "assertion", "description": "Verify account registration completes successfully and user is redirected to login"}, {"type": "action", "description": "Attempt registration with an invalid or expired OTP"}, {"type": "assertion", "description": "Verify that registration fails with appropriate error message"}]}, {"id": "TC002", "title": "User Login with 2FA", "description": "Check user can log in using valid credentials and complete 2FA verification, and session is established securely.", "category": "security", "priority": "High", "steps": [{"type": "action", "description": "Navigate to the login page"}, {"type": "action", "description": "Enter valid username/email and password"}, {"type": "action", "description": "Click the login button"}, {"type": "assertion", "description": "Verify prompt for 2FA/OTP input appears"}, {"type": "action", "description": "Input valid 2FA code"}, {"type": "assertion", "description": "Verify successful login and redirection to user dashboard"}, {"type": "action", "description": "Attempt login with invalid password"}, {"type": "assertion", "description": "Verify login failure with error message"}, {"type": "action", "description": "Attempt 2FA with invalid or expired OTP"}, {"type": "assertion", "description": "Verify login is blocked and appropriate error is shown"}]}, {"id": "TC003", "title": "Password Reset Flow", "description": "Verify that users can request a password reset, receive an OTP, and successfully reset their password.", "category": "functional", "priority": "High", "steps": [{"type": "action", "description": "Navigate to forgot password page"}, {"type": "action", "description": "Submit registered email for password reset"}, {"type": "assertion", "description": "Verify OTP is sent to the registered email"}, {"type": "action", "description": "Input received OTP and new password"}, {"type": "assertion", "description": "Verify password reset success message is shown"}, {"type": "action", "description": "Attempt login with new password"}, {"type": "assertion", "description": "Verify login is successful with the new password"}, {"type": "action", "description": "Attempt password reset with invalid OTP"}, {"type": "assertion", "description": "Verify error message is shown and password is not changed"}]}, {"id": "TC004", "title": "Dashboard Displays Correct Financial Data", "description": "Verify that the user dashboard shows accurate and up-to-date account balances, transactions, and financial statistics.", "category": "functional", "priority": "High", "steps": [{"type": "action", "description": "Log in as a valid user"}, {"type": "assertion", "description": "Verify dashboard loads successfully"}, {"type": "assertion", "description": "Check that all linked accounts and balances are displayed correctly"}, {"type": "assertion", "description": "Verify recent transactions shown match the database records"}, {"type": "assertion", "description": "Confirm monthly spending statistics and graphs are correct"}, {"type": "action", "description": "Simulate account balance update or new transaction"}, {"type": "assertion", "description": "Verify dashboard updates reflect the latest data without errors"}]}, {"id": "TC005", "title": "Sidebar Navigation Functionality", "description": "Verify that the sidebar navigation links operate correctly and allow access to all banking services and sections.", "category": "ui", "priority": "Medium", "steps": [{"type": "action", "description": "Log in and view user dashboard"}, {"type": "action", "description": "Click on each sidebar item: accounts, transactions, transfers, cards, payments, analytics, support"}, {"type": "assertion", "description": "Confirm navigation to corresponding pages without errors"}, {"type": "assertion", "description": "Check that active sidebar item is visually highlighted"}, {"type": "action", "description": "Resize browser window to mobile viewport"}, {"type": "assertion", "description": "Confirm sidebar adapts correctly for responsive design"}]}, {"id": "TC006", "title": "Account Management - View and Update Details", "description": "Verify users can view their account details including balance and status, and updates reflect correctly.", "category": "functional", "priority": "High", "steps": [{"type": "action", "description": "Log in and navigate to accounts section"}, {"type": "assertion", "description": "Verify all accounts linked to the user are displayed with correct balances and status"}, {"type": "action", "description": "Attempt to update an editable account field (e.g. account nickname)"}, {"type": "assertion", "description": "Verify update is successfully saved and displayed"}, {"type": "action", "description": "Attempt invalid updates (e.g., invalid format or blank)"}, {"type": "assertion", "description": "Verify validation errors are shown and data not saved"}]}, {"id": "TC007", "title": "Transaction Management with Search and Filter", "description": "Verify transaction history loading, accurate display, and working search/filter functionality.", "category": "functional", "priority": "High", "steps": [{"type": "action", "description": "Navigate to transaction history page"}, {"type": "assertion", "description": "Verify all recent transactions display correctly with details"}, {"type": "action", "description": "Use search bar to find transactions by description, amount, or date"}, {"type": "assertion", "description": "Verify search results match the criteria"}, {"type": "action", "description": "Apply filters such as date range, transaction type, or status"}, {"type": "assertion", "description": "Verify filtered transactions display correctly"}, {"type": "action", "description": "Attempt invalid filter criteria"}, {"type": "assertion", "description": "Verify system handles gracefully and shows appropriate feedback or empty result"}]}, {"id": "TC008", "title": "Money Transfer – Beneficiary Management and Transfer", "description": "Verify internal/external money transfer including adding beneficiaries, executing transfers, and history tracking.", "category": "functional", "priority": "High", "steps": [{"type": "action", "description": "Log in and navigate to money transfer section"}, {"type": "action", "description": "Add a new beneficiary with valid details"}, {"type": "assertion", "description": "Verify beneficiary appears in beneficiary list"}, {"type": "action", "description": "Attempt to add beneficiary with incomplete or invalid details"}, {"type": "assertion", "description": "Verify validation errors are shown"}, {"type": "action", "description": "Perform a transfer to a valid beneficiary with sufficient balance"}, {"type": "assertion", "description": "Verify transfer succeeds and balance is updated"}, {"type": "action", "description": "Perform a transfer with insufficient balance"}, {"type": "assertion", "description": "Verify transfer is blocked with an appropriate error"}, {"type": "action", "description": "View transfer history"}, {"type": "assertion", "description": "Verify recent transfers are listed accurately"}]}, {"id": "TC009", "title": "Virtual Card Creation and Management", "description": "Verify user can create virtual cards, manage card details, view status, and check balances.", "category": "functional", "priority": "High", "steps": [{"type": "action", "description": "Navigate to virtual cards management"}, {"type": "action", "description": "Create a new virtual card with valid inputs"}, {"type": "assertion", "description": "Verify new card appears in the card list with correct details and active status"}, {"type": "action", "description": "View the details of a virtual card"}, {"type": "assertion", "description": "Confirm card information and balance are displayed correctly"}, {"type": "action", "description": "Attempt to create a card with invalid parameters"}, {"type": "assertion", "description": "Verify error messages are displayed and card is not created"}, {"type": "action", "description": "Deactivate an active virtual card"}, {"type": "assertion", "description": "Verify card status updates and card becomes unusable"}]}, {"id": "TC010", "title": "Bill Payment Processing and History", "description": "Verify users can initiate bill payments, set up recurring payments, and review payment history.", "category": "functional", "priority": "High", "steps": [{"type": "action", "description": "Navigate to bill payment section"}, {"type": "action", "description": "Add a new biller and enter payment details"}, {"type": "action", "description": "Make a one-time bill payment"}, {"type": "assertion", "description": "Verify payment succeeds and is reflected in payment history"}, {"type": "action", "description": "Set up a recurring payment for a biller"}, {"type": "assertion", "description": "Verify recurring payment is scheduled correctly"}, {"type": "action", "description": "Navigate to bill payment history"}, {"type": "assertion", "description": "Verify all past payments are listed with correct statuses and details"}, {"type": "action", "description": "Attempt bill payment with insufficient funds"}, {"type": "assertion", "description": "Verify payment is rejected with appropriate error message"}]}, {"id": "TC011", "title": "User Profile Editing and Security Settings", "description": "Verify users can update personal information, change password, and configure security settings including 2FA.", "category": "functional", "priority": "Medium", "steps": [{"type": "action", "description": "Navigate to profile management page"}, {"type": "action", "description": "Update personal information fields (name, contact details)"}, {"type": "assertion", "description": "Verify updates are saved and displayed correctly"}, {"type": "action", "description": "Change user password with correct current password"}, {"type": "assertion", "description": "Verify password change success and user can login with new password"}, {"type": "action", "description": "Attempt password change with incorrect current password"}, {"type": "assertion", "description": "Verify error message and password not changed"}, {"type": "action", "description": "Enable and disable 2FA in security settings"}, {"type": "assertion", "description": "Verify system prompts for configuration and reflects new 2FA status"}]}, {"id": "TC012", "title": "Email Notification System - OTP and Alerts", "description": "Verify that the system sends email notifications correctly for OTP and alerts using SMTP configuration.", "category": "functional", "priority": "High", "steps": [{"type": "action", "description": "Trigger OTP generation via registration or password reset"}, {"type": "assertion", "description": "Verify OTP email is received with correct content and valid OTP"}, {"type": "action", "description": "Trigger alert notification email (e.g., transaction alert)"}, {"type": "assertion", "description": "Verify alert email is received promptly and contains accurate information"}, {"type": "action", "description": "Test with invalid SMTP configuration"}, {"type": "assertion", "description": "Verify system logs an appropriate error and fails gracefully without crashing"}]}, {"id": "TC013", "title": "Database Connectivity and Query Stability", "description": "Verify the database connection handling and query execution are stable and performant under expected load.", "category": "functional", "priority": "High", "steps": [{"type": "action", "description": "Start application and perform various user operations (login, transactions, updates)"}, {"type": "assertion", "description": "Verify no database connection errors occur during typical usage"}, {"type": "action", "description": "Simulate transient database connection failure"}, {"type": "assertion", "description": "Verify system retries connection gracefully or shows appropriate error messages without data loss"}, {"type": "action", "description": "Execute complex queries like transaction searches and analytics reports"}, {"type": "assertion", "description": "Verify queries complete within acceptable time and data returned is accurate"}]}, {"id": "TC014", "title": "Responsive Design Verification", "description": "Verify that the web interface is responsive and functions correctly across desktop, tablet, and mobile devices.", "category": "ui", "priority": "Medium", "steps": [{"type": "action", "description": "Open the user dashboard on desktop browser"}, {"type": "assertion", "description": "Verify layout is correctly rendered and all UI components are displayed properly"}, {"type": "action", "description": "Resize browser window to tablet and mobile viewport sizes"}, {"type": "assertion", "description": "Verify UI adjusts layout and navigation correctly, including sidebar collapsing"}, {"type": "action", "description": "Perform key operations (login, money transfer, payments) on different device viewports"}, {"type": "assertion", "description": "Verify all operations complete successfully without UI or functional issues"}]}, {"id": "TC015", "title": "Security - Enforce Password and 2FA Policies", "description": "Verify that password complexity requirements and 2FA configurations are correctly enforced and mandatory upon policy.", "category": "security", "priority": "High", "steps": [{"type": "action", "description": "Attempt to set password not meeting complexity requirements"}, {"type": "assertion", "description": "Verify password is rejected with clear validation messages"}, {"type": "action", "description": "Attempt login without completing mandatory 2FA if enabled"}, {"type": "assertion", "description": "Verify login is denied until 2FA is completed"}, {"type": "action", "description": "Attempt to disable 2FA if policy mandates it"}, {"type": "assertion", "description": "Verify system prevents disabling 2FA and shows informative message"}]}, {"id": "TC016", "title": "Production Deployment Compliance", "description": "Verify that the production deployment package excludes test, debug, and documentation files per guidelines.", "category": "functional", "priority": "Medium", "steps": [{"type": "action", "description": "Build the production deployment package"}, {"type": "assertion", "description": "Check that no test scripts, debug logs, or documentation files are included in production build"}, {"type": "assertion", "description": "Verify deployment configuration matches specified web server and database guidelines"}]}, {"id": "TC017", "title": "User Roles and Mandatory Password Changes", "description": "Verify that user roles are enforced and newly created user accounts require mandatory password change on first login.", "category": "security", "priority": "High", "steps": [{"type": "action", "description": "Create a new user with a temporary password"}, {"type": "action", "description": "Log in with the new user credentials"}, {"type": "assertion", "description": "Verify that system forces the user to change password before accessing dashboard"}, {"type": "action", "description": "Attempt to bypass password change screen"}, {"type": "assertion", "description": "Verify access is denied until password change is completed"}, {"type": "action", "description": "Verify that role based permissions restrict or grant access to different system functions appropriately"}]}, {"id": "TC018", "title": "Dynamic CSS and Theme Customization", "description": "Verify that dynamic CSS system correctly applies user selected themes and centralized CSS color management.", "category": "ui", "priority": "Low", "steps": [{"type": "action", "description": "Select different theme colors or appearance settings from user settings"}, {"type": "assertion", "description": "Verify that CSS changes are applied dynamically without page reload errors"}, {"type": "action", "description": "Verify that color schemes are consistent across dashboard and other pages"}]}, {"id": "TC019", "title": "Analytics and Reports Accuracy", "description": "Verify that spending insights, financial reports, and transaction analytics show accurate and consistent data.", "category": "functional", "priority": "Medium", "steps": [{"type": "action", "description": "Navigate to analytics and reports section"}, {"type": "assertion", "description": "Verify displayed spending insights match user transaction data"}, {"type": "action", "description": "Generate financial reports for defined date ranges"}, {"type": "assertion", "description": "Verify reports complete successfully and data is accurate and complete"}, {"type": "action", "description": "Export reports if supported"}, {"type": "assertion", "description": "Verify exported files are correctly formatted and contain expected data"}]}]