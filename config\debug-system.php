<?php
/**
 * Comprehensive Debug and Error Reporting System
 * Provides detailed error reporting, logging, and debugging information
 */

// Enable all error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
ini_set('log_errors', 1);
ini_set('error_log', __DIR__ . '/../logs/php_errors.log');

// Create logs directory if it doesn't exist
$log_dir = __DIR__ . '/../logs';
if (!is_dir($log_dir)) {
    mkdir($log_dir, 0755, true);
}

/**
 * Custom error handler
 */
function customErrorHandler($errno, $errstr, $errfile, $errline) {
    $error_types = [
        E_ERROR => 'FATAL ERROR',
        E_WARNING => 'WARNING',
        E_PARSE => 'PARSE ERROR',
        E_NOTICE => 'NOTICE',
        E_CORE_ERROR => 'CORE ERROR',
        E_CORE_WARNING => 'CORE WARNING',
        E_COMPILE_ERROR => 'COMPILE ERROR',
        E_COMPILE_WARNING => 'COMPILE WARNING',
        E_USER_ERROR => 'USER ERROR',
        E_USER_WARNING => 'USER WARNING',
        E_USER_NOTICE => 'USER NOTICE',
        E_STRICT => 'STRICT NOTICE',
        E_RECOVERABLE_ERROR => 'RECOVERABLE ERROR',
        E_DEPRECATED => 'DEPRECATED',
        E_USER_DEPRECATED => 'USER DEPRECATED'
    ];
    
    $error_type = $error_types[$errno] ?? 'UNKNOWN ERROR';
    $timestamp = date('Y-m-d H:i:s');
    
    // Log to file
    $log_message = "[{$timestamp}] {$error_type}: {$errstr} in {$errfile} on line {$errline}\n";
    file_put_contents(__DIR__ . '/../logs/php_errors.log', $log_message, FILE_APPEND | LOCK_EX);
    
    // Display error if not fatal
    if ($errno !== E_ERROR && $errno !== E_CORE_ERROR && $errno !== E_COMPILE_ERROR) {
        displayError($error_type, $errstr, $errfile, $errline);
    }
    
    return true;
}

/**
 * Custom exception handler
 */
function customExceptionHandler($exception) {
    $timestamp = date('Y-m-d H:i:s');
    $message = $exception->getMessage();
    $file = $exception->getFile();
    $line = $exception->getLine();
    $trace = $exception->getTraceAsString();
    
    // Log to file
    $log_message = "[{$timestamp}] EXCEPTION: {$message} in {$file} on line {$line}\nStack trace:\n{$trace}\n\n";
    file_put_contents(__DIR__ . '/../logs/php_errors.log', $log_message, FILE_APPEND | LOCK_EX);
    
    // Display exception
    displayError('EXCEPTION', $message, $file, $line, $trace);
}

/**
 * Fatal error handler
 */
function fatalErrorHandler() {
    $error = error_get_last();
    if ($error && in_array($error['type'], [E_ERROR, E_CORE_ERROR, E_COMPILE_ERROR, E_PARSE])) {
        $timestamp = date('Y-m-d H:i:s');
        $log_message = "[{$timestamp}] FATAL ERROR: {$error['message']} in {$error['file']} on line {$error['line']}\n";
        file_put_contents(__DIR__ . '/../logs/php_errors.log', $log_message, FILE_APPEND | LOCK_EX);
        
        displayError('FATAL ERROR', $error['message'], $error['file'], $error['line']);
    }
}

/**
 * Display error on screen
 */
function displayError($type, $message, $file, $line, $trace = null) {
    if (!headers_sent()) {
        http_response_code(500);
    }
    
    echo "<!DOCTYPE html>
<html>
<head>
    <title>Debug Error - Banking System</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }
        .error-container { background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .error-header { background: #dc3545; color: white; padding: 15px; margin: -20px -20px 20px -20px; border-radius: 8px 8px 0 0; }
        .error-type { font-size: 18px; font-weight: bold; }
        .error-message { font-size: 16px; margin: 10px 0; }
        .error-location { background: #f8f9fa; padding: 10px; border-left: 4px solid #dc3545; margin: 10px 0; }
        .trace { background: #f8f9fa; padding: 15px; border-radius: 4px; font-family: monospace; font-size: 12px; white-space: pre-wrap; }
        .debug-info { margin-top: 20px; padding: 15px; background: #e9ecef; border-radius: 4px; }
    </style>
</head>
<body>
    <div class='error-container'>
        <div class='error-header'>
            <div class='error-type'>{$type}</div>
        </div>
        <div class='error-message'><strong>Message:</strong> {$message}</div>
        <div class='error-location'>
            <strong>File:</strong> {$file}<br>
            <strong>Line:</strong> {$line}
        </div>";
    
    if ($trace) {
        echo "<div class='trace'><strong>Stack Trace:</strong>\n{$trace}</div>";
    }
    
    echo "<div class='debug-info'>
            <strong>Debug Information:</strong><br>
            <strong>Time:</strong> " . date('Y-m-d H:i:s') . "<br>
            <strong>Memory Usage:</strong> " . formatBytes(memory_get_usage(true)) . "<br>
            <strong>Peak Memory:</strong> " . formatBytes(memory_get_peak_usage(true)) . "<br>
            <strong>PHP Version:</strong> " . PHP_VERSION . "
        </div>
    </div>
</body>
</html>";
}

/**
 * Format bytes for display
 */
function formatBytes($bytes, $precision = 2) {
    $units = array('B', 'KB', 'MB', 'GB', 'TB');
    
    for ($i = 0; $bytes > 1024 && $i < count($units) - 1; $i++) {
        $bytes /= 1024;
    }
    
    return round($bytes, $precision) . ' ' . $units[$i];
}

/**
 * Debug information collector
 */
function getDebugInfo() {
    return [
        'timestamp' => date('Y-m-d H:i:s'),
        'memory_usage' => memory_get_usage(true),
        'peak_memory' => memory_get_peak_usage(true),
        'included_files' => get_included_files(),
        'php_version' => PHP_VERSION,
        'server_info' => $_SERVER,
        'loaded_extensions' => get_loaded_extensions()
    ];
}

/**
 * Log debug information
 */
function logDebugInfo($context = 'general') {
    $debug_info = getDebugInfo();
    $timestamp = date('Y-m-d H:i:s');
    
    $log_message = "[{$timestamp}] DEBUG INFO ({$context}):\n";
    $log_message .= "Memory Usage: " . formatBytes($debug_info['memory_usage']) . "\n";
    $log_message .= "Peak Memory: " . formatBytes($debug_info['peak_memory']) . "\n";
    $log_message .= "Included Files: " . count($debug_info['included_files']) . "\n";
    $log_message .= "PHP Version: " . $debug_info['php_version'] . "\n\n";
    
    file_put_contents(__DIR__ . '/../logs/debug.log', $log_message, FILE_APPEND | LOCK_EX);
}

// Set custom handlers
set_error_handler('customErrorHandler');
set_exception_handler('customExceptionHandler');
register_shutdown_function('fatalErrorHandler');

// Log that debug system is loaded
logDebugInfo('debug_system_loaded');
?>
