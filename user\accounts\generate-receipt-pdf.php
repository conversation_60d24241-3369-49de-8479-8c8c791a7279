<?php
/**
 * Generate Transfer Receipt PDF
 * Creates a downloadable PDF receipt for transfer statements
 */

// Start session and check authentication
session_start();
if (!isset($_SESSION['user_id'])) {
    http_response_code(401);
    exit('Unauthorized');
}

// Check if transfer data is provided
if (!isset($_POST['transfer_data'])) {
    http_response_code(400);
    exit('Transfer data not provided');
}

// Decode transfer data
$transfer_data = json_decode($_POST['transfer_data'], true);
if (!$transfer_data) {
    http_response_code(400);
    exit('Invalid transfer data');
}

// Include required files
require_once '../../config/config.php';
require_once '../../includes/functions.php';

// Get database connection
$db = getDB();
$user_id = $_SESSION['user_id'];

// Verify the transfer belongs to the current user
$verify_query = "SELECT COUNT(*) as count FROM transfers WHERE transaction_id = ? AND sender_id = ?";
$verify_stmt = $db->prepare($verify_query);
$verify_stmt->bind_param("si", $transfer_data['transaction_id'], $user_id);
$verify_stmt->execute();
$verify_result = $verify_stmt->get_result();
$verify_data = $verify_result->fetch_assoc();

if ($verify_data['count'] == 0) {
    http_response_code(403);
    exit('Access denied');
}

// Set headers for HTML display that can be printed as PDF
header('Content-Type: text/html; charset=UTF-8');

?>
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>Transfer Receipt - <?php echo htmlspecialchars($transfer_data['transaction_id']); ?></title>
    <style>
        @page {
            size: A4;
            margin: 1in;
        }
        
        body {
            font-family: 'Courier New', monospace;
            font-size: 12px;
            line-height: 1.4;
            color: #000;
            margin: 0;
            padding: 0;
        }
        
        .receipt-header {
            text-align: center;
            border-bottom: 2px solid #000;
            padding-bottom: 20px;
            margin-bottom: 30px;
        }
        
        .bank-name {
            font-size: 24px;
            font-weight: bold;
            margin-bottom: 10px;
        }
        
        .receipt-title {
            font-size: 18px;
            font-weight: bold;
            letter-spacing: 2px;
        }
        
        .receipt-section {
            margin-bottom: 25px;
            border-bottom: 1px solid #ccc;
            padding-bottom: 15px;
        }
        
        .section-title {
            font-size: 14px;
            font-weight: bold;
            margin-bottom: 10px;
            text-transform: uppercase;
            border-bottom: 1px solid #000;
            padding-bottom: 5px;
        }
        
        .receipt-row {
            display: flex;
            justify-content: space-between;
            margin-bottom: 8px;
            padding: 2px 0;
        }
        
        .receipt-label {
            font-weight: bold;
            width: 40%;
        }
        
        .receipt-value {
            width: 60%;
            text-align: right;
        }
        
        .total-row {
            border-top: 2px solid #000;
            padding-top: 10px;
            margin-top: 10px;
            font-weight: bold;
            font-size: 14px;
        }
        
        .footer {
            position: fixed;
            bottom: 1in;
            left: 1in;
            right: 1in;
            text-align: center;
            font-size: 10px;
            color: #666;
            border-top: 1px solid #ccc;
            padding-top: 10px;
        }
    </style>
</head>
<body>
    <div class="receipt-header">
        <div class="bank-name">PremierBank Pro</div>
        <div class="receipt-title">TRANSFER RECEIPT</div>
    </div>

    <div class="receipt-section">
        <div class="section-title">Transfer Details</div>
        <div class="receipt-row">
            <span class="receipt-label">Reference Number:</span>
            <span class="receipt-value"><?php echo htmlspecialchars($transfer_data['transaction_id']); ?></span>
        </div>
        <div class="receipt-row">
            <span class="receipt-label">Transfer Type:</span>
            <span class="receipt-value"><?php echo ucfirst(htmlspecialchars($transfer_data['transfer_type'])); ?> Transfer</span>
        </div>
        <div class="receipt-row">
            <span class="receipt-label">Date & Time:</span>
            <span class="receipt-value"><?php echo date('M j, Y, g:i A', strtotime($transfer_data['created_at'])); ?></span>
        </div>
        <div class="receipt-row">
            <span class="receipt-label">Status:</span>
            <span class="receipt-value"><?php echo ucfirst(htmlspecialchars($transfer_data['status'])); ?></span>
        </div>
    </div>

    <div class="receipt-section">
        <div class="section-title">Recipient Information</div>
        <div class="receipt-row">
            <span class="receipt-label">Name:</span>
            <span class="receipt-value"><?php echo htmlspecialchars($transfer_data['recipient_display_name'] ?: $transfer_data['recipient_name']); ?></span>
        </div>
        <div class="receipt-row">
            <span class="receipt-label">Account:</span>
            <span class="receipt-value">****<?php echo substr($transfer_data['recipient_account'], -4); ?></span>
        </div>
        <?php if (!empty($transfer_data['transfer_category'])): ?>
        <div class="receipt-row">
            <span class="receipt-label">Bank Type:</span>
            <span class="receipt-value"><?php echo htmlspecialchars($transfer_data['transfer_category']); ?></span>
        </div>
        <?php endif; ?>
    </div>

    <div class="receipt-section">
        <div class="section-title">Amount Details</div>
        <div class="receipt-row">
            <span class="receipt-label">Transfer Amount:</span>
            <span class="receipt-value"><?php echo htmlspecialchars($transfer_data['currency']); ?> <?php echo number_format($transfer_data['amount'], 2); ?></span>
        </div>
        <div class="receipt-row">
            <span class="receipt-label">Transfer Fee:</span>
            <span class="receipt-value">
                <?php if ($transfer_data['fee'] > 0): ?>
                    <?php echo htmlspecialchars($transfer_data['currency']); ?> <?php echo number_format($transfer_data['fee'], 2); ?>
                <?php else: ?>
                    Free
                <?php endif; ?>
            </span>
        </div>
        <div class="receipt-row total-row">
            <span class="receipt-label">Total Debited:</span>
            <span class="receipt-value"><?php echo htmlspecialchars($transfer_data['currency']); ?> <?php echo number_format($transfer_data['amount'] + $transfer_data['fee'], 2); ?></span>
        </div>
    </div>

    <?php if (!empty($transfer_data['description'])): ?>
    <div class="receipt-section">
        <div class="section-title">Purpose</div>
        <div style="padding: 10px; background: #f5f5f5; border-radius: 4px;">
            <?php echo htmlspecialchars($transfer_data['description']); ?>
        </div>
    </div>
    <?php endif; ?>

    <div class="receipt-section">
        <div class="section-title">Transaction Security</div>
        <div class="receipt-row">
            <span class="receipt-label">Transaction ID:</span>
            <span class="receipt-value"><?php echo htmlspecialchars($transfer_data['transaction_id']); ?></span>
        </div>
        <div class="receipt-row">
            <span class="receipt-label">Processed:</span>
            <span class="receipt-value"><?php echo date('M j, Y, g:i A', strtotime($transfer_data['created_at'])); ?></span>
        </div>
    </div>

    <div class="footer">
        <p>This is an electronic transfer receipt generated by PremierBank Pro.</p>
        <p>Please retain this receipt for your records.</p>
        <p>Generated on: <?php echo date('M j, Y, g:i A'); ?></p>
    </div>

    <script>
        // Auto-print when page loads
        window.onload = function() {
            window.print();
        };
    </script>
</body>
</html>
