/**
 * View User Page JavaScript Functions
 * Handles OTP management, UI interactions, and animations
 */

// OTP Management Functions
function generateOTPForUser(userId) {
    if (confirm('Generate a new OTP code for this user?')) {
        fetch('ajax/generate-otp.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                user_id: userId,
                source: 'admin'
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert('OTP generated successfully: ' + data.otp_code);
                location.reload();
            } else {
                alert('Error generating OTP: ' + data.message);
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('An error occurred while generating OTP');
        });
    }
}

function showOTPHistory() {
    $('#otpHistoryModal').modal('show');
}

// UI Enhancement Functions
function initializePageAnimations() {
    // Add smooth animations to cards
    const cards = document.querySelectorAll('.card');
    cards.forEach((card, index) => {
        card.style.animationDelay = (index * 0.1) + 's';
        card.classList.add('fade-in');
    });

    // Add hover effects to list items
    const listItems = document.querySelectorAll('.list-group-item');
    listItems.forEach(item => {
        item.addEventListener('mouseenter', function() {
            this.style.backgroundColor = '#f8f9fa';
        });
        item.addEventListener('mouseleave', function() {
            this.style.backgroundColor = '';
        });
    });
}

// Card Interaction Functions
function initializeCardHoverEffects() {
    const cards = document.querySelectorAll('.card');
    cards.forEach(card => {
        card.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-2px)';
            this.style.boxShadow = '0 4px 12px rgba(0, 0, 0, 0.15)';
        });
        
        card.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(0)';
            this.style.boxShadow = '0 1px 3px rgba(0, 0, 0, 0.1)';
        });
    });
}

// Credit Card Animation Functions
function initializeCreditCardEffects() {
    const creditCards = document.querySelectorAll('.credit-card');
    creditCards.forEach(card => {
        card.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-5px) scale(1.02)';
        });
        
        card.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(0) scale(1)';
        });
    });
}

// Status Badge Functions
function updateStatusBadges() {
    const badges = document.querySelectorAll('.badge');
    badges.forEach(badge => {
        const text = badge.textContent.toLowerCase();
        
        // Add appropriate classes based on status
        if (text.includes('active') || text.includes('verified') || text.includes('completed')) {
            badge.classList.add('status-active');
        } else if (text.includes('suspended') || text.includes('rejected') || text.includes('failed')) {
            badge.classList.add('status-suspended');
        } else if (text.includes('pending') || text.includes('processing')) {
            badge.classList.add('status-pending');
        }
    });
}

// Table Enhancement Functions
function enhanceTransactionTable() {
    const table = document.querySelector('.transaction-table');
    if (table) {
        const rows = table.querySelectorAll('tbody tr');
        rows.forEach((row, index) => {
            row.style.animationDelay = (index * 0.05) + 's';
            row.classList.add('fade-in');
            
            // Add click handler for row highlighting
            row.addEventListener('click', function() {
                // Remove previous highlights
                rows.forEach(r => r.classList.remove('table-active'));
                // Add highlight to clicked row
                this.classList.add('table-active');
            });
        });
    }
}

// Modal Enhancement Functions
function enhanceModals() {
    const modals = document.querySelectorAll('.modal');
    modals.forEach(modal => {
        modal.addEventListener('show.bs.modal', function() {
            this.style.animation = 'fadeIn 0.3s ease-out';
        });
        
        modal.addEventListener('hide.bs.modal', function() {
            this.style.animation = 'fadeOut 0.3s ease-out';
        });
    });
}

// Loading State Functions
function showLoadingState(element) {
    const originalContent = element.innerHTML;
    element.innerHTML = '<span class="loading-spinner"></span> Loading...';
    element.disabled = true;
    
    return function hideLoading() {
        element.innerHTML = originalContent;
        element.disabled = false;
    };
}

// Utility Functions
function formatCurrency(amount, currency = 'USD') {
    return new Intl.NumberFormat('en-US', {
        style: 'currency',
        currency: currency
    }).format(amount);
}

function formatDate(dateString, format = 'short') {
    const date = new Date(dateString);
    const options = format === 'short' 
        ? { month: 'short', day: 'numeric', year: 'numeric' }
        : { month: 'short', day: 'numeric', year: 'numeric', hour: 'numeric', minute: '2-digit' };
    
    return date.toLocaleDateString('en-US', options);
}

// Copy to Clipboard Function
function copyToClipboard(text, element) {
    navigator.clipboard.writeText(text).then(function() {
        // Show success feedback
        const originalText = element.textContent;
        element.textContent = 'Copied!';
        element.classList.add('text-success');
        
        setTimeout(() => {
            element.textContent = originalText;
            element.classList.remove('text-success');
        }, 2000);
    }).catch(function(err) {
        console.error('Could not copy text: ', err);
    });
}

// Search and Filter Functions
function filterTransactions(searchTerm) {
    const table = document.querySelector('.transaction-table tbody');
    if (!table) return;
    
    const rows = table.querySelectorAll('tr');
    rows.forEach(row => {
        const text = row.textContent.toLowerCase();
        if (text.includes(searchTerm.toLowerCase())) {
            row.style.display = '';
        } else {
            row.style.display = 'none';
        }
    });
}

// Initialize all functions when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    initializePageAnimations();
    initializeCardHoverEffects();
    initializeCreditCardEffects();
    updateStatusBadges();
    enhanceTransactionTable();
    enhanceModals();
    
    // Add click-to-copy functionality for account numbers
    const accountNumbers = document.querySelectorAll('.font-monospace');
    accountNumbers.forEach(element => {
        element.style.cursor = 'pointer';
        element.title = 'Click to copy';
        element.addEventListener('click', function() {
            copyToClipboard(this.textContent, this);
        });
    });
    
    console.log('View User page initialized successfully');
});

// Export functions for external use
window.ViewUserPage = {
    generateOTPForUser,
    showOTPHistory,
    copyToClipboard,
    filterTransactions,
    showLoadingState
};
