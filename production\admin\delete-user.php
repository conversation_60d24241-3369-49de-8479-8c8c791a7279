<?php
/**
 * Delete User Functionality - Complete Rewrite
 * Secure and robust user deletion with comprehensive error handling
 */

require_once '../config/config.php';

// Ensure admin access
requireAdmin();

/**
 * Send JSON response and exit
 */
function sendJsonResponse($success, $message, $data = []) {
    header('Content-Type: application/json');
    echo json_encode([
        'success' => $success,
        'message' => $message,
        'data' => $data
    ]);
    exit;
}

/**
 * Get user information for deletion validation
 */
function getUserForDeletion($user_id) {
    try {
        $db = getDB();
        $sql = "SELECT id, username, email, first_name, last_name, is_admin, status, balance 
                FROM accounts WHERE id = ?";
        $result = $db->query($sql, [$user_id]);
        
        if ($result && $result->num_rows > 0) {
            return $result->fetch_assoc();
        }
        return null;
    } catch (Exception $e) {
        error_log("Error fetching user for deletion: " . $e->getMessage());
        return null;
    }
}

/**
 * Perform comprehensive security checks before deletion
 */
function performSecurityChecks($user, $current_admin_id) {
    $errors = [];
    
    // Check if user exists
    if (!$user) {
        $errors[] = "User not found";
        return $errors;
    }
    
    // Prevent admin account deletion
    if ($user['is_admin']) {
        $errors[] = "Administrator accounts cannot be deleted";
    }
    
    // Prevent self-deletion
    if ($user['id'] == $current_admin_id) {
        $errors[] = "You cannot delete your own account";
    }
    
    // Admin can delete users regardless of balance
    // Balance check removed - admin has full control
    
    return $errors;
}

/**
 * Get related records count for user
 */
function getRelatedRecordsCounts($user_id) {
    try {
        $db = getDB();
        $counts = [];
        
        // Check transfers
        $result = $db->query("SELECT COUNT(*) as count FROM transfers WHERE sender_id = ? OR recipient_id = ?", [$user_id, $user_id]);
        $counts['transfers'] = $result->fetch_assoc()['count'];
        
        // Check beneficiaries
        $result = $db->query("SELECT COUNT(*) as count FROM beneficiaries WHERE user_id = ?", [$user_id]);
        $counts['beneficiaries'] = $result->fetch_assoc()['count'];
        
        // Check tickets
        $result = $db->query("SELECT COUNT(*) as count FROM tickets WHERE user_id = ?", [$user_id]);
        $counts['tickets'] = $result->fetch_assoc()['count'];
        
        // Check OTPs
        $result = $db->query("SELECT COUNT(*) as count FROM user_otps WHERE user_id = ?", [$user_id]);
        $counts['user_otps'] = $result->fetch_assoc()['count'];
        
        // Check virtual cards (if table exists)
        try {
            $result = $db->query("SELECT COUNT(*) as count FROM virtual_cards WHERE user_id = ?", [$user_id]);
            $counts['virtual_cards'] = $result->fetch_assoc()['count'];
        } catch (Exception $e) {
            $counts['virtual_cards'] = 0; // Table might not exist
        }
        
        // Check crypto accounts (if table exists)
        try {
            $result = $db->query("SELECT COUNT(*) as count FROM crypto_accounts WHERE user_id = ?", [$user_id]);
            $counts['crypto_accounts'] = $result->fetch_assoc()['count'];
        } catch (Exception $e) {
            $counts['crypto_accounts'] = 0; // Table might not exist
        }
        
        // Check account transactions (if table exists)
        try {
            $result = $db->query("SELECT COUNT(*) as count FROM account_transactions WHERE account_id = ?", [$user_id]);
            $counts['account_transactions'] = $result->fetch_assoc()['count'];
        } catch (Exception $e) {
            $counts['account_transactions'] = 0; // Table might not exist
        }
        
        return $counts;
    } catch (Exception $e) {
        error_log("Error getting related records counts: " . $e->getMessage());
        return [];
    }
}

/**
 * Execute user deletion with transaction support
 */
function executeUserDeletion($user_id, $user_data) {
    try {
        $db = getDB();
        
        // Start transaction
        $db->beginTransaction();
        
        $deletion_log = [];
        
        // Delete related records in order (child tables first)
        
        // 1. Delete user OTPs
        $result = $db->query("DELETE FROM user_otps WHERE user_id = ?", [$user_id]);
        $deletion_log[] = "Deleted " . $db->getConnection()->affected_rows . " OTP records";
        
        // 2. Delete beneficiaries
        $result = $db->query("DELETE FROM beneficiaries WHERE user_id = ?", [$user_id]);
        $deletion_log[] = "Deleted " . $db->getConnection()->affected_rows . " beneficiary records";
        
        // 3. Delete virtual cards (if table exists)
        try {
            $result = $db->query("DELETE FROM virtual_cards WHERE user_id = ?", [$user_id]);
            $deletion_log[] = "Deleted " . $db->getConnection()->affected_rows . " virtual card records";
        } catch (Exception $e) {
            $deletion_log[] = "Virtual cards table not found - skipped";
        }
        
        // 4. Delete crypto accounts (if table exists)
        try {
            $result = $db->query("DELETE FROM crypto_accounts WHERE user_id = ?", [$user_id]);
            $deletion_log[] = "Deleted " . $db->getConnection()->affected_rows . " crypto account records";
        } catch (Exception $e) {
            $deletion_log[] = "Crypto accounts table not found - skipped";
        }
        
        // 5. Delete account transactions (if table exists)
        try {
            $result = $db->query("DELETE FROM account_transactions WHERE account_id = ?", [$user_id]);
            $deletion_log[] = "Deleted " . $db->getConnection()->affected_rows . " account transaction records";
        } catch (Exception $e) {
            $deletion_log[] = "Account transactions table not found - skipped";
        }
        
        // 6. Handle transfers (set foreign keys to NULL instead of deleting)
        $result = $db->query("UPDATE transfers SET sender_id = NULL WHERE sender_id = ?", [$user_id]);
        $sender_transfers = $db->getConnection()->affected_rows;
        
        $result = $db->query("UPDATE transfers SET recipient_id = NULL WHERE recipient_id = ?", [$user_id]);
        $recipient_transfers = $db->getConnection()->affected_rows;
        
        $deletion_log[] = "Updated " . ($sender_transfers + $recipient_transfers) . " transfer records (preserved for audit)";
        
        // 7. Handle tickets (set user_id to NULL to preserve tickets for admin reference)
        $result = $db->query("UPDATE tickets SET user_id = NULL WHERE user_id = ?", [$user_id]);
        $deletion_log[] = "Updated " . $db->getConnection()->affected_rows . " ticket records (preserved for admin reference)";
        
        // 8. Handle audit logs (set user_id to NULL to preserve audit trail)
        $result = $db->query("UPDATE audit_logs SET user_id = NULL WHERE user_id = ?", [$user_id]);
        $deletion_log[] = "Updated " . $db->getConnection()->affected_rows . " audit log records (preserved for compliance)";
        
        // 9. Finally, delete the main account record
        $result = $db->query("DELETE FROM accounts WHERE id = ?", [$user_id]);
        if ($db->getConnection()->affected_rows === 0) {
            throw new Exception("Failed to delete user account - user may have been already deleted");
        }
        $deletion_log[] = "Deleted main account record";
        
        // Commit transaction
        $db->commit();
        
        return [
            'success' => true,
            'message' => 'User successfully deleted',
            'deletion_log' => $deletion_log
        ];
        
    } catch (Exception $e) {
        // Rollback transaction on error
        $db->rollback();
        error_log("Error during user deletion: " . $e->getMessage());
        
        return [
            'success' => false,
            'message' => 'Failed to delete user: ' . $e->getMessage(),
            'deletion_log' => []
        ];
    }
}

/**
 * Log user deletion activity
 */
function logUserDeletion($user_data, $current_admin_id, $deletion_log) {
    try {
        logActivity(
            $current_admin_id,
            'USER_DELETED',
            'accounts',
            $user_data['id'],
            $user_data, // old values
            [
                'deleted_by' => $_SESSION['username'],
                'deletion_details' => $deletion_log,
                'deleted_at' => date('Y-m-d H:i:s')
            ] // new values
        );
    } catch (Exception $e) {
        error_log("Error logging user deletion: " . $e->getMessage());
    }
}

// Main deletion logic
try {
    // Validate request method and parameters
    if ($_SERVER['REQUEST_METHOD'] !== 'POST' && !isset($_GET['ajax'])) {
        sendJsonResponse(false, 'Invalid request method');
    }
    
    // Get user ID
    $user_id = intval($_GET['id'] ?? $_POST['id'] ?? 0);
    
    if ($user_id <= 0) {
        sendJsonResponse(false, 'Invalid user ID');
    }
    
    // Get current admin ID
    $current_admin_id = $_SESSION['user_id'];
    
    // Step 1: Get user information
    $user = getUserForDeletion($user_id);
    
    // Step 2: Perform security checks
    $security_errors = performSecurityChecks($user, $current_admin_id);
    
    if (!empty($security_errors)) {
        sendJsonResponse(false, implode('; ', $security_errors));
    }
    
    // Step 3: Get related records counts for logging
    $related_counts = getRelatedRecordsCounts($user_id);
    
    // Step 4: Execute deletion
    $deletion_result = executeUserDeletion($user_id, $user);
    
    if (!$deletion_result['success']) {
        sendJsonResponse(false, $deletion_result['message']);
    }
    
    // Step 5: Log the deletion
    logUserDeletion($user, $current_admin_id, $deletion_result['deletion_log']);
    
    // Step 6: Send success response
    sendJsonResponse(true, "User '{$user['username']}' has been permanently deleted", [
        'deleted_user' => [
            'id' => $user['id'],
            'username' => $user['username'],
            'email' => $user['email'],
            'name' => $user['first_name'] . ' ' . $user['last_name']
        ],
        'related_records' => $related_counts,
        'deletion_log' => $deletion_result['deletion_log']
    ]);
    
} catch (Exception $e) {
    error_log("Unexpected error in delete-user.php: " . $e->getMessage());
    sendJsonResponse(false, 'An unexpected error occurred. Please try again.');
}
?>
