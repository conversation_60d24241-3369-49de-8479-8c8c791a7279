/* View User Page Styles */

/* Credit Card Styles */
.credit-card {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 15px;
    padding: 20px;
    color: white;
    position: relative;
    min-height: 200px;
    box-shadow: 0 8px 25px rgba(0,0,0,0.15);
    overflow: hidden;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.credit-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 12px 35px rgba(0,0,0,0.2);
}

.credit-card::before {
    content: '';
    position: absolute;
    top: -50%;
    right: -50%;
    width: 100%;
    height: 100%;
    background: radial-gradient(circle, rgba(255,255,255,0.1) 0%, transparent 70%);
    border-radius: 50%;
}

.visa-card {
    background: linear-gradient(135deg, #1a237e 0%, #3949ab 100%);
}

.mastercard-card {
    background: linear-gradient(135deg, #ff6b35 0%, #f7931e 100%);
}

.card-logo {
    z-index: 2;
    position: relative;
}

.visa-logo {
    font-weight: bold;
    font-size: 1.2rem;
    letter-spacing: 2px;
    color: white;
    text-shadow: 0 2px 4px rgba(0,0,0,0.3);
}

.mastercard-logo {
    font-weight: bold;
    font-size: 1rem;
    letter-spacing: 1px;
    color: white;
    text-shadow: 0 2px 4px rgba(0,0,0,0.3);
}

.generic-logo {
    font-weight: bold;
    font-size: 1rem;
    letter-spacing: 1px;
    color: white;
    text-shadow: 0 2px 4px rgba(0,0,0,0.3);
}

.card-number {
    font-family: 'Courier New', monospace;
    font-size: 1.4rem;
    font-weight: bold;
    letter-spacing: 3px;
    margin: 20px 0;
    text-shadow: 0 2px 4px rgba(0,0,0,0.3);
}

.card-label {
    font-size: 0.7rem;
    letter-spacing: 1px;
    opacity: 0.8;
    text-transform: uppercase;
}

.card-holder-name {
    font-size: 0.9rem;
    font-weight: 600;
    letter-spacing: 1px;
    text-shadow: 0 1px 2px rgba(0,0,0,0.3);
}

.card-expiry-date {
    font-family: 'Courier New', monospace;
    font-size: 0.9rem;
    font-weight: 600;
    text-shadow: 0 1px 2px rgba(0,0,0,0.3);
}

.card-balance-section {
    margin-top: 15px;
    padding-top: 15px;
    border-top: 1px solid rgba(255,255,255,0.2);
}

.card-status .badge {
    font-size: 0.7rem;
    padding: 4px 8px;
}

/* User Detail Page Specific Styles */
.user-avatar-large {
    background: linear-gradient(135deg, #206bc4, #1a5490);
}

.personal-info-section .border-bottom {
    border-color: #e9ecef !important;
}

.account-info-section .font-monospace {
    font-family: 'Courier New', monospace;
}

/* Security Section Styles */
.security-card .alert {
    border-radius: 8px;
}

.otp-management-card {
    transition: transform 0.2s ease;
}

.otp-management-card:hover {
    transform: translateY(-2px);
}

/* Transaction Table Styles */
.transaction-table {
    font-size: 0.9rem;
}

.transaction-table .badge {
    font-size: 0.75rem;
}

/* Virtual Card Section */
.virtual-cards-section .credit-card {
    margin-bottom: 1rem;
}

/* Crypto Section */
.crypto-section .card {
    border-left: 4px solid #f59f00;
}

/* Document Section */
.document-section .list-group-item {
    border-left: 3px solid transparent;
}

.document-section .list-group-item.approved {
    border-left-color: #28a745;
}

.document-section .list-group-item.pending {
    border-left-color: #ffc107;
}

.document-section .list-group-item.rejected {
    border-left-color: #dc3545;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .credit-card {
        min-height: 180px;
        padding: 15px;
    }
    
    .card-number {
        font-size: 1.2rem;
        letter-spacing: 2px;
    }
    
    .personal-info-section .col-md-6 {
        margin-bottom: 1rem;
    }
}

/* Modal Styles */
.modal-lg {
    max-width: 900px;
}

.document-preview-modal .modal-body {
    padding: 0;
}

.document-preview-modal iframe {
    width: 100%;
    height: 500px;
    border: none;
}

/* Loading States */
.loading-spinner {
    display: inline-block;
    width: 1rem;
    height: 1rem;
    border: 2px solid #f3f3f3;
    border-top: 2px solid #007bff;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Status Badges */
.status-active {
    background-color: #28a745 !important;
}

.status-suspended {
    background-color: #dc3545 !important;
}

.status-pending {
    background-color: #ffc107 !important;
}

.status-verified {
    background-color: #28a745 !important;
}

.status-rejected {
    background-color: #dc3545 !important;
}

/* Custom animations and styling */
@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes fadeOut {
    from {
        opacity: 1;
        transform: translateY(0);
    }
    to {
        opacity: 0;
        transform: translateY(-20px);
    }
}

.fade-in {
    animation: fadeIn 0.6s ease-out forwards;
}

.fade-out {
    animation: fadeOut 0.3s ease-out forwards;
}

/* Enhanced card styling */
.card {
    border: 1px solid #e9ecef;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
}

.card:hover {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    transform: translateY(-2px);
}

/* Better list group styling */
.list-group-item {
    border: none;
    border-bottom: 1px solid #f1f3f4;
    transition: background-color 0.2s ease;
}

.list-group-item:last-child {
    border-bottom: none;
}

/* Enhanced badge styling */
.badge {
    font-weight: 500;
    letter-spacing: 0.025em;
}

/* Better table styling */
.table th {
    font-weight: 600;
    color: #374151;
    border-bottom: 2px solid #e5e7eb;
}

.table-active {
    background-color: #f8f9fa !important;
}

/* Improved avatar gradient */
.avatar {
    background: linear-gradient(135deg, #206bc4, #1a5490) !important;
    color: white;
    font-weight: 600;
}

/* Enhanced alert styling */
.alert {
    border: none;
    border-radius: 8px;
}

/* Better modal styling */
.modal-content {
    border: none;
    border-radius: 12px;
    box-shadow: 0 10px 40px rgba(0, 0, 0, 0.1);
}

/* Click-to-copy styling */
.font-monospace[title="Click to copy"] {
    transition: all 0.2s ease;
}

.font-monospace[title="Click to copy"]:hover {
    background-color: #f8f9fa;
    padding: 2px 4px;
    border-radius: 4px;
}

/* Page Layout Improvements */
.page-header {
    margin-bottom: 2rem !important;
}

.container-xl {
    padding-left: 1.5rem;
    padding-right: 1.5rem;
}

/* Section Spacing */
.row-cards {
    margin-bottom: 2rem;
}

.row-cards:last-child {
    margin-bottom: 1rem;
}

/* Card Height Standardization */
.card.h-100 {
    height: 100% !important;
}

/* Visual Dividers */
.card-header.border-bottom {
    border-bottom: 2px solid #e9ecef !important;
}

.border-start {
    border-left: 2px solid #e9ecef !important;
    padding-left: 1.5rem !important;
}

/* Enhanced OTP Code Display */
.font-monospace.badge {
    font-size: 1.1rem !important;
    padding: 0.5rem 1rem !important;
    cursor: pointer;
    transition: all 0.2s ease;
}

.font-monospace.badge:hover {
    background-color: #0056b3 !important;
    transform: scale(1.05);
}

/* Copy Button Styling */
.btn-outline-primary:hover {
    background-color: #0d6efd;
    border-color: #0d6efd;
}

/* List Group Improvements */
.list-group-item {
    padding: 1rem;
    border-radius: 0.375rem;
    margin-bottom: 0.5rem;
    border: 1px solid #e9ecef !important;
    transition: all 0.2s ease;
}

.list-group-item:hover {
    background-color: #f8f9fa;
    border-color: #dee2e6 !important;
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.list-group-flush .list-group-item {
    border-left: 0 !important;
    border-right: 0 !important;
    border-radius: 0;
    margin-bottom: 0;
}

.list-group-flush .list-group-item:first-child {
    border-top: 0 !important;
}

.list-group-flush .list-group-item:last-child {
    border-bottom: 0 !important;
}

/* Alert Improvements */
.alert {
    border-radius: 0.5rem;
    border: none;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.alert-info {
    background-color: #e7f3ff;
    color: #0c5460;
}

.alert-secondary {
    background-color: #f8f9fa;
    color: #6c757d;
}

/* Modern Security Section Styles */
.bg-gradient-primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
}

.security-card {
    background: #fff;
    border-radius: 12px;
    border: 1px solid #e9ecef;
    transition: all 0.3s ease;
}

.security-card:hover {
    box-shadow: 0 4px 12px rgba(0,0,0,0.1);
    transform: translateY(-2px);
}

.security-card-header {
    padding: 1.5rem 1.5rem 1rem;
    border-bottom: 1px solid #f1f3f4;
}

.security-card-body {
    padding: 1rem 1.5rem 1.5rem;
}

.security-icon {
    width: 40px;
    height: 40px;
    border-radius: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.1rem;
}

.otp-display {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-radius: 12px;
    padding: 1.5rem;
    margin-bottom: 1rem;
}

.otp-code-container {
    text-align: center;
    margin-bottom: 1rem;
}

.otp-code {
    background: #fff;
    border: 2px solid #e9ecef;
    border-radius: 8px;
    padding: 1rem 1.5rem;
    display: inline-flex;
    align-items: center;
    gap: 1rem;
    cursor: pointer;
    transition: all 0.3s ease;
    margin-top: 0.5rem;
}

.otp-code:hover {
    border-color: #0d6efd;
    box-shadow: 0 0 0 0.2rem rgba(13, 110, 253, 0.25);
    transform: scale(1.02);
}

.otp-digits {
    font-family: 'Courier New', monospace;
    font-size: 1.5rem;
    font-weight: bold;
    color: #0d6efd;
    letter-spacing: 0.2rem;
}

.otp-copy-icon {
    color: #6c757d;
    transition: color 0.3s ease;
}

.otp-code:hover .otp-copy-icon {
    color: #0d6efd;
}

.otp-meta {
    margin-top: 1rem;
}

.meta-item {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.875rem;
    color: #6c757d;
}

.meta-item i {
    width: 16px;
    text-align: center;
}

.security-actions {
    margin-top: 1.5rem;
}

.action-card {
    background: #fff;
    border: 1px solid #e9ecef;
    border-radius: 10px;
    padding: 1.25rem;
    cursor: pointer;
    transition: all 0.3s ease;
    height: 100%;
    display: flex;
    align-items: center;
    gap: 1rem;
}

.action-card:hover {
    border-color: #0d6efd;
    box-shadow: 0 2px 8px rgba(13, 110, 253, 0.15);
    transform: translateY(-1px);
}

.action-icon {
    width: 36px;
    height: 36px;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1rem;
    flex-shrink: 0;
}

.action-content h6 {
    margin: 0 0 0.25rem;
    font-weight: 600;
    color: #212529;
}

.action-content p {
    margin: 0;
    font-size: 0.875rem;
    color: #6c757d;
}

.login-activity-list {
    max-height: 300px;
    overflow-y: auto;
}

.login-attempt-item {
    padding: 0.75rem 0;
    border-bottom: 1px solid #f1f3f4;
}

.login-attempt-item:last-child {
    border-bottom: none;
}

.attempt-status {
    width: 24px;
    height: 24px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 0.75rem;
    flex-shrink: 0;
}

.attempt-details {
    min-width: 0;
}

.attempt-result {
    font-weight: 500;
    font-size: 0.875rem;
    color: #212529;
}

.attempt-time {
    font-size: 0.75rem;
    color: #6c757d;
}

.attempt-ip {
    font-size: 0.75rem;
    color: #6c757d;
}

.empty-state i {
    font-size: 2rem;
}

.alert-info-subtle {
    background-color: #cff4fc;
    border-color: #b6effb;
    color: #055160;
}

.bg-success-subtle {
    background-color: #d1e7dd !important;
}

.text-success {
    color: #198754 !important;
}

.bg-secondary-subtle {
    background-color: #e2e3e5 !important;
}

.text-secondary {
    color: #6c757d !important;
}

/* Responsive improvements */
@media (max-width: 768px) {
    .page-header .btn-list {
        flex-direction: column;
        gap: 0.5rem;
    }

    .page-header .btn-list .btn {
        width: 100%;
    }

    .credit-card {
        margin-bottom: 1rem;
    }

    .container-xl {
        padding-left: 1rem;
        padding-right: 1rem;
    }

    .border-start {
        border-left: none !important;
        border-top: 2px solid #e9ecef !important;
        padding-left: 0 !important;
        padding-top: 1.5rem !important;
        margin-top: 1.5rem;
    }

    .security-card-header,
    .security-card-body {
        padding: 1rem;
    }

    .otp-display {
        padding: 1rem;
    }

    .otp-digits {
        font-size: 1.25rem;
    }

    .action-card {
        padding: 1rem;
        flex-direction: column;
        text-align: center;
        gap: 0.75rem;
    }
}
