<?php
/**
 * Test script to verify super-admin fixes
 */

// Enable error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);
ini_set('log_errors', 1);
ini_set('error_log', __DIR__ . '/../../logs/error.log');

echo "<h1>Super-Admin Fixes Test</h1>";

try {
    echo "<h2>1. Testing Config Include</h2>";
    require_once '../../config/config.php';
    echo "✅ Config included successfully<br>";
    
    echo "<h2>2. Testing Super Admin Settings Include</h2>";
    require_once '../../config/super_admin_settings.php';
    echo "✅ Super admin settings included successfully<br>";
    
    echo "<h2>3. Testing getEmailContactInfo Function</h2>";
    $contact_info = getEmailContactInfo();
    echo "✅ getEmailContactInfo function works<br>";
    echo "Site Name: " . $contact_info['site_name'] . "<br>";
    echo "Support Email: " . $contact_info['support_email'] . "<br>";
    
    echo "<h2>4. Testing Google2FA Function</h2>";
    $google2fa = getGoogle2FA();
    $test_secret = $google2fa->generateSecretKey();
    echo "✅ Google2FA working: " . substr($test_secret, 0, 8) . "...<br>";
    
    echo "<h2>5. Testing Super-Admin Pages</h2>";
    echo "<p><strong>Note:</strong> These links require super-admin login</p>";
    echo "<a href='../../super-admin/email-templates.php' target='_blank'>Test Email Templates Page</a><br>";
    echo "<a href='../../super-admin/setup-2fa.php' target='_blank'>Test Setup 2FA Page</a><br>";
    echo "<a href='../../super-admin/' target='_blank'>Test Super-Admin Dashboard</a><br>";
    
    echo "<h2>6. Function Redeclaration Check</h2>";
    echo "✅ No function redeclaration errors detected<br>";
    
    echo "<h2>✅ All tests passed! Super-admin fixes are working correctly.</h2>";
    
} catch (Exception $e) {
    echo "<h2>❌ Error occurred:</h2>";
    echo "<p><strong>Message:</strong> " . $e->getMessage() . "</p>";
    echo "<p><strong>File:</strong> " . $e->getFile() . "</p>";
    echo "<p><strong>Line:</strong> " . $e->getLine() . "</p>";
    echo "<p><strong>Trace:</strong></p>";
    echo "<pre>" . $e->getTraceAsString() . "</pre>";
} catch (Error $e) {
    echo "<h2>❌ Fatal error occurred:</h2>";
    echo "<p><strong>Message:</strong> " . $e->getMessage() . "</p>";
    echo "<p><strong>File:</strong> " . $e->getFile() . "</p>";
    echo "<p><strong>Line:</strong> " . $e->getLine() . "</p>";
    echo "<p><strong>Trace:</strong></p>";
    echo "<pre>" . $e->getTraceAsString() . "</pre>";
}

echo "<hr>";
echo "<p><strong>Log file location:</strong> " . __DIR__ . '/../../logs/error.log</p>';
echo "<p><strong>Check the error log for detailed information.</strong></p>";

// Test direct function calls to ensure no redeclaration
echo "<h2>7. Direct Function Test</h2>";
try {
    // Test that we can call functions without redeclaration errors
    if (function_exists('getEmailContactInfo')) {
        echo "✅ getEmailContactInfo function exists and is callable<br>";
    }
    
    if (function_exists('getSuperAdminSetting')) {
        echo "✅ getSuperAdminSetting function exists and is callable<br>";
    }
    
    if (function_exists('getGoogle2FA')) {
        echo "✅ getGoogle2FA function exists and is callable<br>";
    }
    
} catch (Exception $e) {
    echo "❌ Function test error: " . $e->getMessage() . "<br>";
}
?>
