<?php
/**
 * Main Configuration File for Online Banking System
 */

// Include Composer autoloader
// require_once __DIR__ . '/../vendor/autoload.php';

// Start session if not already started
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// Error reporting (disable in production)
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Application configuration
define('APP_NAME', 'SecureBank Online');
define('APP_VERSION', '1.0.0');
define('APP_URL', 'http://localhost/online_banking');
define('ADMIN_EMAIL', '<EMAIL>');

// Base URL configuration
define('BASE_URL', 'http://localhost/online_banking');

// Helper function to generate URLs
function url($path = '') {
    $path = ltrim($path, '/');
    return BASE_URL . '/' . $path;
}

// Helper function to generate asset URLs
function asset($path = '') {
    $path = ltrim($path, '/');
    return BASE_URL . '/assets/' . $path;
}

// Security configuration
define('SESSION_TIMEOUT', 1800); // 30 minutes
define('MAX_LOGIN_ATTEMPTS', 5);
define('LOGIN_LOCKOUT_TIME', 900); // 15 minutes

// File upload configuration
define('UPLOAD_MAX_SIZE', 5242880); // 5MB
define('UPLOAD_ALLOWED_TYPES', ['jpg', 'jpeg', 'png', 'pdf', 'doc', 'docx']);
define('UPLOAD_PATH', __DIR__ . '/../assets/uploads/');

// Currency and transfer limits
define('DEFAULT_CURRENCY', 'USD');
define('MIN_TRANSFER_AMOUNT', 1.00);
define('MAX_TRANSFER_AMOUNT', 10000.00);
define('DAILY_TRANSFER_LIMIT', 25000.00);

// Include error logger (must be before database)
require_once __DIR__ . '/ErrorLogger.php';

// Include database configuration
require_once __DIR__ . '/database.php';

// Include email configuration
require_once __DIR__ . '/email.php';

/**
 * Security Functions
 */
function sanitizeInput($data) {
    $data = trim($data);
    $data = stripslashes($data);
    $data = htmlspecialchars($data);
    return $data;
}

function generateAccountNumber() {
    $prefix = '10';
    $timestamp = time();
    $random = mt_rand(1000, 9999);
    return $prefix . substr($timestamp, -6) . $random;
}

function generateTransactionId() {
    return 'TXN' . date('Ymd') . mt_rand(100000, 999999);
}

function generateTicketNumber() {
    return 'TKT' . date('Ymd') . mt_rand(1000, 9999);
}

function hashPassword($password) {
    return password_hash($password, PASSWORD_DEFAULT);
}

function verifyPassword($password, $hash) {
    return password_verify($password, $hash);
}

function isLoggedIn() {
    // Check if user has basic session data
    if (!isset($_SESSION['user_id']) || !isset($_SESSION['username'])) {
        return false;
    }

    // For regular users, ensure they don't have admin session flag
    if (!isset($_SESSION['is_admin_session'])) {
        return true;
    }

    // If admin session flag is set, this is an admin session, not a regular user
    return false;
}

function isUserLoggedIn() {
    // Same logic as isLoggedIn for regular users
    return isLoggedIn();
}

function isAdmin() {
    return isset($_SESSION['is_admin']) && $_SESSION['is_admin'] === true;
}

function isAdminLoggedIn() {
    return isset($_SESSION['user_id']) &&
           isset($_SESSION['username']) &&
           isset($_SESSION['is_admin']) &&
           $_SESSION['is_admin'] === true &&
           isset($_SESSION['is_admin_session']) &&
           $_SESSION['is_admin_session'] === true;
}

function requireLogin() {
    if (!isLoggedIn()) {
        header('Location: ' . url('login.php'));
        exit();
    }
}

function requireAdmin() {
    if (!isAdminLoggedIn()) {
        header('Location: ' . url('admin/login.php'));
        exit();
    }
}

function clearUserSession() {
    // Clear only user-related session variables
    $user_vars = ['user_id', 'username', 'first_name', 'last_name', 'email', 'account_number', 'balance', 'kyc_status', 'last_activity'];
    foreach ($user_vars as $var) {
        unset($_SESSION[$var]);
    }
    unset($_SESSION['is_admin']);
    unset($_SESSION['is_admin_session']);
}

function clearAdminSession() {
    // Clear only admin-related session variables
    $admin_vars = ['user_id', 'username', 'first_name', 'last_name', 'email', 'account_number', 'is_admin', 'is_admin_session', 'last_activity'];
    foreach ($admin_vars as $var) {
        unset($_SESSION[$var]);
    }
}

function checkSessionTimeout() {
    if (isset($_SESSION['last_activity'])) {
        if (time() - $_SESSION['last_activity'] > SESSION_TIMEOUT) {
            $isAdmin = isset($_SESSION['is_admin_session']);
            session_destroy();
            if ($isAdmin) {
                header('Location: ' . url('admin/login.php?timeout=1'));
            } else {
                header('Location: ' . url('login.php?timeout=1'));
            }
            exit();
        }
    }
    $_SESSION['last_activity'] = time();
}

function logout($redirectUrl = null) {
    $isAdmin = isset($_SESSION['is_admin_session']);

    // Log the logout activity
    if (isset($_SESSION['user_id'])) {
        logActivity($_SESSION['user_id'], 'User logged out');
    }

    // Clear session
    session_destroy();

    // Redirect based on user type
    if ($redirectUrl) {
        header('Location: ' . $redirectUrl);
    } elseif ($isAdmin) {
        header('Location: ' . url('admin/login.php'));
    } else {
        header('Location: ' . url('login.php'));
    }
    exit();
}

function logActivity($user_id, $action, $table_name = null, $record_id = null, $old_values = null, $new_values = null) {
    try {
        $db = getDB();
        $ip_address = $_SERVER['REMOTE_ADDR'] ?? 'unknown';
        $user_agent = $_SERVER['HTTP_USER_AGENT'] ?? 'unknown';
        
        $sql = "INSERT INTO audit_logs (user_id, action, table_name, record_id, old_values, new_values, ip_address, user_agent) 
                VALUES (?, ?, ?, ?, ?, ?, ?, ?)";
        
        $params = [
            $user_id,
            $action,
            $table_name,
            $record_id,
            $old_values ? json_encode($old_values) : null,
            $new_values ? json_encode($new_values) : null,
            $ip_address,
            $user_agent
        ];
        
        $db->query($sql, $params);
    } catch (Exception $e) {
        error_log("Failed to log activity: " . $e->getMessage());
    }
}

function recordLoginAttempt($username, $success = false) {
    try {
        $db = getDB();
        $ip_address = $_SERVER['REMOTE_ADDR'] ?? 'unknown';

        $sql = "INSERT INTO login_attempts (username, ip_address, success) VALUES (?, ?, ?)";
        $db->query($sql, [$username, $ip_address, $success ? 1 : 0]);

        // Clear old failed attempts for this user/IP if login was successful
        if ($success) {
            clearFailedLoginAttempts($username, $ip_address);
        }
    } catch (Exception $e) {
        error_log("Failed to record login attempt: " . $e->getMessage());
    }
}

function clearFailedLoginAttempts($username, $ip_address) {
    try {
        $db = getDB();
        $sql = "DELETE FROM login_attempts
                WHERE (username = ? OR ip_address = ?)
                AND success = 0";
        $db->query($sql, [$username, $ip_address]);
    } catch (Exception $e) {
        error_log("Failed to clear login attempts: " . $e->getMessage());
    }
}

function getSecuritySettings() {
    static $settings = null;

    if ($settings === null) {
        try {
            $db = getDB();
            $sql = "SELECT * FROM security_settings WHERE is_active = 1 ORDER BY created_at DESC LIMIT 1";
            $result = $db->query($sql);

            if ($result && $result->num_rows > 0) {
                $settings = $result->fetch_assoc();
            } else {
                // Return default settings if none found
                $settings = [
                    'login_attempts_limit' => 5,
                    'lockout_duration' => 30,
                    'otp_enabled' => 1,
                    'otp_expiry_minutes' => 10
                ];
            }
        } catch (Exception $e) {
            error_log("Failed to get security settings: " . $e->getMessage());
            // Return default settings on error
            $settings = [
                'login_attempts_limit' => 5,
                'lockout_duration' => 30,
                'otp_enabled' => 1,
                'otp_expiry_minutes' => 10
            ];
        }
    }

    return $settings;
}

function getFailedLoginAttempts($username, $ip_address) {
    try {
        $db = getDB();
        $security_settings = getSecuritySettings();
        $lockout_duration_minutes = $security_settings['lockout_duration'] ?? 30;
        $lockout_time_seconds = $lockout_duration_minutes * 60;

        $since = date('Y-m-d H:i:s', time() - $lockout_time_seconds);

        $sql = "SELECT COUNT(*) as attempts FROM login_attempts
                WHERE (username = ? OR ip_address = ?)
                AND success = 0 AND attempted_at > ?";

        $result = $db->query($sql, [$username, $ip_address, $since]);
        $row = $result->fetch_assoc();

        return $row['attempts'] ?? 0;
    } catch (Exception $e) {
        error_log("Failed to get login attempts: " . $e->getMessage());
        return 0;
    }
}

function formatCurrency($amount, $currency = DEFAULT_CURRENCY) {
    $amount = $amount ?? 0; // Handle null values

    // Get currency symbol from database
    static $currency_cache = [];

    if (!isset($currency_cache[$currency])) {
        try {
            $db = getDB();
            $result = $db->query("SELECT symbol FROM currencies WHERE code = ? AND status = 'active'", [$currency]);

            if ($result && $row = $result->fetch_assoc()) {
                $symbol = trim($row['symbol']);
                // Clean any malformed symbols but preserve valid currency symbols
                $symbol = preg_replace('/\s+/', '', $symbol); // Remove spaces only, not commas
                $currency_cache[$currency] = $symbol;
            } else {
                // Fallback to simple $ for USD or currency code
                $currency_cache[$currency] = ($currency === 'USD') ? '$' : $currency;
            }
        } catch (Exception $e) {
            // Fallback to simple $ for USD or currency code
            $currency_cache[$currency] = ($currency === 'USD') ? '$' : $currency;
        }
    }

    // Format the number with proper thousands separators
    $formatted_amount = number_format($amount, 2);

    return $currency_cache[$currency] . $formatted_amount;
}

function getUserCurrency($user_id = null) {
    if (!$user_id && isset($_SESSION['user_id'])) {
        $user_id = $_SESSION['user_id'];
    }

    if (!$user_id) {
        return DEFAULT_CURRENCY;
    }

    try {
        $db = getDB();
        $result = $db->query("SELECT currency FROM accounts WHERE id = ?", [$user_id]);

        if ($result && $row = $result->fetch_assoc()) {
            return $row['currency'] ?? DEFAULT_CURRENCY;
        }
    } catch (Exception $e) {
        // Fallback to default currency
    }

    return DEFAULT_CURRENCY;
}

function formatDate($date, $format = 'M d, Y H:i') {
    return date($format, strtotime($date));
}

function redirect($url) {
    // Handle relative URLs
    if (!str_starts_with($url, 'http')) {
        // If it's a relative URL, use our url() helper
        $url = url($url);
    }
    header("Location: $url");
    exit();
}

function setFlashMessage($type, $message) {
    $_SESSION['flash'][$type] = $message;
}

function getFlashMessage($type) {
    if (isset($_SESSION['flash'][$type])) {
        $message = $_SESSION['flash'][$type];
        unset($_SESSION['flash'][$type]);
        return $message;
    }
    return null;
}

function hasFlashMessage($type) {
    return isset($_SESSION['flash'][$type]);
}

/**
 * Get appearance settings from the database
 * @return array Associative array of settings
 */
function getAppearanceSettings() {
    static $settings = null;
    
    if ($settings === null) {
        $settings = [
            'bank_name' => APP_NAME,
            'theme' => 'light',
            'color_scheme' => 'blue',
            'sidebar_collapsed' => 'false',
            'header_fixed' => 'true',
            'animations_enabled' => 'true',
            'logo_url' => '',
            'favicon_url' => ''
        ];
        
        try {
            $db = getDB();

            // Get appearance settings from system_settings table (legacy)
            $result = $db->query("SELECT setting_key, setting_value FROM system_settings WHERE setting_key IN ('theme', 'color_scheme', 'sidebar_collapsed', 'header_fixed', 'animations_enabled', 'logo_url', 'favicon_url')");

            while ($row = $result->fetch_assoc()) {
                $settings[$row['setting_key']] = $row['setting_value'];
            }

            // Get bank name and appearance settings from super_admin_settings table
            $super_admin_result = $db->query("SELECT setting_key, setting_value FROM super_admin_settings WHERE setting_key IN ('site_name', 'theme_color', 'secondary_color', 'admin_theme', 'enable_animations', 'sidebar_style', 'header_style')");

            while ($row = $super_admin_result->fetch_assoc()) {
                switch ($row['setting_key']) {
                    case 'site_name':
                        $settings['bank_name'] = $row['setting_value'];
                        break;
                    case 'theme_color':
                        $settings['primary_color'] = $row['setting_value'];
                        break;
                    case 'secondary_color':
                        $settings['secondary_color'] = $row['setting_value'];
                        break;
                    case 'admin_theme':
                        $settings['theme'] = $row['setting_value'];
                        break;
                    case 'enable_animations':
                        $settings['animations_enabled'] = $row['setting_value'];
                        break;
                    case 'sidebar_style':
                        $settings['sidebar_style'] = $row['setting_value'];
                        break;
                    case 'header_style':
                        $settings['header_fixed'] = ($row['setting_value'] === 'fixed') ? 'true' : 'false';
                        break;
                }
            }
        } catch (Exception $e) {
            error_log("Error loading appearance settings: " . $e->getMessage());
        }
    }
    
    return $settings;
}

/**
 * Get bank name from super admin settings or fallback to APP_NAME
 * @return string
 */
function getBankName() {
    try {
        $db = getDB();
        $result = $db->query("SELECT setting_value FROM super_admin_settings WHERE setting_key = 'site_name'");

        if ($result && $row = $result->fetch_assoc()) {
            return !empty($row['setting_value']) ? $row['setting_value'] : APP_NAME;
        }
    } catch (Exception $e) {
        error_log("Error getting bank name from super admin settings: " . $e->getMessage());
    }

    // Fallback to APP_NAME if database query fails
    return APP_NAME;
}

/**
 * Get site logo from super admin settings
 * @return string
 */
function getSiteLogo() {
    try {
        $db = getDB();
        $result = $db->query("SELECT setting_value FROM super_admin_settings WHERE setting_key = 'site_logo'");

        if ($result && $row = $result->fetch_assoc()) {
            return !empty($row['setting_value']) ? $row['setting_value'] : '';
        }
    } catch (Exception $e) {
        error_log("Error getting site logo from super admin settings: " . $e->getMessage());
    }

    // Return empty string if no logo found
    return '';
}

/**
 * Get Google2FA instance
 * @return \PragmaRX\Google2FA\Google2FA
 */
function getGoogle2FA() {
    return new \PragmaRX\Google2FA\Google2FA();
}

// Initialize session timeout check for logged-in users
if (isLoggedIn()) {
    checkSessionTimeout();
}
?>
