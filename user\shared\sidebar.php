<?php
// Get current page for active navigation
$current_page = basename($_SERVER['PHP_SELF'], '.php');
$current_dir = basename(dirname($_SERVER['PHP_SELF']));

// Get the base URL for proper linking
$protocol = isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? 'https' : 'http';
$host = $_SERVER['HTTP_HOST'];
$base_url = $protocol . '://' . $host . '/online_banking';

// Get user data from session or database
$user_first_name = $_SESSION['first_name'] ?? 'User';
$user_last_name = $_SESSION['last_name'] ?? '';
$user_account_number = $_SESSION['account_number'] ?? '';

// If session data is missing, try to get from database
if (empty($user_first_name) || $user_first_name === 'User') {
    try {
        require_once '../../config/config.php';
        $db = getDB();
        $user_result = $db->query("SELECT first_name, last_name, account_number FROM accounts WHERE id = ?", [$_SESSION['user_id']]);
        if ($user_result && $user_data = $user_result->fetch_assoc()) {
            $user_first_name = $user_data['first_name'];
            $user_last_name = $user_data['last_name'];
            $user_account_number = $user_data['account_number'];
        }
    } catch (Exception $e) {
        // Keep defaults
    }
}

// Get primary color from database (super admin settings)
$primary_color = '#206bc4'; // Default fallback
try {
    if (isset($db)) {
        // Get theme color from super_admin_settings table (correct table)
        $color_result = $db->query("SELECT setting_value FROM super_admin_settings WHERE setting_key = 'theme_color' LIMIT 1");
        if ($color_result && $color_data = $color_result->fetch_assoc()) {
            $primary_color = $color_data['setting_value'];
        }
    }
} catch (Exception $e) {
    // Keep default fallback color
}

// Color adjustment function is already available from dynamic-css.php

// Include dynamic CSS for consistent theming
require_once '../../config/dynamic-css.php';
?>

<!-- Banking Sidebar CSS -->
<style>
    /* Dynamic CSS Variables */
    <?php echo getInlineDynamicCSS(); ?>
    .banking-sidebar {
        width: 280px;
        height: 100vh;
        background: linear-gradient(180deg, var(--background-white) 0%, var(--sidebar-bg) 100%);
        border-right: 1px solid var(--border-color);
        position: fixed;
        left: 0;
        top: 0;
        overflow-y: auto;
        z-index: 1000;
        box-shadow: 2px 0 10px rgba(0, 0, 0, 0.1);
    }

    .sidebar-header {
        padding: 1.5rem;
        border-bottom: 1px solid var(--border-color);
        background: var(--background-white);
    }

    .bank-logo {
        display: flex;
        align-items: center;
        margin-bottom: 1rem;
    }

    .logo-icon {
        width: 40px;
        height: 40px;
        background: linear-gradient(135deg, <?php echo $primary_color; ?>, <?php echo adjustColorBrightness($primary_color, -20); ?>);
        border-radius: 12px;
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        margin-right: 0.75rem;
    }

    .bank-name {
        font-size: 1.25rem;
        font-weight: 700;
        color: var(--text-primary);
    }

    .user-info {
        display: flex;
        align-items: center;
        padding: 1rem;
        background: linear-gradient(135deg, <?php echo $primary_color; ?>15, <?php echo $primary_color; ?>08);
        border-radius: 12px;
        border: 1px solid <?php echo $primary_color; ?>20;
    }

    .user-avatar {
        width: 48px;
        height: 48px;
        background: linear-gradient(135deg, <?php echo $primary_color; ?>, <?php echo adjustColorBrightness($primary_color, -20); ?>);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-weight: 700;
        font-size: 1.1rem;
        margin-right: 0.75rem;
    }

    .user-details {
        flex: 1;
    }

    .user-name {
        font-weight: 600;
        color: var(--text-primary);
        font-size: 0.95rem;
    }

    .account-number {
        font-size: 0.85rem;
        color: var(--text-secondary);
        font-family: monospace;
    }

    .sidebar-nav {
        padding: 1rem 0;
    }

    .nav-section {
        margin-bottom: 1.5rem;
    }

    .nav-section-title {
        font-size: 0.75rem;
        font-weight: 600;
        color: var(--text-muted);
        text-transform: uppercase;
        letter-spacing: 0.05em;
        padding: 0 1.5rem;
        margin-bottom: 0.5rem;
    }

    .nav-list {
        list-style: none;
        margin: 0;
        padding: 0;
    }

    .nav-item {
        margin: 0;
    }

    .nav-link {
        display: flex;
        align-items: center;
        padding: 0.75rem 1.5rem;
        color: var(--text-secondary);
        text-decoration: none;
        transition: all 0.2s ease;
        border-left: 3px solid transparent;
    }

    .nav-link:hover {
        background: var(--primary-light);
        color: var(--primary-color);
        border-left-color: var(--primary-color);
    }

    .nav-link.active {
        background: var(--primary-light);
        color: var(--primary-color);
        border-left-color: var(--primary-color);
        font-weight: 600;
    }

    .nav-icon {
        width: 20px;
        height: 20px;
        margin-right: 0.75rem;
        flex-shrink: 0;
    }

    .sidebar-footer {
        position: absolute;
        bottom: 0;
        left: 0;
        right: 0;
        padding: 1rem;
        border-top: 1px solid var(--border-color);
        background: var(--background-white);
    }

    .logout-link {
        display: flex;
        align-items: center;
        padding: 0.75rem;
        color: var(--danger-color);
        text-decoration: none;
        border-radius: 8px;
        transition: all 0.2s ease;
    }

    .logout-link:hover {
        background: rgba(239, 68, 68, 0.1);
        color: var(--danger-color);
    }

    .logout-icon {
        width: 20px;
        height: 20px;
        margin-right: 0.75rem;
    }

    /* Responsive */
    @media (max-width: 768px) {
        .banking-sidebar {
            width: 100%;
            transform: translateX(-100%);
            transition: transform 0.3s ease;
        }

        .banking-sidebar.open {
            transform: translateX(0);
        }
    }
</style>

<!-- Banking Sidebar Navigation -->
<div class="banking-sidebar">
    <!-- Bank Logo & Brand -->
    <div class="sidebar-header">
        <div class="bank-logo">
            <div class="logo-icon">
                <svg width="32" height="32" fill="currentColor" viewBox="0 0 20 20">
                    <path d="M4 4a2 2 0 00-2 2v1h16V6a2 2 0 00-2-2H4zM18 9H2v5a2 2 0 002 2h12a2 2 0 002-2V9zM4 13a1 1 0 011-1h1a1 1 0 110 2H5a1 1 0 01-1-1zm5-1a1 1 0 100 2h1a1 1 0 100-2H9z"/>
                </svg>
            </div>
            <div class="bank-name">
                <?php echo htmlspecialchars($site_name ?? 'PremierBank Pro'); ?>
            </div>
        </div>
        <div class="user-info">
            <div class="user-avatar">
                <?php echo strtoupper(substr($user_first_name, 0, 1) . substr($user_last_name, 0, 1)); ?>
            </div>
            <div class="user-details">
                <div class="user-name"><?php echo htmlspecialchars($user_first_name . ' ' . $user_last_name); ?></div>
                <div class="account-number">•••• <?php echo substr($user_account_number, -4); ?></div>
            </div>
        </div>
    </div>

    <!-- Main Banking Navigation -->
    <nav class="sidebar-nav">
        <!-- Overview Section -->
        <div class="nav-section">
            <div class="nav-section-title">Overview</div>
            <ul class="nav-list">
                <li class="nav-item">
                    <a href="<?php echo $base_url; ?>/user/dashboard/" class="nav-link <?php echo $current_page === 'index' && $current_dir === 'dashboard' ? 'active' : ''; ?>">
                        <svg class="nav-icon" fill="currentColor" viewBox="0 0 20 20">
                            <path d="M10.707 2.293a1 1 0 00-1.414 0l-7 7a1 1 0 001.414 1.414L4 10.414V17a1 1 0 001 1h2a1 1 0 001-1v-2a1 1 0 011-1h2a1 1 0 011 1v2a1 1 0 001 1h2a1 1 0 001-1v-6.586l.293.293a1 1 0 001.414-1.414l-7-7z"/>
                        </svg>
                        <span>Dashboard</span>
                    </a>
                </li>
                <li class="nav-item">
                    <a href="<?php echo $base_url; ?>/user/accounts/" class="nav-link <?php echo $current_dir === 'accounts' ? 'active' : ''; ?>">
                        <svg class="nav-icon" fill="currentColor" viewBox="0 0 20 20">
                            <path d="M4 4a2 2 0 00-2 2v8a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2H4zm0 2h12v2H4V6zm0 4h12v4H4v-4z"/>
                        </svg>
                        <span>My Accounts</span>
                    </a>
                </li>
                <li class="nav-item">
                    <a href="<?php echo $base_url; ?>/user/profile/" class="nav-link <?php echo $current_dir === 'profile' ? 'active' : ''; ?>">
                        <svg class="nav-icon" fill="currentColor" viewBox="0 0 20 20">
                            <path d="M4 4a2 2 0 00-2 2v1h16V6a2 2 0 00-2-2H4zM18 9H2v5a2 2 0 002 2h12a2 2 0 002-2V9zM4 13a1 1 0 011-1h1a1 1 0 110 2H5a1 1 0 01-1-1zm5-1a1 1 0 100 2h1a1 1 0 100-2H9z"/>
                        </svg>
                        <span>Profile & Settings</span>
                    </a>
                </li>
            </ul>
        </div>

        <!-- Banking Services Section -->
        <div class="nav-section">
            <div class="nav-section-title">Banking Services</div>
            <ul class="nav-list">
                <li class="nav-item">
                    <a href="<?php echo $base_url; ?>/user/transfers/" class="nav-link <?php echo $current_dir === 'transfers' ? 'active' : ''; ?>">
                        <svg class="nav-icon" fill="currentColor" viewBox="0 0 20 20">
                            <path d="M8 5a1 1 0 100 2h5.586l-1.293 1.293a1 1 0 001.414 1.414l3-3a1 1 0 000-1.414l-3-3a1 1 0 10-1.414 1.414L13.586 5H8zM12 15a1 1 0 100-2H6.414l1.293-1.293a1 1 0 10-1.414-1.414l-3 3a1 1 0 000 1.414l3 3a1 1 0 001.414-1.414L6.414 15H12z"/>
                        </svg>
                        <span>Transfer Money</span>
                    </a>
                </li>
                <li class="nav-item">
                    <a href="<?php echo $base_url; ?>/user/payments/" class="nav-link <?php echo $current_dir === 'payments' ? 'active' : ''; ?>">
                        <svg class="nav-icon" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M4 2a1 1 0 011 1v2.101a7.002 7.002 0 0111.601 2.566 1 1 0 11-1.885.666A5.002 5.002 0 005.999 7H9a1 1 0 010 2H4a1 1 0 01-1-1V3a1 1 0 011-1zm.008 9.057a1 1 0 011.276.61A5.002 5.002 0 0014.001 13H11a1 1 0 110-2h5a1 1 0 011 1v5a1 1 0 11-2 0v-2.101a7.002 7.002 0 01-11.601-2.566 1 1 0 01.61-1.276z" clip-rule="evenodd"/>
                        </svg>
                        <span>Bill Payments</span>
                    </a>
                </li>
                <li class="nav-item">
                    <a href="<?php echo $base_url; ?>/user/transactions/" class="nav-link <?php echo $current_dir === 'transactions' ? 'active' : ''; ?>">
                        <svg class="nav-icon" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M3 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1z" clip-rule="evenodd"/>
                        </svg>
                        <span>Transaction History</span>
                    </a>
                </li>
                <li class="nav-item">
                    <a href="<?php echo $base_url; ?>/user/beneficiaries/" class="nav-link <?php echo $current_dir === 'beneficiaries' ? 'active' : ''; ?>">
                        <svg class="nav-icon" fill="currentColor" viewBox="0 0 20 20">
                            <path d="M9 6a3 3 0 11-6 0 3 3 0 016 0zM17 6a3 3 0 11-6 0 3 3 0 016 0zM12.93 17c.046-.327.07-.66.07-1a6.97 6.97 0 00-1.5-4.33A5 5 0 0119 16v1h-6.07zM6 11a5 5 0 015 5v1H1v-1a5 5 0 015-5z"/>
                        </svg>
                        <span>Beneficiaries</span>
                    </a>
                </li>
                <li class="nav-item">
                    <a href="<?php echo $base_url; ?>/user/wire-beneficiaries/" class="nav-link <?php echo $current_dir === 'wire-beneficiaries' ? 'active' : ''; ?>">
                        <svg class="nav-icon" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM4.332 8.027a6.012 6.012 0 011.912-2.706C6.512 5.73 6.974 6 7.5 6A1.5 1.5 0 019 7.5V8a2 2 0 004 0 2 2 0 011.523-1.943A5.977 5.977 0 0116 10c0 .34-.028.675-.083 1H15a2 2 0 00-2 2v2.197A5.973 5.973 0 0110 16v-2a2 2 0 00-2-2 2 2 0 01-2-2 2 2 0 00-1.668-1.973z" clip-rule="evenodd"/>
                        </svg>
                        <span>Wire Beneficiaries</span>
                    </a>
                </li>
            </ul>
        </div>

        <!-- Cards & Digital Section -->
        <div class="nav-section">
            <div class="nav-section-title">Cards & Digital</div>
            <ul class="nav-list">
                <li class="nav-item">
                    <a href="<?php echo $base_url; ?>/user/cards/" class="nav-link <?php echo $current_dir === 'cards' ? 'active' : ''; ?>">
                        <svg class="nav-icon" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M4 4a2 2 0 00-2 2v8a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2H4zm0 2h12v2H4V6zm0 4h12v4H4v-4z" clip-rule="evenodd"/>
                        </svg>
                        <span>My Cards</span>
                    </a>
                </li>
                <li class="nav-item">
                    <a href="<?php echo $base_url; ?>/user/accounts/statements.php" class="nav-link <?php echo $current_page === 'statements' ? 'active' : ''; ?>">
                        <svg class="nav-icon" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M4 4a2 2 0 012-2h4.586A2 2 0 0112 2.586L15.414 6A2 2 0 0116 7.414V16a2 2 0 01-2 2H6a2 2 0 01-2-2V4zm2 6a1 1 0 011-1h6a1 1 0 110 2H7a1 1 0 01-1-1zm1 3a1 1 0 100 2h6a1 1 0 100-2H7z" clip-rule="evenodd"/>
                        </svg>
                        <span>Transaction Statements</span>
                    </a>
                </li>
            </ul>
        </div>

        <!-- Analytics & Reports Section -->
        <div class="nav-section">
            <div class="nav-section-title">Analytics</div>
            <ul class="nav-list">
                <li class="nav-item">
                    <a href="<?php echo $base_url; ?>/analytics/" class="nav-link <?php echo $current_dir === 'analytics' ? 'active' : ''; ?>">
                        <svg class="nav-icon" fill="currentColor" viewBox="0 0 20 20">
                            <path d="M2 11a1 1 0 011-1h2a1 1 0 011 1v5a1 1 0 01-1 1H3a1 1 0 01-1-1v-5zM8 7a1 1 0 011-1h2a1 1 0 011 1v9a1 1 0 01-1 1H9a1 1 0 01-1-1V7zM14 4a1 1 0 011-1h2a1 1 0 011 1v12a1 1 0 01-1 1h-2a1 1 0 01-1-1V4z"/>
                        </svg>
                        <span>Spending Insights</span>
                    </a>
                </li>
                <li class="nav-item">
                    <a href="<?php echo $base_url; ?>/reports/" class="nav-link <?php echo $current_dir === 'reports' ? 'active' : ''; ?>">
                        <svg class="nav-icon" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M6 2a2 2 0 00-2 2v12a2 2 0 002 2h8a2 2 0 002-2V7.414A2 2 0 0015.414 6L12 2.586A2 2 0 0010.586 2H6zm5 6a1 1 0 10-2 0v3.586l-1.293-1.293a1 1 0 10-1.414 1.414l3 3a1 1 0 001.414 0l3-3a1 1 0 00-1.414-1.414L11 11.586V8z" clip-rule="evenodd"/>
                        </svg>
                        <span>Reports</span>
                    </a>
                </li>
            </ul>
        </div>

        <!-- Support & Settings Section -->
        <div class="nav-section">
            <div class="nav-section-title">Support</div>
            <ul class="nav-list">
                <li class="nav-item">
                    <a href="<?php echo $base_url; ?>/user/profile/security.php" class="nav-link <?php echo $current_dir === 'security' ? 'active' : ''; ?>">
                        <svg class="nav-icon" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M18 8a6 6 0 01-7.743 5.743L10 14l-1 1-1 1H6v2H2v-4l4.257-4.257A6 6 0 1118 8zm-6-4a1 1 0 100 2 2 2 0 012 2 1 1 0 102 0 4 4 0 00-4-4z" clip-rule="evenodd"/>
                        </svg>
                        <span>Security Center</span>
                    </a>
                </li>
                <li class="nav-item">
                    <a href="<?php echo $base_url; ?>/user/support/" class="nav-link <?php echo $current_dir === 'support' ? 'active' : ''; ?>">
                        <svg class="nav-icon" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-8-3a1 1 0 00-.867.5 1 1 0 11-1.731-1A3 3 0 0113 8a3.001 3.001 0 01-2 2.83V11a1 1 0 11-2 0v-1a1 1 0 011-1 1 1 0 100-2zm0 8a1 1 0 100-2 1 1 0 000 2z" clip-rule="evenodd"/>
                        </svg>
                        <span>Help & Support</span>
                    </a>
                </li>
                <li class="nav-item">
                    <a href="<?php echo $base_url; ?>/user/profile/" class="nav-link <?php echo $current_dir === 'profile' ? 'active' : ''; ?>">
                        <svg class="nav-icon" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M11.49 3.17c-.38-1.56-2.6-1.56-2.98 0a1.532 1.532 0 01-2.286.948c-1.372-.836-2.942.734-2.106 2.106.54.886.061 2.042-.947 2.287-1.561.379-1.561 2.6 0 2.978a1.532 1.532 0 01.947 2.287c-.836 1.372.734 2.942 2.106 2.106a1.532 1.532 0 012.287.947c.379 1.561 2.6 1.561 2.978 0a1.533 1.533 0 012.287-.947c1.372.836 2.942-.734 2.106-2.106a1.533 1.533 0 01.947-2.287c1.561-.379 1.561-2.6 0-2.978a1.532 1.532 0 01-.947-2.287c.836-1.372-.734-2.942-2.106-2.106a1.532 1.532 0 01-2.287-.947zM10 13a3 3 0 100-6 3 3 0 000 6z" clip-rule="evenodd"/>
                        </svg>
                        <span>Profile & Settings</span>
                    </a>
                </li>
            </ul>
        </div>
    </nav>

    <!-- Logout Section -->
    <div class="sidebar-footer">
        <a href="<?php echo $base_url; ?>/auth/logout.php" class="logout-link">
            <svg class="logout-icon" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M3 3a1 1 0 00-1 1v12a1 1 0 102 0V4a1 1 0 01-1-1zm10.293 9.293a1 1 0 001.414 1.414l3-3a1 1 0 000-1.414l-3-3a1 1 0 10-1.414 1.414L14.586 9H7a1 1 0 100 2h7.586l-1.293 1.293z" clip-rule="evenodd"/>
            </svg>
            <span>Sign Out</span>
        </a>
    </div>
</div>
