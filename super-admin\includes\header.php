<?php
/**
 * Super Admin Header Component
 * Consistent header layout for all super admin pages
 */

// Enable error reporting for debugging
error_reporting(E_ALL);
ini_set('display_errors', 1);
ini_set('log_errors', 1);
ini_set('error_log', __DIR__ . '/../../logs/error.log');

// Start session if not already started
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

try {
    error_log("Super Admin Header: Starting header load");

    // Include authentication functions
    require_once 'auth.php';
    error_log("Super Admin Header: Successfully included auth.php");

    // Require super admin authentication
    requireSuperAdminAuth();
    error_log("Super Admin Header: Successfully completed authentication check");

} catch (Exception $e) {
    error_log("Super Admin Header Error: " . $e->getMessage() . " in " . $e->getFile() . " on line " . $e->getLine());
    die("Error loading super admin header: " . $e->getMessage());
} catch (Error $e) {
    error_log("Super Admin Header Fatal Error: " . $e->getMessage() . " in " . $e->getFile() . " on line " . $e->getLine());
    die("Fatal error loading super admin header: " . $e->getMessage());
}

// Get session info
$session_info = getSuperAdminSessionInfo();
$current_page = basename($_SERVER['PHP_SELF']);
$page_title = $page_title ?? 'Super Admin Panel';
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo htmlspecialchars($page_title); ?> - Super Admin</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <style>
        :root {
            --super-admin-primary: #dc2626;
            --super-admin-secondary: #b91c1c;
            --super-admin-dark: #991b1b;
            --sidebar-width: 280px;
        }
        
        body {
            font-family: 'Inter', sans-serif;
            background: #f8fafc;
            margin: 0;
            padding: 0;
        }
        
        /* Sidebar Styles */
        .sidebar {
            position: fixed;
            top: 0;
            left: 0;
            height: 100vh;
            width: var(--sidebar-width);
            background: linear-gradient(180deg, var(--super-admin-primary) 0%, var(--super-admin-secondary) 100%);
            color: white;
            z-index: 1000;
            overflow-y: auto;
            transition: all 0.3s ease;
        }
        
        .sidebar-header {
            padding: 25px 20px;
            border-bottom: 1px solid rgba(255,255,255,0.1);
            text-align: center;
        }
        
        .sidebar-logo {
            font-size: 24px;
            font-weight: 700;
            margin-bottom: 5px;
            text-shadow: 0 2px 4px rgba(0,0,0,0.2);
        }
        
        .sidebar-subtitle {
            font-size: 12px;
            opacity: 0.8;
            text-transform: uppercase;
            letter-spacing: 1px;
            font-weight: 500;
        }
        
        .sidebar-nav {
            padding: 20px 0;
        }
        
        .nav-item {
            margin: 0;
        }
        
        .nav-link {
            color: rgba(255,255,255,0.8);
            padding: 15px 25px;
            text-decoration: none;
            display: flex;
            align-items: center;
            transition: all 0.3s ease;
            border: none;
            background: none;
        }
        
        .nav-link:hover {
            background: rgba(255,255,255,0.1);
            color: white;
            text-decoration: none;
        }
        
        .nav-link.active {
            background: rgba(255,255,255,0.15);
            color: white;
            border-right: 4px solid white;
        }
        
        .nav-link i {
            width: 20px;
            margin-right: 12px;
            text-align: center;
        }
        
        .sidebar-footer {
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            padding: 20px;
            border-top: 1px solid rgba(255,255,255,0.1);
            background: rgba(0,0,0,0.1);
        }
        
        .user-info {
            display: flex;
            align-items: center;
            margin-bottom: 15px;
        }
        
        .user-avatar {
            width: 40px;
            height: 40px;
            background: rgba(255,255,255,0.2);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 12px;
        }
        
        .user-details h6 {
            margin: 0;
            font-size: 14px;
            font-weight: 600;
        }
        
        .user-details small {
            opacity: 0.7;
            font-size: 12px;
        }
        
        .logout-btn {
            background: rgba(255,255,255,0.1);
            border: 1px solid rgba(255,255,255,0.2);
            color: white;
            padding: 8px 16px;
            border-radius: 6px;
            font-size: 12px;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            transition: all 0.3s ease;
        }
        
        .logout-btn:hover {
            background: rgba(255,255,255,0.2);
            color: white;
            text-decoration: none;
        }
        
        /* Main Content Styles */
        .main-content {
            margin-left: var(--sidebar-width);
            min-height: 100vh;
            background: #f8fafc;
        }
        
        .top-navbar {
            background: white;
            padding: 20px 30px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.05);
            border-bottom: 1px solid #e5e7eb;
        }
        
        .navbar-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .page-title {
            margin: 0;
            font-size: 28px;
            font-weight: 700;
            color: #1f2937;
        }
        
        .page-subtitle {
            color: #6b7280;
            font-size: 14px;
            margin: 5px 0 0 0;
        }
        
        .navbar-actions {
            display: flex;
            align-items: center;
            gap: 15px;
        }
        
        .session-info {
            background: #f3f4f6;
            padding: 8px 12px;
            border-radius: 6px;
            font-size: 12px;
            color: #374151;
        }
        
        .breadcrumb {
            background: none;
            padding: 15px 30px 0 30px;
            margin: 0;
        }
        
        .breadcrumb-item a {
            color: var(--super-admin-primary);
            text-decoration: none;
        }
        
        .breadcrumb-item.active {
            color: #6b7280;
        }
        
        .content-wrapper {
            padding: 30px;
        }
        
        /* Responsive Design */
        @media (max-width: 768px) {
            .sidebar {
                transform: translateX(-100%);
            }
            
            .sidebar.show {
                transform: translateX(0);
            }
            
            .main-content {
                margin-left: 0;
            }
            
            .mobile-menu-btn {
                display: block;
            }
        }
        
        .mobile-menu-btn {
            display: none;
            background: var(--super-admin-primary);
            color: white;
            border: none;
            padding: 10px 15px;
            border-radius: 6px;
            font-size: 16px;
        }
        
        /* Custom Scrollbar for Sidebar */
        .sidebar::-webkit-scrollbar {
            width: 6px;
        }
        
        .sidebar::-webkit-scrollbar-track {
            background: rgba(255,255,255,0.1);
        }
        
        .sidebar::-webkit-scrollbar-thumb {
            background: rgba(255,255,255,0.3);
            border-radius: 3px;
        }
        
        .sidebar::-webkit-scrollbar-thumb:hover {
            background: rgba(255,255,255,0.5);
        }

        /* Activity Timeline Styles */
        .activity-timeline {
            position: relative;
            padding: 1rem 0;
        }

        .activity-item {
            position: relative;
            display: flex;
            align-items-start;
            padding: 0 0 1.5rem 0;
            margin-left: 1rem;
        }

        .activity-item:not(.last)::before {
            content: '';
            position: absolute;
            left: 20px;
            top: 40px;
            bottom: -10px;
            width: 2px;
            background: linear-gradient(to bottom, #e5e7eb, #f3f4f6);
        }

        .activity-icon {
            position: relative;
            z-index: 2;
            margin-right: 1rem;
        }

        .activity-icon .icon-wrapper {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            border: 3px solid white;
        }

        .activity-content {
            flex: 1;
            background: white;
            border-radius: 12px;
            padding: 1rem 1.25rem;
            box-shadow: 0 2px 8px rgba(0,0,0,0.06);
            border: 1px solid #e5e7eb;
            transition: all 0.3s ease;
        }

        .activity-content:hover {
            box-shadow: 0 4px 16px rgba(0,0,0,0.1);
            transform: translateY(-1px);
        }

        .activity-header {
            display: flex;
            justify-content: between;
            align-items: flex-start;
            margin-bottom: 0.5rem;
        }

        .activity-title {
            font-size: 0.95rem;
            font-weight: 600;
            color: #1f2937;
            margin: 0;
            flex: 1;
            line-height: 1.4;
        }

        .activity-time {
            font-size: 0.8rem;
            color: #6b7280;
            font-weight: 500;
            white-space: nowrap;
            margin-left: 1rem;
        }

        .activity-details {
            display: flex;
            gap: 1rem;
            font-size: 0.8rem;
            color: #6b7280;
        }

        .activity-user,
        .activity-ip {
            display: flex;
            align-items: center;
            gap: 0.25rem;
        }

        .activity-user i,
        .activity-ip i {
            font-size: 0.75rem;
            opacity: 0.7;
        }

        /* Responsive adjustments */
        @media (max-width: 768px) {
            .activity-header {
                flex-direction: column;
                align-items: flex-start;
            }

            .activity-time {
                margin-left: 0;
                margin-top: 0.25rem;
            }

            .activity-details {
                flex-direction: column;
                gap: 0.25rem;
            }
        }

        /* Audit Trail Styles */
        .audit-trail-container {
            position: relative;
            padding: 1rem 0;
        }

        .audit-entry {
            position: relative;
            display: flex;
            align-items-start;
            padding: 0 0 1.5rem 0;
            margin-left: 2rem;
        }

        .audit-entry:not(.last)::before {
            content: '';
            position: absolute;
            left: 35px;
            top: 50px;
            bottom: -10px;
            width: 2px;
            background: linear-gradient(to bottom, #e5e7eb, #f3f4f6);
        }

        .audit-number {
            position: absolute;
            left: -2rem;
            top: 0.5rem;
            z-index: 3;
        }

        .entry-number {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            width: 28px;
            height: 28px;
            background: linear-gradient(135deg, #6b7280, #9ca3af);
            color: white;
            border-radius: 50%;
            font-size: 0.75rem;
            font-weight: 600;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            border: 2px solid white;
        }

        .audit-icon {
            position: relative;
            z-index: 2;
            margin-right: 1rem;
        }

        .audit-icon .icon-wrapper {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            box-shadow: 0 2px 8px rgba(0,0,0,0.15);
            border: 3px solid white;
        }

        .audit-content {
            flex: 1;
            background: white;
            border-radius: 12px;
            padding: 1.25rem;
            box-shadow: 0 2px 8px rgba(0,0,0,0.08);
            border: 1px solid #e5e7eb;
            transition: all 0.3s ease;
            position: relative;
        }

        .audit-content:hover {
            box-shadow: 0 4px 16px rgba(0,0,0,0.12);
            transform: translateY(-2px);
            border-color: var(--super-admin-primary);
        }

        .audit-header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 0.75rem;
        }

        .audit-title {
            font-size: 1rem;
            font-weight: 600;
            color: #1f2937;
            margin: 0;
            flex: 1;
            line-height: 1.4;
        }

        .audit-time {
            font-size: 0.8rem;
            color: #6b7280;
            font-weight: 500;
            white-space: nowrap;
            margin-left: 1rem;
            background: #f3f4f6;
            padding: 0.25rem 0.5rem;
            border-radius: 6px;
        }

        .audit-meta {
            display: flex;
            gap: 1rem;
            margin-bottom: 0.75rem;
            font-size: 0.8rem;
            color: #6b7280;
            flex-wrap: wrap;
        }

        .audit-user,
        .audit-ip,
        .audit-action {
            display: flex;
            align-items: center;
            gap: 0.25rem;
            background: #f8fafc;
            padding: 0.25rem 0.5rem;
            border-radius: 6px;
            border: 1px solid #e5e7eb;
        }

        .audit-user i,
        .audit-ip i,
        .audit-action i {
            font-size: 0.7rem;
            opacity: 0.7;
        }

        .audit-actions {
            margin-top: 0.75rem;
        }

        .details-card {
            background: #f8fafc;
            border: 1px solid #e5e7eb;
            border-radius: 8px;
            padding: 1rem;
            margin-top: 0.5rem;
        }

        .details-content {
            margin: 0;
            font-size: 0.8rem;
            color: #374151;
            background: none;
            border: none;
            white-space: pre-wrap;
            word-wrap: break-word;
        }

        /* Responsive adjustments for audit trail */
        @media (max-width: 768px) {
            .audit-entry {
                margin-left: 1.5rem;
            }

            .audit-number {
                left: -1.5rem;
            }

            .entry-number {
                width: 24px;
                height: 24px;
                font-size: 0.7rem;
            }

            .audit-header {
                flex-direction: column;
                align-items: flex-start;
            }

            .audit-time {
                margin-left: 0;
                margin-top: 0.5rem;
            }

            .audit-meta {
                flex-direction: column;
                gap: 0.5rem;
            }

            .audit-icon .icon-wrapper {
                width: 35px;
                height: 35px;
            }
        }

        /* Collapse Icon Animation */
        .collapse-icon {
            transition: transform 0.3s ease;
        }

        .collapsed .collapse-icon {
            transform: rotate(-90deg);
        }
    </style>
</head>
<body>
    <!-- Sidebar -->
    <div class="sidebar" id="sidebar">
        <div class="sidebar-header">
            <div class="sidebar-logo">
                <i class="fas fa-crown"></i> Super Admin
            </div>
            <div class="sidebar-subtitle">System Administration</div>
        </div>
        
        <nav class="sidebar-nav">
            <?php
            $menu_items = getSuperAdminMenuItems();
            foreach ($menu_items as $item):
            ?>
                <div class="nav-item">
                    <a href="<?php echo $item['url']; ?>" 
                       class="nav-link <?php echo $item['active'] ? 'active' : ''; ?>">
                        <i class="<?php echo $item['icon']; ?>"></i>
                        <?php echo $item['title']; ?>
                    </a>
                </div>
            <?php endforeach; ?>
        </nav>
        
        <div class="sidebar-footer">
            <div class="user-info">
                <div class="user-avatar">
                    <i class="fas fa-user-shield"></i>
                </div>
                <div class="user-details">
                    <h6><?php echo htmlspecialchars($session_info['username']); ?></h6>
                    <small>Session: <?php echo formatDuration($session_info['session_duration']); ?></small>
                </div>
            </div>
            <a href="logout.php" class="logout-btn">
                <i class="fas fa-sign-out-alt"></i> Logout
            </a>
        </div>
    </div>
    
    <!-- Main Content -->
    <div class="main-content">
        <!-- Top Navbar -->
        <div class="top-navbar">
            <div class="navbar-content">
                <div>
                    <button class="mobile-menu-btn" onclick="toggleSidebar()">
                        <i class="fas fa-bars"></i>
                    </button>
                    <h1 class="page-title"><?php echo htmlspecialchars($page_title); ?></h1>
                    <?php if (isset($page_subtitle)): ?>
                        <p class="page-subtitle"><?php echo htmlspecialchars($page_subtitle); ?></p>
                    <?php endif; ?>
                </div>
                
                <div class="navbar-actions">
                    <div class="session-info">
                        <i class="fas fa-clock"></i> 
                        Session: <?php echo formatDuration($session_info['session_duration']); ?>
                    </div>
                    <div class="session-info">
                        <i class="fas fa-map-marker-alt"></i> 
                        IP: <?php echo htmlspecialchars($session_info['ip_address']); ?>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Breadcrumbs -->
        <?php
        $breadcrumbs = generateBreadcrumbs($current_page);
        if (count($breadcrumbs) > 1):
        ?>
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <?php foreach ($breadcrumbs as $index => $crumb): ?>
                        <li class="breadcrumb-item <?php echo $crumb['url'] ? '' : 'active'; ?>">
                            <?php if ($crumb['url']): ?>
                                <a href="<?php echo $crumb['url']; ?>"><?php echo htmlspecialchars($crumb['title']); ?></a>
                            <?php else: ?>
                                <?php echo htmlspecialchars($crumb['title']); ?>
                            <?php endif; ?>
                        </li>
                    <?php endforeach; ?>
                </ol>
            </nav>
        <?php endif; ?>
        
        <!-- Content Wrapper -->
        <div class="content-wrapper">
            <!-- Page content will be inserted here -->
