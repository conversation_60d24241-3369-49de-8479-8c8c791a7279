<?php
session_start();

// Check if user is logged in (admin or regular user)
if (!isset($_SESSION['user_id'])) {
    http_response_code(401);
    header('Content-Type: application/json');
    echo json_encode(['success' => false, 'message' => 'Not authenticated']);
    exit;
}

// Extend the session by updating the last activity time
$_SESSION['last_activity'] = time();

// Return success response
header('Content-Type: application/json');
echo json_encode([
    'success' => true,
    'message' => 'Session extended successfully',
    'timestamp' => date('Y-m-d H:i:s')
]);
?>
