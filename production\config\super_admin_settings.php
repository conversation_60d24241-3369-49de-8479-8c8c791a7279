<?php
/**
 * Super Admin Settings Management
 * Handles configuration settings that only super admins can modify
 */

// Include required dependencies
require_once __DIR__ . '/database.php';
require_once __DIR__ . '/config.php';

/**
 * Check if current user is a super admin
 */
function isSuperAdmin() {
    if (!isLoggedIn()) {
        return false;
    }
    
    try {
        $db = getDB();
        $user_id = $_SESSION['user_id'];
        
        $result = $db->query("SELECT is_super_admin, role FROM accounts WHERE id = ?", [$user_id]);
        if ($result && $row = $result->fetch_assoc()) {
            return ($row['is_super_admin'] == 1 || $row['role'] === 'super_admin');
        }
        
        return false;
    } catch (Exception $e) {
        error_log("Error checking super admin status: " . $e->getMessage());
        return false;
    }
}

/**
 * Require super admin access (redirect if not super admin)
 */
function requireSuperAdmin() {
    if (!isSuperAdmin()) {
        setFlashMessage('error', 'Access denied. Super admin privileges required.');
        redirect('admin/dashboard.php');
    }
}

/**
 * Get all super admin settings
 */
function getSuperAdminSettings() {
    static $settings = null;
    
    if ($settings === null) {
        $settings = [];
        
        try {
            $db = getDB();
            $result = $db->query("SELECT setting_key, setting_value, setting_type, is_encrypted FROM super_admin_settings ORDER BY setting_key");
            
            while ($row = $result->fetch_assoc()) {
                $value = $row['setting_value'];
                
                // Decrypt if encrypted
                if ($row['is_encrypted'] == 1) {
                    $value = decryptSetting($value);
                }
                
                // Convert boolean strings to actual booleans
                if ($row['setting_type'] === 'boolean') {
                    $value = ($value === '1' || strtolower($value) === 'true');
                }
                
                // Convert numbers
                if ($row['setting_type'] === 'number') {
                    $value = is_numeric($value) ? (float)$value : 0;
                }
                
                $settings[$row['setting_key']] = $value;
            }
        } catch (Exception $e) {
            error_log("Error loading super admin settings: " . $e->getMessage());
            
            // Return default settings if database fails
            $settings = getDefaultSuperAdminSettings();
        }
    }
    
    return $settings;
}

/**
 * Get a specific super admin setting
 */
function getSuperAdminSetting($key, $default = null) {
    $settings = getSuperAdminSettings();
    return isset($settings[$key]) ? $settings[$key] : $default;
}

/**
 * Update a super admin setting
 */
function updateSuperAdminSetting($key, $value, $reason = null) {
    if (!isSuperAdmin()) {
        throw new Exception("Access denied. Super admin privileges required.");
    }
    
    try {
        $db = getDB();
        $user_id = $_SESSION['user_id'];
        
        // Get current value for audit log
        $current_result = $db->query("SELECT setting_value, is_encrypted FROM super_admin_settings WHERE setting_key = ?", [$key]);
        $old_value = null;
        $is_encrypted = 0;
        
        if ($current_result && $current_row = $current_result->fetch_assoc()) {
            $old_value = $current_row['setting_value'];
            $is_encrypted = $current_row['is_encrypted'];
            
            if ($is_encrypted == 1) {
                $old_value = decryptSetting($old_value);
            }
        }
        
        // Encrypt sensitive settings
        $encrypted_value = $value;
        if (in_array($key, ['smtp_password'])) {
            $encrypted_value = encryptSetting($value);
            $is_encrypted = 1;
        }
        
        // Convert boolean to string
        if (is_bool($value)) {
            $encrypted_value = $value ? '1' : '0';
        }
        
        // Update or insert setting
        $sql = "INSERT INTO super_admin_settings (setting_key, setting_value, is_encrypted, updated_by) 
                VALUES (?, ?, ?, ?) 
                ON DUPLICATE KEY UPDATE 
                setting_value = VALUES(setting_value), 
                is_encrypted = VALUES(is_encrypted),
                updated_by = VALUES(updated_by),
                updated_at = CURRENT_TIMESTAMP";
        
        $db->query($sql, [$key, $encrypted_value, $is_encrypted, $user_id]);
        
        // Log the change
        logSuperAdminSettingChange($key, $old_value, $value, $user_id, $reason);
        
        // Clear cached settings (reset static variable)
        // Note: PHP doesn't have a direct way to clear static variables,
        // so we'll rely on the function to reload from database on next call
        
        return true;
        
    } catch (Exception $e) {
        error_log("Error updating super admin setting '$key': " . $e->getMessage());
        throw $e;
    }
}

/**
 * Log super admin setting changes for audit trail
 */
function logSuperAdminSettingChange($key, $old_value, $new_value, $user_id, $reason = null) {
    try {
        $db = getDB();
        
        $sql = "INSERT INTO super_admin_settings_audit 
                (setting_key, old_value, new_value, changed_by, change_reason, ip_address, user_agent) 
                VALUES (?, ?, ?, ?, ?, ?, ?)";
        
        $ip_address = $_SERVER['REMOTE_ADDR'] ?? null;
        $user_agent = $_SERVER['HTTP_USER_AGENT'] ?? null;
        
        $db->query($sql, [
            $key, 
            $old_value, 
            $new_value, 
            $user_id, 
            $reason, 
            $ip_address, 
            $user_agent
        ]);
        
    } catch (Exception $e) {
        error_log("Error logging super admin setting change: " . $e->getMessage());
    }
}

/**
 * Get default super admin settings (fallback)
 */
function getDefaultSuperAdminSettings() {
    return [
        'site_name' => 'Online Banking System',
        'site_url' => 'http://localhost/online_banking',
        'support_email' => '<EMAIL>',
        'support_phone' => '1-800-BANKING',
        'security_email' => '<EMAIL>',
        'security_phone' => '1-800-SECURITY',
        'noreply_email' => '<EMAIL>',
        'admin_email' => '<EMAIL>',
        'smtp_host' => 'smtp.hostinger.com',
        'smtp_port' => 465,
        'smtp_username' => '<EMAIL>',
        'smtp_password' => 'Money2025@Demo#',
        'smtp_encryption' => 'ssl',
        'smtp_from_email' => '<EMAIL>',
        'smtp_from_name' => 'Online Banking System',
        'email_footer_text' => 'Your trusted financial partner',
        'max_login_attempts' => 5,
        'session_timeout' => 30,
        'otp_expiry_minutes' => 10,
        'maintenance_mode' => false
    ];
}

/**
 * Simple encryption for sensitive settings
 */
function encryptSetting($value) {
    $key = 'SuperAdminKey2024!'; // In production, use a proper key management system
    return base64_encode(openssl_encrypt($value, 'AES-256-CBC', $key, 0, substr(md5($key), 0, 16)));
}

/**
 * Simple decryption for sensitive settings
 */
function decryptSetting($encrypted_value) {
    $key = 'SuperAdminKey2024!'; // In production, use a proper key management system
    return openssl_decrypt(base64_decode($encrypted_value), 'AES-256-CBC', $key, 0, substr(md5($key), 0, 16));
}

/**
 * Get dynamic contact information for email templates
 */
function getEmailContactInfo() {
    $settings = getSuperAdminSettings();
    
    return [
        'site_name' => $settings['site_name'] ?? 'Online Banking System',
        'site_url' => $settings['site_url'] ?? 'http://localhost/online_banking',
        'support_email' => $settings['support_email'] ?? '<EMAIL>',
        'support_phone' => $settings['support_phone'] ?? '1-800-BANKING',
        'security_email' => $settings['security_email'] ?? '<EMAIL>',
        'security_phone' => $settings['security_phone'] ?? '1-800-SECURITY',
        'noreply_email' => $settings['noreply_email'] ?? '<EMAIL>',
        'admin_email' => $settings['admin_email'] ?? '<EMAIL>',
        'email_footer_text' => $settings['email_footer_text'] ?? 'Your trusted financial partner',
        'help_center_url' => $settings['help_center_url'] ?? '#',
        'privacy_policy_url' => $settings['privacy_policy_url'] ?? '#',
        'terms_of_service_url' => $settings['terms_of_service_url'] ?? '#'
    ];
}

/**
 * Get SMTP configuration from super admin settings
 */
function getSMTPConfig() {
    $settings = getSuperAdminSettings();
    
    return [
        'host' => $settings['smtp_host'] ?? 'smtp.hostinger.com',
        'port' => $settings['smtp_port'] ?? 465,
        'username' => $settings['smtp_username'] ?? '<EMAIL>',
        'password' => $settings['smtp_password'] ?? 'Money2025@Demo#',
        'encryption' => $settings['smtp_encryption'] ?? 'ssl',
        'from_email' => $settings['smtp_from_email'] ?? '<EMAIL>',
        'from_name' => $settings['smtp_from_name'] ?? 'Online Banking System'
    ];
}

/**
 * Validate super admin setting value
 */
function validateSuperAdminSetting($key, $value) {
    $errors = [];
    
    switch ($key) {
        case 'support_email':
        case 'security_email':
        case 'noreply_email':
        case 'admin_email':
        case 'smtp_username':
        case 'smtp_from_email':
            if (!filter_var($value, FILTER_VALIDATE_EMAIL)) {
                $errors[] = "Invalid email format for $key";
            }
            break;
            
        case 'site_url':
        case 'privacy_policy_url':
        case 'terms_of_service_url':
        case 'help_center_url':
            if (!empty($value) && !filter_var($value, FILTER_VALIDATE_URL)) {
                $errors[] = "Invalid URL format for $key";
            }
            break;
            
        case 'smtp_port':
        case 'max_login_attempts':
        case 'session_timeout':
        case 'otp_expiry_minutes':
            if (!is_numeric($value) || $value < 1) {
                $errors[] = "$key must be a positive number";
            }
            break;
            
        case 'site_name':
        case 'smtp_from_name':
            if (empty(trim($value))) {
                $errors[] = "$key cannot be empty";
            }
            break;
    }
    
    return $errors;
}
?>
