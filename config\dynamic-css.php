<?php
/**
 * Dynamic CSS Generation System
 * Generates CSS variables from database settings
 */

require_once __DIR__ . '/config.php';

/**
 * Generate dynamic CSS variables from database settings
 */
function generateDynamicCSS() {
    try {
        $db = getDB();
        
        // Get colors and appearance settings from super admin settings
        $settings_result = $db->query("SELECT setting_key, setting_value FROM super_admin_settings WHERE setting_key IN ('theme_color', 'secondary_color', 'accent_color', 'success_color', 'warning_color', 'danger_color', 'card_header_gradient')");

        $settings = [];
        while ($row = $settings_result->fetch_assoc()) {
            $settings[$row['setting_key']] = $row['setting_value'];
        }
        
        // Set default colors with #206bc4 as primary
        $primary_color = $settings['theme_color'] ?? '#206bc4';
        $secondary_color = $settings['secondary_color'] ?? '#6c757d';
        $accent_color = $settings['accent_color'] ?? '#10b981';
        $success_color = $settings['success_color'] ?? '#10b981';
        $warning_color = $settings['warning_color'] ?? '#f59e0b';
        $danger_color = $settings['danger_color'] ?? '#ef4444';

        // Get gradient setting
        $enable_gradient = ($settings['card_header_gradient'] ?? '1') === '1';
        
        // Generate hover and variant colors
        $primary_hover = adjustColorBrightness($primary_color, -15);
        $primary_light = adjustColorBrightness($primary_color, 40);
        $primary_dark = adjustColorBrightness($primary_color, -25);
        
        // Generate gradient CSS based on setting
        $card_header_gradient = $enable_gradient
            ? "linear-gradient(135deg, {$primary_color} 0%, {$primary_dark} 100%)"
            : $primary_color;

        $card_gradient = $enable_gradient
            ? "linear-gradient(135deg, {$primary_color} 0%, {$primary_dark} 100%)"
            : $primary_color;

        // Generate CSS
        $css = ":root {
    /* Primary Brand Colors */
    --primary-color: {$primary_color};
    --primary-hover: {$primary_hover};
    --primary-light: {$primary_light};
    --primary-dark: {$primary_dark};
    --admin-primary: {$primary_color};

    /* Gradient Settings */
    --card-header-gradient: {$card_header_gradient};
    --card-gradient: {$card_gradient};

    /* Secondary Colors */
    --secondary-color: {$secondary_color};
    --accent-color: {$accent_color};
    
    /* Status Colors */
    --success-color: {$success_color};
    --warning-color: {$warning_color};
    --danger-color: {$danger_color};
    
    /* Text Colors */
    --text-primary: #1f2937;
    --text-secondary: #6b7280;
    --text-muted: #9ca3af;
    
    /* Background Colors */
    --background-light: #f8fafc;
    --background-white: #ffffff;
    --sidebar-bg: #f8fafc;
    
    /* Border Colors */
    --border-color: #e5e7eb;
    --border-light: #f3f4f6;
    --admin-sidebar-border: #e9ecef;
    
    /* Shadow Variables */
    --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
    --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
    --card-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
    --card-shadow-hover: 0 4px 12px 0 rgba(0, 0, 0, 0.15);
    
    /* Border Radius */
    --radius-sm: 0.375rem;
    --radius-md: 0.5rem;
    --radius-lg: 0.75rem;
    --radius-xl: 1rem;
    
    /* Admin Specific */
    --admin-sidebar-bg: #ffffff;
    --admin-text-primary: #1e293b;
    --admin-text-secondary: #64748b;
    --admin-text-muted: #94a3b8;
}";
        
        return $css;
        
    } catch (Exception $e) {
        // Fallback CSS with default colors
        return ":root {
    --primary-color: #206bc4;
    --primary-hover: #1a5490;
    --primary-light: #4a8bc2;
    --primary-dark: #164a73;
    --admin-primary: #206bc4;
    --secondary-color: #6c757d;
    --accent-color: #10b981;
    --success-color: #10b981;
    --warning-color: #f59e0b;
    --danger-color: #ef4444;
    --text-primary: #1f2937;
    --text-secondary: #6b7280;
    --text-muted: #9ca3af;
    --background-light: #f8fafc;
    --background-white: #ffffff;
    --border-color: #e5e7eb;
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    --radius-md: 0.5rem;
}";
    }
}

/**
 * Adjust color brightness for dynamic CSS
 */
function adjustColorBrightness($hex, $percent) {
    $hex = ltrim($hex, '#');
    
    if (strlen($hex) == 3) {
        $hex = $hex[0] . $hex[0] . $hex[1] . $hex[1] . $hex[2] . $hex[2];
    }
    
    $r = hexdec(substr($hex, 0, 2));
    $g = hexdec(substr($hex, 2, 2));
    $b = hexdec(substr($hex, 4, 2));

    $r = max(0, min(255, $r + ($r * $percent / 100)));
    $g = max(0, min(255, $g + ($g * $percent / 100)));
    $b = max(0, min(255, $b + ($b * $percent / 100)));

    return sprintf("#%02x%02x%02x", $r, $g, $b);
}

/**
 * Output dynamic CSS with proper headers
 */
function outputDynamicCSS() {
    header('Content-Type: text/css');
    header('Cache-Control: public, max-age=3600'); // Cache for 1 hour
    echo generateDynamicCSS();
}

/**
 * Get inline dynamic CSS for embedding in HTML
 */
function getInlineDynamicCSS() {
    return generateDynamicCSS();
}
?>
