<?php
require_once '../config/config.php';
requireAdmin();

$page_title = 'All Transactions';

// Pagination settings
$records_per_page = 25;
$current_page = intval($_GET['page'] ?? 1);
$offset = ($current_page - 1) * $records_per_page;

// Filter parameters
$filter_type = $_GET['type'] ?? '';
$filter_status = $_GET['status'] ?? '';
$filter_user = $_GET['user'] ?? '';
$filter_date_from = $_GET['date_from'] ?? '';
$filter_date_to = $_GET['date_to'] ?? '';
$filter_amount_min = floatval($_GET['amount_min'] ?? 0);
$filter_amount_max = floatval($_GET['amount_max'] ?? 0);

// Build WHERE clause for filters
$where_conditions = [];
$params = [];

if (!empty($filter_type)) {
    $where_conditions[] = "at.transaction_type = ?";
    $params[] = $filter_type;
}

if (!empty($filter_status)) {
    $where_conditions[] = "at.status = ?";
    $params[] = $filter_status;
}

if (!empty($filter_user)) {
    $where_conditions[] = "(a.first_name LIKE ? OR a.last_name LIKE ? OR a.username LIKE ?)";
    $search_term = "%$filter_user%";
    $params[] = $search_term;
    $params[] = $search_term;
    $params[] = $search_term;
}

if (!empty($filter_date_from)) {
    $where_conditions[] = "DATE(at.created_at) >= ?";
    $params[] = $filter_date_from;
}

if (!empty($filter_date_to)) {
    $where_conditions[] = "DATE(at.created_at) <= ?";
    $params[] = $filter_date_to;
}

if ($filter_amount_min > 0) {
    $where_conditions[] = "at.amount >= ?";
    $params[] = $filter_amount_min;
}

if ($filter_amount_max > 0) {
    $where_conditions[] = "at.amount <= ?";
    $params[] = $filter_amount_max;
}

$where_clause = !empty($where_conditions) ? "WHERE " . implode(" AND ", $where_conditions) : "";

try {
    $db = getDB();

    // Get total count for pagination
    $count_query = "SELECT COUNT(*) as total
                    FROM account_transactions at
                    LEFT JOIN accounts a ON at.account_id = a.id
                    $where_clause";
    $count_result = $db->query($count_query, $params);
    $total_records = $count_result->fetch_assoc()['total'];
    $total_pages = ceil($total_records / $records_per_page);

    // Get transactions with pagination
    $transactions_query = "SELECT at.*,
                          a.first_name, a.last_name, a.username, a.account_number,
                          admin.first_name as admin_first_name, admin.last_name as admin_last_name
                          FROM account_transactions at
                          LEFT JOIN accounts a ON at.account_id = a.id
                          LEFT JOIN accounts admin ON at.processed_by = admin.id
                          $where_clause
                          ORDER BY at.created_at DESC
                          LIMIT $records_per_page OFFSET $offset";

    $transactions_result = $db->query($transactions_query, $params);
    $transactions = [];
    while ($row = $transactions_result->fetch_assoc()) {
        $transactions[] = $row;
    }

    // Get users for filter dropdown
    $users_result = $db->query("SELECT id, first_name, last_name, username FROM accounts WHERE is_admin = 0 ORDER BY first_name, last_name");
    $users = [];
    while ($user = $users_result->fetch_assoc()) {
        $users[] = $user;
    }

} catch (Exception $e) {
    $error = "Failed to load transactions: " . $e->getMessage();
    $transactions = [];
    $total_records = 0;
    $total_pages = 0;
}

include 'includes/admin-header.php';
?>

<!-- Breadcrumb -->
<nav aria-label="breadcrumb" style="margin-bottom: 1rem;">
    <ol class="breadcrumb">
        <li class="breadcrumb-item"><a href="index.php">Admin</a></li>
        <li class="breadcrumb-item active" aria-current="page">All Transactions</li>
    </ol>
</nav>

<!-- Error Message -->
<?php if (isset($error)): ?>
<div class="alert alert-danger" role="alert">
    <div class="d-flex">
        <div><i class="fas fa-exclamation-circle me-2"></i></div>
        <div>
            <h4 class="alert-title">Error!</h4>
            <div class="text-muted"><?php echo htmlspecialchars($error); ?></div>
        </div>
    </div>
</div>
<?php endif; ?>

<!-- Filters Card -->
<div class="row row-cards mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h3 class="card-title">
                    <i class="fas fa-filter me-2"></i>
                    Transaction Filters
                </h3>
                <div class="card-actions">
                    <a href="transactions.php" class="btn btn-outline-secondary btn-sm">
                        <i class="fas fa-times me-2"></i>
                        Clear Filters
                    </a>
                </div>
            </div>
            <div class="card-body">
                <form method="GET" action="" class="row g-2">
                    <!-- Row 1: Main Filters -->
                    <div class="col-md-2">
                        <label class="form-label">Type</label>
                        <select name="type" class="form-select form-select-sm">
                            <option value="">All Types</option>
                            <option value="credit" <?php echo $filter_type === 'credit' ? 'selected' : ''; ?>>Credit</option>
                            <option value="debit" <?php echo $filter_type === 'debit' ? 'selected' : ''; ?>>Debit</option>
                            <option value="transfer_in" <?php echo $filter_type === 'transfer_in' ? 'selected' : ''; ?>>Transfer In</option>
                            <option value="transfer_out" <?php echo $filter_type === 'transfer_out' ? 'selected' : ''; ?>>Transfer Out</option>
                            <option value="deposit" <?php echo $filter_type === 'deposit' ? 'selected' : ''; ?>>Deposit</option>
                            <option value="withdrawal" <?php echo $filter_type === 'withdrawal' ? 'selected' : ''; ?>>Withdrawal</option>
                        </select>
                    </div>

                    <div class="col-md-2">
                        <label class="form-label">Status</label>
                        <select name="status" class="form-select form-select-sm">
                            <option value="">All Status</option>
                            <option value="pending" <?php echo $filter_status === 'pending' ? 'selected' : ''; ?>>Pending</option>
                            <option value="processing" <?php echo $filter_status === 'processing' ? 'selected' : ''; ?>>Processing</option>
                            <option value="completed" <?php echo $filter_status === 'completed' ? 'selected' : ''; ?>>Completed</option>
                            <option value="cancelled" <?php echo $filter_status === 'cancelled' ? 'selected' : ''; ?>>Cancelled</option>
                            <option value="failed" <?php echo $filter_status === 'failed' ? 'selected' : ''; ?>>Failed</option>
                        </select>
                    </div>

                    <div class="col-md-2">
                        <label class="form-label">User</label>
                        <input type="text" name="user" class="form-control form-control-sm" placeholder="Search user..." value="<?php echo htmlspecialchars($filter_user); ?>">
                    </div>

                    <div class="col-md-2">
                        <label class="form-label">Date From</label>
                        <input type="date" name="date_from" class="form-control form-control-sm" value="<?php echo htmlspecialchars($filter_date_from); ?>">
                    </div>

                    <div class="col-md-2">
                        <label class="form-label">Date To</label>
                        <input type="date" name="date_to" class="form-control form-control-sm" value="<?php echo htmlspecialchars($filter_date_to); ?>">
                    </div>

                    <div class="col-md-2">
                        <label class="form-label">Amount Range</label>
                        <div class="input-group input-group-sm">
                            <input type="number" name="amount_min" class="form-control" step="0.01" placeholder="Min" value="<?php echo $filter_amount_min > 0 ? $filter_amount_min : ''; ?>">
                            <span class="input-group-text">-</span>
                            <input type="number" name="amount_max" class="form-control" step="0.01" placeholder="Max" value="<?php echo $filter_amount_max > 0 ? $filter_amount_max : ''; ?>">
                        </div>
                    </div>

                    <!-- Row 2: Action Buttons -->
                    <div class="col-12 mt-3">
                        <div class="btn-group" role="group">
                            <button type="submit" class="btn btn-primary btn-sm">
                                <i class="fas fa-search me-1"></i>
                                Filter
                            </button>
                            <a href="transactions.php" class="btn btn-outline-secondary btn-sm">
                                <i class="fas fa-times me-1"></i>
                                Clear
                            </a>
                        </div>

                        <div class="btn-group ms-2" role="group">
                            <a href="credit-debit.php" class="btn btn-success btn-sm">
                                <i class="fas fa-plus me-1"></i>
                                New Transaction
                            </a>
                            <button type="button" class="btn btn-outline-primary btn-sm" onclick="exportTransactions()">
                                <i class="fas fa-download me-1"></i>
                                Export CSV
                            </button>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- Transactions Table -->
<div class="row row-cards">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h3 class="card-title">
                    <i class="fas fa-list me-2"></i>
                    All Transactions
                    <span class="badge bg-secondary ms-2"><?php echo number_format($total_records); ?> total</span>
                </h3>
                <div class="card-actions">
                    <span class="text-muted">
                        Showing <?php echo number_format(($current_page - 1) * $records_per_page + 1); ?> -
                        <?php echo number_format(min($current_page * $records_per_page, $total_records)); ?>
                        of <?php echo number_format($total_records); ?> transactions
                    </span>
                </div>
            </div>
            <div class="card-body p-0">
                <?php if (!empty($transactions)): ?>
                <div class="table-responsive">
                    <table class="table table-vcenter card-table table-sm">
                        <thead>
                            <tr>
                                <th class="w-1">#</th>
                                <th>ID & Ref</th>
                                <th>User</th>
                                <th>Type</th>
                                <th>Amount</th>
                                <th>Status</th>
                                <th>Date</th>
                                <th class="w-1">Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php
                            $row_number = ($current_page - 1) * $records_per_page + 1;
                            foreach ($transactions as $transaction):
                            ?>
                            <tr>
                                <!-- Row Number -->
                                <td>
                                    <span class="text-muted"><?php echo $row_number++; ?></span>
                                </td>

                                <!-- Transaction ID & Reference -->
                                <td>
                                    <div class="d-flex flex-column">
                                        <div class="fw-bold">#<?php echo $transaction['id']; ?></div>
                                        <small class="text-muted"><?php echo htmlspecialchars(substr($transaction['reference_number'], -8)); ?></small>
                                    </div>
                                </td>

                                <!-- User -->
                                <td>
                                    <div class="d-flex align-items-center">
                                        <div class="avatar avatar-xs me-2">
                                            <?php echo strtoupper(substr($transaction['first_name'] ?? 'U', 0, 1) . substr($transaction['last_name'] ?? 'U', 0, 1)); ?>
                                        </div>
                                        <div>
                                            <div class="fw-bold" style="font-size: 0.85rem;"><?php echo htmlspecialchars(($transaction['first_name'] ?? 'Unknown') . ' ' . ($transaction['last_name'] ?? 'User')); ?></div>
                                            <small class="text-muted">@<?php echo htmlspecialchars($transaction['username'] ?? 'unknown'); ?></small>
                                        </div>
                                    </div>
                                </td>

                                <!-- Type -->
                                <td>
                                    <?php
                                    $type_colors = [
                                        'credit' => 'success',
                                        'debit' => 'danger',
                                        'transfer_in' => 'info',
                                        'transfer_out' => 'warning',
                                        'deposit' => 'primary',
                                        'withdrawal' => 'secondary'
                                    ];
                                    $color = $type_colors[$transaction['transaction_type']] ?? 'secondary';
                                    $type_short = [
                                        'credit' => 'Credit',
                                        'debit' => 'Debit',
                                        'transfer_in' => 'Transfer In',
                                        'transfer_out' => 'Transfer Out',
                                        'deposit' => 'Deposit',
                                        'withdrawal' => 'Withdrawal'
                                    ];
                                    ?>
                                    <span class="badge bg-<?php echo $color; ?> badge-sm">
                                        <?php echo $type_short[$transaction['transaction_type']] ?? ucfirst($transaction['transaction_type']); ?>
                                    </span>
                                </td>

                                <!-- Amount -->
                                <td>
                                    <span class="fw-bold <?php echo in_array($transaction['transaction_type'], ['credit', 'deposit', 'transfer_in']) ? 'text-success' : 'text-danger'; ?>">
                                        <?php echo in_array($transaction['transaction_type'], ['credit', 'deposit', 'transfer_in']) ? '+' : '-'; ?>
                                        <?php echo formatCurrency($transaction['amount'], $transaction['currency']); ?>
                                    </span>
                                </td>

                                <!-- Status -->
                                <td>
                                    <?php
                                    $status_colors = [
                                        'pending' => 'warning',
                                        'processing' => 'info',
                                        'completed' => 'success',
                                        'cancelled' => 'secondary',
                                        'failed' => 'danger'
                                    ];
                                    $status_color = $status_colors[$transaction['status']] ?? 'secondary';
                                    ?>
                                    <span class="badge bg-<?php echo $status_color; ?> badge-sm">
                                        <?php echo ucfirst($transaction['status']); ?>
                                    </span>
                                </td>

                                <!-- Date -->
                                <td>
                                    <div class="d-flex flex-column">
                                        <div class="text-muted" style="font-size: 0.85rem;"><?php echo date('M j, Y', strtotime($transaction['created_at'])); ?></div>
                                        <small class="text-muted"><?php echo date('g:i A', strtotime($transaction['created_at'])); ?></small>
                                    </div>
                                </td>

                                <!-- Actions -->
                                <td>
                                    <div class="btn-list">
                                        <button type="button" class="btn btn-sm btn-outline-primary" onclick="viewTransaction(<?php echo $transaction['id']; ?>)" title="View Details">
                                            <i class="fas fa-eye"></i>
                                        </button>
                                        <?php if (in_array($transaction['status'], ['pending', 'processing'])): ?>
                                        <button type="button" class="btn btn-sm btn-outline-success" onclick="updateStatus(<?php echo $transaction['id']; ?>, 'completed')" title="Mark Completed">
                                            <i class="fas fa-check"></i>
                                        </button>
                                        <?php endif; ?>
                                    </div>
                                </td>
                            </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
                <?php else: ?>
                <div class="empty">
                    <div class="empty-img">
                        <i class="fas fa-search" style="font-size: 3rem; color: #6c757d;"></i>
                    </div>
                    <p class="empty-title">No transactions found</p>
                    <p class="empty-subtitle text-muted">
                        <?php if (!empty(array_filter([$filter_type, $filter_status, $filter_user, $filter_date_from, $filter_date_to]))): ?>
                        Try adjusting your filters to see more results.
                        <?php else: ?>
                        No transactions have been recorded yet.
                        <?php endif; ?>
                    </p>
                    <div class="empty-action">
                        <a href="credit-debit.php" class="btn btn-primary">
                            <i class="fas fa-plus me-2"></i>
                            Create First Transaction
                        </a>
                    </div>
                </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<!-- Compact Pagination -->
<?php if ($total_pages > 1): ?>
<div class="row mt-3">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center">
            <small class="text-muted">
                Page <?php echo $current_page; ?> of <?php echo $total_pages; ?>
                (<?php echo number_format($total_records); ?> total)
            </small>
            <nav aria-label="Transaction pagination">
                <ul class="pagination pagination-sm mb-0">
                    <?php if ($current_page > 1): ?>
                    <li class="page-item">
                        <a class="page-link" href="?<?php echo http_build_query(array_merge($_GET, ['page' => 1])); ?>">
                            <i class="fas fa-angle-double-left"></i>
                        </a>
                    </li>
                    <li class="page-item">
                        <a class="page-link" href="?<?php echo http_build_query(array_merge($_GET, ['page' => $current_page - 1])); ?>">
                            <i class="fas fa-angle-left"></i>
                        </a>
                    </li>
                    <?php endif; ?>

                    <?php
                    $start_page = max(1, $current_page - 1);
                    $end_page = min($total_pages, $current_page + 1);

                    for ($i = $start_page; $i <= $end_page; $i++):
                    ?>
                    <li class="page-item <?php echo $i === $current_page ? 'active' : ''; ?>">
                        <a class="page-link" href="?<?php echo http_build_query(array_merge($_GET, ['page' => $i])); ?>">
                            <?php echo $i; ?>
                        </a>
                    </li>
                    <?php endfor; ?>

                    <?php if ($current_page < $total_pages): ?>
                    <li class="page-item">
                        <a class="page-link" href="?<?php echo http_build_query(array_merge($_GET, ['page' => $current_page + 1])); ?>">
                            <i class="fas fa-angle-right"></i>
                        </a>
                    </li>
                    <li class="page-item">
                        <a class="page-link" href="?<?php echo http_build_query(array_merge($_GET, ['page' => $total_pages])); ?>">
                            <i class="fas fa-angle-double-right"></i>
                        </a>
                    </li>
                    <?php endif; ?>
                </ul>
            </nav>
        </div>
    </div>
</div>
<?php endif; ?>

<!-- Transaction Details Modal -->
<div class="modal modal-blur fade" id="transactionModal" tabindex="-1" role="dialog" aria-hidden="true">
    <div class="modal-dialog modal-xl modal-dialog-centered" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-edit me-2"></i>
                    Transaction Details & Editor
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body" id="transactionDetails">
                <!-- Transaction details will be loaded here -->
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                <button type="button" class="btn btn-success" id="saveTransactionBtn" onclick="saveTransaction()" style="display: none;">
                    <i class="fas fa-save me-2"></i>
                    Save Changes
                </button>
                <button type="button" class="btn btn-primary" onclick="printTransaction()">
                    <i class="fas fa-print me-2"></i>
                    Print
                </button>
            </div>
        </div>
    </div>
</div>

<script>
// Transaction management functions
function viewTransaction(transactionId) {
    // Show loading state
    document.getElementById('transactionDetails').innerHTML = `
        <div class="text-center py-4">
            <div class="spinner-border text-primary" role="status">
                <span class="visually-hidden">Loading...</span>
            </div>
            <p class="mt-2">Loading transaction details...</p>
        </div>
    `;

    // Show modal
    const modal = new bootstrap.Modal(document.getElementById('transactionModal'));
    modal.show();

    // Fetch transaction details
    fetch(`transaction-details.php?id=${transactionId}&type=account`)
        .then(response => response.json())
        .then(transaction => {
            if (transaction.error) {
                throw new Error(transaction.error);
            }

            // Format the transaction details with editing capabilities - compact layout
            const detailsHtml = `
                <form id="transactionEditForm">
                    <input type="hidden" id="transactionId" value="${transaction.id}">
                    <div class="row g-3">
                        <div class="col-md-6">
                            <div class="card border-0 shadow-sm">
                                <div class="card-header py-2" style="background: linear-gradient(135deg, #4361ee 0%, #3730a3 100%); color: white; border-bottom: 3px solid rgba(255,255,255,0.1);">
                                    <h3 class="card-title mb-0 h6">
                                        <i class="fas fa-info-circle me-2"></i>
                                        Transaction Information
                                    </h3>
                                    <div class="card-actions">
                                        <button type="button" class="btn btn-sm btn-light" onclick="toggleEditMode()" style="border-radius: 6px; font-weight: 500;">
                                            <i class="fas fa-edit me-1"></i>
                                            <span id="editModeText">Edit</span>
                                        </button>
                                    </div>
                                </div>
                                <div class="card-body py-3" style="background: rgba(67, 97, 238, 0.02);">
                                    <div class="row mb-2">
                                        <div class="col-sm-4">
                                            <label class="form-label mb-0 small">Transaction ID:</label>
                                        </div>
                                        <div class="col-sm-8">
                                            <strong>#${transaction.id}</strong>
                                        </div>
                                    </div>

                                    <div class="row mb-2">
                                        <div class="col-sm-4">
                                            <label class="form-label mb-0 small">Reference:</label>
                                        </div>
                                        <div class="col-sm-8">
                                            <span class="view-mode"><code>${transaction.reference_number}</code></span>
                                            <input type="text" class="form-control form-control-sm edit-mode" id="referenceNumber" value="${transaction.reference_number}" style="display: none;" required>
                                        </div>
                                    </div>

                                    <div class="row mb-2">
                                        <div class="col-sm-4">
                                            <label class="form-label mb-0 small">Type:</label>
                                        </div>
                                        <div class="col-sm-8">
                                            <span class="view-mode">
                                                <span class="badge bg-${getTypeColor(transaction.transaction_type)}">
                                                    ${formatTransactionType(transaction.transaction_type)}
                                                </span>
                                            </span>
                                            <select class="form-select form-select-sm edit-mode" id="transactionType" style="display: none;" required>
                                                <option value="credit" ${transaction.transaction_type === 'credit' ? 'selected' : ''}>Credit</option>
                                                <option value="debit" ${transaction.transaction_type === 'debit' ? 'selected' : ''}>Debit</option>
                                                <option value="transfer_in" ${transaction.transaction_type === 'transfer_in' ? 'selected' : ''}>Transfer In</option>
                                                <option value="transfer_out" ${transaction.transaction_type === 'transfer_out' ? 'selected' : ''}>Transfer Out</option>
                                                <option value="deposit" ${transaction.transaction_type === 'deposit' ? 'selected' : ''}>Deposit</option>
                                                <option value="withdrawal" ${transaction.transaction_type === 'withdrawal' ? 'selected' : ''}>Withdrawal</option>
                                            </select>
                                        </div>
                                    </div>

                                    <hr class="my-2" style="border-color: rgba(67, 97, 238, 0.1);">

                            <div class="row mb-2">
                                <div class="col-sm-4">
                                    <label class="form-label mb-0 small">Amount:</label>
                                </div>
                                <div class="col-sm-8">
                                    <span class="view-mode fw-bold ${getAmountColor(transaction.transaction_type)} fs-6">
                                        ${getAmountPrefix(transaction.transaction_type)}${formatCurrency(transaction.amount, transaction.currency)}
                                    </span>
                                    <div class="edit-mode input-group input-group-sm" style="display: none;">
                                        <span class="input-group-text">$</span>
                                        <input type="number" class="form-control" id="transactionAmount" value="${transaction.amount}" step="0.01" min="0" required>
                                    </div>
                                </div>
                            </div>

                            <div class="row mb-2">
                                <div class="col-sm-4">
                                    <label class="form-label mb-0 small">Currency:</label>
                                </div>
                                <div class="col-sm-8">
                                    <span class="view-mode">${transaction.currency}</span>
                                    <select class="form-select form-select-sm edit-mode" id="transactionCurrency" style="display: none;" required>
                                        <option value="USD" ${transaction.currency === 'USD' ? 'selected' : ''}>USD</option>
                                        <option value="EUR" ${transaction.currency === 'EUR' ? 'selected' : ''}>EUR</option>
                                        <option value="GBP" ${transaction.currency === 'GBP' ? 'selected' : ''}>GBP</option>
                                        <option value="CAD" ${transaction.currency === 'CAD' ? 'selected' : ''}>CAD</option>
                                    </select>
                                </div>
                            </div>

                            <div class="row mb-2">
                                <div class="col-sm-4">
                                    <label class="form-label mb-0 small">Status:</label>
                                </div>
                                <div class="col-sm-8">
                                    <span class="view-mode">
                                        <span class="badge bg-${getStatusColor(transaction.status)}">
                                            ${transaction.status.charAt(0).toUpperCase() + transaction.status.slice(1)}
                                        </span>
                                    </span>
                                    <select class="form-select form-select-sm edit-mode" id="transactionStatus" style="display: none;" required>
                                        <option value="pending" ${transaction.status === 'pending' ? 'selected' : ''}>Pending</option>
                                        <option value="processing" ${transaction.status === 'processing' ? 'selected' : ''}>Processing</option>
                                        <option value="completed" ${transaction.status === 'completed' ? 'selected' : ''}>Completed</option>
                                        <option value="cancelled" ${transaction.status === 'cancelled' ? 'selected' : ''}>Cancelled</option>
                                        <option value="failed" ${transaction.status === 'failed' ? 'selected' : ''}>Failed</option>
                                    </select>
                                </div>
                            </div>

                            <div class="row mb-2">
                                <div class="col-sm-4">
                                    <label class="form-label mb-0 small">Category:</label>
                                </div>
                                <div class="col-sm-8">
                                    <span class="view-mode">${transaction.category}</span>
                                    <input type="text" class="form-control form-control-sm edit-mode" id="transactionCategory" value="${transaction.category}" style="display: none;" required>
                                </div>
                            </div>

                            <div class="row mb-2">
                                <div class="col-sm-4">
                                    <label class="form-label mb-0 small">Date:</label>
                                </div>
                                <div class="col-sm-8">
                                    <span class="view-mode">${formatDate(transaction.transaction_date || transaction.created_at)}</span>
                                    <input type="date" class="form-control form-control-sm edit-mode" id="transactionDate" value="${(transaction.transaction_date || transaction.created_at).split(' ')[0]}" style="display: none;">
                                </div>
                            </div>
                        </dl>
                    </div>
                </div>
            </div>

            <div class="col-md-6">
                <div class="card border-0 shadow-sm">
                    <div class="card-header py-2" style="background: linear-gradient(135deg, #6c757d 0%, #495057 100%); color: white; border-bottom: 3px solid rgba(255,255,255,0.1);">
                        <h3 class="card-title mb-0 h6">
                            <i class="fas fa-user-circle me-2"></i>
                            Account & Processing Details
                        </h3>
                    </div>
                    <div class="card-body py-3" style="background: rgba(108, 117, 125, 0.02);">
                        <dl class="row mb-0">
                            <dt class="col-sm-4 small">Account Holder:</dt>
                            <dd class="col-sm-8 mb-2">
                                <div class="d-flex align-items-center">
                                    <div class="avatar avatar-sm me-2" style="font-size: 0.7rem;">
                                        ${(transaction.first_name || 'U').charAt(0).toUpperCase()}${(transaction.last_name || 'U').charAt(0).toUpperCase()}
                                    </div>
                                    <div>
                                        <div class="fw-bold small">${transaction.first_name || 'Unknown'} ${transaction.last_name || 'User'}</div>
                                        <small class="text-muted">@${transaction.username || 'unknown'}</small>
                                    </div>
                                </div>
                            </dd>

                            <dt class="col-sm-4 small">Account Number:</dt>
                            <dd class="col-sm-8 mb-2"><code class="small">${transaction.account_number || 'N/A'}</code></dd>

                            <dt class="col-sm-4 small">Processed By:</dt>
                            <dd class="col-sm-8 mb-2 small">
                                ${transaction.admin_first_name ?
                                    `${transaction.admin_first_name} ${transaction.admin_last_name}` :
                                    '<span class="text-muted">System</span>'}
                            </dd>

                            <dt class="col-sm-4 small">Created:</dt>
                            <dd class="col-sm-8 mb-2">
                                <div class="small">${formatDate(transaction.created_at)} ${formatTime(transaction.created_at)}</div>
                            </dd>

                            <dt class="col-sm-4 small">Last Updated:</dt>
                            <dd class="col-sm-8 mb-0">
                                <div class="small">${formatDate(transaction.updated_at)} ${formatTime(transaction.updated_at)}</div>
                                ${transaction.updated_at !== transaction.created_at ?
                                    '<small class="badge bg-warning ms-1">Modified</small>' : ''}
                            </dd>
                        </dl>
                    </div>
                </div>
            </div>
        </div>

        <div class="row mt-2">
            <div class="col-12">
                <div class="card border-0 shadow-sm">
                    <div class="card-header py-2" style="background: linear-gradient(135deg, #28a745 0%, #1e7e34 100%); color: white; border-bottom: 3px solid rgba(255,255,255,0.1);">
                        <h3 class="card-title mb-0 h6">
                            <i class="fas fa-comment-alt me-2"></i>
                            Transaction Description
                        </h3>
                    </div>
                    <div class="card-body py-3" style="background: rgba(40, 167, 69, 0.02);">
                        <div class="view-mode">
                            <p class="mb-0 small">${transaction.description}</p>
                        </div>
                        <div class="edit-mode" style="display: none;">
                            <textarea class="form-control form-control-sm" id="transactionDescription" rows="3" required>${transaction.description}</textarea>
                        </div>
                    </div>
                </div>
            </div>
        </div>
            `;

            // Update modal content
            document.getElementById('transactionDetails').innerHTML = detailsHtml;
        })
        .catch(error => {
            console.error('Error loading transaction details:', error);
            document.getElementById('transactionDetails').innerHTML = `
                <div class="alert alert-danger">
                    <h4 class="alert-title">Error</h4>
                    <p class="mb-0">Failed to load transaction details: ${error.message}</p>
                </div>
            `;
        });
}

// Helper functions for modal
function getTypeColor(type) {
    const colors = {
        'credit': 'success',
        'debit': 'danger',
        'transfer_in': 'info',
        'transfer_out': 'warning',
        'deposit': 'primary',
        'withdrawal': 'secondary'
    };
    return colors[type] || 'secondary';
}

function getStatusColor(status) {
    const colors = {
        'pending': 'warning',
        'processing': 'info',
        'completed': 'success',
        'cancelled': 'secondary',
        'failed': 'danger'
    };
    return colors[status] || 'secondary';
}

function getAmountColor(type) {
    return ['credit', 'deposit', 'transfer_in'].includes(type) ? 'text-success' : 'text-danger';
}

function getAmountPrefix(type) {
    return ['credit', 'deposit', 'transfer_in'].includes(type) ? '+' : '-';
}

function formatTransactionType(type) {
    const types = {
        'credit': 'Credit',
        'debit': 'Debit',
        'transfer_in': 'Transfer In',
        'transfer_out': 'Transfer Out',
        'deposit': 'Deposit',
        'withdrawal': 'Withdrawal'
    };
    return types[type] || type.charAt(0).toUpperCase() + type.slice(1);
}

function formatCurrency(amount, currency = 'USD') {
    return new Intl.NumberFormat('en-US', {
        style: 'currency',
        currency: currency
    }).format(amount);
}

function formatDate(dateString) {
    return new Date(dateString).toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'short',
        day: 'numeric'
    });
}

function formatTime(dateString) {
    return new Date(dateString).toLocaleTimeString('en-US', {
        hour: 'numeric',
        minute: '2-digit',
        hour12: true
    });
}

function printTransaction() {
    window.print();
}

// Toggle edit mode for transaction details
function toggleEditMode() {
    const editModeElements = document.querySelectorAll('.edit-mode');
    const viewModeElements = document.querySelectorAll('.view-mode');
    const editButton = document.getElementById('editModeText');
    const saveButton = document.getElementById('saveTransactionBtn');

    const isEditMode = editModeElements[0].style.display !== 'none';

    if (isEditMode) {
        // Switch to view mode
        editModeElements.forEach(el => el.style.display = 'none');
        viewModeElements.forEach(el => el.style.display = 'block');
        editButton.textContent = 'Edit';
        saveButton.style.display = 'none';
    } else {
        // Switch to edit mode
        editModeElements.forEach(el => el.style.display = 'block');
        viewModeElements.forEach(el => el.style.display = 'none');
        editButton.textContent = 'Cancel';
        saveButton.style.display = 'inline-block';
    }
}

// Save transaction changes
function saveTransaction() {
    const form = document.getElementById('transactionEditForm');
    if (!form.checkValidity()) {
        form.reportValidity();
        return;
    }

    const transactionId = document.getElementById('transactionId').value;
    const referenceNumber = document.getElementById('referenceNumber').value;
    const transactionType = document.getElementById('transactionType').value;
    const amount = parseFloat(document.getElementById('transactionAmount').value);
    const currency = document.getElementById('transactionCurrency').value;
    const status = document.getElementById('transactionStatus').value;
    const category = document.getElementById('transactionCategory').value;
    const description = document.getElementById('transactionDescription').value;
    const transactionDate = document.getElementById('transactionDate').value;

    // Show loading state
    const saveButton = document.getElementById('saveTransactionBtn');
    const originalText = saveButton.innerHTML;
    saveButton.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Saving...';
    saveButton.disabled = true;

    // Send update request
    fetch('update-transaction.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            transaction_id: transactionId,
            reference_number: referenceNumber,
            transaction_type: transactionType,
            amount: amount,
            currency: currency,
            status: status,
            category: category,
            description: description,
            transaction_date: transactionDate
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showNotification('Transaction updated successfully', 'success');
            // Close modal and reload page
            setTimeout(() => {
                const modal = bootstrap.Modal.getInstance(document.getElementById('transactionModal'));
                modal.hide();
                window.location.reload();
            }, 1000);
        } else {
            showNotification('Failed to update transaction: ' + data.message, 'error');
        }
    })
    .catch(error => {
        console.error('Error updating transaction:', error);
        showNotification('Error updating transaction', 'error');
    })
    .finally(() => {
        // Restore button state
        saveButton.innerHTML = originalText;
        saveButton.disabled = false;
    });
}

function updateStatus(transactionId, newStatus) {
    if (!confirm('Are you sure you want to change the transaction status to "' + newStatus + '"?')) {
        return;
    }

    fetch('update-transaction-status.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            transaction_id: transactionId,
            status: newStatus
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showNotification('Transaction status updated successfully', 'success');
            // Reload page to show updated status
            setTimeout(() => {
                window.location.reload();
            }, 1000);
        } else {
            showNotification('Failed to update transaction status: ' + data.message, 'error');
        }
    })
    .catch(error => {
        console.error('Error updating transaction status:', error);
        showNotification('Error updating transaction status', 'error');
    });
}

function exportTransactions() {
    // Get current filter parameters
    const params = new URLSearchParams(window.location.search);
    params.set('export', 'csv');

    // Create download link
    const downloadUrl = 'export-transactions.php?' + params.toString();

    // Create temporary link and trigger download
    const link = document.createElement('a');
    link.href = downloadUrl;
    link.download = 'transactions_' + new Date().toISOString().split('T')[0] + '.csv';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);

    showNotification('Export started. Download will begin shortly.', 'info');
}

// Notification system
function showNotification(message, type) {
    const alertClass = type === 'success' ? 'alert-success' :
                     type === 'error' ? 'alert-danger' :
                     type === 'info' ? 'alert-info' : 'alert-warning';

    const notification = document.createElement('div');
    notification.className = `alert ${alertClass} alert-dismissible fade show position-fixed`;
    notification.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
    notification.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    document.body.appendChild(notification);

    // Auto remove after 5 seconds
    setTimeout(() => {
        if (notification.parentNode) {
            notification.parentNode.removeChild(notification);
        }
    }, 5000);
}

// Auto-submit form when filters change (optional)
document.addEventListener('DOMContentLoaded', function() {
    const filterForm = document.querySelector('form');
    const autoSubmitElements = filterForm.querySelectorAll('select[name="type"], select[name="status"]');

    autoSubmitElements.forEach(element => {
        element.addEventListener('change', function() {
            // Optional: Auto-submit on filter change
            // filterForm.submit();
        });
    });
});
</script>

<?php include 'includes/admin-footer.php'; ?>
