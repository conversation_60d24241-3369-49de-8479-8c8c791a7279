/**
 * Banking Dashboard JavaScript
 * Handles banking-specific dashboard functionality
 */

document.addEventListener('DOMContentLoaded', function() {
    initializeBankingDashboard();
    initializeAnimations();
    initializeInteractions();
    initializeRealTimeUpdates();
});

/**
 * Initialize Banking Dashboard
 */
function initializeBankingDashboard() {
    // Add loading states
    showLoadingStates();
    
    // Initialize balance card animations
    initializeBalanceCard();
    
    // Initialize transaction list
    initializeTransactionList();
    
    // Initialize quick stats
    initializeQuickStats();
    
    // Remove loading states after initialization
    setTimeout(removeLoadingStates, 500);
}

/**
 * Initialize Animations
 */
function initializeAnimations() {
    // Stagger animation for cards
    const cards = document.querySelectorAll('.stat-card, .transaction-section');
    cards.forEach((card, index) => {
        card.style.opacity = '0';
        card.style.transform = 'translateY(20px)';
        
        setTimeout(() => {
            card.style.transition = 'all 0.6s ease';
            card.style.opacity = '1';
            card.style.transform = 'translateY(0)';
        }, index * 100);
    });
    
    // Balance card entrance animation
    const balanceCard = document.querySelector('.balance-card');
    if (balanceCard) {
        balanceCard.style.opacity = '0';
        balanceCard.style.transform = 'scale(0.95)';
        
        setTimeout(() => {
            balanceCard.style.transition = 'all 0.8s ease';
            balanceCard.style.opacity = '1';
            balanceCard.style.transform = 'scale(1)';
        }, 200);
    }
}

/**
 * Initialize Balance Card
 */
function initializeBalanceCard() {
    const balanceAmount = document.querySelector('.balance-amount');
    if (balanceAmount) {
        // Animate balance number counting up
        const finalAmount = balanceAmount.textContent;
        const numericValue = parseFloat(finalAmount.replace(/[^0-9.-]+/g, ''));
        
        if (!isNaN(numericValue)) {
            animateBalance(balanceAmount, 0, numericValue, finalAmount);
        }
    }
    
    // Add hover effects to balance actions
    const balanceActions = document.querySelectorAll('.balance-action');
    balanceActions.forEach(action => {
        action.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-2px) scale(1.05)';
        });
        
        action.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(0) scale(1)';
        });
    });
}

/**
 * Initialize Transaction List
 */
function initializeTransactionList() {
    const transactionItems = document.querySelectorAll('.transaction-item');
    
    transactionItems.forEach((item, index) => {
        // Add entrance animation
        item.style.opacity = '0';
        item.style.transform = 'translateX(-20px)';
        
        setTimeout(() => {
            item.style.transition = 'all 0.5s ease';
            item.style.opacity = '1';
            item.style.transform = 'translateX(0)';
        }, 600 + (index * 100));
        
        // Add click interaction
        item.addEventListener('click', function() {
            // Add ripple effect
            createRippleEffect(event, this);
            
            // Optional: Navigate to transaction details
            const transactionId = this.dataset.transactionId;
            if (transactionId) {
                // You can add navigation logic here
                console.log('Transaction clicked:', transactionId);
            }
        });
    });
}

/**
 * Initialize Quick Stats
 */
function initializeQuickStats() {
    const statCards = document.querySelectorAll('.stat-card');
    
    statCards.forEach((card, index) => {
        // Add hover effects
        card.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-4px) scale(1.02)';
        });
        
        card.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(0) scale(1)';
        });
        
        // Animate stat values
        const statValue = card.querySelector('.stat-value');
        if (statValue) {
            const finalValue = statValue.textContent;
            const numericValue = parseFloat(finalValue.replace(/[^0-9.-]+/g, ''));
            
            if (!isNaN(numericValue)) {
                setTimeout(() => {
                    animateStatValue(statValue, 0, numericValue, finalValue);
                }, 800 + (index * 200));
            }
        }
    });
}

/**
 * Initialize Interactions
 */
function initializeInteractions() {
    // Quick action buttons
    const quickActionBtns = document.querySelectorAll('.quick-action-btn');
    quickActionBtns.forEach(btn => {
        btn.addEventListener('click', function(e) {
            createRippleEffect(e, this);
        });
    });
    
    // Add smooth scrolling for internal links
    document.querySelectorAll('a[href^="#"]').forEach(anchor => {
        anchor.addEventListener('click', function (e) {
            e.preventDefault();
            const target = document.querySelector(this.getAttribute('href'));
            if (target) {
                target.scrollIntoView({
                    behavior: 'smooth',
                    block: 'start'
                });
            }
        });
    });
}

/**
 * Initialize Real-time Updates
 */
function initializeRealTimeUpdates() {
    // Update balance every 30 seconds
    setInterval(updateBalance, 30000);
    
    // Update recent transactions every 60 seconds
    setInterval(updateRecentTransactions, 60000);
    
    // Show last updated time
    updateLastRefreshTime();
    setInterval(updateLastRefreshTime, 1000);
}

/**
 * Animate Balance Amount
 */
function animateBalance(element, start, end, finalText) {
    const duration = 2000;
    const startTime = performance.now();
    const isNegative = end < 0;
    const absEnd = Math.abs(end);
    
    function updateBalance(currentTime) {
        const elapsed = currentTime - startTime;
        const progress = Math.min(elapsed / duration, 1);
        
        // Use easing function for smooth animation
        const easeProgress = easeOutQuart(progress);
        const currentValue = start + (absEnd - start) * easeProgress;
        
        // Format the current value
        const formattedValue = formatCurrency(isNegative ? -currentValue : currentValue);
        element.textContent = formattedValue;
        
        if (progress < 1) {
            requestAnimationFrame(updateBalance);
        } else {
            element.textContent = finalText;
        }
    }
    
    requestAnimationFrame(updateBalance);
}

/**
 * Animate Stat Values
 */
function animateStatValue(element, start, end, finalText) {
    const duration = 1500;
    const startTime = performance.now();
    
    function updateValue(currentTime) {
        const elapsed = currentTime - startTime;
        const progress = Math.min(elapsed / duration, 1);
        
        const easeProgress = easeOutQuart(progress);
        const currentValue = start + (end - start) * easeProgress;
        
        // Format based on the original text
        if (finalText.includes('$')) {
            element.textContent = formatCurrency(currentValue);
        } else {
            element.textContent = Math.floor(currentValue).toLocaleString();
        }
        
        if (progress < 1) {
            requestAnimationFrame(updateValue);
        } else {
            element.textContent = finalText;
        }
    }
    
    requestAnimationFrame(updateValue);
}

/**
 * Create Ripple Effect
 */
function createRippleEffect(event, element) {
    const ripple = document.createElement('span');
    const rect = element.getBoundingClientRect();
    const size = Math.max(rect.width, rect.height);
    const x = event.clientX - rect.left - size / 2;
    const y = event.clientY - rect.top - size / 2;
    
    ripple.style.cssText = `
        position: absolute;
        width: ${size}px;
        height: ${size}px;
        left: ${x}px;
        top: ${y}px;
        background: rgba(255, 255, 255, 0.3);
        border-radius: 50%;
        transform: scale(0);
        animation: ripple 0.6s ease-out;
        pointer-events: none;
        z-index: 1000;
    `;
    
    element.style.position = 'relative';
    element.style.overflow = 'hidden';
    element.appendChild(ripple);
    
    setTimeout(() => {
        ripple.remove();
    }, 600);
}

/**
 * Show Loading States
 */
function showLoadingStates() {
    const loadingElements = document.querySelectorAll('.stat-value, .balance-amount');
    loadingElements.forEach(element => {
        element.style.opacity = '0.5';
        element.style.transform = 'scale(0.95)';
    });
}

/**
 * Remove Loading States
 */
function removeLoadingStates() {
    const loadingElements = document.querySelectorAll('.stat-value, .balance-amount');
    loadingElements.forEach(element => {
        element.style.opacity = '1';
        element.style.transform = 'scale(1)';
        element.style.transition = 'all 0.3s ease';
    });
}

/**
 * Update Balance (Real-time)
 */
function updateBalance() {
    fetch('../api/get-balance.php')
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                const balanceElement = document.querySelector('.balance-amount');
                if (balanceElement && data.balance !== balanceElement.dataset.currentBalance) {
                    balanceElement.dataset.currentBalance = data.balance;
                    balanceElement.textContent = formatCurrency(data.balance);
                    
                    // Add update animation
                    balanceElement.style.transform = 'scale(1.05)';
                    setTimeout(() => {
                        balanceElement.style.transform = 'scale(1)';
                    }, 200);
                }
            }
        })
        .catch(error => {
            console.error('Failed to update balance:', error);
        });
}

/**
 * Update Recent Transactions
 */
function updateRecentTransactions() {
    fetch('../api/get-recent-transactions.php')
        .then(response => response.json())
        .then(data => {
            if (data.success && data.transactions) {
                updateTransactionList(data.transactions);
            }
        })
        .catch(error => {
            console.error('Failed to update transactions:', error);
        });
}

/**
 * Update Last Refresh Time
 */
function updateLastRefreshTime() {
    const now = new Date();
    const timeString = now.toLocaleTimeString();
    
    let refreshElement = document.querySelector('.last-refresh');
    if (!refreshElement) {
        refreshElement = document.createElement('div');
        refreshElement.className = 'last-refresh';
        refreshElement.style.cssText = `
            position: fixed;
            bottom: 1rem;
            right: 1rem;
            background: rgba(0, 0, 0, 0.7);
            color: white;
            padding: 0.5rem 1rem;
            border-radius: 20px;
            font-size: 0.75rem;
            z-index: 1000;
            opacity: 0.7;
        `;
        document.body.appendChild(refreshElement);
    }
    
    refreshElement.textContent = `Last updated: ${timeString}`;
}

/**
 * Utility Functions
 */
function easeOutQuart(t) {
    return 1 - (--t) * t * t * t;
}

function formatCurrency(amount) {
    return new Intl.NumberFormat('en-US', {
        style: 'currency',
        currency: 'USD'
    }).format(amount);
}

/**
 * Add CSS animations
 */
const style = document.createElement('style');
style.textContent = `
    @keyframes ripple {
        to {
            transform: scale(2);
            opacity: 0;
        }
    }
    
    @keyframes fadeInUp {
        from {
            opacity: 0;
            transform: translateY(30px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }
    
    @keyframes pulse {
        0%, 100% {
            transform: scale(1);
        }
        50% {
            transform: scale(1.05);
        }
    }
    
    .fade-in-up {
        animation: fadeInUp 0.6s ease-out forwards;
    }
    
    .pulse {
        animation: pulse 2s ease-in-out infinite;
    }
`;
document.head.appendChild(style);
