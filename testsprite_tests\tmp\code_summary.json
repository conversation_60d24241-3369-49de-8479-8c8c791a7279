{"tech_stack": ["PHP", "MySQL", "HTML", "CSS", "JavaScript", "Bootstrap", "Font Awesome", "MAMP", "Apache"], "features": [{"name": "User Authentication System", "description": "Complete user login, registration, 2FA/OTP verification, password reset, and session management with secure authentication flow", "files": ["auth/login.php", "auth/register.php", "auth/verify-otp.php", "auth/logout.php", "auth/forgot-password.php", "config/config.php"]}, {"name": "Modern User Dashboard", "description": "Comprehensive banking dashboard showing account balance, user profile, transactions, virtual cards, and monthly statistics with modern design", "files": ["user/dashboard/index.php", "user/shared/header.php", "user/shared/sidebar.php", "user/shared/footer.php"]}, {"name": "Enhanced Sidebar Navigation", "description": "Professional banking sidebar with user info, account sections, banking services, cards & digital, analytics, and support sections", "files": ["user/shared/sidebar.php", "assets/css/user-dashboard.css"]}, {"name": "Account Management System", "description": "Complete account information display, balance management, account details, and account status tracking", "files": ["user/accounts/index.php", "dashboard/wallet.php", "dashboard/accounts/index.php"]}, {"name": "Transaction Management", "description": "Transaction history display, transaction processing, search and filtering, with comprehensive transaction details", "files": ["user/transactions/index.php", "dashboard/transactions/index.php", "assets/css/transactions.css"]}, {"name": "Money Transfer System", "description": "Internal and external money transfer functionality with beneficiary management and transfer history", "files": ["user/transfers/index.php", "dashboard/transfers/index.php", "dashboard/beneficiaries/index.php", "assets/css/transfers.css"]}, {"name": "Virtual Cards Management", "description": "Virtual card creation, management, card details display, balance tracking, and card status management", "files": ["user/cards/index.php", "dashboard/cards/index.php", "dashboard/ajax/create_card.php", "assets/css/cards.css"]}, {"name": "Bill Payment System", "description": "Bill payment processing, payment history, recurring payments, and payment management", "files": ["user/payments/index.php", "dashboard/payments/index.php", "dashboard/payments.php"]}, {"name": "User Profile Management", "description": "User profile editing, settings management, security settings, and personal information updates", "files": ["user/profile/index.php", "dashboard/settings.php", "dashboard/security/index.php"]}, {"name": "Database Layer", "description": "Database connection management, query methods, data access layer, and database configuration", "files": ["config/database.php", "config/config.php"]}, {"name": "Dynamic CSS System", "description": "Centralized color management, dynamic CSS generation, theme customization, and appearance settings", "files": ["assets/css/dynamic.css.php", "config/dynamic-css.php", "assets/css/user-dashboard.css"]}, {"name": "Email Notification System", "description": "Email notifications, OTP delivery, SMTP configuration, and automated email sending", "files": ["config/email.php"]}, {"name": "Security Center", "description": "Security settings, password management, 2FA configuration, and security monitoring", "files": ["dashboard/security/index.php", "user/profile/security.php"]}, {"name": "Analytics and Reports", "description": "Spending insights, financial reports, transaction analytics, and account statements", "files": ["dashboard/statements/index.php", "user/accounts/statements.php"]}, {"name": "Responsive Design System", "description": "Mobile-first responsive design, Bootstrap integration, modern UI components, and cross-device compatibility", "files": ["assets/css/user-dashboard.css", "assets/css/components.css", "assets/css/dashboard-clean.css"]}]}