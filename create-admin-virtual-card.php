<?php
require_once 'config/config.php';

echo "<h2>Create Virtual Card for Admin User</h2>";

try {
    $db = getDB();
    
    // Get admin user details
    $admin_query = "SELECT * FROM accounts WHERE username = 'admin' AND is_admin = 1";
    $admin_result = $db->query($admin_query);
    
    if ($admin = $admin_result->fetch_assoc()) {
        echo "<h3>Admin User Found</h3>";
        echo "ID: " . $admin['id'] . "<br>";
        echo "Username: " . $admin['username'] . "<br>";
        echo "Name: " . $admin['first_name'] . " " . $admin['last_name'] . "<br>";
        echo "Account Number: " . $admin['account_number'] . "<br>";
        
        // Check if admin already has a virtual card
        $existing_card_query = "SELECT * FROM virtual_cards WHERE account_id = ?";
        $existing_card_result = $db->query($existing_card_query, [$admin['id']]);
        
        if ($existing_card_result && $existing_card_result->num_rows > 0) {
            echo "<p style='color: orange;'>Admin user already has a virtual card.</p>";
            while ($card = $existing_card_result->fetch_assoc()) {
                echo "Card ID: " . $card['card_id'] . "<br>";
                echo "Card Number: ****" . substr($card['card_number'], -4) . "<br>";
                echo "Balance: $" . number_format($card['card_balance'], 2) . "<br>";
                echo "Status: " . $card['status'] . "<br><br>";
            }
        } else {
            // Generate card details
            $card_number = '4532' . str_pad(mt_rand(0, ************), 12, '0', STR_PAD_LEFT);
            $cvv = str_pad(mt_rand(100, 999), 3, '0', STR_PAD_LEFT);
            $expiry_date = date('Y-m-d', strtotime('+3 years'));
            $card_holder_name = $admin['first_name'] . ' ' . $admin['last_name'];
            $initial_balance = 1000.00; // Give admin $1000 initial balance
            
            // Insert virtual card
            $insert_card = "INSERT INTO virtual_cards (
                account_id, card_number, card_holder_name, expiry_date, cvv,
                card_balance, daily_limit, monthly_limit, status, approved_by, approved_at
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, 'active', ?, NOW())";
            
            $result = $db->query($insert_card, [
                $admin['id'], 
                $card_number, 
                $card_holder_name, 
                $expiry_date, 
                $cvv, 
                $initial_balance, 
                5000.00, // Daily limit
                50000.00, // Monthly limit
                $admin['id'] // Approved by admin
            ]);
            
            if ($result) {
                $card_id = $db->lastInsertId();
                
                // Record the initial balance transaction
                $transaction_ref = 'ADMIN' . date('YmdHis') . str_pad($card_id, 4, '0', STR_PAD_LEFT);
                $transaction_query = "INSERT INTO virtual_card_transactions (
                    card_id, account_id, transaction_type, amount, currency, description, 
                    reference_number, status, created_by, created_at
                ) VALUES (?, ?, 'credit', ?, 'USD', 'Initial card balance - Admin card creation', ?, 'completed', ?, NOW())";
                
                $transaction_result = $db->query($transaction_query, [
                    $card_id, 
                    $admin['id'], 
                    $initial_balance, 
                    $transaction_ref, 
                    $admin['id']
                ]);
                
                echo "<p style='color: green;'><strong>✓ Virtual card created successfully!</strong></p>";
                echo "Card ID: " . $card_id . "<br>";
                echo "Card Number: " . $card_number . "<br>";
                echo "Card Holder: " . $card_holder_name . "<br>";
                echo "CVV: " . $cvv . "<br>";
                echo "Expiry Date: " . $expiry_date . "<br>";
                echo "Initial Balance: $" . number_format($initial_balance, 2) . "<br>";
                echo "Daily Limit: $5,000.00<br>";
                echo "Monthly Limit: $50,000.00<br>";
                
                if ($transaction_result) {
                    echo "<p style='color: green;'>✓ Initial balance transaction recorded successfully!</p>";
                    echo "Transaction Reference: " . $transaction_ref . "<br>";
                } else {
                    echo "<p style='color: orange;'>⚠ Card created but transaction recording failed.</p>";
                }
                
            } else {
                echo "<p style='color: red;'>✗ Failed to create virtual card.</p>";
            }
        }
        
    } else {
        echo "<p style='color: red;'>Admin user not found.</p>";
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'>Error: " . $e->getMessage() . "</p>";
}
?>
