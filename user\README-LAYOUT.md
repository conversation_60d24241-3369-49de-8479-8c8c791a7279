# User Dashboard Layout System

## Overview
This document explains the universal layout system for user dashboard pages that ensures no gaps between the sidebar and main content.

## Problem Solved
Previously, the dashboard used Bootstrap's grid system (`col-md-3`, `col-lg-2` for sidebar and `col-md-9`, `col-lg-10` for content) which created unwanted gaps between the sidebar and content area.

## Solution
The new layout system uses:
- **Fixed positioned sidebar** (280px wide)
- **Main content wrapper** with `margin-left: 280px`
- **Universal CSS** that applies to all user pages
- **Responsive design** that works on mobile devices

## File Structure

### Core Layout Files
- `assets/css/universal-layout.css` - Universal layout styles
- `user/shared/header.php` - Includes universal-layout.css
- `user/shared/sidebar.php` - Fixed positioned sidebar
- `user/shared/user_header.php` - Header component
- `user/shared/user_footer.php` - Footer component
- `user/shared/page-template.php` - Template for new pages

### Layout Structure
```html
<!-- Sidebar (fixed positioned) -->
<?php require_once '../shared/sidebar.php'; ?>

<!-- Main Content Wrapper -->
<div class="main-content-wrapper">
    <!-- Header -->
    <?php require_once '../shared/user_header.php'; ?>
    
    <!-- Content -->
    <div class="main-content">
        <!-- Your page content here -->
    </div>
    
    <!-- Footer -->
    <?php require_once '../shared/user_footer.php'; ?>
</div>
```

## CSS Classes

### Main Layout Classes
- `.main-content-wrapper` - Main container with margin-left: 280px
- `.main-content` - Content area with padding and flex: 1
- `.banking-sidebar` - Fixed positioned sidebar (280px wide)

### Responsive Behavior
- **Desktop**: Sidebar fixed at 280px, content with margin-left
- **Mobile** (< 768px): Sidebar hidden/overlay, content full width

## Creating New Pages

### Use the Template
1. Copy `user/shared/page-template.php`
2. Rename to your page name
3. Update the page title and content
4. Follow the existing structure

### DO NOT Use
- Bootstrap grid system for main layout (`col-md-*`, `col-lg-*`)
- Container-fluid with rows for sidebar/content separation
- Manual positioning that conflicts with universal layout

### DO Use
- The provided template structure
- Bootstrap components INSIDE the `.main-content` area
- The universal CSS classes

## Key Benefits
1. **No gaps** between sidebar and content
2. **Universal** - works on all user pages
3. **Responsive** - mobile-friendly
4. **Maintainable** - centralized CSS
5. **Consistent** - same layout across all pages

## Troubleshooting

### If you see gaps:
1. Check if you're using Bootstrap grid for main layout
2. Ensure you're including `universal-layout.css`
3. Verify the HTML structure matches the template
4. Check for custom CSS that overrides the layout

### Mobile issues:
1. The layout automatically adjusts on mobile
2. Sidebar becomes hidden/overlay on screens < 768px
3. Content takes full width on mobile

## Migration Guide

### From Old Layout:
```html
<!-- OLD - Creates gaps -->
<div class="container-fluid">
    <div class="row">
        <div class="col-md-3 col-lg-2 px-0">
            <?php require_once '../shared/sidebar.php'; ?>
        </div>
        <div class="col-md-9 col-lg-10">
            <!-- content -->
        </div>
    </div>
</div>
```

### To New Layout:
```html
<!-- NEW - No gaps -->
<?php require_once '../shared/sidebar.php'; ?>
<div class="main-content-wrapper">
    <?php require_once '../shared/user_header.php'; ?>
    <div class="main-content">
        <!-- content -->
    </div>
    <?php require_once '../shared/user_footer.php'; ?>
</div>
```

## Support
If you encounter layout issues, check:
1. HTML structure matches template
2. Universal CSS is loaded
3. No conflicting custom CSS
4. Bootstrap grid not used for main layout
