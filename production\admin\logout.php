<?php
session_start();
require_once '../config/config.php';

// Log the logout activity if user is logged in
if (isset($_SESSION['user_id'])) {
    try {
        logActivity($_SESSION['user_id'], 'Admin logout', 'accounts', $_SESSION['user_id']);
    } catch (Exception $e) {
        error_log("Logout logging error: " . $e->getMessage());
    }
}

// Clear all session data
session_unset();
session_destroy();

// Redirect to admin login
header('Location: login.php');
exit();
?>
