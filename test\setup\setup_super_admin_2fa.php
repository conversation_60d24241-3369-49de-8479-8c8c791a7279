<?php
/**
 * Setup script for Super Admin 2FA tables and settings
 * Run this script to initialize the Google Authenticator functionality for super admin
 */

require_once '../config/database.php';

$message = '';
$error = '';

if (isset($_GET['setup'])) {
    try {
        $db = getDB();
        
        echo "<h2>Setting up Super Admin 2FA System...</h2>";
        
        // Read and execute the SQL file
        $sql = file_get_contents('database/create_super_admin_2fa_table.sql');
        
        // Split SQL statements by semicolon
        $statements = array_filter(array_map('trim', explode(';', $sql)));
        
        $executed_count = 0;
        foreach ($statements as $statement) {
            if (!empty($statement)) {
                try {
                    $db->query($statement);
                    echo "<div style='color: green;'>✓ Executed: " . substr(str_replace(["\n", "\r"], ' ', $statement), 0, 80) . "...</div>";
                    $executed_count++;
                } catch (Exception $e) {
                    if (strpos($e->getMessage(), 'already exists') !== false || 
                        strpos($e->getMessage(), 'Duplicate') !== false) {
                        echo "<div style='color: orange;'>ℹ️ Already exists: " . substr($statement, 0, 50) . "...</div>";
                    } else {
                        echo "<div style='color: red;'>✗ Error: " . $e->getMessage() . "</div>";
                        echo "<div style='color: red;'>Statement: " . substr($statement, 0, 100) . "...</div>";
                    }
                }
            }
        }
        
        echo "<br><div style='color: green; font-weight: bold;'>✓ Super Admin 2FA setup complete! Executed $executed_count statements.</div>";
        echo "<br><div style='background: #e3f2fd; padding: 15px; border-radius: 5px;'>";
        echo "<h3>Next Steps:</h3>";
        echo "<ol>";
        echo "<li>Access the super admin panel: <a href='super-admin/login.php'>super-admin/login.php</a></li>";
        echo "<li>Login with username: <strong>superadmin</strong> and password: <strong>admin123</strong></li>";
        echo "<li>Navigate to the 2FA Setup page to configure Google Authenticator</li>";
        echo "<li>Install Google Authenticator on your mobile device</li>";
        echo "<li>Scan the QR code and complete the setup</li>";
        echo "</ol>";
        echo "</div>";
        
        echo "<br><div style='background: #fff3cd; padding: 15px; border-radius: 5px;'>";
        echo "<h3>Security Recommendations:</h3>";
        echo "<ul>";
        echo "<li>Enable 2FA requirement in super admin settings for enhanced security</li>";
        echo "<li>Save backup codes in a secure location</li>";
        echo "<li>Regularly review 2FA audit logs</li>";
        echo "<li>Consider changing default super admin credentials</li>";
        echo "</ul>";
        echo "</div>";
        
    } catch (Exception $e) {
        $error = "Error setting up Super Admin 2FA: " . $e->getMessage();
        echo "<div style='color: red; font-weight: bold;'>✗ $error</div>";
    }
} else {
    // Show setup form
    ?>
    <!DOCTYPE html>
    <html lang="en">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Super Admin 2FA Setup</title>
        <style>
            body {
                font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
                max-width: 800px;
                margin: 0 auto;
                padding: 20px;
                background-color: #f5f5f5;
            }
            .container {
                background: white;
                padding: 30px;
                border-radius: 10px;
                box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            }
            h1 {
                color: #333;
                text-align: center;
                margin-bottom: 30px;
            }
            .info-box {
                background: #e3f2fd;
                border-left: 4px solid #2196f3;
                padding: 15px;
                margin: 20px 0;
                border-radius: 4px;
            }
            .warning-box {
                background: #fff3cd;
                border-left: 4px solid #ffc107;
                padding: 15px;
                margin: 20px 0;
                border-radius: 4px;
            }
            .btn {
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                color: white;
                padding: 12px 30px;
                border: none;
                border-radius: 5px;
                cursor: pointer;
                font-size: 16px;
                text-decoration: none;
                display: inline-block;
                transition: transform 0.2s;
            }
            .btn:hover {
                transform: translateY(-2px);
                color: white;
            }
            .feature-list {
                background: #f8f9fa;
                padding: 20px;
                border-radius: 5px;
                margin: 20px 0;
            }
            .feature-list h3 {
                margin-top: 0;
                color: #495057;
            }
            .feature-list ul {
                margin: 0;
                padding-left: 20px;
            }
            .feature-list li {
                margin-bottom: 8px;
                color: #6c757d;
            }
        </style>
    </head>
    <body>
        <div class="container">
            <h1>🔐 Super Admin 2FA Setup</h1>
            
            <div class="info-box">
                <h3>📱 Google Authenticator Integration</h3>
                <p>This setup will enable Google Authenticator (2FA) for the super admin system, providing an additional layer of security for administrative access.</p>
            </div>
            
            <div class="feature-list">
                <h3>🚀 Features to be installed:</h3>
                <ul>
                    <li><strong>Super Admin 2FA Settings Table:</strong> Stores Google Authenticator secrets and settings</li>
                    <li><strong>2FA Audit Log:</strong> Tracks all 2FA-related activities for security monitoring</li>
                    <li><strong>Backup Codes System:</strong> Generates recovery codes for emergency access</li>
                    <li><strong>Account Lockout Protection:</strong> Prevents brute force attacks on 2FA codes</li>
                    <li><strong>QR Code Generation:</strong> Easy setup with mobile authenticator apps</li>
                    <li><strong>Session Management:</strong> Integrates 2FA with existing super admin authentication</li>
                </ul>
            </div>
            
            <div class="warning-box">
                <h3>⚠️ Important Notes:</h3>
                <ul>
                    <li>This setup is specifically for <strong>super admin accounts only</strong></li>
                    <li>Regular user 2FA system will remain unchanged</li>
                    <li>Make sure you have Google Authenticator app installed on your mobile device</li>
                    <li>Backup codes will be generated - save them securely</li>
                    <li>This setup requires database write permissions</li>
                </ul>
            </div>
            
            <div class="info-box">
                <h3>📋 Prerequisites:</h3>
                <ul>
                    <li>✅ Super admin system already installed</li>
                    <li>✅ Google2FA library available in vendor directory</li>
                    <li>✅ Database connection configured</li>
                    <li>✅ PHP session support enabled</li>
                </ul>
            </div>
            
            <div style="text-align: center; margin-top: 30px;">
                <a href="?setup=1" class="btn">
                    🔧 Install Super Admin 2FA System
                </a>
            </div>
            
            <div style="text-align: center; margin-top: 20px;">
                <p style="color: #6c757d; font-size: 14px;">
                    This will create the necessary database tables and configure the 2FA system.<br>
                    The process is safe and can be run multiple times.
                </p>
            </div>
        </div>
    </body>
    </html>
    <?php
}
?>
