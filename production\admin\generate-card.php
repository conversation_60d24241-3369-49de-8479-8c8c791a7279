<?php
require_once '../config/config.php';
requireAdmin();

$page_title = 'Generate Virtual Card';

// Handle form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    try {
        $db = getDB();
        
        $account_id = intval($_POST['account_id']);
        $card_holder_name = trim($_POST['card_holder_name']);
        $daily_limit = floatval($_POST['daily_limit']);
        $monthly_limit = floatval($_POST['monthly_limit']);
        $initial_balance = floatval($_POST['initial_balance'] ?? 0);
        
        // Validate inputs
        if (empty($account_id) || empty($card_holder_name)) {
            throw new Exception("Account and card holder name are required.");
        }
        
        if ($daily_limit <= 0 || $monthly_limit <= 0) {
            throw new Exception("Limits must be greater than zero.");
        }
        
        if ($daily_limit > $monthly_limit) {
            throw new Exception("Daily limit cannot exceed monthly limit.");
        }
        
        // Check if account exists
        $account_check = $db->query("SELECT id, first_name, last_name FROM accounts WHERE id = ? AND is_admin = 0", [$account_id]);
        if (!$account_check->fetch_assoc()) {
            throw new Exception("Invalid account selected.");
        }
        
        // Generate card details
        $card_number = generateCardNumber();
        $cvv = str_pad(rand(100, 999), 3, '0', STR_PAD_LEFT);
        $expiry_date = date('Y-m-d', strtotime('+3 years'));
        
        // Start transaction
        $db->query("START TRANSACTION");
        
        // Insert virtual card
        $insert_card = "INSERT INTO virtual_cards (
            account_id, card_number, card_holder_name, expiry_date, cvv,
            card_balance, daily_limit, monthly_limit, status, approved_by, approved_at
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, 'active', ?, NOW())";

        $card_id = $db->insert($insert_card, [
            $account_id, $card_number, $card_holder_name, $expiry_date, $cvv,
            $initial_balance, $daily_limit, $monthly_limit, $_SESSION['user_id']
        ]);

        if (!$card_id) {
            throw new Exception("Failed to create virtual card.");
        }
        
        // If initial balance > 0, create a credit transaction
        if ($initial_balance > 0) {
            $reference = 'CARD' . date('Ymd') . str_pad($card_id, 6, '0', STR_PAD_LEFT);
            
            $insert_transaction = "INSERT INTO virtual_card_transactions (
                card_id, account_id, transaction_type, amount, description, 
                reference_number, status, processed_by
            ) VALUES (?, ?, 'credit', ?, ?, ?, 'completed', ?)";
            
            $trans_result = $db->query($insert_transaction, [
                $card_id, $account_id, $initial_balance, 
                "Initial card balance - Card generation", $reference, $_SESSION['user_id']
            ]);
            
            if (!$trans_result) {
                throw new Exception("Failed to create initial balance transaction.");
            }
        }
        
        // Commit transaction
        $db->query("COMMIT");
        
        $success = "Virtual card generated successfully! Card ID: $card_id";
        
    } catch (Exception $e) {
        if (isset($db)) {
            $db->query("ROLLBACK");
        }
        $error = $e->getMessage();
    }
}

// Get users for dropdown
try {
    $db = getDB();
    $users_result = $db->query("SELECT id, first_name, last_name, username, account_number FROM accounts WHERE is_admin = 0 ORDER BY first_name, last_name");
    $users = [];
    while ($user = $users_result->fetch_assoc()) {
        $users[] = $user;
    }
} catch (Exception $e) {
    $users = [];
}

// Helper function to generate card number
function generateCardNumber() {
    // Generate a 16-digit card number starting with 4 (Visa-like)
    $prefix = '4000';
    $middle = '';
    for ($i = 0; $i < 8; $i++) {
        $middle .= rand(0, 9);
    }
    
    // Calculate check digit using Luhn algorithm
    $number = $prefix . $middle;
    $sum = 0;
    $alternate = false;
    
    for ($i = strlen($number) - 1; $i >= 0; $i--) {
        $digit = intval($number[$i]);
        if ($alternate) {
            $digit *= 2;
            if ($digit > 9) {
                $digit = ($digit % 10) + 1;
            }
        }
        $sum += $digit;
        $alternate = !$alternate;
    }
    
    $checkDigit = (10 - ($sum % 10)) % 10;
    return $number . $checkDigit;
}

include 'includes/admin-header.php';
?>

<!-- Breadcrumb -->
<nav aria-label="breadcrumb" style="margin-bottom: 1rem;">
    <ol class="breadcrumb">
        <li class="breadcrumb-item"><a href="index.php">Admin</a></li>
        <li class="breadcrumb-item"><a href="virtual-cards.php">Virtual Cards</a></li>
        <li class="breadcrumb-item active" aria-current="page">Generate Card</li>
    </ol>
</nav>

<!-- Success Message -->
<?php if (isset($success)): ?>
<div class="alert alert-success" role="alert">
    <div class="d-flex">
        <div><i class="fas fa-check-circle me-2"></i></div>
        <div>
            <h4 class="alert-title">Success!</h4>
            <div class="text-muted"><?php echo htmlspecialchars($success); ?></div>
        </div>
    </div>
</div>
<?php endif; ?>

<!-- Error Message -->
<?php if (isset($error)): ?>
<div class="alert alert-danger" role="alert">
    <div class="d-flex">
        <div><i class="fas fa-exclamation-circle me-2"></i></div>
        <div>
            <h4 class="alert-title">Error!</h4>
            <div class="text-muted"><?php echo htmlspecialchars($error); ?></div>
        </div>
    </div>
</div>
<?php endif; ?>

<div class="row row-cards">
    <!-- Card Generation Form -->
    <div class="col-md-8">
        <div class="card">
            <div class="card-header">
                <h3 class="card-title">
                    <i class="fas fa-plus-circle me-2"></i>
                    Generate New Virtual Card
                </h3>
            </div>
            <div class="card-body">
                <form method="POST" action="">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">Select Account Holder <span class="text-danger">*</span></label>
                                <select name="account_id" class="form-select" required>
                                    <option value="">Choose account holder...</option>
                                    <?php foreach ($users as $user): ?>
                                    <option value="<?php echo $user['id']; ?>" <?php echo (isset($_POST['account_id']) && $_POST['account_id'] == $user['id']) ? 'selected' : ''; ?>>
                                        <?php echo htmlspecialchars($user['first_name'] . ' ' . $user['last_name']); ?> 
                                        (@<?php echo htmlspecialchars($user['username']); ?>) - 
                                        Acc: <?php echo htmlspecialchars($user['account_number']); ?>
                                    </option>
                                    <?php endforeach; ?>
                                </select>
                            </div>
                        </div>
                        
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">Card Holder Name <span class="text-danger">*</span></label>
                                <input type="text" name="card_holder_name" class="form-control" 
                                       placeholder="Name as it appears on card" 
                                       value="<?php echo htmlspecialchars($_POST['card_holder_name'] ?? ''); ?>" required>
                                <small class="form-hint">This will be printed on the virtual card</small>
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">Daily Spending Limit <span class="text-danger">*</span></label>
                                <div class="input-group">
                                    <span class="input-group-text">$</span>
                                    <input type="number" name="daily_limit" class="form-control"
                                           step="0.01" min="1" max="1000000000"
                                           value="<?php echo $_POST['daily_limit'] ?? '5000'; ?>" required>
                                </div>
                                <small class="form-hint">Maximum amount that can be spent per day (up to $1 billion)</small>
                            </div>
                        </div>

                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">Monthly Spending Limit <span class="text-danger">*</span></label>
                                <div class="input-group">
                                    <span class="input-group-text">$</span>
                                    <input type="number" name="monthly_limit" class="form-control"
                                           step="0.01" min="1" max="1000000000"
                                           value="<?php echo $_POST['monthly_limit'] ?? '50000'; ?>" required>
                                </div>
                                <small class="form-hint">Maximum amount that can be spent per month (up to $1 billion)</small>
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">Initial Card Balance</label>
                                <div class="input-group">
                                    <span class="input-group-text">$</span>
                                    <input type="number" name="initial_balance" class="form-control"
                                           step="0.01" min="0" max="1000000000"
                                           value="<?php echo $_POST['initial_balance'] ?? '0'; ?>">
                                </div>
                                <small class="form-hint">Optional: Add initial balance to the card (up to $1 billion)</small>
                            </div>
                        </div>
                    </div>
                    
                    <div class="form-footer">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-credit-card me-2"></i>
                            Generate Virtual Card
                        </button>
                        <a href="virtual-cards.php" class="btn btn-secondary ms-2">
                            <i class="fas fa-arrow-left me-2"></i>
                            Back to Cards
                        </a>
                    </div>
                </form>
            </div>
        </div>
    </div>
    
    <!-- Virtual Card Preview -->
    <div class="col-md-4">
        <div class="card border-0 shadow-sm">
            <div class="card-header" style="background: linear-gradient(135deg, #4361ee 0%, #3b82f6 100%); color: white;">
                <h3 class="card-title mb-0">
                    <i class="fas fa-credit-card me-2"></i>
                    Card Preview
                </h3>
            </div>
            <div class="card-body">
                <!-- Virtual Card Design -->
                <div class="virtual-card-preview" id="cardPreview">
                    <div class="virtual-card">
                        <div class="card-background">
                            <div class="card-chip">
                                <div class="chip-line"></div>
                                <div class="chip-line"></div>
                                <div class="chip-line"></div>
                                <div class="chip-line"></div>
                            </div>
                            <div class="card-number" id="previewCardNumber">
                                •••• •••• •••• ••••
                            </div>
                            <div class="card-details">
                                <div class="card-holder">
                                    <div class="label">CARD HOLDER</div>
                                    <div class="value" id="previewCardHolder">SELECT ACCOUNT</div>
                                </div>
                                <div class="card-expiry">
                                    <div class="label">VALID THRU</div>
                                    <div class="value" id="previewExpiry"><?php echo date('m/y', strtotime('+3 years')); ?></div>
                                </div>
                            </div>
                            <div class="card-logo">
                                <div class="logo-text">VIRTUAL</div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Card Info -->
                <div class="mt-3">
                    <div class="row text-center">
                        <div class="col-6">
                            <div class="text-muted small">Daily Limit</div>
                            <div class="fw-bold" id="previewDailyLimit">$5,000</div>
                        </div>
                        <div class="col-6">
                            <div class="text-muted small">Monthly Limit</div>
                            <div class="fw-bold" id="previewMonthlyLimit">$50,000</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
/* Virtual Card Preview Styles */
.virtual-card-preview {
    perspective: 1000px;
    margin-bottom: 1rem;
}

.virtual-card {
    width: 100%;
    height: 200px;
    position: relative;
    transform-style: preserve-3d;
    transition: transform 0.3s ease;
}

.virtual-card:hover {
    transform: rotateY(5deg) rotateX(5deg);
}

.card-background {
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 16px;
    padding: 20px;
    color: white;
    position: relative;
    box-shadow: 0 8px 25px rgba(0,0,0,0.15);
    overflow: hidden;
}

.card-background::before {
    content: '';
    position: absolute;
    top: -50%;
    right: -50%;
    width: 100%;
    height: 100%;
    background: radial-gradient(circle, rgba(255,255,255,0.1) 0%, transparent 70%);
    border-radius: 50%;
}

.card-chip {
    width: 35px;
    height: 25px;
    background: linear-gradient(135deg, #ffd700 0%, #ffed4e 100%);
    border-radius: 4px;
    position: relative;
    margin-bottom: 20px;
    display: flex;
    flex-direction: column;
    justify-content: space-around;
    padding: 3px;
}

.chip-line {
    height: 1px;
    background: rgba(0,0,0,0.2);
    margin: 1px 0;
}

.card-number {
    font-family: 'Courier New', monospace;
    font-size: 18px;
    font-weight: bold;
    letter-spacing: 2px;
    margin-bottom: 20px;
    text-shadow: 0 1px 2px rgba(0,0,0,0.3);
}

.card-details {
    display: flex;
    justify-content: space-between;
    align-items: flex-end;
}

.card-holder, .card-expiry {
    flex: 1;
}

.card-expiry {
    text-align: right;
}

.label {
    font-size: 10px;
    opacity: 0.8;
    margin-bottom: 2px;
    letter-spacing: 1px;
}

.value {
    font-size: 14px;
    font-weight: bold;
    text-shadow: 0 1px 2px rgba(0,0,0,0.3);
}

.card-logo {
    position: absolute;
    top: 20px;
    right: 20px;
}

.logo-text {
    font-size: 12px;
    font-weight: bold;
    opacity: 0.7;
    letter-spacing: 1px;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .virtual-card {
        height: 160px;
    }

    .card-background {
        padding: 15px;
    }

    .card-number {
        font-size: 16px;
        margin-bottom: 15px;
    }

    .value {
        font-size: 12px;
    }
}
</style>

<script>
// Auto-populate card holder name when account is selected
document.querySelector('select[name="account_id"]').addEventListener('change', function() {
    const selectedOption = this.options[this.selectedIndex];
    if (selectedOption.value) {
        const text = selectedOption.text;
        const name = text.split(' (@')[0]; // Extract name before username
        const upperName = name.toUpperCase();
        document.querySelector('input[name="card_holder_name"]').value = upperName;

        // Update preview
        document.getElementById('previewCardHolder').textContent = upperName;
        document.getElementById('previewCardNumber').textContent = '4000 •••• •••• ••••';
    } else {
        document.getElementById('previewCardHolder').textContent = 'SELECT ACCOUNT';
        document.getElementById('previewCardNumber').textContent = '•••• •••• •••• ••••';
    }
});

// Update card holder name preview
document.querySelector('input[name="card_holder_name"]').addEventListener('input', function() {
    const name = this.value.toUpperCase();
    document.getElementById('previewCardHolder').textContent = name || 'CARD HOLDER';
});

// Update limit previews
document.querySelector('input[name="daily_limit"]').addEventListener('input', function() {
    const limit = parseFloat(this.value) || 0;
    document.getElementById('previewDailyLimit').textContent = '$' + limit.toLocaleString();
    validateLimits();
});

document.querySelector('input[name="monthly_limit"]').addEventListener('input', function() {
    const limit = parseFloat(this.value) || 0;
    document.getElementById('previewMonthlyLimit').textContent = '$' + limit.toLocaleString();
    validateLimits();
});

function validateLimits() {
    const dailyLimit = parseFloat(document.querySelector('input[name="daily_limit"]').value) || 0;
    const monthlyLimit = parseFloat(document.querySelector('input[name="monthly_limit"]').value) || 0;

    if (dailyLimit > monthlyLimit && monthlyLimit > 0) {
        document.querySelector('input[name="daily_limit"]').setCustomValidity('Daily limit cannot exceed monthly limit');
    } else {
        document.querySelector('input[name="daily_limit"]').setCustomValidity('');
    }
}
</script>

<?php include 'includes/admin-footer.php'; ?>
