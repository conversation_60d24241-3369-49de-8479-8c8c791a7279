<?php
require_once '../vendor/GoogleAuthenticator/Google2FA.php';

$google2fa = new Google2FA();
$secret = 'DDMKZ6W5XOVUX43T'; // The secret from the QR code

echo "Secret: " . $secret . "\n";
echo "Current OTP: " . $google2fa->getCurrentOtp($secret) . "\n";
echo "Time: " . date('Y-m-d H:i:s') . "\n";

// Test a few time windows
for ($i = -2; $i <= 2; $i++) {
    $time = time() + ($i * 30);
    $code = $google2fa->getCurrentOtp($secret);
    echo "Time offset {$i}: " . date('H:i:s', $time) . " -> " . $code . "\n";
}
?>
