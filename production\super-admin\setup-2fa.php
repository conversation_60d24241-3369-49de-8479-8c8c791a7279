<?php
/**
 * Super Admin 2FA Setup Checker and Installer
 * Safe setup process for Google Authenticator
 */

session_start();

// Check if user is logged in as super admin
if (!isset($_SESSION['super_admin_logged_in']) || $_SESSION['super_admin_logged_in'] !== true) {
    header('Location: login.php');
    exit;
}

$status = [];
$can_setup_2fa = true;
$error = '';
$success = '';

// Check database connection
try {
    require_once '../config/database.php';
    $db = getDB();
    $status['database'] = ['status' => 'success', 'message' => 'Database connection successful'];
} catch (Exception $e) {
    $status['database'] = ['status' => 'error', 'message' => 'Database connection failed: ' . $e->getMessage()];
    $can_setup_2fa = false;
}

// Check if Google2FA library exists
if (file_exists('../vendor/GoogleAuthenticator/Google2FA.php')) {
    $status['library'] = ['status' => 'success', 'message' => 'Google2FA library found'];
    
    // Test library loading
    try {
        require_once '../vendor/GoogleAuthenticator/Google2FA.php';
        $google2fa = new Google2FA();
        $test_secret = $google2fa->generateSecretKey();
        $status['library_test'] = ['status' => 'success', 'message' => 'Google2FA library working correctly'];
    } catch (Exception $e) {
        $status['library_test'] = ['status' => 'error', 'message' => 'Google2FA library error: ' . $e->getMessage()];
        $can_setup_2fa = false;
    }
} else {
    $status['library'] = ['status' => 'error', 'message' => 'Google2FA library not found'];
    $can_setup_2fa = false;
}

// Check if 2FA tables exist
try {
    $db = getDB();
    
    // Check super_admin_2fa_settings table
    $result = $db->query("SHOW TABLES LIKE 'super_admin_2fa_settings'");
    if ($result && $result->num_rows > 0) {
        $status['2fa_table'] = ['status' => 'success', 'message' => '2FA settings table exists'];
    } else {
        $status['2fa_table'] = ['status' => 'warning', 'message' => '2FA settings table not found - will be created'];
    }
    
    // Check super_admin_2fa_audit table
    $result = $db->query("SHOW TABLES LIKE 'super_admin_2fa_audit'");
    if ($result && $result->num_rows > 0) {
        $status['audit_table'] = ['status' => 'success', 'message' => '2FA audit table exists'];
    } else {
        $status['audit_table'] = ['status' => 'warning', 'message' => '2FA audit table not found - will be created'];
    }
    
} catch (Exception $e) {
    $status['tables'] = ['status' => 'error', 'message' => 'Error checking tables: ' . $e->getMessage()];
    $can_setup_2fa = false;
}

// Handle setup action
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action'])) {
    if ($_POST['action'] === 'install_tables' && $can_setup_2fa) {
        try {
            $db = getDB();
            
            // Read and execute the SQL file
            $sql_file = '../database/create_super_admin_2fa_table.sql';
            if (file_exists($sql_file)) {
                $sql = file_get_contents($sql_file);
                
                // Split SQL statements
                $statements = array_filter(array_map('trim', explode(';', $sql)));
                
                $executed = 0;
                foreach ($statements as $statement) {
                    if (!empty($statement)) {
                        try {
                            $db->query($statement);
                            $executed++;
                        } catch (Exception $e) {
                            // Ignore "already exists" errors
                            if (strpos($e->getMessage(), 'already exists') === false && 
                                strpos($e->getMessage(), 'Duplicate') === false) {
                                throw $e;
                            }
                        }
                    }
                }
                
                $success = "2FA database tables installed successfully! ($executed statements executed)";
                
                // Refresh status
                header('Location: setup-2fa.php?installed=1');
                exit;
                
            } else {
                $error = 'SQL file not found: ' . $sql_file;
            }
            
        } catch (Exception $e) {
            $error = 'Error installing tables: ' . $e->getMessage();
        }
    }
}

if (isset($_GET['installed'])) {
    $success = '2FA system installed successfully! You can now set up Google Authenticator.';
}

$page_title = '2FA Setup';
$page_subtitle = 'Configure Google Authenticator for enhanced security';

// Include header
include 'includes/header.php';
?>
<!-- Success/Error Messages -->
<?php if ($error): ?>
    <div class="alert alert-danger alert-dismissible fade show">
        <i class="fas fa-exclamation-triangle"></i> <?php echo htmlspecialchars($error); ?>
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
<?php endif; ?>

<?php if ($success): ?>
    <div class="alert alert-success alert-dismissible fade show">
        <i class="fas fa-check-circle"></i> <?php echo htmlspecialchars($success); ?>
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
<?php endif; ?>

<div class="card border-0 shadow-sm mb-4">
    <div class="card-header bg-primary text-white">
        <h5 class="card-title mb-0">
            <i class="fas fa-clipboard-check"></i> System Requirements Check
        </h5>
    </div>
    <div class="card-body">
            <h1 class="text-center mb-4">
                <i class="fas fa-mobile-alt text-primary"></i> 
                Super Admin 2FA Setup
            </h1>
            
            <?php if ($error): ?>
                <div class="alert alert-danger">
                    <i class="fas fa-exclamation-triangle"></i> <?php echo htmlspecialchars($error); ?>
                </div>
            <?php endif; ?>
            
            <?php if ($success): ?>
                <div class="alert alert-success">
                    <i class="fas fa-check-circle"></i> <?php echo htmlspecialchars($success); ?>
                </div>
            <?php endif; ?>
            
            <h5><i class="fas fa-clipboard-check"></i> System Requirements Check</h5>
            
        <?php foreach ($status as $check => $result): ?>
            <div class="d-flex align-items-center p-3 mb-2 border-start border-4
                        <?php echo $result['status'] === 'success' ? 'border-success bg-light-success' :
                                   ($result['status'] === 'warning' ? 'border-warning bg-light-warning' : 'border-danger bg-light-danger'); ?>">
                <div class="me-3">
                    <?php if ($result['status'] === 'success'): ?>
                        <i class="fas fa-check-circle text-success fs-5"></i>
                    <?php elseif ($result['status'] === 'warning'): ?>
                        <i class="fas fa-exclamation-triangle text-warning fs-5"></i>
                    <?php else: ?>
                        <i class="fas fa-times-circle text-danger fs-5"></i>
                    <?php endif; ?>
                </div>
                <div>
                    <strong><?php echo ucfirst(str_replace('_', ' ', $check)); ?>:</strong>
                    <?php echo htmlspecialchars($result['message']); ?>
                </div>
            </div>
        <?php endforeach; ?>
    </div>
</div>

<?php if ($can_setup_2fa): ?>
    <?php if (!isset($status['2fa_table']) || $status['2fa_table']['status'] !== 'success'): ?>
        <div class="card border-0 shadow-sm mb-4">
            <div class="card-header bg-success text-white">
                <h5 class="card-title mb-0">
                    <i class="fas fa-download"></i> Ready to Install 2FA System
                </h5>
            </div>
            <div class="card-body text-center">
                <p class="text-muted">Click the button below to create the necessary database tables for 2FA.</p>

                <form method="POST" style="display: inline;">
                    <input type="hidden" name="action" value="install_tables">
                    <button type="submit" class="btn btn-primary btn-lg">
                        <i class="fas fa-download"></i> Install 2FA Database Tables
                    </button>
                </form>
            </div>
        </div>
    <?php else: ?>
        <div class="card border-0 shadow-sm mb-4">
            <div class="card-header bg-success text-white">
                <h5 class="card-title mb-0">
                    <i class="fas fa-check-circle"></i> 2FA System Ready!
                </h5>
            </div>
            <div class="card-body text-center">
                <p class="text-muted">All requirements are met. You can now configure Google Authenticator.</p>

                <a href="2fa-setup.php" class="btn btn-success btn-lg">
                    <i class="fas fa-mobile-alt"></i> Configure Google Authenticator
                </a>
            </div>
        </div>
    <?php endif; ?>
<?php else: ?>
    <div class="alert alert-danger">
        <h6><i class="fas fa-exclamation-triangle"></i> Cannot Setup 2FA</h6>
        <p>Please resolve the errors above before proceeding with 2FA setup.</p>
    </div>
<?php endif; ?>

<div class="card border-0 shadow-sm mb-4">
    <div class="card-header bg-info text-white">
        <h5 class="card-title mb-0">
            <i class="fas fa-info-circle"></i> Setup Information
        </h5>
    </div>
    <div class="card-body">
        <div class="row">
            <div class="col-md-6">
                <h6><i class="fas fa-list-ol"></i> What happens next?</h6>
                <ol>
                    <li>Install database tables (if needed)</li>
                    <li>Configure Google Authenticator</li>
                    <li>Test 2FA verification</li>
                    <li>Enable 2FA requirement (optional)</li>
                </ol>
            </div>
            <div class="col-md-6">
                <h6><i class="fas fa-shield-alt"></i> Security Notes</h6>
                <ul>
                    <li>2FA is optional until you enable it</li>
                    <li>Backup codes will be generated</li>
                    <li>You won't be locked out during setup</li>
                    <li>All actions are logged for security</li>
                </ul>
            </div>
        </div>
    </div>
</div>

<div class="d-flex justify-content-between">
    <a href="dashboard.php" class="btn btn-outline-secondary">
        <i class="fas fa-arrow-left"></i> Back to Dashboard
    </a>
</div>

<?php
// Include footer
include 'includes/footer.php';
?>
