# Online Banking Email System Documentation

## 📧 Email System Overview

The online banking application uses a comprehensive email system for notifications, OTP delivery, and user communications. The system supports both SMTP and localhost configurations with professional email templates.

## 🔧 Configuration Files

### Core Email Files
- **`config/email.php`** - Main email configuration and sending functions
- **`config/email_templates.php`** - Centralized email template system
- **`config/EmailManager.php`** - Alternative email manager class
- **`config/super_admin_settings.php`** - Dynamic SMTP configuration from database

### SMTP Configuration
```php
// Dynamic SMTP settings loaded from super admin settings
SMTP_HOST: smtp.hostinger.com
SMTP_PORT: 465
SMTP_USERNAME: <EMAIL>
SMTP_PASSWORD: [Encrypted in database]
SMTP_ENCRYPTION: ssl
FROM_EMAIL: <EMAIL>
FROM_NAME: Online Banking System
```

## 📨 Email Functions

### Primary Functions
1. **`sendEmail($to, $subject, $message, $isHTML = true)`**
   - Main email sending function
   - Automatically detects localhost vs production
   - Uses SMTP for production, fallback for localhost

2. **`sendOTPEmail($email, $otp, $user_name)`**
   - Specialized OTP email function
   - Uses professional OTP template
   - Forces SMTP sending (bypasses localhost detection)

3. **`sendWelcomeEmail($email, $user_data)`**
   - Welcome email for new users
   - Uses branded welcome template

4. **`isValidEmail($email)`**
   - Email address validation
   - Uses PHP filter_var with FILTER_VALIDATE_EMAIL

### Environment Detection
- **Localhost**: Uses `sendEmailLocalhost()` for development
- **Production**: Uses `sendEmailSMTP()` with PHPMailer

## 🎨 Email Templates

### Template System Features
- **Master Template**: Consistent branding across all emails
- **Responsive Design**: Mobile-friendly email layouts
- **Professional Styling**: Banking-appropriate design
- **Dynamic Content**: Personalized user information
- **Security Elements**: Professional security notices

### Available Templates
1. **`generateWelcomeEmailTemplate($user_data)`** - New user welcome
2. **`generateOTPEmailTemplate($user_data, $otp, $expires_in)`** - Login OTP
3. **`generateTransferOTPEmailTemplate($user_data, $otp, $transfer_data)`** - Transfer verification
4. **`generateCreditAlertEmailTemplate($user_data, $transaction_data)`** - Credit notifications
5. **`generateDebitAlertEmailTemplate($user_data, $transaction_data)`** - Debit notifications

### Transfer OTP Email Template
```php
generateTransferOTPEmailTemplate($user_data, $otp_code, $transfer_data, $expires_in = 10)
```

**Features:**
- Large, prominent OTP code display
- Complete transfer details (amount, beneficiary, account, type)
- Security warnings and notices
- Professional banking design
- Call-to-action button to access account
- Expiration time display

## 🔐 Security Features

### Email Security
- **HTML Email Support**: Rich formatting with security considerations
- **Header Security**: Proper email headers to prevent spoofing
- **Content Validation**: Input sanitization for email content
- **Rate Limiting**: Prevents email spam (implementation dependent)

### OTP Security
- **Expiration Time**: 10-minute expiration for transfer OTPs
- **Single Use**: OTPs marked as used after verification
- **Secure Storage**: OTPs stored with purpose and timestamps
- **User Validation**: OTPs tied to specific user accounts

## 🚀 Implementation Examples

### Transfer OTP Email Implementation
```php
// Include email system
require_once '../../config/email.php';
require_once '../../config/email_templates.php';

// Generate OTP
$otp_code = sprintf('%06d', mt_rand(100000, 999999));

// Prepare transfer data
$transfer_data = [
    'amount' => '$100.00',
    'beneficiary_name' => 'John Doe',
    'beneficiary_account' => '**********',
    'transfer_type' => 'Local Bank Transfer'
];

// Generate email content
$email_body = generateTransferOTPEmailTemplate([
    'first_name' => $user['first_name'],
    'last_name' => $user['last_name'],
    'email' => $user['email']
], $otp_code, $transfer_data);

// Send email
$email_sent = sendEmail($user['email'], $subject, $email_body, true);
```

## 🐛 Debugging and Logging

### Error Logging
- **Email Attempts**: All email sending attempts are logged
- **Success/Failure**: Results logged with user ID and email
- **SMTP Errors**: Detailed SMTP error logging
- **Template Errors**: Template generation error logging

### Debug Information
```php
// Enable error logging for email debugging
error_log("Transfer OTP email attempt - User: {$user_id}, Email: {$email}");
error_log("Transfer OTP email result - Success: " . ($email_sent ? 'YES' : 'NO'));
```

## 🧪 Testing

### Email Testing Tools
- **`test/debug_email_system.php`** - Comprehensive email system testing
- **`test/test_transfer_otp_email.php`** - Transfer OTP email testing
- **Template Preview**: Generated HTML files for email preview

### Test Results
- ✅ Email configuration working
- ✅ SMTP settings loaded correctly
- ✅ Email templates generating properly
- ✅ Email sending functional (localhost and production)
- ✅ OTP generation and storage working
- ✅ Transfer OTP emails delivered successfully

## 📋 Maintenance

### Regular Checks
1. **SMTP Credentials**: Verify SMTP settings in super admin panel
2. **Email Logs**: Monitor error logs for email failures
3. **Template Updates**: Keep email templates current with branding
4. **Delivery Rates**: Monitor email delivery success rates

### Common Issues
1. **SMTP Authentication**: Check credentials and server settings
2. **Email Blocking**: Verify emails not going to spam folders
3. **Template Errors**: Check for missing template functions
4. **Database Issues**: Verify OTP table structure and constraints

## 🔄 Recent Fixes Applied

### Transfer OTP Email Issue Resolution
1. **Problem**: OTP emails not being sent for transfers
2. **Root Cause**: Missing email configuration includes in `generate-transfer-otp.php`
3. **Solution**: Added proper includes and template system integration
4. **Result**: ✅ Transfer OTP emails now working correctly

### Improvements Made
- ✅ Added email configuration includes to OTP generation
- ✅ Created professional transfer OTP email template
- ✅ Enhanced error logging for email debugging
- ✅ Improved email sending logic with fallbacks
- ✅ Added comprehensive testing tools

## 📞 Support

### Email System Support
- **Configuration**: Check super admin settings for SMTP configuration
- **Templates**: Email templates located in `config/email_templates.php`
- **Testing**: Use provided testing tools for debugging
- **Logs**: Check PHP error logs for email-related errors

### Contact Information
- **SMTP Provider**: Hostinger (smtp.hostinger.com)
- **From Email**: <EMAIL>
- **Support Email**: <EMAIL>
- **Security Email**: <EMAIL>
