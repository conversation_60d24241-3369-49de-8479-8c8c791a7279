<?php
require_once 'config/config.php';

echo "<h2>Virtual Cards Table Structure</h2>";

try {
    $db = getDB();
    
    // Check table structure
    $result = $db->query('DESCRIBE virtual_cards');
    echo "<table border='1'>";
    echo "<tr><th>Field</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th></tr>";
    while($row = $result->fetch_assoc()) {
        echo "<tr>";
        echo "<td>" . $row['Field'] . "</td>";
        echo "<td>" . $row['Type'] . "</td>";
        echo "<td>" . $row['Null'] . "</td>";
        echo "<td>" . $row['Key'] . "</td>";
        echo "<td>" . $row['Default'] . "</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    // Check sample data
    echo "<h3>Sample Data</h3>";
    $sample = $db->query('SELECT * FROM virtual_cards LIMIT 3');
    if ($sample && $sample->num_rows > 0) {
        echo "<table border='1'>";
        $first = true;
        while($row = $sample->fetch_assoc()) {
            if ($first) {
                echo "<tr>";
                foreach (array_keys($row) as $key) {
                    echo "<th>$key</th>";
                }
                echo "</tr>";
                $first = false;
            }
            echo "<tr>";
            foreach ($row as $value) {
                echo "<td>" . htmlspecialchars($value) . "</td>";
            }
            echo "</tr>";
        }
        echo "</table>";
    } else {
        echo "No data found";
    }
    
} catch (Exception $e) {
    echo "Error: " . $e->getMessage();
}
?>
