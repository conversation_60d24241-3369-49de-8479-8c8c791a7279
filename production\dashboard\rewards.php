<?php
require_once '../config/config.php';
requireLogin();

$page_title = 'Rewards & Loyalty';
$site_name = getBankName();

// Calculate user rewards based on transaction activity
try {
    $db = getDB();
    $user_id = $_SESSION['user_id'];

    // Get user account info
    $account_sql = "SELECT * FROM accounts WHERE id = ?";
    $account_result = $db->query($account_sql, [$user_id]);
    $account_data = $account_result->fetch_assoc();

    // Calculate rewards points based on transactions
    $rewards_sql = "SELECT
                      COUNT(*) as total_transactions,
                      SUM(CASE WHEN sender_id = ? THEN amount ELSE 0 END) as total_spent,
                      SUM(CASE WHEN recipient_id = ? THEN amount ELSE 0 END) as total_received,
                      COUNT(CASE WHEN sender_id = ? AND amount >= 100 THEN 1 END) as large_transactions,
                      COUNT(CASE WHEN MONTH(created_at) = MONTH(CURRENT_DATE()) AND YEAR(created_at) = YEAR(CURRENT_DATE()) THEN 1 END) as monthly_transactions
                    FROM transfers
                    WHERE (sender_id = ? OR recipient_id = ?)
                    AND status = 'completed'";

    $rewards_result = $db->query($rewards_sql, [$user_id, $user_id, $user_id, $user_id, $user_id]);
    $transaction_data = $rewards_result->fetch_assoc();

    // Calculate points (example reward system)
    $points_per_transaction = 10;
    $points_per_dollar = 0.1;
    $bonus_points_large_transaction = 50;
    $monthly_bonus = 100;

    $transaction_points = $transaction_data['total_transactions'] * $points_per_transaction;
    $spending_points = floor($transaction_data['total_spent'] * $points_per_dollar);
    $large_transaction_bonus = $transaction_data['large_transactions'] * $bonus_points_large_transaction;
    $monthly_activity_bonus = ($transaction_data['monthly_transactions'] >= 5) ? $monthly_bonus : 0;

    $total_points = $transaction_points + $spending_points + $large_transaction_bonus + $monthly_activity_bonus;

    // Determine loyalty tier
    $loyalty_tier = 'Bronze';
    $tier_color = '#cd7f32';
    $next_tier_points = 1000;

    if ($total_points >= 5000) {
        $loyalty_tier = 'Platinum';
        $tier_color = '#e5e4e2';
        $next_tier_points = null;
    } elseif ($total_points >= 2500) {
        $loyalty_tier = 'Gold';
        $tier_color = '#ffd700';
        $next_tier_points = 5000;
    } elseif ($total_points >= 1000) {
        $loyalty_tier = 'Silver';
        $tier_color = '#c0c0c0';
        $next_tier_points = 2500;
    }

    // Calculate cashback earned (1% of spending)
    $cashback_earned = $transaction_data['total_spent'] * 0.01;

    // Get recent activity for rewards
    $recent_activity_sql = "SELECT t.*,
                                   CASE
                                       WHEN t.sender_id = ? THEN 'sent'
                                       ELSE 'received'
                                   END as direction,
                                   CASE
                                       WHEN t.sender_id = ? THEN t.recipient_name
                                       ELSE (SELECT CONCAT(first_name, ' ', last_name) FROM accounts WHERE id = t.sender_id)
                                   END as other_party
                            FROM transfers t
                            WHERE (t.sender_id = ? OR t.recipient_id = ?)
                            AND t.status = 'completed'
                            ORDER BY t.created_at DESC
                            LIMIT 5";

    $activity_result = $db->query($recent_activity_sql, [$user_id, $user_id, $user_id, $user_id]);
    $recent_activity = [];
    while ($activity = $activity_result->fetch_assoc()) {
        // Calculate points earned for this transaction
        $activity_points = $points_per_transaction;
        if ($activity['direction'] === 'sent') {
            $activity_points += floor($activity['amount'] * $points_per_dollar);
            if ($activity['amount'] >= 100) {
                $activity_points += $bonus_points_large_transaction;
            }
        }
        $activity['points_earned'] = $activity_points;
        $recent_activity[] = $activity;
    }

} catch (Exception $e) {
    error_log("Rewards page error: " . $e->getMessage());
    $account_data = [];
    $transaction_data = ['total_transactions' => 0, 'total_spent' => 0, 'total_received' => 0, 'large_transactions' => 0, 'monthly_transactions' => 0];
    $total_points = 0;
    $loyalty_tier = 'Bronze';
    $tier_color = '#cd7f32';
    $next_tier_points = 1000;
    $cashback_earned = 0;
    $recent_activity = [];
}

// Include header
require_once '../includes/dashboard/header.php';

// Include sidebar
require_once '../includes/dashboard/sidebar.php';
?>

<!-- Main Content -->
<div class="main-content">
    <!-- Top Bar -->
    <div class="top-bar">
        <h1>Rewards & Loyalty</h1>
        <div class="top-bar-actions">
            <button class="btn-outline" onclick="redeemPoints()">
                <svg width="16" height="16" fill="currentColor" viewBox="0 0 16 16">
                    <path fill-rule="evenodd" d="M5 2a1 1 0 011 1v1h1a1 1 0 010 2H6v1a1 1 0 01-2 0V6H3a1 1 0 010-2h1V3a1 1 0 011-1zm0 10a1 1 0 011 1v1h1a1 1 0 110 2H6v1a1 1 0 11-2 0v-1H3a1 1 0 110-2h1v-1a1 1 0 011-1zM12 2a1 1 0 01.967.744L14.146 7.2 17.5 9.134a1 1 0 010 1.732L14.146 12.8l-1.179 4.456a1 1 0 01-1.934 0L9.854 12.8 6.5 10.866a1 1 0 010-1.732L9.854 7.2l1.179-4.456A1 1 0 0112 2z"/>
                </svg>
                Redeem Points
            </button>
            <button class="btn-primary" onclick="window.location.reload()">
                <svg width="16" height="16" fill="currentColor" viewBox="0 0 16 16">
                    <path fill-rule="evenodd" d="M8 3a5 5 0 104.546 2.914.5.5 0 00-.908-.417A4 4 0 118 4v1z"/>
                    <path d="M8 4.466V.534a.25.25 0 01.41-.192l2.36 1.966c.12.1.12.284 0 .384L8.41 4.658A.25.25 0 018 4.466z"/>
                </svg>
                Refresh
            </button>
            <div style="width: 40px; height: 40px; border-radius: 50%; background: #6366f1; display: flex; align-items: center; justify-content: center; color: white; font-weight: 600;">
                <?php echo strtoupper(substr($_SESSION['first_name'] ?? 'U', 0, 1)); ?>
            </div>
        </div>
    </div>

    <!-- Loyalty Status Card -->
    <div style="background: linear-gradient(135deg, <?php echo $tier_color; ?> 0%, <?php echo $tier_color; ?>80 100%); border-radius: 16px; padding: 2rem; margin-bottom: 2rem; color: white; position: relative; overflow: hidden;">
        <div style="position: absolute; top: -50px; right: -50px; width: 200px; height: 200px; background: rgba(255,255,255,0.1); border-radius: 50%;"></div>
        <div style="position: relative; z-index: 1;">
            <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 1.5rem;">
                <div>
                    <h2 style="margin: 0; font-size: 2rem; font-weight: 700;"><?php echo $loyalty_tier; ?> Member</h2>
                    <p style="margin: 0; opacity: 0.9;">Welcome, <?php echo htmlspecialchars($_SESSION['first_name'] ?? 'Valued Customer'); ?>!</p>
                </div>
                <div style="text-align: right;">
                    <div style="font-size: 2.5rem; font-weight: 700;"><?php echo number_format($total_points); ?></div>
                    <div style="opacity: 0.9;">Reward Points</div>
                </div>
            </div>
            <?php if ($next_tier_points): ?>
                <div style="background: rgba(255,255,255,0.2); border-radius: 8px; padding: 1rem;">
                    <div style="display: flex; justify-content: space-between; margin-bottom: 0.5rem;">
                        <span>Progress to <?php echo $next_tier_points == 1000 ? 'Silver' : ($next_tier_points == 2500 ? 'Gold' : 'Platinum'); ?></span>
                        <span><?php echo number_format($next_tier_points - $total_points); ?> points to go</span>
                    </div>
                    <div style="background: rgba(255,255,255,0.3); height: 8px; border-radius: 4px; overflow: hidden;">
                        <div style="background: white; height: 100%; width: <?php echo min(100, ($total_points / $next_tier_points) * 100); ?>%; border-radius: 4px;"></div>
                    </div>
                </div>
            <?php else: ?>
                <div style="background: rgba(255,255,255,0.2); border-radius: 8px; padding: 1rem; text-align: center;">
                    <span style="font-weight: 600;">🎉 Congratulations! You've reached the highest tier!</span>
                </div>
            <?php endif; ?>
        </div>
    </div>

    <!-- Rewards Statistics -->
    <div class="stats-grid" style="margin-bottom: 2rem;">
        <div class="stat-card">
            <h3 class="stat-title">Total Points</h3>
            <p class="stat-value"><?php echo number_format($total_points); ?></p>
            <p class="stat-change">Lifetime earned</p>
        </div>
        <div class="stat-card">
            <h3 class="stat-title">Cashback Earned</h3>
            <p class="stat-value">$<?php echo number_format($cashback_earned, 2); ?></p>
            <p class="stat-change">1% on all spending</p>
        </div>
        <div class="stat-card">
            <h3 class="stat-title">This Month</h3>
            <p class="stat-value"><?php echo number_format($transaction_data['monthly_transactions']); ?></p>
            <p class="stat-change">Transactions</p>
        </div>
        <div class="stat-card">
            <h3 class="stat-title">Tier Benefits</h3>
            <p class="stat-value"><?php echo $loyalty_tier; ?></p>
            <p class="stat-change">Current status</p>
        </div>
    </div>

    <!-- Main Dashboard Grid -->
    <div class="dashboard-grid">
        <div class="main-section">
            <!-- Recent Rewards Activity -->
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">Recent Rewards Activity</h3>
                    <a href="../transfers/history.php" style="color: #6366f1; font-size: 0.875rem; text-decoration: none;">View All Transactions</a>
                </div>
                <div class="card-body">
                    <?php if (!empty($recent_activity)): ?>
                        <?php foreach ($recent_activity as $activity): ?>
                            <div class="transaction-item">
                                <div class="transaction-icon" style="background: <?php echo $activity['direction'] === 'sent' ? '#6366f1' : '#10b981'; ?>;">
                                    <svg width="16" height="16" fill="currentColor" viewBox="0 0 16 16">
                                        <path fill-rule="evenodd" d="M5 2a1 1 0 011 1v1h1a1 1 0 010 2H6v1a1 1 0 01-2 0V6H3a1 1 0 010-2h1V3a1 1 0 011-1zm0 10a1 1 0 011 1v1h1a1 1 0 110 2H6v1a1 1 0 11-2 0v-1H3a1 1 0 110-2h1v-1a1 1 0 011-1zM12 2a1 1 0 01.967.744L14.146 7.2 17.5 9.134a1 1 0 010 1.732L14.146 12.8l-1.179 4.456a1 1 0 01-1.934 0L9.854 12.8 6.5 10.866a1 1 0 010-1.732L9.854 7.2l1.179-4.456A1 1 0 0112 2z"/>
                                    </svg>
                                </div>
                                <div class="transaction-content">
                                    <div class="transaction-title">
                                        <?php echo $activity['direction'] === 'sent' ? 'Payment to' : 'Payment from'; ?>
                                        <?php echo htmlspecialchars($activity['other_party']); ?>
                                    </div>
                                    <div class="transaction-subtitle">
                                        Earned <?php echo $activity['points_earned']; ?> points •
                                        <?php echo htmlspecialchars($activity['description'] ?? 'No description'); ?>
                                    </div>
                                </div>
                                <div style="text-align: right;">
                                    <div style="font-weight: 600; color: #6366f1; margin-bottom: 0.25rem;">
                                        +<?php echo $activity['points_earned']; ?> pts
                                    </div>
                                    <div class="transaction-amount <?php echo $activity['direction'] === 'sent' ? 'amount-negative' : 'amount-positive'; ?>">
                                        <?php echo $activity['direction'] === 'sent' ? '-' : '+'; ?>$<?php echo number_format($activity['amount'], 2); ?>
                                    </div>
                                    <div class="transaction-date">
                                        <?php echo date('M j, Y', strtotime($activity['created_at'])); ?>
                                    </div>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    <?php else: ?>
                        <div style="text-align: center; padding: 3rem 1rem;">
                            <div style="font-size: 3rem; color: #e5e7eb; margin-bottom: 1rem;">⭐</div>
                            <h3 style="color: #6b7280; margin-bottom: 0.5rem;">Start Earning Rewards</h3>
                            <p style="color: #9ca3af; margin-bottom: 2rem;">Make transactions to start earning points and climbing the loyalty tiers.</p>
                            <button class="btn-primary" onclick="window.location.href='../transfers/'">Make Your First Transaction</button>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>

        <!-- Sidebar Section -->
        <div class="sidebar-section">
            <!-- Redeem Points -->
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">Redeem Points</h3>
                </div>
                <div style="padding: 1.5rem;">
                    <div style="display: flex; flex-direction: column; gap: 0.75rem;">
                        <button class="btn-primary" onclick="redeemCashback()" style="width: 100%;">
                            <svg width="16" height="16" fill="currentColor" viewBox="0 0 16 16" style="margin-right: 0.5rem;">
                                <path d="M4 10.781c.148 1.667 1.513 2.85 3.591 3.003V15h1.043v-1.216c2.27-.179 3.678-1.438 3.678-3.3 0-1.59-.947-2.51-2.956-3.028l-.722-.187V3.467c1.122.11 1.879.714 2.07 1.616h1.47c-.166-1.6-1.54-2.748-3.54-2.875V1H7.591v1.233c-1.939.23-3.27 1.472-3.27 3.156 0 1.454.966 2.483 2.661 2.917l.61.162v4.031c-1.149-.17-1.94-.8-2.131-1.718H4zm3.391-3.836c-1.043-.263-1.6-.825-1.6-1.616 0-.944.704-1.641 1.8-1.828v3.495l-.2-.05zm1.591 1.872c1.287.323 1.852.859 1.852 1.769 0 1.097-.826 1.828-2.2 1.939V8.73l.348.086z"/>
                            </svg>
                            Redeem for Cash
                        </button>
                        <button class="btn-outline" onclick="redeemGiftCard()" style="width: 100%;">
                            <svg width="16" height="16" fill="currentColor" viewBox="0 0 16 16" style="margin-right: 0.5rem;">
                                <path d="M2.5 8.5a.5.5 0 01.5-.5h10a.5.5 0 010 1H3a.5.5 0 01-.5-.5zm0-2a.5.5 0 01.5-.5h10a.5.5 0 010 1H3a.5.5 0 01-.5-.5zm0-2a.5.5 0 01.5-.5h10a.5.5 0 010 1H3a.5.5 0 01-.5-.5z"/>
                                <path d="M2 2a2 2 0 012-2h8a2 2 0 012 2v12a2 2 0 01-2 2H4a2 2 0 01-2-2V2zm2-1a1 1 0 00-1 1v12a1 1 0 001 1h8a1 1 0 001-1V2a1 1 0 00-1-1H4z"/>
                            </svg>
                            Gift Cards
                        </button>
                        <button class="btn-outline" onclick="redeemDiscounts()" style="width: 100%;">
                            <svg width="16" height="16" fill="currentColor" viewBox="0 0 16 16" style="margin-right: 0.5rem;">
                                <path d="M2.5 15a.5.5 0 01-.5-.5v-1a.5.5 0 01.5-.5h1v-.5a.5.5 0 01.5-.5h9a.5.5 0 01.5.5v.5h1a.5.5 0 01.5.5v1a.5.5 0 01-.5.5h-13zm2-2.5v.5h7v-.5h-7z"/>
                                <path d="M7.991 11.674L9.53 4.455c.123-.595.246-.71 1.347-.807l.11-.023a.5.5 0 11.11.99l-.11.023c-.71.07-.803.157-.897.624L8.85 11.674c-.092.467-.185.554-.897.624l-.11.023a.5.5 0 11-.11-.99l.11-.023c1.101-.097 1.224-.212 1.347-.807zM2.31 6.593l1.928.323c.433.072.614.327.614.648 0 .32-.18.576-.614.648l-1.928.323c-.433.072-.614-.008-.614-.329V6.922c0-.32.18-.401.614-.329z"/>
                            </svg>
                            Discounts
                        </button>
                    </div>
                </div>
            </div>

            <!-- Tier Benefits -->
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title"><?php echo $loyalty_tier; ?> Benefits</h3>
                </div>
                <div style="padding: 1.5rem;">
                    <div style="display: flex; flex-direction: column; gap: 1rem;">
                        <?php if ($loyalty_tier === 'Bronze'): ?>
                            <div style="display: flex; align-items: center; gap: 0.75rem;">
                                <div style="width: 8px; height: 8px; border-radius: 50%; background: #10b981;"></div>
                                <div>
                                    <div style="font-weight: 500;">10 points per transaction</div>
                                    <div style="font-size: 0.875rem; color: #6b7280;">Basic earning rate</div>
                                </div>
                            </div>
                            <div style="display: flex; align-items: center; gap: 0.75rem;">
                                <div style="width: 8px; height: 8px; border-radius: 50%; background: #10b981;"></div>
                                <div>
                                    <div style="font-weight: 500;">1% cashback</div>
                                    <div style="font-size: 0.875rem; color: #6b7280;">On all spending</div>
                                </div>
                            </div>
                        <?php elseif ($loyalty_tier === 'Silver'): ?>
                            <div style="display: flex; align-items: center; gap: 0.75rem;">
                                <div style="width: 8px; height: 8px; border-radius: 50%; background: #10b981;"></div>
                                <div>
                                    <div style="font-weight: 500;">15 points per transaction</div>
                                    <div style="font-size: 0.875rem; color: #6b7280;">Enhanced earning rate</div>
                                </div>
                            </div>
                            <div style="display: flex; align-items: center; gap: 0.75rem;">
                                <div style="width: 8px; height: 8px; border-radius: 50%; background: #10b981;"></div>
                                <div>
                                    <div style="font-weight: 500;">1.5% cashback</div>
                                    <div style="font-size: 0.875rem; color: #6b7280;">On all spending</div>
                                </div>
                            </div>
                            <div style="display: flex; align-items: center; gap: 0.75rem;">
                                <div style="width: 8px; height: 8px; border-radius: 50%; background: #10b981;"></div>
                                <div>
                                    <div style="font-weight: 500;">Priority support</div>
                                    <div style="font-size: 0.875rem; color: #6b7280;">Faster response times</div>
                                </div>
                            </div>
                        <?php elseif ($loyalty_tier === 'Gold'): ?>
                            <div style="display: flex; align-items: center; gap: 0.75rem;">
                                <div style="width: 8px; height: 8px; border-radius: 50%; background: #10b981;"></div>
                                <div>
                                    <div style="font-weight: 500;">20 points per transaction</div>
                                    <div style="font-size: 0.875rem; color: #6b7280;">Premium earning rate</div>
                                </div>
                            </div>
                            <div style="display: flex; align-items: center; gap: 0.75rem;">
                                <div style="width: 8px; height: 8px; border-radius: 50%; background: #10b981;"></div>
                                <div>
                                    <div style="font-weight: 500;">2% cashback</div>
                                    <div style="font-size: 0.875rem; color: #6b7280;">On all spending</div>
                                </div>
                            </div>
                            <div style="display: flex; align-items: center; gap: 0.75rem;">
                                <div style="width: 8px; height: 8px; border-radius: 50%; background: #10b981;"></div>
                                <div>
                                    <div style="font-weight: 500;">Free transfers</div>
                                    <div style="font-size: 0.875rem; color: #6b7280;">No transaction fees</div>
                                </div>
                            </div>
                        <?php else: // Platinum ?>
                            <div style="display: flex; align-items: center; gap: 0.75rem;">
                                <div style="width: 8px; height: 8px; border-radius: 50%; background: #10b981;"></div>
                                <div>
                                    <div style="font-weight: 500;">25 points per transaction</div>
                                    <div style="font-size: 0.875rem; color: #6b7280;">Maximum earning rate</div>
                                </div>
                            </div>
                            <div style="display: flex; align-items: center; gap: 0.75rem;">
                                <div style="width: 8px; height: 8px; border-radius: 50%; background: #10b981;"></div>
                                <div>
                                    <div style="font-weight: 500;">3% cashback</div>
                                    <div style="font-size: 0.875rem; color: #6b7280;">On all spending</div>
                                </div>
                            </div>
                            <div style="display: flex; align-items: center; gap: 0.75rem;">
                                <div style="width: 8px; height: 8px; border-radius: 50%; background: #10b981;"></div>
                                <div>
                                    <div style="font-weight: 500;">VIP support</div>
                                    <div style="font-size: 0.875rem; color: #6b7280;">Dedicated account manager</div>
                                </div>
                            </div>
                            <div style="display: flex; align-items: center; gap: 0.75rem;">
                                <div style="width: 8px; height: 8px; border-radius: 50%; background: #10b981;"></div>
                                <div>
                                    <div style="font-weight: 500;">Exclusive offers</div>
                                    <div style="font-size: 0.875rem; color: #6b7280;">Special promotions</div>
                                </div>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>

            <!-- How to Earn -->
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">How to Earn Points</h3>
                </div>
                <div style="padding: 1.5rem;">
                    <div style="display: flex; flex-direction: column; gap: 1rem;">
                        <div style="display: flex; align-items: center; gap: 0.75rem;">
                            <div style="width: 32px; height: 32px; border-radius: 50%; background: #6366f1; display: flex; align-items: center; justify-content: center; color: white; font-size: 0.75rem; font-weight: 600;">
                                10
                            </div>
                            <div>
                                <div style="font-weight: 500;">Per Transaction</div>
                                <div style="font-size: 0.875rem; color: #6b7280;">Every completed transfer</div>
                            </div>
                        </div>
                        <div style="display: flex; align-items: center; gap: 0.75rem;">
                            <div style="width: 32px; height: 32px; border-radius: 50%; background: #10b981; display: flex; align-items: center; justify-content: center; color: white; font-size: 0.75rem; font-weight: 600;">
                                0.1
                            </div>
                            <div>
                                <div style="font-weight: 500;">Per Dollar Spent</div>
                                <div style="font-size: 0.875rem; color: #6b7280;">On outgoing transfers</div>
                            </div>
                        </div>
                        <div style="display: flex; align-items: center; gap: 0.75rem;">
                            <div style="width: 32px; height: 32px; border-radius: 50%; background: #f59e0b; display: flex; align-items: center; justify-content: center; color: white; font-size: 0.75rem; font-weight: 600;">
                                50
                            </div>
                            <div>
                                <div style="font-weight: 500;">Large Transactions</div>
                                <div style="font-size: 0.875rem; color: #6b7280;">$100+ transfers</div>
                            </div>
                        </div>
                        <div style="display: flex; align-items: center; gap: 0.75rem;">
                            <div style="width: 32px; height: 32px; border-radius: 50%; background: #ef4444; display: flex; align-items: center; justify-content: center; color: white; font-size: 0.75rem; font-weight: 600;">
                                100
                            </div>
                            <div>
                                <div style="font-weight: 500;">Monthly Bonus</div>
                                <div style="font-size: 0.875rem; color: #6b7280;">5+ transactions per month</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function redeemPoints() {
    alert('Point redemption functionality coming soon!');
}

function redeemCashback() {
    alert('Cashback redemption functionality coming soon!');
}

function redeemGiftCard() {
    alert('Gift card redemption functionality coming soon!');
}

function redeemDiscounts() {
    alert('Discount redemption functionality coming soon!');
}
</script>

<?php
// Include footer
require_once '../includes/dashboard/footer.php';
?>
