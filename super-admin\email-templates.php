<?php
/**
 * Super Admin Email Templates Management
 * Comprehensive email template management system
 */

session_start();

// Include required files
require_once 'includes/auth.php';

// Require super admin authentication
requireSuperAdminAuth();

$page_title = 'Email Templates';
$page_subtitle = 'Manage and customize all email templates';

// Handle template testing
$test_result = '';
$test_error = '';

// Include super admin settings to get getEmailContactInfo function
require_once '../config/super_admin_settings.php';

// Simple template testing (disabled for now)
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['test_template'])) {
    $template_type = $_POST['template_type'] ?? '';
    $test_email = $_POST['test_email'] ?? '<EMAIL>';

    // For now, just show a message that testing is available
    $test_result = "Email template testing for '$template_type' is available. Template would be sent to $test_email.";

    logSuperAdminAction('email_template_test', "Viewed $template_type template test", [
        'template_type' => $template_type,
        'test_email' => $test_email
    ]);
}

// Get current email settings
$contact_info = getEmailContactInfo();

// Include header
include 'includes/header.php';
?>

<!-- Test Results -->
<?php if ($test_result): ?>
    <div class="alert alert-success alert-dismissible fade show">
        <i class="fas fa-check-circle"></i> <?php echo htmlspecialchars($test_result); ?>
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
<?php endif; ?>

<?php if ($test_error): ?>
    <div class="alert alert-danger alert-dismissible fade show">
        <i class="fas fa-exclamation-circle"></i> <?php echo htmlspecialchars($test_error); ?>
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
<?php endif; ?>

<!-- Email Template Categories -->
<div class="row">
    <!-- Account Management Templates -->
    <div class="col-lg-6 mb-4">
        <div class="card border-0 shadow-sm h-100">
            <div class="card-header bg-primary text-white">
                <h5 class="card-title mb-0">
                    <i class="fas fa-user-cog"></i> Account Management
                </h5>
            </div>
            <div class="card-body">
                <div class="list-group list-group-flush">
                    <div class="list-group-item d-flex justify-content-between align-items-center">
                        <div>
                            <h6 class="mb-1">Welcome Email</h6>
                            <small class="text-muted">Sent when new accounts are created</small>
                        </div>
                        <div class="btn-group">
                            <button class="btn btn-sm btn-outline-primary" onclick="previewTemplate('welcome')">
                                <i class="fas fa-eye"></i> Preview
                            </button>
                            <button class="btn btn-sm btn-outline-success" onclick="testTemplate('welcome')">
                                <i class="fas fa-paper-plane"></i> Test
                            </button>
                        </div>
                    </div>
                    
                    <div class="list-group-item d-flex justify-content-between align-items-center">
                        <div>
                            <h6 class="mb-1">Account Suspension</h6>
                            <small class="text-muted">Sent when accounts are suspended</small>
                        </div>
                        <div class="btn-group">
                            <button class="btn btn-sm btn-outline-primary" onclick="previewTemplate('account_suspension')">
                                <i class="fas fa-eye"></i> Preview
                            </button>
                            <button class="btn btn-sm btn-outline-success" onclick="testTemplate('account_suspension')">
                                <i class="fas fa-paper-plane"></i> Test
                            </button>
                        </div>
                    </div>
                    
                    <div class="list-group-item d-flex justify-content-between align-items-center">
                        <div>
                            <h6 class="mb-1">KYC Verification</h6>
                            <small class="text-muted">Sent when KYC status changes</small>
                        </div>
                        <div class="btn-group">
                            <button class="btn btn-sm btn-outline-primary" onclick="previewTemplate('kyc_verified')">
                                <i class="fas fa-eye"></i> Preview
                            </button>
                            <button class="btn btn-sm btn-outline-success" onclick="testTemplate('kyc_verified')">
                                <i class="fas fa-paper-plane"></i> Test
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Authentication Templates -->
    <div class="col-lg-6 mb-4">
        <div class="card border-0 shadow-sm h-100">
            <div class="card-header bg-success text-white">
                <h5 class="card-title mb-0">
                    <i class="fas fa-lock"></i> Authentication
                </h5>
            </div>
            <div class="card-body">
                <div class="list-group list-group-flush">
                    <div class="list-group-item d-flex justify-content-between align-items-center">
                        <div>
                            <h6 class="mb-1">OTP Verification</h6>
                            <small class="text-muted">Sent for login verification</small>
                        </div>
                        <div class="btn-group">
                            <button class="btn btn-sm btn-outline-primary" onclick="previewTemplate('otp')">
                                <i class="fas fa-eye"></i> Preview
                            </button>
                            <button class="btn btn-sm btn-outline-success" onclick="testTemplate('otp')">
                                <i class="fas fa-paper-plane"></i> Test
                            </button>
                        </div>
                    </div>
                    
                    <div class="list-group-item d-flex justify-content-between align-items-center">
                        <div>
                            <h6 class="mb-1">Login Alert</h6>
                            <small class="text-muted">Sent for new login detection</small>
                        </div>
                        <div class="btn-group">
                            <button class="btn btn-sm btn-outline-primary" onclick="previewTemplate('login_alert')">
                                <i class="fas fa-eye"></i> Preview
                            </button>
                            <button class="btn btn-sm btn-outline-success" onclick="testTemplate('login_alert')">
                                <i class="fas fa-paper-plane"></i> Test
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <!-- Transaction Templates -->
    <div class="col-lg-6 mb-4">
        <div class="card border-0 shadow-sm h-100">
            <div class="card-header bg-warning text-dark">
                <h5 class="card-title mb-0">
                    <i class="fas fa-exchange-alt"></i> Transaction Alerts
                </h5>
            </div>
            <div class="card-body">
                <div class="list-group list-group-flush">
                    <div class="list-group-item d-flex justify-content-between align-items-center">
                        <div>
                            <h6 class="mb-1">Credit Alert</h6>
                            <small class="text-muted">Sent when money is received</small>
                        </div>
                        <div class="btn-group">
                            <button class="btn btn-sm btn-outline-primary" onclick="previewTemplate('credit_alert')">
                                <i class="fas fa-eye"></i> Preview
                            </button>
                            <button class="btn btn-sm btn-outline-success" onclick="testTemplate('credit_alert')">
                                <i class="fas fa-paper-plane"></i> Test
                            </button>
                        </div>
                    </div>
                    
                    <div class="list-group-item d-flex justify-content-between align-items-center">
                        <div>
                            <h6 class="mb-1">Debit Alert</h6>
                            <small class="text-muted">Sent when money is sent</small>
                        </div>
                        <div class="btn-group">
                            <button class="btn btn-sm btn-outline-primary" onclick="previewTemplate('debit_alert')">
                                <i class="fas fa-eye"></i> Preview
                            </button>
                            <button class="btn btn-sm btn-outline-success" onclick="testTemplate('debit_alert')">
                                <i class="fas fa-paper-plane"></i> Test
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Email Configuration -->
    <div class="col-lg-6 mb-4">
        <div class="card border-0 shadow-sm h-100">
            <div class="card-header bg-info text-white">
                <h5 class="card-title mb-0">
                    <i class="fas fa-cogs"></i> Email Configuration
                </h5>
            </div>
            <div class="card-body">
                <h6 class="mb-3">Current Settings</h6>
                <div class="row">
                    <div class="col-12 mb-2">
                        <small class="text-muted">Site Name:</small><br>
                        <strong><?php echo htmlspecialchars($contact_info['site_name']); ?></strong>
                    </div>
                    <div class="col-12 mb-2">
                        <small class="text-muted">Support Email:</small><br>
                        <strong><?php echo htmlspecialchars($contact_info['support_email']); ?></strong>
                    </div>
                    <div class="col-12 mb-2">
                        <small class="text-muted">Support Phone:</small><br>
                        <strong><?php echo htmlspecialchars($contact_info['support_phone']); ?></strong>
                    </div>
                    <div class="col-12 mb-3">
                        <small class="text-muted">Footer Text:</small><br>
                        <strong><?php echo htmlspecialchars($contact_info['email_footer_text']); ?></strong>
                    </div>
                </div>
                
                <div class="d-grid gap-2">
                    <a href="system-settings.php" class="btn btn-outline-primary">
                        <i class="fas fa-edit"></i> Edit Settings
                    </a>
                    <a href="smtp-config.php" class="btn btn-outline-warning">
                        <i class="fas fa-server"></i> SMTP Config
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Test Email Modal -->
<div class="modal fade" id="testEmailModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <form method="POST">
                <div class="modal-header">
                    <h5 class="modal-title">
                        <i class="fas fa-paper-plane"></i> Test Email Template
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="test_email" class="form-label">Test Email Address</label>
                        <input type="email" class="form-control" id="test_email" name="test_email" 
                               value="<EMAIL>" required>
                        <div class="form-text">Email will be sent to this address for testing</div>
                    </div>
                    <input type="hidden" id="template_type" name="template_type" value="">
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" name="test_template" class="btn btn-success">
                        <i class="fas fa-paper-plane"></i> Send Test Email
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Preview Modal -->
<div class="modal fade" id="previewModal" tabindex="-1">
    <div class="modal-dialog modal-xl">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-eye"></i> Email Template Preview
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <iframe id="previewFrame" style="width: 100%; height: 600px; border: 1px solid #dee2e6; border-radius: 6px;"></iframe>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
            </div>
        </div>
    </div>
</div>

<script>
function testTemplate(templateType) {
    document.getElementById('template_type').value = templateType;
    const modal = new bootstrap.Modal(document.getElementById('testEmailModal'));
    modal.show();
}

function previewTemplate(templateType) {
    const previewFrame = document.getElementById('previewFrame');
    previewFrame.src = 'preview-template.php?type=' + templateType;
    
    const modal = new bootstrap.Modal(document.getElementById('previewModal'));
    modal.show();
}
</script>

<?php
// Include footer
include 'includes/footer.php';
?>
