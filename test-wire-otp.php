<?php
/**
 * Test Wire Transfer OTP Verification
 */

// Start session
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// Include database connection
require_once '../config/database.php';

// Set content type
header('Content-Type: application/json');

try {
    // Get POST data
    $input = json_decode(file_get_contents('php://input'), true);
    
    if (!$input) {
        throw new Exception('Invalid request data');
    }
    
    echo json_encode([
        'success' => true,
        'message' => 'Test successful',
        'received_data' => $input,
        'session_otp' => $_SESSION['wire_transfer_otp'] ?? 'not set',
        'session_expires' => $_SESSION['wire_transfer_otp_expires'] ?? 'not set',
        'current_time' => time()
    ]);
    
} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'error' => $e->getMessage()
    ]);
}

exit();
