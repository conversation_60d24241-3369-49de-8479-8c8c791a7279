/**
 * Transaction History JavaScript - SIMPLE VERSION
 * Interactive functionality for transaction page
 */

// SIMPLE MODAL FUNCTIONS
// Enhanced receipt-style modal function
window.showTransactionDetails = function(transaction) {
    console.log('🧾 Opening bank receipt for transaction:', transaction);

    const modal = document.getElementById('transactionModal');
    const content = document.getElementById('modalContent');

    if (!modal || !content) {
        console.error('❌ Modal elements not found');
        alert('Receipt not available. Please refresh the page.');
        return;
    }

    // Generate receipt content with banking details
    content.innerHTML = generateReceiptContent(transaction);

    // Show modal with animation
    modal.style.display = 'block';
    document.body.style.overflow = 'hidden';

    console.log('✅ Bank receipt opened successfully');
};

// Generate professional bank receipt content
function generateReceiptContent(transaction) {
    const currentDate = new Date().toLocaleString();
    const transactionDate = transaction.formatted_date || new Date(transaction.created_at).toLocaleString();
    const amount = parseFloat(transaction.amount) || 0;
    const isCredit = ['credit', 'deposit', 'transfer_in'].includes(transaction.transaction_type);

    return `
        <!-- Transaction Information Section -->
        <div class="receipt-section">
            <div class="receipt-section-title">Transaction Information</div>
            <div class="receipt-row">
                <span class="receipt-label">Transaction ID:</span>
                <span class="receipt-value">${transaction.id || 'N/A'}</span>
            </div>
            <div class="receipt-row">
                <span class="receipt-label">Reference Number:</span>
                <span class="receipt-value">${transaction.reference_number || 'N/A'}</span>
            </div>
            <div class="receipt-row">
                <span class="receipt-label">Transaction Type:</span>
                <span class="receipt-value">${formatTransactionType(transaction.transaction_type)}</span>
            </div>
            <div class="receipt-row">
                <span class="receipt-label">Status:</span>
                <span class="receipt-value">
                    <span class="receipt-status ${transaction.status || 'completed'}">${(transaction.status || 'Completed').toUpperCase()}</span>
                </span>
            </div>
        </div>

        <!-- Amount Details Section -->
        <div class="receipt-section">
            <div class="receipt-section-title">Amount Details</div>
            <div class="receipt-row">
                <span class="receipt-label">Transaction Amount:</span>
                <span class="receipt-value receipt-amount ${isCredit ? 'positive' : 'negative'}">
                    ${isCredit ? '+' : '-'}$${amount.toFixed(2)}
                </span>
            </div>
            <div class="receipt-row">
                <span class="receipt-label">Processing Fee:</span>
                <span class="receipt-value">$0.00</span>
            </div>
            <div class="receipt-row">
                <span class="receipt-label">Total Amount:</span>
                <span class="receipt-value receipt-amount ${isCredit ? 'positive' : 'negative'}">
                    ${isCredit ? '+' : '-'}$${amount.toFixed(2)}
                </span>
            </div>
        </div>

        <!-- Account Information Section -->
        <div class="receipt-section full-width">
            <div class="receipt-section-title">Account & Party Information</div>
            <div class="receipt-content" style="grid-template-columns: 1fr 1fr;">
                <div>
                    <div class="receipt-row">
                        <span class="receipt-label">Account Holder:</span>
                        <span class="receipt-value">James Bong</span>
                    </div>
                    <div class="receipt-row">
                        <span class="receipt-label">Account Number:</span>
                        <span class="receipt-value">****7890</span>
                    </div>
                    <div class="receipt-row">
                        <span class="receipt-label">Account Type:</span>
                        <span class="receipt-value">Checking Account</span>
                    </div>
                </div>
                <div>
                    <div class="receipt-row">
                        <span class="receipt-label">${isCredit ? 'From' : 'To'} Party:</span>
                        <span class="receipt-value">${transaction.sender_name || 'N/A'}</span>
                    </div>
                    <div class="receipt-row">
                        <span class="receipt-label">Institution:</span>
                        <span class="receipt-value">${getInstitutionName(transaction.sender_name)}</span>
                    </div>
                    <div class="receipt-row">
                        <span class="receipt-label">Category:</span>
                        <span class="receipt-value">${getTransactionCategory(transaction.transaction_type)}</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- Transaction Details Section -->
        <div class="receipt-section full-width">
            <div class="receipt-section-title">Transaction Details</div>
            <div class="receipt-row">
                <span class="receipt-label">Description:</span>
                <span class="receipt-value">${transaction.description || 'No additional description provided'}</span>
            </div>
            <div class="receipt-row">
                <span class="receipt-label">Transaction Date:</span>
                <span class="receipt-value">${transactionDate}</span>
            </div>
            <div class="receipt-row">
                <span class="receipt-label">Processing Time:</span>
                <span class="receipt-value">${getProcessingTime(transaction.transaction_type)}</span>
            </div>
            <div class="receipt-row">
                <span class="receipt-label">Receipt Generated:</span>
                <span class="receipt-value">${currentDate}</span>
            </div>
        </div>

        <!-- Balance Information Section -->
        <div class="receipt-section full-width">
            <div class="receipt-section-title">Account Balance Information</div>
            <div class="receipt-content" style="grid-template-columns: 1fr 1fr;">
                <div>
                    <div class="receipt-row">
                        <span class="receipt-label">Previous Balance:</span>
                        <span class="receipt-value">$${(amount + (isCredit ? -amount : amount) + 15420.50).toFixed(2)}</span>
                    </div>
                </div>
                <div>
                    <div class="receipt-row">
                        <span class="receipt-label">Current Balance:</span>
                        <span class="receipt-value receipt-amount positive">$${(15420.50 + (isCredit ? amount : -amount)).toFixed(2)}</span>
                    </div>
                </div>
            </div>
        </div>
    `;
}

// Enhanced close function
window.closeModal = function() {
    const modal = document.getElementById('transactionModal');
    if (modal) {
        modal.style.display = 'none';
        document.body.style.overflow = ''; // Restore background scroll
        console.log('✅ Receipt modal closed successfully');
    }
};

// Print receipt function
window.printReceipt = function() {
    console.log('🖨️ Printing receipt...');

    const receiptContent = document.querySelector('.receipt-container');
    if (!receiptContent) {
        alert('Receipt content not found');
        return;
    }

    // Create a new window for printing
    const printWindow = window.open('', '_blank', 'width=800,height=600');

    // Add print-specific styles that match the modal design
    const printStyles = `
        <style>
            body {
                font-family: 'Courier New', monospace;
                margin: 0;
                padding: 20px;
                background: white;
                font-size: 12px;
                line-height: 1.4;
            }
            .receipt-container {
                max-width: none;
                width: 100%;
                border: 2px solid #000;
                box-shadow: none;
                position: static;
                transform: none;
                font-family: 'Courier New', monospace;
            }
            .receipt-header {
                background: white;
                border-bottom: 2px solid #000;
                padding: 1rem;
                display: flex;
                justify-content: space-between;
                align-items: center;
            }
            .bank-logo {
                display: flex;
                align-items: center;
                gap: 0.5rem;
                font-size: 1.2rem;
                font-weight: bold;
            }
            .receipt-title {
                font-size: 1rem;
                font-weight: bold;
                letter-spacing: 2px;
                text-align: center;
            }
            .receipt-body {
                padding: 1.5rem;
                background: white;
            }
            .receipt-content {
                display: grid;
                grid-template-columns: 1fr 1fr;
                gap: 1.5rem;
                font-size: 0.85rem;
                line-height: 1.5;
            }
            .receipt-section {
                margin-bottom: 1rem;
            }
            .receipt-section-title {
                font-size: 0.9rem;
                font-weight: bold;
                margin-bottom: 0.5rem;
                padding-bottom: 0.3rem;
                border-bottom: 1px solid #000;
                text-transform: uppercase;
                letter-spacing: 1px;
            }
            .receipt-row {
                display: flex;
                justify-content: space-between;
                padding: 0.2rem 0;
                border-bottom: 1px dotted #666;
            }
            .receipt-label {
                font-weight: 500;
                text-transform: uppercase;
                font-size: 0.8rem;
            }
            .receipt-value {
                font-weight: 600;
                text-align: right;
                font-family: 'Courier New', monospace;
            }
            .receipt-amount {
                font-size: 1rem;
                font-weight: bold;
            }
            .receipt-amount.positive { color: #000; }
            .receipt-amount.negative { color: #000; }
            .receipt-status {
                display: inline-block;
                padding: 0.2rem 0.5rem;
                border: 1px solid #000;
                font-size: 0.7rem;
                font-weight: bold;
                text-transform: uppercase;
            }
            .receipt-section.full-width {
                grid-column: 1 / -1;
                margin-top: 1rem;
                padding-top: 1rem;
                border-top: 2px solid #000;
            }
            .receipt-close { display: none; }
            .receipt-actions { display: none; }
            .modal-overlay { display: none; }
            .receipt-footer { display: none; }
            @media print {
                body { margin: 0; padding: 10px; }
                .receipt-container { border: 1px solid #000; }
                .receipt-content { grid-template-columns: 1fr 1fr; }
            }
        </style>
    `;

    // Write content to print window
    printWindow.document.write(`
        <!DOCTYPE html>
        <html>
        <head>
            <title>Transaction Receipt</title>
            ${printStyles}
        </head>
        <body>
            ${receiptContent.outerHTML}
        </body>
        </html>
    `);

    printWindow.document.close();

    // Wait for content to load then print
    setTimeout(() => {
        printWindow.print();
        printWindow.close();
    }, 500);
};

// Helper function to get amount color based on transaction type
function getAmountColor(type) {
    switch(type) {
        case 'credit':
        case 'deposit':
        case 'transfer_in':
            return '#10b981'; // Green for positive amounts
        case 'debit':
        case 'withdrawal':
        case 'transfer_out':
            return '#ef4444'; // Red for negative amounts
        default:
            return '#374151'; // Default gray
    }
}

// Helper functions for receipt generation
function formatTransactionType(type) {
    if (!type) return 'Unknown Transaction';

    const typeMap = {
        'credit': 'Credit Transaction',
        'debit': 'Debit Transaction',
        'transfer_in': 'Incoming Transfer',
        'transfer_out': 'Outgoing Transfer',
        'deposit': 'Account Deposit',
        'withdrawal': 'Account Withdrawal'
    };

    return typeMap[type] || type.charAt(0).toUpperCase() + type.slice(1) + ' Transaction';
}

function getInstitutionName(senderName) {
    if (!senderName) return 'PremierBank Pro';

    const institutionMap = {
        'Seabridge Oil Bank': 'Seabridge Financial Group',
        'PremierBank Pro': 'PremierBank Pro',
        'Bank Deposit Center': 'Federal Reserve Bank',
        'Bank Withdrawal Center': 'ATM Network',
        'Payroll Department': 'Corporate Banking',
        'Bank Interest Department': 'PremierBank Pro'
    };

    return institutionMap[senderName] || 'External Financial Institution';
}

function getTransactionCategory(type) {
    const categoryMap = {
        'credit': 'Income & Deposits',
        'debit': 'Payments & Withdrawals',
        'transfer_in': 'Transfers Received',
        'transfer_out': 'Transfers Sent',
        'deposit': 'Account Funding',
        'withdrawal': 'Cash Withdrawal'
    };

    return categoryMap[type] || 'General Banking';
}

function getProcessingTime(type) {
    const processingMap = {
        'credit': 'Instant',
        'debit': 'Instant',
        'transfer_in': '1-2 Business Days',
        'transfer_out': '1-2 Business Days',
        'deposit': 'Instant',
        'withdrawal': 'Instant'
    };

    return processingMap[type] || 'Standard Processing';
}

function formatAmount(amount, type) {
    if (!amount) return 'N/A';
    const creditTypes = ['credit', 'transfer_in', 'deposit'];
    const sign = creditTypes.includes(type) ? '+' : '-';
    return `${sign}$${parseFloat(amount).toFixed(2)}`;
}

function formatDateTime(date, time) {
    if (!date) return 'N/A';

    try {
        if (time) {
            return `${date} at ${time}`;
        }

        const dateObj = new Date(date);
        return dateObj.toLocaleDateString('en-US', {
            year: 'numeric',
            month: 'short',
            day: 'numeric',
            hour: '2-digit',
            minute: '2-digit'
        });
    } catch (e) {
        return date;
    }
}

// Main initialization function
document.addEventListener('DOMContentLoaded', function() {
    console.log('🚀 Transaction page initializing...');

    // Check for modal element
    const modal = document.getElementById('transactionModal');
    if (modal) {
        console.log('✅ Modal element found');
    } else {
        console.error('❌ Modal element NOT found');
    }

    // Initialize all components
    initializeModal();
    initializeFilters();
    initializeTableInteractions();
    initializeExportFunctionality();
    initializePaginationScrolling();
    animateBalanceCards();

    // Make functions globally available
    window.showTransactionDetails = showTransactionDetails;
    window.closeModal = closeModal;
    window.printReceipt = printReceipt;
    window.exportToPDF = exportToPDF;
    window.clearFilters = clearFilters;

    console.log('✅ Transaction page fully initialized');
});

function initializeModal() {
    // Add escape key handler
    document.addEventListener('keydown', function(e) {
        if (e.key === 'Escape') {
            closeModal();
        }
    });

    // Add backdrop click handler
    const modal = document.getElementById('transactionModal');
    if (modal) {
        modal.addEventListener('click', function(e) {
            if (e.target === modal || e.target.classList.contains('modal-overlay')) {
                closeModal();
            }
        });
    }

    // Add click listeners to description cells
    const descriptionCells = document.querySelectorAll('.description');
    console.log(`🔍 Found ${descriptionCells.length} description cells`);

    descriptionCells.forEach((cell, index) => {
        cell.style.cursor = 'pointer';
        cell.title = 'Click to view transaction details';

        cell.addEventListener('click', function(e) {
            console.log(`🖱️ Description cell ${index + 1} clicked`);
            const row = this.closest('tr');
            if (row && row.dataset.transaction) {
                try {
                    const transaction = JSON.parse(row.dataset.transaction);
                    showTransactionDetails(transaction);
                } catch (e) {
                    console.error('❌ Error parsing transaction data:', e);
                    alert('Error loading transaction details. Please try again.');
                }
            }
        });
    });

    console.log('✅ Modal initialized successfully!');
}

// Test modal function
window.testModal = function() {
    const testTransaction = {
        id: 'TEST001',
        sender_name: 'Test Bank',
        transaction_type: 'credit',
        description: 'Test transaction for debugging',
        amount: '100.00',
        reference_number: 'TEST-REF-001',
        status: 'completed',
        formatted_date: 'Dec 29, 2024',
        formatted_amount: '+$100.00'
    };

    showTransactionDetails(testTransaction);
};

/**
 * Initialize filter functionality
 */
function initializeFilters() {
    const filterForm = document.querySelector('.filters-form');
    const clearButton = document.querySelector('[onclick="clearFilters()"]');
    
    if (filterForm) {
        // Auto-submit form when date inputs change
        const dateInputs = filterForm.querySelectorAll('input[type="date"]');
        dateInputs.forEach(input => {
            input.addEventListener('change', function() {
                // Add small delay to allow user to set both dates
                setTimeout(() => {
                    if (this.value) {
                        filterForm.submit();
                    }
                }, 500);
            });
        });
        
        // Auto-submit when select changes
        const selectInputs = filterForm.querySelectorAll('select');
        selectInputs.forEach(select => {
            select.addEventListener('change', function() {
                filterForm.submit();
            });
        });
    }
}

/**
 * Initialize table interactions
 */
function initializeTableInteractions() {
    const table = document.querySelector('.transactions-table');
    
    if (table) {
        // Add click handlers for transaction rows
        const rows = table.querySelectorAll('tbody tr');
        rows.forEach(row => {
            row.addEventListener('click', function() {
                // Toggle row selection
                this.classList.toggle('selected');
            });
            
            // Add hover effect enhancement
            row.addEventListener('mouseenter', function() {
                this.style.transform = 'scale(1.01)';
                this.style.transition = 'transform 0.2s ease';
            });
            
            row.addEventListener('mouseleave', function() {
                this.style.transform = 'scale(1)';
            });
        });
        
        // Add sorting functionality to table headers
        const headers = table.querySelectorAll('th');
        headers.forEach((header, index) => {
            if (index > 0 && index < headers.length - 1) { // Skip # and Status columns
                header.style.cursor = 'pointer';
                header.addEventListener('click', function() {
                    sortTable(index);
                });
                
                // Add sort indicator
                const sortIcon = document.createElement('i');
                sortIcon.className = 'fas fa-sort ms-1';
                sortIcon.style.opacity = '0.5';
                header.appendChild(sortIcon);
            }
        });
    }
}

/**
 * Initialize export functionality
 */
function initializeExportFunctionality() {
    // Add loading state to export button
    const exportButton = document.querySelector('[onclick="exportToPDF()"]');
    
    if (exportButton) {
        exportButton.addEventListener('click', function() {
            // Add loading state
            const originalText = this.innerHTML;
            this.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Generating PDF...';
            this.disabled = true;
            
            // Reset button after 3 seconds
            setTimeout(() => {
                this.innerHTML = originalText;
                this.disabled = false;
            }, 3000);
        });
    }
}

/**
 * Sort table by column
 */
function sortTable(columnIndex) {
    const table = document.querySelector('.transactions-table');
    const tbody = table.querySelector('tbody');
    const rows = Array.from(tbody.querySelectorAll('tr'));
    
    // Determine sort direction
    const header = table.querySelectorAll('th')[columnIndex];
    const sortIcon = header.querySelector('i');
    const isAscending = sortIcon.classList.contains('fa-sort') || sortIcon.classList.contains('fa-sort-down');
    
    // Update sort icons
    table.querySelectorAll('th i').forEach(icon => {
        icon.className = 'fas fa-sort ms-1';
        icon.style.opacity = '0.5';
    });
    
    sortIcon.className = isAscending ? 'fas fa-sort-up ms-1' : 'fas fa-sort-down ms-1';
    sortIcon.style.opacity = '1';
    
    // Sort rows
    rows.sort((a, b) => {
        const aValue = a.cells[columnIndex].textContent.trim();
        const bValue = b.cells[columnIndex].textContent.trim();
        
        // Handle different data types
        let comparison = 0;
        
        if (columnIndex === 4) { // Amount column
            const aAmount = parseFloat(aValue.replace(/[^0-9.-]/g, ''));
            const bAmount = parseFloat(bValue.replace(/[^0-9.-]/g, ''));
            comparison = aAmount - bAmount;
        } else if (columnIndex === 5) { // Date column
            const aDate = new Date(aValue);
            const bDate = new Date(bValue);
            comparison = aDate - bDate;
        } else {
            comparison = aValue.localeCompare(bValue);
        }
        
        return isAscending ? comparison : -comparison;
    });
    
    // Re-append sorted rows
    rows.forEach(row => tbody.appendChild(row));
}

/**
 * Clear all filters
 */
function clearFilters() {
    window.location.href = window.location.pathname;
}

/**
 * Export transactions to PDF
 */
function exportToPDF() {
    // Show loading state
    const button = event.target.closest('button');
    const originalText = button.innerHTML;
    button.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Generating PDF...';
    button.disabled = true;
    
    // Create form for PDF export
    const form = document.createElement('form');
    form.method = 'POST';
    form.action = 'export-pdf.php';
    form.style.display = 'none';
    
    // Add current filters as hidden inputs
    const urlParams = new URLSearchParams(window.location.search);
    for (const [key, value] of urlParams) {
        const input = document.createElement('input');
        input.type = 'hidden';
        input.name = key;
        input.value = value;
        form.appendChild(input);
    }
    
    document.body.appendChild(form);
    form.submit();
    document.body.removeChild(form);
    
    // Reset button after delay
    setTimeout(() => {
        button.innerHTML = originalText;
        button.disabled = false;
    }, 3000);
}

/**
 * Format currency values
 */
function formatCurrency(amount, currency = 'USD') {
    return new Intl.NumberFormat('en-US', {
        style: 'currency',
        currency: currency
    }).format(amount);
}

/**
 * Animate balance cards on load
 */
function animateBalanceCards() {
    const cards = document.querySelectorAll('.balance-card-new');
    cards.forEach((card, index) => {
        card.style.opacity = '0';
        card.style.transform = 'translateY(20px)';
        
        setTimeout(() => {
            card.style.transition = 'all 0.5s ease';
            card.style.opacity = '1';
            card.style.transform = 'translateY(0)';
        }, index * 100);
    });
}





// Add smooth scrolling to pagination links
function initializePaginationScrolling() {
    const paginationLinks = document.querySelectorAll('.pagination .page-link');
    paginationLinks.forEach(link => {
        link.addEventListener('click', function(e) {
            // Scroll to top of transactions table
            const transactionsSection = document.querySelector('.transactions-section');
            if (transactionsSection) {
                setTimeout(() => {
                    transactionsSection.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }, 100);
            }
        });
    });
}
