<?php
/**
 * Quick Database Upgrade Script for Dual Authentication
 * Run this script once to upgrade your database to support dual authentication
 */

// Only allow execution from command line or super admin
if (php_sapi_name() !== 'cli') {
    session_start();
    require_once 'includes/auth.php';
    requireSuperAdminAuth();
}

echo "=== Super Admin Dual Authentication Upgrade ===\n";
echo "This script will upgrade your database to support both 6-digit security codes and Google Authenticator.\n\n";

try {
    require_once '../config/database.php';
    $db = getDB();
    
    echo "✓ Database connection established\n";
    
    // Read the upgrade SQL file
    $sql_file = '../database/upgrade_super_admin_dual_auth.sql';
    if (!file_exists($sql_file)) {
        throw new Exception("Upgrade SQL file not found: $sql_file");
    }
    
    echo "✓ Found upgrade SQL file\n";
    
    $sql = file_get_contents($sql_file);
    $statements = array_filter(array_map('trim', explode(';', $sql)));
    
    echo "✓ Parsed " . count($statements) . " SQL statements\n\n";
    
    $executed = 0;
    $errors = [];
    
    echo "Executing upgrade statements:\n";
    
    foreach ($statements as $i => $statement) {
        if (!empty($statement) && !preg_match('/^\s*--/', $statement)) {
            try {
                echo "  " . ($i + 1) . ". ";
                
                // Show a preview of the statement
                $preview = substr(preg_replace('/\s+/', ' ', $statement), 0, 50);
                echo $preview . (strlen($statement) > 50 ? '...' : '') . " ";
                
                $db->query($statement);
                echo "✓\n";
                $executed++;
            } catch (Exception $e) {
                // Ignore "already exists" errors and duplicate key errors
                if (strpos($e->getMessage(), 'already exists') !== false || 
                    strpos($e->getMessage(), 'Duplicate') !== false ||
                    strpos($e->getMessage(), 'duplicate') !== false) {
                    echo "⚠ (already exists)\n";
                } else {
                    echo "✗ Error: " . $e->getMessage() . "\n";
                    $errors[] = $e->getMessage();
                }
            }
        }
    }
    
    echo "\n=== Upgrade Results ===\n";
    echo "✓ Successfully executed: $executed statements\n";
    
    if (!empty($errors)) {
        echo "⚠ Errors encountered: " . count($errors) . "\n";
        foreach ($errors as $error) {
            echo "  - $error\n";
        }
    } else {
        echo "✓ No errors encountered\n";
    }
    
    // Verify the upgrade
    echo "\n=== Verification ===\n";
    
    // Check if new columns exist
    $result = $db->query("SHOW COLUMNS FROM super_admin_2fa_settings LIKE 'security_code_hash'");
    if ($result && $result->num_rows > 0) {
        echo "✓ New columns added successfully\n";
    } else {
        echo "✗ New columns not found\n";
    }
    
    // Check if settings exist
    $result = $db->query("SELECT COUNT(*) as count FROM super_admin_settings WHERE setting_key = 'dual_auth_enabled'");
    if ($result && $row = $result->fetch_assoc() && $row['count'] > 0) {
        echo "✓ New settings configured successfully\n";
    } else {
        echo "✗ New settings not found\n";
    }
    
    echo "\n=== Next Steps ===\n";
    echo "1. Visit super-admin/2fa-setup.php to configure your authentication methods\n";
    echo "2. Set up either a 6-digit security code or Google Authenticator (or both)\n";
    echo "3. Test the login process to ensure everything works correctly\n";
    echo "\n✓ Upgrade completed successfully!\n";
    
} catch (Exception $e) {
    echo "\n✗ Upgrade failed: " . $e->getMessage() . "\n";
    exit(1);
}

if (php_sapi_name() !== 'cli') {
    echo "<br><br><a href='2fa-setup.php' class='btn btn-primary'>Configure Authentication</a>";
}
?>
