<?php
/**
 * Force SMTP Test - Bypasses localhost detection
 * Direct PHPMailer SMTP <NAME_EMAIL>
 */

// Enable error reporting
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

echo "<!DOCTYPE html>";
echo "<html><head><title>Force SMTP Test</title>";
echo "<style>
body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }
.container { max-width: 700px; margin: 0 auto; background: white; padding: 30px; border-radius: 10px; box-shadow: 0 4px 6px rgba(0,0,0,0.1); }
.success { background: #d4edda; border: 1px solid #c3e6cb; color: #155724; padding: 20px; border-radius: 5px; margin: 15px 0; }
.error { background: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; padding: 20px; border-radius: 5px; margin: 15px 0; }
.info { background: #e7f3ff; border: 1px solid #b3d9ff; color: #0c5460; padding: 20px; border-radius: 5px; margin: 15px 0; }
pre { background: #f8f9fa; padding: 15px; border-radius: 5px; font-size: 12px; overflow-x: auto; }
</style>";
echo "</head><body>";

echo "<div class='container'>";
echo "<h1>🏦 Force SMTP Test</h1>";
echo "<p>Direct PHPMailer SMTP test bypassing localhost detection</p>";

$test_email = '<EMAIL>';

try {
    // Load PHPMailer
    require_once 'vendor/autoload.php';
    
    // SMTP Configuration (hardcoded to ensure no issues)
    $smtp_host = 'smtp.hostinger.com';
    $smtp_port = 587;
    $smtp_username = '<EMAIL>';
    $smtp_password = 'Money2025@Demo#';
    $smtp_encryption = 'tls';
    $from_email = '<EMAIL>';
    $from_name = 'Online Banking System';
    
    echo "<div class='info'>";
    echo "<h3>📧 SMTP Configuration</h3>";
    echo "<strong>Host:</strong> $smtp_host<br>";
    echo "<strong>Port:</strong> $smtp_port<br>";
    echo "<strong>Username:</strong> $smtp_username<br>";
    echo "<strong>Encryption:</strong> $smtp_encryption<br>";
    echo "<strong>From:</strong> $from_email<br>";
    echo "</div>";
    
    // Create PHPMailer instance
    $mail = new PHPMailer\PHPMailer\PHPMailer(true);
    
    // Enable SMTP debugging
    $mail->SMTPDebug = 2;
    $mail->Debugoutput = function($str, $level) {
        echo "<div style='background: #f8f9fa; padding: 5px; margin: 2px 0; font-size: 11px; border-left: 3px solid #007bff;'>";
        echo "SMTP Debug (Level $level): " . htmlspecialchars($str);
        echo "</div>";
    };
    
    // Server settings
    $mail->isSMTP();
    $mail->Host = $smtp_host;
    $mail->SMTPAuth = true;
    $mail->Username = $smtp_username;
    $mail->Password = $smtp_password;
    $mail->SMTPSecure = $smtp_encryption;
    $mail->Port = $smtp_port;
    
    // Recipients
    $mail->setFrom($from_email, $from_name);
    $mail->addAddress($test_email, 'Demo Developer');
    
    // Content
    $mail->isHTML(true);
    $mail->Subject = '🏦 FORCE SMTP TEST - ' . date('Y-m-d H:i:s');
    $mail->Body = '
    <!DOCTYPE html>
    <html>
    <head>
        <meta charset="UTF-8">
        <title>Force SMTP Test</title>
    </head>
    <body style="font-family: Arial, sans-serif; margin: 0; padding: 20px; background-color: #f0f2f5;">
        <div style="max-width: 600px; margin: 0 auto; background-color: white; border-radius: 12px; overflow: hidden; box-shadow: 0 4px 12px rgba(0,0,0,0.15);">
            
            <!-- Header -->
            <div style="background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%); color: white; padding: 30px; text-align: center;">
                <h1 style="margin: 0; font-size: 2.2em;">🏦 Online Banking System</h1>
                <h2 style="margin: 10px 0 0 0; font-weight: normal; opacity: 0.9;">FORCE SMTP TEST SUCCESS!</h2>
            </div>
            
            <!-- Main Content -->
            <div style="padding: 30px;">
                
                <!-- Success Alert -->
                <div style="background: linear-gradient(135deg, #27ae60 0%, #2ecc71 100%); color: white; padding: 20px; border-radius: 8px; text-align: center; margin-bottom: 25px;">
                    <h2 style="margin: 0 0 10px 0;">🎉 SMTP WORKING PERFECTLY!</h2>
                    <p style="margin: 0; font-size: 1.1em;">Direct PHPMailer SMTP connection successful - bypassed localhost detection!</p>
                </div>
                
                <!-- Test Information -->
                <h3 style="color: #2c3e50; margin: 0 0 15px 0; border-bottom: 2px solid #3498db; padding-bottom: 8px;">📋 Test Details</h3>
                <div style="background: #f8f9fa; padding: 20px; border-radius: 8px; border-left: 4px solid #3498db;">
                    <table style="width: 100%; border-collapse: collapse;">
                        <tr>
                            <td style="padding: 8px 0; color: #7f8c8d; width: 30%; font-weight: bold;">Recipient:</td>
                            <td style="padding: 8px 0; color: #2c3e50;">' . $test_email . '</td>
                        </tr>
                        <tr>
                            <td style="padding: 8px 0; color: #7f8c8d; font-weight: bold;">Test Date:</td>
                            <td style="padding: 8px 0; color: #2c3e50;">' . date('Y-m-d H:i:s') . '</td>
                        </tr>
                        <tr>
                            <td style="padding: 8px 0; color: #7f8c8d; font-weight: bold;">SMTP Host:</td>
                            <td style="padding: 8px 0; color: #2c3e50;">' . $smtp_host . '</td>
                        </tr>
                        <tr>
                            <td style="padding: 8px 0; color: #7f8c8d; font-weight: bold;">SMTP Port:</td>
                            <td style="padding: 8px 0; color: #2c3e50;">' . $smtp_port . '</td>
                        </tr>
                        <tr>
                            <td style="padding: 8px 0; color: #7f8c8d; font-weight: bold;">Encryption:</td>
                            <td style="padding: 8px 0; color: #2c3e50;">' . strtoupper($smtp_encryption) . '</td>
                        </tr>
                        <tr>
                            <td style="padding: 8px 0; color: #7f8c8d; font-weight: bold;">Method:</td>
                            <td style="padding: 8px 0; color: #2c3e50;">Direct PHPMailer SMTP (Forced)</td>
                        </tr>
                    </table>
                </div>
                
                <!-- What This Confirms -->
                <h3 style="color: #2c3e50; margin: 25px 0 15px 0; border-bottom: 2px solid #e74c3c; padding-bottom: 8px;">✅ What This Confirms</h3>
                <div style="background: #d4edda; padding: 20px; border-radius: 8px; border-left: 4px solid #28a745;">
                    <ul style="margin: 0; padding-left: 20px; color: #155724; line-height: 1.8;">
                        <li><strong>SMTP Server Connection:</strong> Successfully connected to Hostinger SMTP</li>
                        <li><strong>Authentication Working:</strong> Username and password are correct</li>
                        <li><strong>Port & Encryption:</strong> TLS on port 587 is working perfectly</li>
                        <li><strong>Email Delivery:</strong> Messages can be sent to external email addresses</li>
                        <li><strong>PHPMailer Integration:</strong> Library is properly installed and configured</li>
                        <li><strong>Banking System Ready:</strong> All email features will work in production</li>
                    </ul>
                </div>
                
                <!-- Next Steps -->
                <h3 style="color: #2c3e50; margin: 25px 0 15px 0; border-bottom: 2px solid #f39c12; padding-bottom: 8px;">🎯 Next Steps</h3>
                <div style="background: #fff3cd; padding: 20px; border-radius: 8px; border-left: 4px solid #ffc107;">
                    <ol style="margin: 0; padding-left: 20px; color: #856404; line-height: 1.8;">
                        <li>Verify you received this email in your <NAME_EMAIL></li>
                        <li>Check spam/junk folder if not in main inbox</li>
                        <li>Update banking system to force SMTP in production</li>
                        <li>Test user registration and OTP email flows</li>
                        <li>Configure email templates for all banking operations</li>
                        <li>Set up email monitoring and logging</li>
                    </ol>
                </div>
            </div>
            
            <!-- Footer -->
            <div style="background: #2c3e50; color: white; padding: 20px; text-align: center;">
                <p style="margin: 0; opacity: 0.8; font-size: 0.9em;">This email confirms your SMTP configuration is working perfectly!</p>
                <p style="margin: 5px 0 0 0; opacity: 0.6; font-size: 0.8em;">&copy; ' . date('Y') . ' Online Banking System. All rights reserved.</p>
            </div>
        </div>
    </body>
    </html>';
    
    echo "<h3>📤 Sending Email...</h3>";
    echo "<div style='background: #f8f9fa; padding: 15px; border-radius: 5px; margin: 15px 0;'>";
    
    // Send the email
    $result = $mail->send();
    
    echo "</div>";
    
    if ($result) {
        echo "<div class='success'>";
        echo "<h2>🎉 EMAIL SENT SUCCESSFULLY!</h2>";
        echo "<p>The force SMTP test email has been sent to <strong>$test_email</strong></p>";
        echo "<p><strong>Subject:</strong> FORCE SMTP TEST - " . date('Y-m-d H:i:s') . "</p>";
        echo "<p><strong>Method:</strong> Direct PHPMailer SMTP (bypassed localhost detection)</p>";
        echo "<p><strong>Time:</strong> " . date('Y-m-d H:i:s') . "</p>";
        echo "</div>";
        
        echo "<div class='info'>";
        echo "<h3>📧 What to Check:</h3>";
        echo "<ol>";
        echo "<li>Check your inbox at <strong><EMAIL></strong></li>";
        echo "<li>Look for email with subject: <em>FORCE SMTP TEST</em></li>";
        echo "<li>Check spam/junk folder if not in main inbox</li>";
        echo "<li>Email should display with full HTML formatting</li>";
        echo "<li>This confirms your SMTP settings are working perfectly!</li>";
        echo "</ol>";
        echo "</div>";
        
    } else {
        echo "<div class='error'>";
        echo "<h2>❌ EMAIL SENDING FAILED</h2>";
        echo "<p>The force SMTP test failed. Check the debug output above for details.</p>";
        echo "</div>";
    }
    
} catch (Exception $e) {
    echo "<div class='error'>";
    echo "<h2>❌ SMTP ERROR</h2>";
    echo "<p><strong>Error Message:</strong> " . htmlspecialchars($e->getMessage()) . "</p>";
    echo "<p><strong>Error Code:</strong> " . $e->getCode() . "</p>";
    echo "</div>";
    
    echo "<h3>📄 Full Error Details:</h3>";
    echo "<pre>" . htmlspecialchars($e->getTraceAsString()) . "</pre>";
}

// Log the test
$log_entry = "=== FORCE SMTP TEST ===\n";
$log_entry .= "To: $test_email\n";
$log_entry .= "Time: " . date('Y-m-d H:i:s') . "\n";
$log_entry .= "Result: " . (isset($result) && $result ? 'SUCCESS' : 'FAILED') . "\n";
$log_entry .= "Method: Direct PHPMailer SMTP (Forced)\n";
$log_entry .= "Host: $smtp_host:$smtp_port\n";
$log_entry .= "========================\n\n";

if (!is_dir('logs')) {
    mkdir('logs', 0755, true);
}
file_put_contents('logs/force_smtp_test.log', $log_entry, FILE_APPEND | LOCK_EX);

echo "<div class='info'>";
echo "<h3>📄 Test Summary:</h3>";
echo "<ul>";
echo "<li><strong>Target Email:</strong> $test_email</li>";
echo "<li><strong>Test Time:</strong> " . date('Y-m-d H:i:s') . "</li>";
echo "<li><strong>SMTP Host:</strong> $smtp_host</li>";
echo "<li><strong>SMTP Port:</strong> $smtp_port</li>";
echo "<li><strong>Result:</strong> " . (isset($result) && $result ? 'SUCCESS' : 'FAILED') . "</li>";
echo "<li><strong>Log File:</strong> logs/force_smtp_test.log</li>";
echo "</ul>";
echo "</div>";

echo "</div>";
echo "</body></html>";
?>
