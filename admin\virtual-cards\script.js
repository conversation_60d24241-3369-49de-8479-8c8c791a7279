/**
 * Virtual Cards Management JavaScript
 * Handles virtual card creation, management, and interactions
 */

document.addEventListener('DOMContentLoaded', function() {
    initializeVirtualCards();
    initializeCardPreview();
    initializeCardActions();
    initializeFormValidation();
});

/**
 * Initialize Virtual Cards Management
 */
function initializeVirtualCards() {
    // Add animation to cards
    const cards = document.querySelectorAll('.virtual-card');
    cards.forEach((card, index) => {
        card.style.animationDelay = `${index * 0.1}s`;
        card.classList.add('fade-in-up');
    });

    // Initialize tooltips
    const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });

    // Add card hover effects
    cards.forEach(card => {
        card.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-8px) rotateX(5deg)';
        });
        
        card.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(0) rotateX(0)';
        });
    });
}

/**
 * Initialize Card Preview
 */
function initializeCardPreview() {
    const cardTypeInputs = document.querySelectorAll('input[name="card_type"]');
    const userSelect = document.querySelector('select[name="user_id"]');
    const spendingLimitInput = document.querySelector('input[name="spending_limit"]');
    
    // Create preview card
    createCardPreview();
    
    // Update preview on card type change
    cardTypeInputs.forEach(input => {
        input.addEventListener('change', function() {
            updateCardPreview();
        });
    });
    
    // Update preview on user change
    if (userSelect) {
        userSelect.addEventListener('change', function() {
            updateCardPreview();
        });
    }
    
    // Update preview on spending limit change
    if (spendingLimitInput) {
        spendingLimitInput.addEventListener('input', function() {
            updateCardPreview();
        });
    }
}

/**
 * Create Card Preview
 */
function createCardPreview() {
    const form = document.querySelector('form');
    if (!form) return;
    
    const previewContainer = document.createElement('div');
    previewContainer.className = 'mt-3';
    previewContainer.innerHTML = `
        <label class="form-label">Card Preview</label>
        <div class="card-preview visa" id="card-preview">
            <div class="d-flex justify-content-between align-items-center">
                <div class="card-type-badge">VISA</div>
                <div class="card-chip"></div>
            </div>
            <div class="card-preview-number">**** **** **** ****</div>
            <div class="card-preview-details">
                <div>
                    <div style="font-size: 0.6rem; opacity: 0.8;">CARD HOLDER</div>
                    <div>SELECT USER</div>
                </div>
                <div>
                    <div style="font-size: 0.6rem; opacity: 0.8;">EXPIRES</div>
                    <div>${getExpiryDate()}</div>
                </div>
                <div>
                    <div style="font-size: 0.6rem; opacity: 0.8;">LIMIT</div>
                    <div>$1,000</div>
                </div>
            </div>
        </div>
    `;
    
    form.appendChild(previewContainer);
}

/**
 * Update Card Preview
 */
function updateCardPreview() {
    const preview = document.getElementById('card-preview');
    if (!preview) return;
    
    const selectedCardType = document.querySelector('input[name="card_type"]:checked')?.value || 'visa';
    const selectedUser = document.querySelector('select[name="user_id"]');
    const spendingLimit = document.querySelector('input[name="spending_limit"]')?.value || '1000';
    
    // Update card type styling
    preview.className = `card-preview ${selectedCardType}`;
    
    // Update card type badge
    const typeBadge = preview.querySelector('.card-type-badge');
    if (typeBadge) {
        typeBadge.textContent = selectedCardType.toUpperCase();
    }
    
    // Update card holder name
    const cardHolder = preview.querySelector('.card-preview-details > div:first-child > div:last-child');
    if (cardHolder && selectedUser && selectedUser.value) {
        const selectedOption = selectedUser.options[selectedUser.selectedIndex];
        const userName = selectedOption.textContent.split('(@')[0].trim().toUpperCase();
        cardHolder.textContent = userName;
    } else if (cardHolder) {
        cardHolder.textContent = 'SELECT USER';
    }
    
    // Update spending limit
    const limitDisplay = preview.querySelector('.card-preview-details > div:last-child > div:last-child');
    if (limitDisplay) {
        limitDisplay.textContent = formatCurrency(parseFloat(spendingLimit) || 0);
    }
    
    // Add animation
    preview.style.animation = 'cardFlip 0.6s ease-in-out';
    setTimeout(() => {
        preview.style.animation = '';
    }, 600);
}

/**
 * Get Expiry Date (3 years from now)
 */
function getExpiryDate() {
    const now = new Date();
    const expiry = new Date(now.getFullYear() + 3, now.getMonth());
    return `${String(expiry.getMonth() + 1).padStart(2, '0')}/${String(expiry.getFullYear()).slice(-2)}`;
}

/**
 * Initialize Card Actions
 */
function initializeCardActions() {
    // Add click handlers for card action buttons
    document.addEventListener('click', function(e) {
        if (e.target.matches('[onclick*="topUpCard"]')) {
            e.preventDefault();
            const cardId = e.target.getAttribute('onclick').match(/\d+/)[0];
            topUpCard(parseInt(cardId));
        }
        
        if (e.target.matches('[onclick*="viewCardDetails"]')) {
            e.preventDefault();
            const cardId = e.target.getAttribute('onclick').match(/\d+/)[0];
            viewCardDetails(parseInt(cardId));
        }
        
        if (e.target.matches('[onclick*="blockCard"]')) {
            e.preventDefault();
            const cardId = e.target.getAttribute('onclick').match(/\d+/)[0];
            blockCard(parseInt(cardId));
        }
        
        if (e.target.matches('[onclick*="activateCard"]')) {
            e.preventDefault();
            const cardId = e.target.getAttribute('onclick').match(/\d+/)[0];
            activateCard(parseInt(cardId));
        }
    });
}

/**
 * Top Up Card
 */
function topUpCard(cardId) {
    const modal = createTopUpModal(cardId);
    document.body.appendChild(modal);
    
    const bootstrapModal = new bootstrap.Modal(modal);
    bootstrapModal.show();
    
    modal.addEventListener('hidden.bs.modal', function() {
        modal.remove();
    });
}

/**
 * Create Top Up Modal
 */
function createTopUpModal(cardId) {
    const modal = document.createElement('div');
    modal.className = 'modal fade';
    modal.innerHTML = `
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Top Up Virtual Card</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="topup-form">
                        <input type="hidden" name="card_id" value="${cardId}">
                        <div class="mb-3">
                            <label class="form-label">Top Up Amount</label>
                            <div class="input-group">
                                <span class="input-group-text">$</span>
                                <input type="number" name="amount" class="form-control" step="0.01" min="1" required>
                            </div>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">Description</label>
                            <textarea name="description" class="form-control" rows="2" placeholder="Top up description...">Virtual card top-up</textarea>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="button" class="btn btn-primary" onclick="processTopUp(${cardId})">Top Up Card</button>
                </div>
            </div>
        </div>
    `;
    return modal;
}

/**
 * Process Top Up
 */
function processTopUp(cardId) {
    const form = document.getElementById('topup-form');
    const formData = new FormData(form);
    
    // Add loading state
    const submitBtn = document.querySelector('.modal-footer .btn-primary');
    setButtonLoading(submitBtn, true);
    
    fetch('ajax/topup-card.php', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showNotification('Card topped up successfully!', 'success');
            location.reload(); // Refresh to show updated balance
        } else {
            showNotification(data.message || 'Top up failed', 'error');
        }
    })
    .catch(error => {
        console.error('Top up error:', error);
        showNotification('Top up failed. Please try again.', 'error');
    })
    .finally(() => {
        setButtonLoading(submitBtn, false);
        const modalElement = document.querySelector('.modal');
        const modalInstance = modalElement ? bootstrap.Modal.getInstance(modalElement) : null;
        if (modalInstance) {
            modalInstance.hide();
        }
    });
}

/**
 * View Card Details
 */
function viewCardDetails(cardId) {
    const modal = createDetailsModal(cardId);
    document.body.appendChild(modal);
    
    const bootstrapModal = new bootstrap.Modal(modal);
    bootstrapModal.show();
    
    // Load card details
    loadCardDetails(cardId);
    
    modal.addEventListener('hidden.bs.modal', function() {
        modal.remove();
    });
}

/**
 * Create Details Modal
 */
function createDetailsModal(cardId) {
    const modal = document.createElement('div');
    modal.className = 'modal fade';
    modal.innerHTML = `
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Virtual Card Details</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body" id="card-details-content">
                    <div class="text-center">
                        <div class="spinner-border" role="status">
                            <span class="visually-hidden">Loading...</span>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                </div>
            </div>
        </div>
    `;
    return modal;
}

/**
 * Load Card Details
 */
function loadCardDetails(cardId) {
    fetch(`api/card-details.php?id=${cardId}`)
        .then(response => response.json())
        .then(data => {
            const content = document.getElementById('card-details-content');
            if (data.success) {
                content.innerHTML = generateCardDetailsHTML(data.card);
            } else {
                content.innerHTML = '<div class="alert alert-danger">Failed to load card details</div>';
            }
        })
        .catch(error => {
            console.error('Load details error:', error);
            const content = document.getElementById('card-details-content');
            content.innerHTML = '<div class="alert alert-danger">Failed to load card details</div>';
        });
}

/**
 * Generate Card Details HTML
 */
function generateCardDetailsHTML(card) {
    return `
        <div class="row">
            <div class="col-md-6">
                <div class="virtual-card ${card.card_type} ${card.status}" style="transform: none; margin-bottom: 1rem;">
                    <div class="card-header-info">
                        <div class="card-type-badge">${card.card_type.toUpperCase()}</div>
                        <div class="card-status-badge status-${card.status}">${card.status.toUpperCase()}</div>
                    </div>
                    <div class="card-number">${formatCardNumber(card.card_number)}</div>
                    <div class="card-details">
                        <div>
                            <div class="label">CARD HOLDER</div>
                            <div class="value">${card.card_holder_name}</div>
                        </div>
                        <div>
                            <div class="label">EXPIRES</div>
                            <div class="value">${String(card.expiry_month).padStart(2, '0')}/${card.expiry_year}</div>
                        </div>
                        <div>
                            <div class="label">CVV</div>
                            <div class="value">${card.cvv}</div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <table class="table">
                    <tr><th>Card ID:</th><td>#${card.id}</td></tr>
                    <tr><th>Current Balance:</th><td>${formatCurrency(card.current_balance)}</td></tr>
                    <tr><th>Spending Limit:</th><td>${formatCurrency(card.spending_limit)}</td></tr>
                    <tr><th>Available:</th><td>${formatCurrency(card.spending_limit - card.current_balance)}</td></tr>
                    <tr><th>Created:</th><td>${formatDate(card.created_at)}</td></tr>
                    <tr><th>Last Updated:</th><td>${formatDate(card.updated_at)}</td></tr>
                </table>
            </div>
        </div>
    `;
}

/**
 * Block Card
 */
function blockCard(cardId) {
    if (confirm('Are you sure you want to block this card?')) {
        updateCardStatus(cardId, 'blocked');
    }
}

/**
 * Activate Card
 */
function activateCard(cardId) {
    updateCardStatus(cardId, 'active');
}

/**
 * Update Card Status
 */
function updateCardStatus(cardId, status) {
    const formData = new FormData();
    formData.append('card_id', cardId);
    formData.append('status', status);
    
    fetch('api/update-card-status.php', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showNotification(`Card ${status} successfully!`, 'success');
            location.reload();
        } else {
            showNotification(data.message || 'Status update failed', 'error');
        }
    })
    .catch(error => {
        console.error('Status update error:', error);
        showNotification('Status update failed. Please try again.', 'error');
    });
}

/**
 * Initialize Form Validation
 */
function initializeFormValidation() {
    const form = document.querySelector('form');
    
    if (form) {
        form.addEventListener('submit', function(e) {
            if (!validateCardForm(this)) {
                e.preventDefault();
                return false;
            }
        });
    }
}

/**
 * Validate Card Form
 */
function validateCardForm(form) {
    let isValid = true;
    
    const userSelect = form.querySelector('select[name="user_id"]');
    if (!userSelect.value) {
        showFieldError(userSelect, 'Please select a user');
        isValid = false;
    }
    
    const spendingLimit = form.querySelector('input[name="spending_limit"]');
    if (spendingLimit && parseFloat(spendingLimit.value) <= 0) {
        showFieldError(spendingLimit, 'Spending limit must be greater than zero');
        isValid = false;
    }
    
    return isValid;
}

/**
 * Utility Functions
 */
function formatCurrency(amount) {
    return new Intl.NumberFormat('en-US', {
        style: 'currency',
        currency: 'USD'
    }).format(amount);
}

function formatCardNumber(number) {
    return number.replace(/(.{4})/g, '$1 ').trim();
}

function formatDate(dateString) {
    return new Date(dateString).toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'short',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit'
    });
}

function showFieldError(field, message) {
    field.classList.add('is-invalid');
    
    let errorDiv = field.parentElement.querySelector('.invalid-feedback');
    if (!errorDiv) {
        errorDiv = document.createElement('div');
        errorDiv.className = 'invalid-feedback';
        field.parentElement.appendChild(errorDiv);
    }
    
    errorDiv.textContent = message;
}

function setButtonLoading(button, loading) {
    if (loading) {
        button.disabled = true;
        button.innerHTML = '<span class="spinner-border spinner-border-sm me-2"></span>Processing...';
    } else {
        button.disabled = false;
        button.innerHTML = button.getAttribute('data-original-text') || 'Submit';
    }
}

function showNotification(message, type) {
    // Create notification element
    const notification = document.createElement('div');
    notification.className = `alert alert-${type === 'success' ? 'success' : 'danger'} alert-dismissible fade show position-fixed`;
    notification.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
    notification.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    
    document.body.appendChild(notification);
    
    // Auto remove after 5 seconds
    setTimeout(() => {
        if (notification.parentElement) {
            notification.remove();
        }
    }, 5000);
}

// Add CSS animations
const style = document.createElement('style');
style.textContent = `
    @keyframes fade-in-up {
        from {
            opacity: 0;
            transform: translateY(30px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }
    
    @keyframes cardFlip {
        0%, 100% { transform: rotateY(0deg); }
        50% { transform: rotateY(180deg); }
    }
    
    .fade-in-up {
        animation: fade-in-up 0.6s ease-out forwards;
        opacity: 0;
    }
`;
document.head.appendChild(style);

// Export functions for global access
window.VirtualCards = {
    topUpCard,
    viewCardDetails,
    blockCard,
    activateCard,
    formatCurrency,
    formatCardNumber
};
