# Online Banking System - Database Documentation

## Overview

The Online Banking System uses a MySQL/MariaDB database with a comprehensive schema designed for secure financial transactions, user management, and administrative oversight. The database is configured with UTF-8 character encoding and follows best practices for data integrity and security.

## Database Configuration

- **Database Name**: `online_banking`
- **Character Set**: `utf8mb4`
- **Collation**: `utf8mb4_unicode_ci`
- **Engine**: InnoDB (for ACID compliance and foreign key support)

### Connection Details
- **Host**: localhost
- **Default Username**: root
- **Default Password**: root (development environment)
- **Port**: 3306 (default MySQL port)

## Core Database Tables

### 1. accounts
**Primary user and account management table**

| Column | Type | Constraints | Description |
|--------|------|-------------|-------------|
| id | INT | PRIMARY KEY, AUTO_INCREMENT | Unique account identifier |
| account_number | VARCHAR(20) | UNIQUE, NOT NULL | Bank account number |
| username | VARCHAR(50) | UNIQUE, NOT NULL | Login username |
| password | VARCHAR(255) | NOT NULL | Hashed password |
| email | VARCHAR(100) | UNIQUE, NOT NULL | User email address |
| first_name | VARCHAR(50) | NOT NULL | User's first name |
| last_name | VARCHAR(50) | NOT NULL | User's last name |
| phone | VARCHAR(20) | NULL | Contact phone number |
| address | TEXT | NULL | Physical address |
| date_of_birth | DATE | NULL | Date of birth |
| occupation | VARCHAR(100) | NULL | User's occupation |
| marital_status | ENUM | DEFAULT 'single' | single, married, divorced, widowed |
| gender | ENUM | DEFAULT 'male' | male, female, other |
| currency | VARCHAR(3) | DEFAULT 'USD' | Account currency code |
| account_type | ENUM | DEFAULT 'savings' | savings, checking, business |
| balance | DECIMAL(15,2) | DEFAULT 0.00 | Current account balance |
| status | ENUM | DEFAULT 'pending' | active, suspended, closed, pending, rejected |
| pin | VARCHAR(255) | NULL | Encrypted PIN for transactions |
| is_admin | TINYINT(1) | DEFAULT 0 | Admin privileges flag |
| is_super_admin | TINYINT(1) | DEFAULT 0 | Super admin privileges flag |
| role | ENUM | DEFAULT 'user' | user, admin, super_admin |
| kyc_status | ENUM | DEFAULT 'pending' | pending, verified, rejected |
| kyc_verified_at | TIMESTAMP | NULL | KYC verification timestamp |
| kyc_verified_by | INT | NULL | Admin who verified KYC |
| last_login | TIMESTAMP | NULL | Last login timestamp |
| login_attempts | INT | DEFAULT 0 | Failed login attempt counter |
| locked_until | TIMESTAMP | NULL | Account lockout expiration |
| created_at | TIMESTAMP | DEFAULT CURRENT_TIMESTAMP | Account creation time |
| updated_at | TIMESTAMP | DEFAULT CURRENT_TIMESTAMP ON UPDATE | Last update time |
| deleted_at | DATETIME | NULL | Soft deletion timestamp |
| deleted_by | INT | NULL | Admin who deleted account |
| deletion_reason | TEXT | NULL | Reason for account deletion |

**Indexes:**
- PRIMARY KEY (id)
- UNIQUE KEY (account_number)
- UNIQUE KEY (username)
- UNIQUE KEY (email)
- INDEX (status)
- INDEX (kyc_status)
- INDEX (is_admin)

### 2. transfers
**Transaction and transfer management table**

| Column | Type | Constraints | Description |
|--------|------|-------------|-------------|
| id | INT | PRIMARY KEY, AUTO_INCREMENT | Unique transfer identifier |
| transaction_id | VARCHAR(50) | UNIQUE, NOT NULL | Unique transaction reference |
| sender_id | INT | FOREIGN KEY → accounts(id) | Sender account ID |
| recipient_id | INT | FOREIGN KEY → accounts(id) | Recipient account ID |
| recipient_account | VARCHAR(20) | NULL | External recipient account |
| recipient_name | VARCHAR(100) | NULL | Recipient name |
| amount | DECIMAL(15,2) | NOT NULL | Transfer amount |
| currency | VARCHAR(3) | DEFAULT 'USD' | Transfer currency |
| transfer_type | ENUM | DEFAULT 'local' | local, international, bitcoin |
| status | ENUM | DEFAULT 'pending' | pending, completed, failed, cancelled |
| description | TEXT | NULL | Transfer description |
| fee | DECIMAL(10,2) | DEFAULT 0.00 | Transaction fee |
| exchange_rate | DECIMAL(10,4) | DEFAULT 1.0000 | Currency exchange rate |
| created_at | TIMESTAMP | DEFAULT CURRENT_TIMESTAMP | Transfer initiation time |
| completed_at | TIMESTAMP | NULL | Transfer completion time |

**Foreign Keys:**
- sender_id → accounts(id) ON DELETE SET NULL
- recipient_id → accounts(id) ON DELETE SET NULL

### 3. transactions
**General transaction logging table**

| Column | Type | Constraints | Description |
|--------|------|-------------|-------------|
| id | INT | PRIMARY KEY, AUTO_INCREMENT | Unique transaction identifier |
| user_id | INT | FOREIGN KEY → accounts(id) | Account holder ID |
| transaction_type | ENUM | NOT NULL | credit, debit |
| amount | DECIMAL(15,2) | NOT NULL | Transaction amount |
| currency | VARCHAR(3) | DEFAULT 'USD' | Transaction currency |
| description | TEXT | NULL | Transaction description |
| reference_number | VARCHAR(50) | NULL | External reference number |
| category | ENUM | DEFAULT 'adjustment' | deposit, withdrawal, transfer, fee, interest, adjustment, virtual_card, crypto |
| status | ENUM | DEFAULT 'completed' | pending, completed, failed, cancelled |
| processed_by | INT | FOREIGN KEY → accounts(id) | Admin who processed |
| created_at | TIMESTAMP | DEFAULT CURRENT_TIMESTAMP | Transaction time |
| updated_at | TIMESTAMP | DEFAULT CURRENT_TIMESTAMP ON UPDATE | Last update time |

**Foreign Keys:**
- user_id → accounts(id) ON DELETE CASCADE
- processed_by → accounts(id) ON DELETE SET NULL

### 4. beneficiaries
**Saved recipient management table**

| Column | Type | Constraints | Description |
|--------|------|-------------|-------------|
| id | INT | PRIMARY KEY, AUTO_INCREMENT | Unique beneficiary identifier |
| user_id | INT | FOREIGN KEY → accounts(id) | Account holder ID |
| name | VARCHAR(100) | NOT NULL | Beneficiary name |
| account_number | VARCHAR(20) | NOT NULL | Beneficiary account number |
| bank_name | VARCHAR(100) | NULL | Beneficiary bank name |
| bank_code | VARCHAR(20) | NULL | Bank routing/SWIFT code |
| country | VARCHAR(50) | DEFAULT 'USA' | Beneficiary country |
| currency | VARCHAR(3) | DEFAULT 'USD' | Preferred currency |
| is_favorite | BOOLEAN | DEFAULT FALSE | Favorite beneficiary flag |
| created_at | TIMESTAMP | DEFAULT CURRENT_TIMESTAMP | Creation time |

**Foreign Keys:**
- user_id → accounts(id) ON DELETE CASCADE

### 5. virtual_cards
**Virtual card management table**

| Column | Type | Constraints | Description |
|--------|------|-------------|-------------|
| id | INT | PRIMARY KEY, AUTO_INCREMENT | Unique card identifier |
| user_id | INT | FOREIGN KEY → accounts(id) | Card holder ID |
| card_number | VARCHAR(19) | NOT NULL | Virtual card number |
| card_holder_name | VARCHAR(100) | NOT NULL | Name on card |
| expiry_month | INT | NOT NULL | Card expiry month |
| expiry_year | INT | NOT NULL | Card expiry year |
| cvv | VARCHAR(4) | NOT NULL | Card verification value |
| card_type | ENUM | DEFAULT 'visa' | visa, mastercard, amex |
| status | ENUM | DEFAULT 'active' | active, blocked, expired |
| spending_limit | DECIMAL(15,2) | DEFAULT 1000.00 | Card spending limit |
| current_balance | DECIMAL(15,2) | DEFAULT 0.00 | Available balance |
| linked_account_id | INT | FOREIGN KEY → accounts(id) | Linked account |
| created_at | TIMESTAMP | DEFAULT CURRENT_TIMESTAMP | Card creation time |
| updated_at | TIMESTAMP | DEFAULT CURRENT_TIMESTAMP ON UPDATE | Last update time |

**Foreign Keys:**
- user_id → accounts(id) ON DELETE CASCADE
- linked_account_id → accounts(id) ON DELETE SET NULL

## Security and Authentication Tables

### 6. user_otps
**One-Time Password management table**

| Column | Type | Constraints | Description |
|--------|------|-------------|-------------|
| id | INT | PRIMARY KEY, AUTO_INCREMENT | Unique OTP identifier |
| user_id | INT | FOREIGN KEY → accounts(id) | User account ID |
| otp_code | VARCHAR(10) | NOT NULL | Generated OTP code |
| expires_at | DATETIME | NOT NULL | OTP expiration time |
| source | VARCHAR(20) | DEFAULT 'login' | OTP generation source |
| used | TINYINT(1) | DEFAULT 0 | Usage status flag |
| used_at | DATETIME | NULL | Usage timestamp |
| created_at | DATETIME | DEFAULT CURRENT_TIMESTAMP | OTP generation time |

**Indexes:**
- INDEX (user_id)
- INDEX (otp_code)
- INDEX (expires_at)

**Foreign Keys:**
- user_id → accounts(id) ON DELETE CASCADE

### 7. user_security_settings
**User-specific security configuration table**

| Column | Type | Constraints | Description |
|--------|------|-------------|-------------|
| id | INT | PRIMARY KEY, AUTO_INCREMENT | Unique setting identifier |
| user_id | INT | UNIQUE, FOREIGN KEY → accounts(id) | User account ID |
| otp_enabled | TINYINT(1) | DEFAULT 1 | OTP authentication enabled |
| google_2fa_enabled | TINYINT(1) | DEFAULT 0 | Google 2FA enabled |
| require_2fa | TINYINT(1) | DEFAULT 1 | Require 2FA for login |
| allow_remember_device | TINYINT(1) | DEFAULT 0 | Allow device remembering |
| login_attempts_limit | INT | DEFAULT 5 | Max failed login attempts |
| lockout_duration | INT | DEFAULT 30 | Lockout duration (minutes) |
| otp_expiry_minutes | INT | DEFAULT 10 | OTP validity period |
| failed_attempts | INT | DEFAULT 0 | Current failed attempts |
| locked_until | DATETIME | NULL | Account lockout expiration |
| last_login_attempt | DATETIME | NULL | Last login attempt time |
| created_by | INT | NULL | Creator admin ID |
| updated_by | INT | NULL | Last updater admin ID |
| created_at | TIMESTAMP | DEFAULT CURRENT_TIMESTAMP | Creation time |
| updated_at | TIMESTAMP | DEFAULT CURRENT_TIMESTAMP ON UPDATE | Last update time |

**Foreign Keys:**
- user_id → accounts(id) ON DELETE CASCADE

### 8. login_attempts
**Login attempt tracking table**

| Column | Type | Constraints | Description |
|--------|------|-------------|-------------|
| id | INT | PRIMARY KEY, AUTO_INCREMENT | Unique attempt identifier |
| username | VARCHAR(50) | NULL | Attempted username |
| ip_address | VARCHAR(45) | NULL | Source IP address |
| success | BOOLEAN | DEFAULT FALSE | Login success flag |
| attempted_at | TIMESTAMP | DEFAULT CURRENT_TIMESTAMP | Attempt timestamp |

## Administrative and System Tables

### 9. system_settings
**System-wide configuration table**

| Column | Type | Constraints | Description |
|--------|------|-------------|-------------|
| id | INT | PRIMARY KEY, AUTO_INCREMENT | Unique setting identifier |
| setting_key | VARCHAR(100) | UNIQUE, NOT NULL | Configuration key |
| setting_value | TEXT | NULL | Configuration value |
| description | TEXT | NULL | Setting description |
| updated_by | INT | FOREIGN KEY → accounts(id) | Last updater admin ID |
| created_at | TIMESTAMP | DEFAULT CURRENT_TIMESTAMP | Creation time |
| updated_at | TIMESTAMP | DEFAULT CURRENT_TIMESTAMP ON UPDATE | Last update time |

**Foreign Keys:**
- updated_by → accounts(id) ON DELETE SET NULL

### 10. super_admin_settings
**Super administrator configuration table**

| Column | Type | Constraints | Description |
|--------|------|-------------|-------------|
| id | INT | PRIMARY KEY, AUTO_INCREMENT | Unique setting identifier |
| setting_key | VARCHAR(100) | UNIQUE, NOT NULL | Configuration key |
| setting_value | TEXT | NULL | Configuration value |
| setting_description | TEXT | NULL | Setting description |
| setting_type | ENUM | DEFAULT 'text' | text, number, boolean, json |
| is_encrypted | TINYINT(1) | DEFAULT 0 | Encryption flag |
| created_at | TIMESTAMP | DEFAULT CURRENT_TIMESTAMP | Creation time |
| updated_at | TIMESTAMP | DEFAULT CURRENT_TIMESTAMP ON UPDATE | Last update time |

### 11. super_admin_2fa_settings
**Super admin 2FA configuration table**

| Column | Type | Constraints | Description |
|--------|------|-------------|-------------|
| id | INT | PRIMARY KEY, AUTO_INCREMENT | Unique setting identifier |
| super_admin_username | VARCHAR(50) | UNIQUE, NOT NULL | Super admin username |
| google_2fa_enabled | TINYINT(1) | DEFAULT 0 | Google 2FA status |
| google_2fa_secret | VARCHAR(255) | NULL | 2FA secret key |
| backup_codes | TEXT | NULL | Backup recovery codes |
| last_used_timestamp | INT | NULL | Last 2FA usage time |
| failed_attempts | INT | DEFAULT 0 | Failed 2FA attempts |
| locked_until | DATETIME | NULL | 2FA lockout expiration |
| created_at | TIMESTAMP | DEFAULT CURRENT_TIMESTAMP | Creation time |
| updated_at | TIMESTAMP | DEFAULT CURRENT_TIMESTAMP ON UPDATE | Last update time |

## Document and File Management Tables

### 12. user_documents
**User document storage and verification table**

| Column | Type | Constraints | Description |
|--------|------|-------------|-------------|
| id | INT | PRIMARY KEY, AUTO_INCREMENT | Unique document identifier |
| user_id | INT | FOREIGN KEY → accounts(id) | Document owner ID |
| document_type | VARCHAR(50) | NOT NULL | Document category |
| document_name | VARCHAR(255) | NOT NULL | Original filename |
| file_path | VARCHAR(500) | NOT NULL | Server file path |
| file_size | INT | NOT NULL | File size in bytes |
| file_type | VARCHAR(100) | NOT NULL | MIME type |
| verification_status | ENUM | DEFAULT 'pending' | pending, approved, rejected |
| notes | TEXT | NULL | Verification notes |
| uploaded_at | TIMESTAMP | DEFAULT CURRENT_TIMESTAMP | Upload time |
| created_at | TIMESTAMP | DEFAULT CURRENT_TIMESTAMP | Creation time |
| updated_at | TIMESTAMP | DEFAULT CURRENT_TIMESTAMP ON UPDATE | Last update time |

**Indexes:**
- INDEX (user_id)
- INDEX (verification_status)

**Foreign Keys:**
- user_id → accounts(id) ON DELETE CASCADE

## Currency and Financial Management Tables

### 13. currencies
**Supported currency management table**

| Column | Type | Constraints | Description |
|--------|------|-------------|-------------|
| id | INT | PRIMARY KEY, AUTO_INCREMENT | Unique currency identifier |
| code | VARCHAR(3) | UNIQUE, NOT NULL | ISO currency code |
| name | VARCHAR(50) | NOT NULL | Currency full name |
| symbol | VARCHAR(10) | NOT NULL | Currency symbol |
| status | ENUM | DEFAULT 'active' | active, inactive |
| created_at | TIMESTAMP | DEFAULT CURRENT_TIMESTAMP | Creation time |
| updated_at | TIMESTAMP | DEFAULT CURRENT_TIMESTAMP ON UPDATE | Last update time |

**Indexes:**
- INDEX (code)
- INDEX (status)
- INDEX (status, code)

## Audit and Security Tracking Tables

### 14. audit_logs
**System activity audit trail table**

| Column | Type | Constraints | Description |
|--------|------|-------------|-------------|
| id | INT | PRIMARY KEY, AUTO_INCREMENT | Unique log identifier |
| user_id | INT | FOREIGN KEY → accounts(id) | User who performed action |
| action | VARCHAR(100) | NOT NULL | Action performed |
| table_name | VARCHAR(50) | NULL | Affected table |
| record_id | INT | NULL | Affected record ID |
| old_values | JSON | NULL | Previous values |
| new_values | JSON | NULL | New values |
| ip_address | VARCHAR(45) | NULL | Source IP address |
| user_agent | TEXT | NULL | Browser user agent |
| created_at | TIMESTAMP | DEFAULT CURRENT_TIMESTAMP | Action timestamp |

**Foreign Keys:**
- user_id → accounts(id) ON DELETE SET NULL

### 15. deletion_audit
**User deletion activity tracking table**

| Column | Type | Constraints | Description |
|--------|------|-------------|-------------|
| id | INT | PRIMARY KEY, AUTO_INCREMENT | Unique audit identifier |
| user_id | INT | NOT NULL | Deleted user ID |
| action_type | ENUM | NOT NULL | soft_delete, archive, hard_delete, restore |
| performed_by | INT | NOT NULL | Admin who performed action |
| performed_at | DATETIME | DEFAULT CURRENT_TIMESTAMP | Action timestamp |
| reason | TEXT | NULL | Deletion reason |
| user_data_snapshot | JSON | NULL | User data backup |
| related_data_summary | JSON | NULL | Related data summary |
| ip_address | VARCHAR(45) | NULL | Source IP address |
| user_agent | TEXT | NULL | Browser user agent |

**Indexes:**
- INDEX (user_id)
- INDEX (performed_by)
- INDEX (performed_at)
- INDEX (action_type)

### 16. user_archive
**Archived user data storage table**

| Column | Type | Constraints | Description |
|--------|------|-------------|-------------|
| id | INT | PRIMARY KEY, AUTO_INCREMENT | Unique archive identifier |
| original_user_id | INT | NOT NULL | Original user ID |
| username | VARCHAR(50) | NOT NULL | Archived username |
| email | VARCHAR(100) | NOT NULL | Archived email |
| first_name | VARCHAR(50) | NULL | Archived first name |
| last_name | VARCHAR(50) | NULL | Archived last name |
| balance | DECIMAL(15,2) | DEFAULT 0.00 | Final balance |
| status | VARCHAR(20) | NULL | Final status |
| created_at | DATETIME | NULL | Original creation time |
| archived_at | DATETIME | NOT NULL | Archive timestamp |
| archived_by | INT | NOT NULL | Admin who archived |
| archive_reason | TEXT | NULL | Archive reason |
| related_data_summary | JSON | NULL | Related data summary |

**Indexes:**
- INDEX (original_user_id)
- INDEX (archived_by)
- INDEX (archived_at)

### 17. deletion_codes
**Secure deletion verification codes table**

| Column | Type | Constraints | Description |
|--------|------|-------------|-------------|
| id | INT | PRIMARY KEY, AUTO_INCREMENT | Unique code identifier |
| user_id | INT | UNIQUE, NOT NULL | Target user ID |
| code | VARCHAR(20) | NOT NULL | Verification code |
| generated_by | INT | NOT NULL | Admin who generated code |
| generated_at | DATETIME | DEFAULT CURRENT_TIMESTAMP | Generation time |
| expires_at | DATETIME | NOT NULL | Code expiration time |
| used_at | DATETIME | NULL | Code usage time |

**Indexes:**
- INDEX (code)
- INDEX (expires_at)

## Database Relationships and Constraints

### Primary Relationships
1. **accounts** → **transfers** (sender_id, recipient_id)
2. **accounts** → **transactions** (user_id, processed_by)
3. **accounts** → **beneficiaries** (user_id)
4. **accounts** → **virtual_cards** (user_id, linked_account_id)
5. **accounts** → **user_otps** (user_id)
6. **accounts** → **user_security_settings** (user_id)
7. **accounts** → **user_documents** (user_id)
8. **accounts** → **audit_logs** (user_id)
9. **accounts** → **system_settings** (updated_by)

### Cascade Rules
- **ON DELETE CASCADE**: Child records are automatically deleted when parent is deleted
  - user_otps, beneficiaries, virtual_cards, user_documents, user_security_settings
- **ON DELETE SET NULL**: Foreign key is set to NULL when parent is deleted
  - transfers (sender_id, recipient_id), audit_logs (user_id), system_settings (updated_by)

## Database Triggers

### create_user_security_settings
**Automatically creates security settings for new non-admin users**
- **Trigger Type**: AFTER INSERT ON accounts
- **Condition**: NEW.is_admin = 0
- **Action**: Creates default security settings with OTP enabled and 2FA required

## Default System Data

### Default Admin User
- **Username**: admin
- **Password**: admin123 (hashed)
- **Email**: <EMAIL>
- **Account Number**: **********
- **Role**: super_admin

### Default System Settings
- bank_name: "SecureBank Online"
- currency: "USD"
- min_transfer_amount: "1.00"
- max_transfer_amount: "10000.00"
- daily_transfer_limit: "25000.00"
- maintenance_mode: "false"
- registration_enabled: "true"

### Supported Currencies
The system supports 50+ international currencies including:
- USD (US Dollar) - $
- EUR (Euro) - €
- GBP (British Pound) - £
- JPY (Japanese Yen) - ¥
- CAD (Canadian Dollar) - C$
- And many more...

## Database Maintenance and Backup

### Backup Scripts
- **Full Backup**: `sql_export_generator.php` (option 1)
- **Schema Only**: `sql_export_generator.php` (option 2)
- **Data Only**: `sql_export_generator.php` (option 3)

### Command Line Tools
- **Windows**: `sql/export_database.bat`
- **Linux/Mac**: `sql/export_database.sh`

### Web Interface
- **URL**: `sql_export_interface.php`
- **Features**: Browser-based backup generation and download

## Security Considerations

### Data Encryption
- Passwords: bcrypt hashing
- PINs: Encrypted storage
- Sensitive settings: Optional encryption flag

### Access Control
- Role-based permissions (user, admin, super_admin)
- IP-based login tracking
- Failed attempt monitoring
- Account lockout mechanisms

### Audit Trail
- Complete action logging
- Data change tracking
- Deletion audit trail
- IP and user agent logging

### Data Retention
- Soft deletion for user accounts
- Archive system for deleted users
- Secure deletion with verification codes
- Audit log retention

---

*This documentation covers the complete database structure for the Online Banking System. For implementation details and API documentation, refer to the respective code files in the project.*
