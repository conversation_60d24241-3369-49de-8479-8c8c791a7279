<?php
/**
 * Super Admin Dashboard
 * Main dashboard for super administrators
 */

// Enable error reporting for debugging
error_reporting(E_ALL);
ini_set('display_errors', 1);
ini_set('log_errors', 1);
ini_set('error_log', __DIR__ . '/../logs/error.log');

$page_title = 'Dashboard';
$page_subtitle = 'System overview and quick actions';

try {
    error_log("Super Admin Dashboard: Starting dashboard load");

    // Include header
    include 'includes/header.php';
    error_log("Super Admin Dashboard: Successfully included header");

} catch (Exception $e) {
    error_log("Super Admin Dashboard Error: " . $e->getMessage() . " in " . $e->getFile() . " on line " . $e->getLine());
    die("Error loading dashboard: " . $e->getMessage());
} catch (Error $e) {
    error_log("Super Admin Dashboard Fatal Error: " . $e->getMessage() . " in " . $e->getFile() . " on line " . $e->getLine());
    die("Fatal error loading dashboard: " . $e->getMessage());
}

try {
    // Get dashboard statistics
    error_log("Super Admin Dashboard: Getting dashboard statistics");
    $stats = getSuperAdminStats();
    error_log("Super Admin Dashboard: Successfully retrieved stats");

    // Get recent audit logs (reduced to 5 for better dashboard performance)
    error_log("Super Admin Dashboard: Getting recent audit logs");
    $recent_logs = getSuperAdminAuditLogs(5);
    error_log("Super Admin Dashboard: Successfully retrieved audit logs");

    // Log dashboard access only once per session to reduce audit log noise
    if (!isset($_SESSION['dashboard_logged_this_session'])) {
        logSuperAdminAction('dashboard_access', 'Super admin accessed dashboard');
        $_SESSION['dashboard_logged_this_session'] = true;
        error_log("Super Admin Dashboard: Successfully logged dashboard access");
    }

} catch (Exception $e) {
    error_log("Super Admin Dashboard Data Error: " . $e->getMessage() . " in " . $e->getFile() . " on line " . $e->getLine());
    // Set default values to prevent further errors
    $stats = ['total_users' => 0, 'active_users' => 0, 'total_transactions' => 0, 'pending_approvals' => 0];
    $recent_logs = [];
}
?>

<!-- Dashboard Stats Cards -->
<div class="row mb-4">
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-0 shadow-sm h-100">
            <div class="card-body">
                <div class="d-flex align-items-center">
                    <div class="flex-shrink-0">
                        <div class="bg-primary bg-gradient rounded-3 p-3">
                            <i class="fas fa-users text-white fa-2x"></i>
                        </div>
                    </div>
                    <div class="flex-grow-1 ms-3">
                        <h6 class="text-muted mb-1">Total Users</h6>
                        <h3 class="mb-0"><?php echo number_format($stats['total_users']); ?></h3>
                        <small class="text-success">
                            <i class="fas fa-arrow-up"></i> 
                            <?php echo number_format($stats['active_users']); ?> active
                        </small>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-0 shadow-sm h-100">
            <div class="card-body">
                <div class="d-flex align-items-center">
                    <div class="flex-shrink-0">
                        <div class="bg-success bg-gradient rounded-3 p-3">
                            <i class="fas fa-exchange-alt text-white fa-2x"></i>
                        </div>
                    </div>
                    <div class="flex-grow-1 ms-3">
                        <h6 class="text-muted mb-1">Transactions Today</h6>
                        <h3 class="mb-0"><?php echo number_format($stats['transactions_today']); ?></h3>
                        <small class="text-muted">
                            <i class="fas fa-calendar-day"></i> 
                            Last 24 hours
                        </small>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-0 shadow-sm h-100">
            <div class="card-body">
                <div class="d-flex align-items-center">
                    <div class="flex-shrink-0">
                        <div class="bg-warning bg-gradient rounded-3 p-3">
                            <i class="fas fa-users-cog text-white fa-2x"></i>
                        </div>
                    </div>
                    <div class="flex-grow-1 ms-3">
                        <h6 class="text-muted mb-1">Total Admins</h6>
                        <h3 class="mb-0"><?php echo number_format($stats['total_admins']); ?></h3>
                        <small class="text-muted">
                            <i class="fas fa-shield-alt"></i>
                            Admin Accounts
                        </small>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-0 shadow-sm h-100">
            <div class="card-body">
                <div class="d-flex align-items-center">
                    <div class="flex-shrink-0">
                        <div class="bg-info bg-gradient rounded-3 p-3">
                            <i class="fas fa-clipboard-list text-white fa-2x"></i>
                        </div>
                    </div>
                    <div class="flex-grow-1 ms-3">
                        <h6 class="text-muted mb-1">Audit Entries</h6>
                        <h3 class="mb-0"><?php echo number_format($stats['audit_entries_24h']); ?></h3>
                        <small class="text-muted">
                            <i class="fas fa-clock"></i> 
                            Last 24 hours
                        </small>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Quick Actions -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card border-0 shadow-sm">
            <div class="card-header bg-white border-0 py-3">
                <h5 class="card-title mb-0">
                    <i class="fas fa-bolt text-warning"></i> Quick Actions
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-lg-3 col-md-6 mb-3">
                        <a href="system-settings.php" class="btn btn-outline-primary w-100 h-100 d-flex flex-column align-items-center justify-content-center py-3">
                            <i class="fas fa-cogs fa-2x mb-2"></i>
                            <span>System Settings</span>
                        </a>
                    </div>
                    <div class="col-lg-3 col-md-6 mb-3">
                        <a href="appearance-settings.php" class="btn btn-outline-info w-100 h-100 d-flex flex-column align-items-center justify-content-center py-3">
                            <i class="fas fa-palette fa-2x mb-2"></i>
                            <span>Appearance Settings</span>
                        </a>
                    </div>
                    <div class="col-lg-3 col-md-6 mb-3">
                        <a href="email-templates.php" class="btn btn-outline-success w-100 h-100 d-flex flex-column align-items-center justify-content-center py-3">
                            <i class="fas fa-envelope fa-2x mb-2"></i>
                            <span>Email Templates</span>
                        </a>
                    </div>
                    <div class="col-lg-3 col-md-6 mb-3">
                        <a href="smtp-config.php" class="btn btn-outline-warning w-100 h-100 d-flex flex-column align-items-center justify-content-center py-3">
                            <i class="fas fa-server fa-2x mb-2"></i>
                            <span>SMTP Config</span>
                        </a>
                    </div>
                    <div class="col-lg-3 col-md-6 mb-3">
                        <a href="user-management.php" class="btn btn-outline-info w-100 h-100 d-flex flex-column align-items-center justify-content-center py-3">
                            <i class="fas fa-users fa-2x mb-2"></i>
                            <span>User Management</span>
                        </a>
                    </div>
                </div>
                <div class="row">
                    <div class="col-lg-3 col-md-6 mb-3">
                        <a href="setup-2fa.php" class="btn btn-outline-danger w-100 h-100 d-flex flex-column align-items-center justify-content-center py-3">
                            <i class="fas fa-mobile-alt fa-2x mb-2"></i>
                            <span>2FA Setup</span>
                            <?php
                            $is_2fa_enabled = false;
                            try {
                                if (function_exists('isSuperAdmin2FAEnabled')) {
                                    $is_2fa_enabled = isSuperAdmin2FAEnabled();
                                }
                            } catch (Exception $e) {
                                // 2FA system not set up yet
                            }
                            ?>
                            <?php if ($is_2fa_enabled): ?>
                                <small class="text-success mt-1"><i class="fas fa-check"></i> Enabled</small>
                            <?php else: ?>
                                <small class="text-warning mt-1"><i class="fas fa-exclamation-triangle"></i> Not Setup</small>
                            <?php endif; ?>
                        </a>
                    </div>
                    <div class="col-lg-3 col-md-6 mb-3">
                        <a href="security.php" class="btn btn-outline-secondary w-100 h-100 d-flex flex-column align-items-center justify-content-center py-3">
                            <i class="fas fa-shield-alt fa-2x mb-2"></i>
                            <span>Security Center</span>
                        </a>
                    </div>
                    <div class="col-lg-3 col-md-6 mb-3">
                        <a href="audit-logs.php" class="btn btn-outline-dark w-100 h-100 d-flex flex-column align-items-center justify-content-center py-3">
                            <i class="fas fa-clipboard-list fa-2x mb-2"></i>
                            <span>Audit Logs</span>
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Recent Activity and System Status -->
<div class="row">
    <!-- Recent Audit Logs -->
    <div class="col-lg-8 mb-4">
        <div class="card border-0 shadow-sm h-100">
            <div class="card-header bg-white border-0 py-3 d-flex justify-content-between align-items-center">
                <h5 class="card-title mb-0">
                    <i class="fas fa-history text-info"></i> Recent Activity
                </h5>
                <a href="audit-logs.php" class="btn btn-sm btn-outline-primary">
                    View All <i class="fas fa-arrow-right"></i>
                </a>
            </div>
            <div class="card-body p-0">
                <?php if (empty($recent_logs)): ?>
                    <div class="text-center py-4">
                        <i class="fas fa-clipboard-list fa-3x text-muted mb-3"></i>
                        <p class="text-muted">No recent activity found.</p>
                    </div>
                <?php else: ?>
                    <div class="activity-timeline">
                        <?php foreach ($recent_logs as $index => $log): ?>
                            <div class="activity-item <?php echo $index === count($recent_logs) - 1 ? 'last' : ''; ?>">
                                <div class="activity-icon">
                                    <?php
                                    $icon_class = 'fas fa-info-circle';
                                    $icon_bg = 'bg-info';
                                    switch ($log['action']) {
                                        case 'login_successful':
                                        case 'login':
                                            $icon_class = 'fas fa-sign-in-alt';
                                            $icon_bg = 'bg-success';
                                            break;
                                        case 'logout':
                                        case 'logged_out':
                                            $icon_class = 'fas fa-sign-out-alt';
                                            $icon_bg = 'bg-warning';
                                            break;
                                        case 'login_failed':
                                            $icon_class = 'fas fa-exclamation-triangle';
                                            $icon_bg = 'bg-danger';
                                            break;
                                        case 'auth_verification_success':
                                        case '2fa_verification_success':
                                            $icon_class = 'fas fa-shield-alt';
                                            $icon_bg = 'bg-primary';
                                            break;
                                        case 'settings_update':
                                        case 'system_settings_updated':
                                            $icon_class = 'fas fa-cogs';
                                            $icon_bg = 'bg-primary';
                                            break;
                                        case 'dashboard_access':
                                        case 'accessed_dashboard':
                                            $icon_class = 'fas fa-tachometer-alt';
                                            $icon_bg = 'bg-info';
                                            break;
                                        case 'security_code_set':
                                        case '2fa_setup':
                                            $icon_class = 'fas fa-key';
                                            $icon_bg = 'bg-success';
                                            break;
                                    }
                                    ?>
                                    <div class="icon-wrapper <?php echo $icon_bg; ?>">
                                        <i class="<?php echo $icon_class; ?> text-white"></i>
                                    </div>
                                </div>
                                <div class="activity-content">
                                    <div class="activity-header">
                                        <h6 class="activity-title"><?php echo htmlspecialchars($log['description']); ?></h6>
                                        <span class="activity-time">
                                            <?php echo date('M j, g:i A', strtotime($log['created_at'])); ?>
                                        </span>
                                    </div>
                                    <div class="activity-details">
                                        <span class="activity-user">
                                            <i class="fas fa-user"></i> <?php echo htmlspecialchars($log['username']); ?>
                                        </span>
                                        <span class="activity-ip">
                                            <i class="fas fa-map-marker-alt"></i> <?php echo htmlspecialchars($log['ip_address']); ?>
                                        </span>
                                    </div>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
    
    <!-- System Status -->
    <div class="col-lg-4 mb-4">
        <div class="card border-0 shadow-sm h-100">
            <div class="card-header bg-white border-0 py-3">
                <h5 class="card-title mb-0">
                    <i class="fas fa-heartbeat text-success"></i> System Status
                </h5>
            </div>
            <div class="card-body">
                <!-- Server Status -->
                <div class="d-flex justify-content-between align-items-center mb-3">
                    <span>Server Status</span>
                    <span class="badge bg-success">
                        <i class="fas fa-check"></i> Online
                    </span>
                </div>
                
                <!-- Database Status -->
                <div class="d-flex justify-content-between align-items-center mb-3">
                    <span>Database</span>
                    <span class="badge bg-success">
                        <i class="fas fa-check"></i> Connected
                    </span>
                </div>
                
                <!-- Email System -->
                <div class="d-flex justify-content-between align-items-center mb-3">
                    <span>Email System</span>
                    <span class="badge bg-success">
                        <i class="fas fa-check"></i> Operational
                    </span>
                </div>
                
                <!-- Session Info -->
                <hr>
                <h6 class="mb-3">Session Information</h6>
                <div class="small text-muted">
                    <div class="d-flex justify-content-between mb-2">
                        <span>Login Time:</span>
                        <span><?php echo date('g:i A', $session_info['login_time']); ?></span>
                    </div>
                    <div class="d-flex justify-content-between mb-2">
                        <span>Duration:</span>
                        <span><?php echo formatDuration($session_info['session_duration']); ?></span>
                    </div>
                    <div class="d-flex justify-content-between mb-2">
                        <span>IP Address:</span>
                        <span><?php echo htmlspecialchars($session_info['ip_address']); ?></span>
                    </div>
                </div>
                
                <!-- Quick System Actions -->
                <hr>
                <div class="d-grid gap-2">
                    <a href="security.php" class="btn btn-sm btn-outline-danger">
                        <i class="fas fa-shield-alt"></i> Security Center
                    </a>
                    <a href="audit-logs.php" class="btn btn-sm btn-outline-info">
                        <i class="fas fa-clipboard-list"></i> View Audit Logs
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- System Alerts (if any) -->
<?php
// Check for any system alerts or maintenance notices
$maintenance_mode = false; // This would come from settings
if ($maintenance_mode):
?>
<div class="row">
    <div class="col-12">
        <div class="alert alert-warning alert-dismissible fade show" role="alert">
            <i class="fas fa-exclamation-triangle"></i>
            <strong>Maintenance Mode Active:</strong> The system is currently in maintenance mode. Users cannot access the platform.
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    </div>
</div>
<?php endif; ?>

<?php
// Include footer
include 'includes/footer.php';
?>
