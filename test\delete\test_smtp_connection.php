<?php
// Enable error reporting
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

try {
    // Include only the necessary email functions
    require 'config/config.php'; // For getBankName() and other dependencies
    require_once 'config/email.php'; // Prevent redeclaration issues

    // Copy only the specific email sending function we need
    if (!function_exists('sendEmailSMTP')) {
        function sendEmailSMTP($to, $subject, $message, $isHTML = true) {
            try {
                // Load Composer's autoloader
                require_once __DIR__ . '/../vendor/autoload.php';
                
                // Create an instance; passing `true` enables exceptions
                $mail = new PHPMailer\PHPMailer\PHPMailer(true);

                // Server settings
                $mail->isSMTP();
                $mail->Host       = SMTP_HOST;
                $mail->SMTPAuth   = true;
                $mail->Username   = SMTP_USERNAME;
                $mail->Password   = SMTP_PASSWORD;
                $mail->SMTPSecure = SMTP_ENCRYPTION;
                $mail->Port       = SMTP_PORT;

                // Recipients
                $mail->setFrom(FROM_EMAIL, FROM_NAME);
                $mail->addAddress($to);

                // Content
                $mail->isHTML($isHTML);
                $mail->Subject = $subject;
                $mail->Body    = $message;

                $mail->send();
                error_log("Email sent successfully to: $to using PHPMailer");
                return true;
            } catch (Exception $e) {
                error_log("PHPMailer SMTP error: " . $e->getMessage());
                return false;
            }
        }
    }

    // Test email settings
    echo "Testing SMTP connection to: " . SMTP_HOST . ":" . SMTP_PORT . "<br>";
    echo "Using username: " . SMTP_USERNAME . "<br><br>";

    // Test email sending
    $to = '<EMAIL>';
    $subject = 'SMTP Connection Test';
    $message = 'This is a test email from the banking system to verify SMTP functionality.';

    if (sendEmailSMTP($to, $subject, $message)) {
        echo "Email sent successfully! Please check your inbox.";
    } else {
        echo "Failed to send email. Check error logs for details.";
    }
} catch (Exception $e) {
    echo "<div style='color:red;'><strong>Error:</strong> " . htmlspecialchars($e->getMessage()) . "</div>";
    echo "<pre>" . htmlspecialchars($e->getTraceAsString()) . "</pre>";
}

// Display recent errors from the error log (last 20 lines)
$error_log = file('logs/error.log');
$recent_errors = array_slice($error_log, -20);
echo "<br><br>Recent error log entries:<br>";
echo nl2br(htmlspecialchars(implode('', $recent_errors)));
?>
