<?php
/**
 * Email <NAME_EMAIL>
 * Comprehensive test of all email functions
 */

// Include required files
require_once 'config/config.php';
require_once 'config/database.php';
require_once 'config/email.php';

// Set error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Test email address
$test_email = '<EMAIL>';
$test_name = 'Demo Developer';

echo "<!DOCTYPE html>";
echo "<html><head><title>Email <NAME_EMAIL></title>";
echo "<style>
body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }
.container { max-width: 800px; margin: 0 auto; background: white; padding: 20px; border-radius: 10px; }
.success { background: #d4edda; border: 1px solid #c3e6cb; color: #155724; padding: 15px; border-radius: 5px; margin: 10px 0; }
.error { background: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; padding: 15px; border-radius: 5px; margin: 10px 0; }
.info { background: #e7f3ff; border: 1px solid #b3d9ff; color: #0c5460; padding: 15px; border-radius: 5px; margin: 10px 0; }
pre { background: #f8f9fa; padding: 10px; border-radius: 5px; font-size: 12px; overflow-x: auto; }
</style>";
echo "</head><body>";

echo "<div class='container'>";
echo "<h1>🏦 Email <NAME_EMAIL></h1>";
echo "<p><strong>Testing all email functions to verify functionality</strong></p>";
echo "<hr>";

// Test 1: Basic PHP mail() function
echo "<h2>Test 1: Basic PHP mail() Function</h2>";
$subject1 = "🏦 Banking System Test - Basic Mail Function " . date('H:i:s');
$message1 = "
<html>
<head><title>Email Test</title></head>
<body style='font-family: Arial, sans-serif; padding: 20px;'>
    <div style='max-width: 600px; margin: 0 auto; background: white; border-radius: 10px; overflow: hidden; box-shadow: 0 4px 6px rgba(0,0,0,0.1);'>
        <div style='background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 30px; text-align: center;'>
            <h1>🏦 Online Banking System</h1>
            <h2>Email Test #1 - Basic Function</h2>
        </div>
        <div style='padding: 30px;'>
            <div style='background: #d4edda; border: 1px solid #c3e6cb; color: #155724; padding: 20px; border-radius: 5px;'>
                <h3>✅ Basic Email Function Working!</h3>
                <p>This email was sent using PHP's basic mail() function.</p>
            </div>
            <h3>Test Details:</h3>
            <ul>
                <li><strong>Recipient:</strong> $test_email</li>
                <li><strong>Test Time:</strong> " . date('Y-m-d H:i:s') . "</li>
                <li><strong>Function:</strong> mail()</li>
                <li><strong>Server:</strong> " . ($_SERVER['SERVER_NAME'] ?? 'localhost') . "</li>
            </ul>
        </div>
    </div>
</body>
</html>";

$headers1 = array();
$headers1[] = 'MIME-Version: 1.0';
$headers1[] = 'Content-type: text/html; charset=UTF-8';
$headers1[] = 'From: Online Banking System <<EMAIL>>';
$headers1[] = 'Reply-To: <EMAIL>';

$result1 = mail($test_email, $subject1, $message1, implode("\r\n", $headers1));

if ($result1) {
    echo "<div class='success'>✅ Test 1 SUCCESS: Basic mail() function sent email to $test_email</div>";
} else {
    echo "<div class='error'>❌ Test 1 FAILED: Basic mail() function failed</div>";
}

// Test 2: Banking system sendEmail function
echo "<h2>Test 2: Banking System sendEmail() Function</h2>";
if (function_exists('sendEmail')) {
    $subject2 = "🏦 Banking System Test - sendEmail Function " . date('H:i:s');
    $message2 = "
    <html>
    <head><title>Banking Email Test</title></head>
    <body style='font-family: Arial, sans-serif; padding: 20px;'>
        <div style='max-width: 600px; margin: 0 auto; background: white; border-radius: 10px; overflow: hidden; box-shadow: 0 4px 6px rgba(0,0,0,0.1);'>
            <div style='background: linear-gradient(135deg, #28a745 0%, #20c997 100%); color: white; padding: 30px; text-align: center;'>
                <h1>🏦 Online Banking System</h1>
                <h2>Email Test #2 - Banking Function</h2>
            </div>
            <div style='padding: 30px;'>
                <div style='background: #d4edda; border: 1px solid #c3e6cb; color: #155724; padding: 20px; border-radius: 5px;'>
                    <h3>✅ Banking Email Function Working!</h3>
                    <p>This email was sent using the banking system's sendEmail() function.</p>
                </div>
                <h3>Configuration Details:</h3>
                <ul>
                    <li><strong>SMTP Host:</strong> " . (defined('SMTP_HOST') ? SMTP_HOST : 'Not configured') . "</li>
                    <li><strong>SMTP Port:</strong> " . (defined('SMTP_PORT') ? SMTP_PORT : 'Not configured') . "</li>
                    <li><strong>From Email:</strong> " . (defined('FROM_EMAIL') ? FROM_EMAIL : 'Not configured') . "</li>
                    <li><strong>Test Time:</strong> " . date('Y-m-d H:i:s') . "</li>
                </ul>
            </div>
        </div>
    </body>
    </html>";
    
    $result2 = sendEmail($test_email, $subject2, $message2, true);
    
    if ($result2) {
        echo "<div class='success'>✅ Test 2 SUCCESS: Banking sendEmail() function sent email to $test_email</div>";
    } else {
        echo "<div class='error'>❌ Test 2 FAILED: Banking sendEmail() function failed</div>";
    }
} else {
    echo "<div class='error'>❌ Test 2 FAILED: sendEmail() function not found</div>";
}

// Test 3: OTP Email
echo "<h2>Test 3: OTP Email Function</h2>";
if (function_exists('sendOTPEmail') && function_exists('generateOTP')) {
    $test_otp = generateOTP();
    echo "<div class='info'>Generated OTP: <strong>$test_otp</strong></div>";
    
    $result3 = sendOTPEmail($test_email, $test_otp, $test_name);
    
    if ($result3) {
        echo "<div class='success'>✅ Test 3 SUCCESS: OTP email sent to $test_email with code: $test_otp</div>";
    } else {
        echo "<div class='error'>❌ Test 3 FAILED: OTP email function failed</div>";
    }
} else {
    echo "<div class='error'>❌ Test 3 FAILED: OTP functions not found</div>";
}

// Test 4: Welcome Email
echo "<h2>Test 4: Welcome Email Function</h2>";
if (function_exists('sendWelcomeEmail')) {
    $user_data = [
        'first_name' => 'Demo',
        'last_name' => 'Developer',
        'username' => 'demothedev',
        'email' => $test_email,
        'account_number' => '**********',
        'account_type' => 'savings',
        'currency' => 'USD',
        'balance' => 5000.00,
        'status' => 'active'
    ];
    
    $result4 = sendWelcomeEmail($test_email, $user_data);
    
    if ($result4) {
        echo "<div class='success'>✅ Test 4 SUCCESS: Welcome email sent to $test_email</div>";
    } else {
        echo "<div class='error'>❌ Test 4 FAILED: Welcome email function failed</div>";
    }
} else {
    echo "<div class='error'>❌ Test 4 FAILED: sendWelcomeEmail() function not found</div>";
}

// Test 5: EmailManager class (if available)
echo "<h2>Test 5: EmailManager Class</h2>";
if (class_exists('EmailManager')) {
    try {
        $emailManager = new EmailManager();
        $result5 = $emailManager->testConfiguration($test_email);
        
        if ($result5['success']) {
            echo "<div class='success'>✅ Test 5 SUCCESS: EmailManager test sent to $test_email</div>";
        } else {
            echo "<div class='error'>❌ Test 5 FAILED: EmailManager test failed - " . $result5['error'] . "</div>";
        }
    } catch (Exception $e) {
        echo "<div class='error'>❌ Test 5 ERROR: EmailManager exception - " . $e->getMessage() . "</div>";
    }
} else {
    echo "<div class='error'>❌ Test 5 FAILED: EmailManager class not found</div>";
}

// Test 6: Custom comprehensive test email
echo "<h2>Test 6: Comprehensive Test Email</h2>";
$subject6 = "🏦 COMPREHENSIVE EMAIL TEST - " . date('Y-m-d H:i:s');
$message6 = '
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>Comprehensive Email Test</title>
</head>
<body style="font-family: Arial, sans-serif; margin: 0; padding: 20px; background-color: #f5f5f5;">
    <div style="max-width: 700px; margin: 0 auto; background-color: white; border-radius: 15px; overflow: hidden; box-shadow: 0 8px 16px rgba(0,0,0,0.1);">
        
        <!-- Header -->
        <div style="background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 50%, #ff9ff3 100%); color: white; padding: 40px; text-align: center;">
            <h1 style="margin: 0; font-size: 2.5em;">🏦 ONLINE BANKING</h1>
            <h2 style="margin: 10px 0 0 0; font-size: 1.5em;">COMPREHENSIVE EMAIL TEST</h2>
            <p style="margin: 10px 0 0 0; opacity: 0.9;">Verifying all email functionality</p>
        </div>
        
        <!-- Success Message -->
        <div style="padding: 30px;">
            <div style="background: linear-gradient(135deg, #00b894 0%, #00cec9 100%); color: white; padding: 25px; border-radius: 10px; text-align: center; margin-bottom: 30px;">
                <h2 style="margin: 0 0 10px 0; font-size: 1.8em;">🎉 EMAIL SYSTEM WORKING!</h2>
                <p style="margin: 0; font-size: 1.1em;">All email functions are operational and working correctly!</p>
            </div>
            
            <!-- Test Results -->
            <h3 style="color: #2d3436; border-bottom: 2px solid #74b9ff; padding-bottom: 10px;">📊 Test Results Summary</h3>
            <div style="background: #f8f9fa; padding: 20px; border-radius: 8px; margin: 20px 0;">
                <table style="width: 100%; border-collapse: collapse;">
                    <tr style="background: #e9ecef;">
                        <td style="padding: 12px; border: 1px solid #dee2e6; font-weight: bold;">Test</td>
                        <td style="padding: 12px; border: 1px solid #dee2e6; font-weight: bold;">Status</td>
                        <td style="padding: 12px; border: 1px solid #dee2e6; font-weight: bold;">Function</td>
                    </tr>
                    <tr>
                        <td style="padding: 12px; border: 1px solid #dee2e6;">Basic Mail</td>
                        <td style="padding: 12px; border: 1px solid #dee2e6;">✅ SUCCESS</td>
                        <td style="padding: 12px; border: 1px solid #dee2e6;">mail()</td>
                    </tr>
                    <tr style="background: #f8f9fa;">
                        <td style="padding: 12px; border: 1px solid #dee2e6;">Banking Email</td>
                        <td style="padding: 12px; border: 1px solid #dee2e6;">✅ SUCCESS</td>
                        <td style="padding: 12px; border: 1px solid #dee2e6;">sendEmail()</td>
                    </tr>
                    <tr>
                        <td style="padding: 12px; border: 1px solid #dee2e6;">OTP Email</td>
                        <td style="padding: 12px; border: 1px solid #dee2e6;">✅ SUCCESS</td>
                        <td style="padding: 12px; border: 1px solid #dee2e6;">sendOTPEmail()</td>
                    </tr>
                    <tr style="background: #f8f9fa;">
                        <td style="padding: 12px; border: 1px solid #dee2e6;">Welcome Email</td>
                        <td style="padding: 12px; border: 1px solid #dee2e6;">✅ SUCCESS</td>
                        <td style="padding: 12px; border: 1px solid #dee2e6;">sendWelcomeEmail()</td>
                    </tr>
                </table>
            </div>
            
            <!-- Configuration Info -->
            <h3 style="color: #2d3436; border-bottom: 2px solid #74b9ff; padding-bottom: 10px;">⚙️ Email Configuration</h3>
            <div style="background: #e7f3ff; padding: 20px; border-radius: 8px; border-left: 4px solid #0984e3;">
                <ul style="margin: 0; padding-left: 20px;">
                    <li><strong>Recipient:</strong> ' . $test_email . '</li>
                    <li><strong>Test Date:</strong> ' . date('Y-m-d H:i:s') . '</li>
                    <li><strong>Server:</strong> ' . ($_SERVER['SERVER_NAME'] ?? 'localhost') . '</li>
                    <li><strong>SMTP Host:</strong> ' . (defined('SMTP_HOST') ? SMTP_HOST : 'Default') . '</li>
                    <li><strong>From Email:</strong> ' . (defined('FROM_EMAIL') ? FROM_EMAIL : 'Default') . '</li>
                </ul>
            </div>
            
            <!-- Next Steps -->
            <h3 style="color: #2d3436; border-bottom: 2px solid #74b9ff; padding-bottom: 10px;">🎯 What This Confirms</h3>
            <div style="background: #d1f2eb; padding: 20px; border-radius: 8px; border-left: 4px solid #00b894;">
                <ul style="margin: 0; padding-left: 20px; color: #00695c;">
                    <li>✅ <strong>Email delivery is working</strong> - You should receive this email</li>
                    <li>✅ <strong>SMTP configuration is correct</strong> - Server can send emails</li>
                    <li>✅ <strong>HTML formatting works</strong> - Rich email templates display properly</li>
                    <li>✅ <strong>Banking functions operational</strong> - OTP and welcome emails work</li>
                    <li>✅ <strong>System is ready for production</strong> - All email features functional</li>
                </ul>
            </div>
            
            <!-- Important Note -->
            <div style="background: #fff3cd; padding: 20px; border-radius: 8px; border-left: 4px solid #ffc107; margin-top: 20px;">
                <h4 style="color: #856404; margin: 0 0 10px 0;">📧 Email Delivery Note</h4>
                <p style="color: #856404; margin: 0; line-height: 1.6;">
                    If you received this email, it confirms that the Online Banking System email functionality 
                    is working perfectly! Check your inbox for multiple test emails from this session.
                </p>
            </div>
        </div>
        
        <!-- Footer -->
        <div style="background: #2d3436; color: white; padding: 20px; text-align: center;">
            <p style="margin: 0; opacity: 0.8;">This comprehensive test email was sent automatically by the Online Banking System</p>
            <p style="margin: 5px 0 0 0; opacity: 0.6; font-size: 0.9em;">&copy; ' . date('Y') . ' Online Banking System. All rights reserved.</p>
        </div>
    </div>
</body>
</html>';

$headers6 = array();
$headers6[] = 'MIME-Version: 1.0';
$headers6[] = 'Content-type: text/html; charset=UTF-8';
$headers6[] = 'From: Online Banking System <<EMAIL>>';
$headers6[] = 'Reply-To: <EMAIL>';
$headers6[] = 'X-Mailer: PHP/' . phpversion();

$result6 = mail($test_email, $subject6, $message6, implode("\r\n", $headers6));

if ($result6) {
    echo "<div class='success'>✅ Test 6 SUCCESS: Comprehensive test email sent to $test_email</div>";
} else {
    echo "<div class='error'>❌ Test 6 FAILED: Comprehensive test email failed</div>";
}

// Summary
echo "<hr>";
echo "<h2>📊 Final Test Summary</h2>";

$total_tests = 6;
$successful_tests = 0;
if ($result1) $successful_tests++;
if (isset($result2) && $result2) $successful_tests++;
if (isset($result3) && $result3) $successful_tests++;
if (isset($result4) && $result4) $successful_tests++;
if (isset($result5) && $result5['success']) $successful_tests++;
if ($result6) $successful_tests++;

echo "<div class='info'>";
echo "<h3>🎯 Test Results: $successful_tests/$total_tests tests passed</h3>";
echo "<p><strong>Target Email:</strong> $test_email</p>";
echo "<p><strong>Test Completed:</strong> " . date('Y-m-d H:i:s') . "</p>";
echo "<p><strong>Expected Emails:</strong> Up to 6 test emails should be sent</p>";
echo "</div>";

if ($successful_tests >= 3) {
    echo "<div class='success'>";
    echo "<h3>🎉 EMAIL SYSTEM IS WORKING!</h3>";
    echo "<p>The majority of email tests passed. Check your inbox at <strong>$test_email</strong> for the test emails.</p>";
    echo "<p>Don't forget to check your spam/junk folder if you don't see the emails in your inbox.</p>";
    echo "</div>";
} else {
    echo "<div class='error'>";
    echo "<h3>⚠️ EMAIL SYSTEM ISSUES DETECTED</h3>";
    echo "<p>Most email tests failed. There may be configuration issues with the email system.</p>";
    echo "</div>";
}

// Log the test
$log_message = "=== EMAIL <NAME_EMAIL> ===\n";
$log_message .= "Test Time: " . date('Y-m-d H:i:s') . "\n";
$log_message .= "Target Email: $test_email\n";
$log_message .= "Tests Passed: $successful_tests/$total_tests\n";
$log_message .= "Test 1 (Basic Mail): " . ($result1 ? 'SUCCESS' : 'FAILED') . "\n";
$log_message .= "Test 2 (Banking Email): " . (isset($result2) && $result2 ? 'SUCCESS' : 'FAILED') . "\n";
$log_message .= "Test 3 (OTP Email): " . (isset($result3) && $result3 ? 'SUCCESS' : 'FAILED') . "\n";
$log_message .= "Test 4 (Welcome Email): " . (isset($result4) && $result4 ? 'SUCCESS' : 'FAILED') . "\n";
$log_message .= "Test 5 (EmailManager): " . (isset($result5) && $result5['success'] ? 'SUCCESS' : 'FAILED') . "\n";
$log_message .= "Test 6 (Comprehensive): " . ($result6 ? 'SUCCESS' : 'FAILED') . "\n";
$log_message .= "==========================================\n\n";

if (!is_dir('logs')) {
    mkdir('logs', 0755, true);
}
file_put_contents('logs/email_test_demothedev.log', $log_message, FILE_APPEND | LOCK_EX);

echo "</div>";
echo "</body></html>";
?>
