/* Admin Panel Specific Styles */

/* Admin Dashboard Cards */
.admin-stat-card {
    transition: transform 0.2s ease, box-shadow 0.2s ease;
    border: none;
    border-radius: 12px;
}

.admin-stat-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.1);
}

.admin-stat-icon {
    width: 48px;
    height: 48px;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 1rem;
}

.admin-stat-icon.bg-blue { background: linear-gradient(135deg, #3b82f6, #1d4ed8); }
.admin-stat-icon.bg-green { background: linear-gradient(135deg, #10b981, #059669); }
.admin-stat-icon.bg-yellow { background: linear-gradient(135deg, #f59e0b, #d97706); }
.admin-stat-icon.bg-red { background: linear-gradient(135deg, #ef4444, #dc2626); }
.admin-stat-icon.bg-purple { background: linear-gradient(135deg, #8b5cf6, #7c3aed); }

/* Admin Navigation */
.admin-nav {
    background: linear-gradient(135deg, #1e293b, #334155);
    border-radius: 12px;
    padding: 1rem;
    margin-bottom: 2rem;
}

.admin-nav .nav-link {
    color: rgba(255, 255, 255, 0.8);
    border-radius: 8px;
    padding: 0.75rem 1rem;
    margin: 0 0.25rem;
    transition: all 0.3s ease;
}

.admin-nav .nav-link:hover {
    background: rgba(255, 255, 255, 0.1);
    color: white;
    transform: translateY(-1px);
}

.admin-nav .nav-link.active {
    background: rgba(255, 255, 255, 0.2);
    color: white;
    box-shadow: 0 4px 12px rgba(0,0,0,0.2);
}

/* User Management Table */
.user-table {
    background: white;
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 4px 6px rgba(0,0,0,0.05);
}

.user-table .table {
    margin-bottom: 0;
}

.user-table .table th {
    background: #f8fafc;
    border-bottom: 2px solid #e2e8f0;
    font-weight: 600;
    color: #475569;
    padding: 1rem;
}

.user-table .table td {
    padding: 1rem;
    vertical-align: middle;
    border-bottom: 1px solid #f1f5f9;
}

.user-table .table tbody tr:hover {
    background: #f8fafc;
}

/* User Avatar Styles */
.user-avatar {
    width: 40px;
    height: 40px;
    border-radius: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 600;
    font-size: 14px;
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
    margin-right: 0.75rem;
}

/* Status Badges */
.status-badge {
    padding: 0.375rem 0.75rem;
    border-radius: 6px;
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.025em;
}

.status-badge.active {
    background: #dcfce7;
    color: #166534;
}

.status-badge.suspended {
    background: #fee2e2;
    color: #991b1b;
}

.status-badge.pending {
    background: #fef3c7;
    color: #92400e;
}

.status-badge.verified {
    background: #dcfce7;
    color: #166534;
}

.status-badge.rejected {
    background: #fee2e2;
    color: #991b1b;
}

/* Admin Forms */
.admin-form {
    background: white;
    border-radius: 12px;
    padding: 2rem;
    box-shadow: 0 4px 6px rgba(0,0,0,0.05);
}

.admin-form .form-label {
    font-weight: 600;
    color: #374151;
    margin-bottom: 0.5rem;
}

.admin-form .form-control,
.admin-form .form-select {
    border: 2px solid #e5e7eb;
    border-radius: 8px;
    padding: 0.75rem;
    transition: all 0.3s ease;
}

.admin-form .form-control:focus,
.admin-form .form-select:focus {
    border-color: #3b82f6;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

/* Admin Buttons */
.btn-admin-primary {
    background: linear-gradient(135deg, #3b82f6, #1d4ed8);
    border: none;
    border-radius: 8px;
    padding: 0.75rem 1.5rem;
    font-weight: 600;
    color: white;
    transition: all 0.3s ease;
}

.btn-admin-primary:hover {
    transform: translateY(-1px);
    box-shadow: 0 8px 25px rgba(59, 130, 246, 0.3);
    color: white;
}

.btn-admin-danger {
    background: linear-gradient(135deg, #ef4444, #dc2626);
    border: none;
    border-radius: 8px;
    padding: 0.75rem 1.5rem;
    font-weight: 600;
    color: white;
    transition: all 0.3s ease;
}

.btn-admin-danger:hover {
    transform: translateY(-1px);
    box-shadow: 0 8px 25px rgba(239, 68, 68, 0.3);
    color: white;
}

/* Admin Search Bar */
.admin-search {
    background: white;
    border-radius: 12px;
    padding: 1.5rem;
    margin-bottom: 2rem;
    box-shadow: 0 4px 6px rgba(0,0,0,0.05);
}

.admin-search .form-control {
    border: 2px solid #e5e7eb;
    border-radius: 8px;
    padding: 0.75rem 1rem;
}

/* Admin Sidebar Enhancements */
.admin-sidebar {
    background: linear-gradient(180deg, #1e293b 0%, #334155 100%);
    min-height: 100vh;
    padding: 2rem 0;
}

.admin-sidebar .nav-link {
    color: rgba(255, 255, 255, 0.8);
    padding: 0.75rem 1.5rem;
    margin: 0.25rem 1rem;
    border-radius: 8px;
    transition: all 0.3s ease;
}

.admin-sidebar .nav-link:hover {
    background: rgba(255, 255, 255, 0.1);
    color: white;
    transform: translateX(4px);
}

.admin-sidebar .nav-link.active {
    background: rgba(255, 255, 255, 0.2);
    color: white;
    box-shadow: 0 4px 12px rgba(0,0,0,0.2);
}

/* Admin Header */
.admin-header {
    background: white;
    border-bottom: 1px solid #e5e7eb;
    padding: 1rem 0;
    margin-bottom: 2rem;
}

.admin-breadcrumb {
    background: transparent;
    padding: 0;
    margin: 0;
}

.admin-breadcrumb .breadcrumb-item {
    color: #6b7280;
}

.admin-breadcrumb .breadcrumb-item.active {
    color: #374151;
    font-weight: 600;
}

/* Admin Quick Actions */
.admin-quick-actions {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1rem;
    margin-bottom: 2rem;
}

.quick-action-item {
    background: white;
    border-radius: 12px;
    padding: 1.5rem;
    text-decoration: none;
    color: inherit;
    transition: all 0.3s ease;
    border: 2px solid transparent;
    box-shadow: 0 4px 6px rgba(0,0,0,0.05);
}

.quick-action-item:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.1);
    border-color: #3b82f6;
    color: inherit;
    text-decoration: none;
}

.quick-action-icon {
    width: 48px;
    height: 48px;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 1rem;
    color: white;
}

/* Responsive Design */
@media (max-width: 768px) {
    .admin-nav {
        padding: 0.5rem;
    }
    
    .admin-nav .nav-link {
        padding: 0.5rem;
        margin: 0.125rem;
        font-size: 0.875rem;
    }
    
    .admin-form {
        padding: 1rem;
    }
    
    .user-table .table th,
    .user-table .table td {
        padding: 0.5rem;
        font-size: 0.875rem;
    }
    
    .admin-quick-actions {
        grid-template-columns: 1fr;
    }
}

/* Dark Mode Support */
@media (prefers-color-scheme: dark) {
    .admin-form {
        background: #1f2937;
        color: white;
    }
    
    .admin-form .form-label {
        color: #e5e7eb;
    }
    
    .admin-form .form-control,
    .admin-form .form-select {
        background: #374151;
        border-color: #4b5563;
        color: white;
    }
    
    .user-table {
        background: #1f2937;
    }
    
    .user-table .table th {
        background: #374151;
        color: #e5e7eb;
    }
    
    .user-table .table td {
        color: #e5e7eb;
        border-bottom-color: #374151;
    }
    
    .user-table .table tbody tr:hover {
        background: #374151;
    }
}
