/**
 * Transaction History Page CSS
 * Dedicated styles for transaction statement page
 * Extends dashboard patterns with transaction-specific styling
 */

/* Import dashboard base styles */
@import url('../dashboard/dashboard.css');

/* Transaction Hero Section - Mini version of dashboard hero */
.transaction-hero {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
    border-radius: 16px;
    padding: 1.5rem 2rem;
    color: white;
    margin-bottom: 2rem;
    box-shadow: 0 8px 25px rgba(0,0,0,0.15);
    position: relative;
    overflow: hidden;
}

.transaction-hero::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, rgba(255,255,255,0.1) 0%, transparent 50%);
    pointer-events: none;
}

.transaction-hero .hero-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    position: relative;
    z-index: 1;
}

.transaction-hero .hero-main {
    flex: 1;
}

.transaction-hero .hero-title {
    font-size: 1.75rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
    text-shadow: 0 2px 4px rgba(0,0,0,0.3);
}

.transaction-hero .hero-subtitle {
    font-size: 1rem;
    opacity: 0.9;
    margin-bottom: 0.5rem;
}

.transaction-hero .hero-stats {
    font-size: 0.9rem;
    opacity: 0.8;
    font-weight: 500;
}

.transaction-hero .hero-actions {
    display: flex;
    gap: 0.75rem;
}

/* Filters Section */
.filters-section {
    margin-bottom: 2rem;
}

.filters-card {
    background: var(--background-white);
    border-radius: 12px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.08);
    border: 1px solid var(--border-color);
    overflow: hidden;
}

.filters-header {
    background: var(--background-light);
    padding: 1rem 1.5rem;
    border-bottom: 1px solid var(--border-color);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.filters-header h5 {
    margin: 0;
    color: var(--text-primary);
    font-size: 1rem;
    font-weight: 600;
}

.filters-form {
    padding: 1.5rem;
}

.filters-actions {
    display: flex;
    gap: 0.75rem;
    justify-content: flex-start;
}

/* Enhanced Transaction Table Styles */
.transactions-section {
    background: var(--background-white);
    border-radius: 12px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.08);
    border: 1px solid var(--border-color);
    margin-bottom: 2rem;
    overflow: hidden;
}

.transactions-header {
    background: var(--background-light);
    padding: 1.5rem;
    border-bottom: 1px solid var(--border-color);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.transactions-header h3 {
    margin: 0;
    color: var(--text-primary);
    font-size: 1.25rem;
    font-weight: 600;
}

.transactions-summary {
    font-size: 0.9rem;
    color: var(--text-secondary);
    font-weight: 500;
}

.transactions-table {
    width: 100%;
    min-width: 1000px;
    border-collapse: collapse;
    font-size: 0.9rem;
    table-layout: fixed;
}

.transactions-table th {
    background: var(--background-light);
    padding: 0.75rem 0.5rem;
    text-align: left;
    font-weight: 600;
    color: var(--text-primary);
    border-bottom: 2px solid var(--border-color);
    font-size: 0.85rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.transactions-table td {
    padding: 0.75rem 0.5rem;
    border-bottom: 1px solid var(--border-light);
    vertical-align: middle;
}

.transactions-table tbody tr:nth-child(even) {
    background: rgba(0,0,0,0.02);
}

.transactions-table tbody tr:hover {
    background: rgba(79, 70, 229, 0.05);
    transition: background-color 0.2s ease;
}

/* Transaction Table Cell Styles */
.transaction-number {
    font-weight: 600;
    color: var(--text-secondary);
    font-family: 'Courier New', monospace;
}

.sender-name {
    font-weight: 500;
    color: var(--text-primary);
}

.type-badge {
    display: inline-flex;
    align-items: center;
    padding: 0.25rem 0.75rem;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.type-badge.type-credit {
    background: rgba(34, 197, 94, 0.1);
    color: #16a34a;
    border: 1px solid rgba(34, 197, 94, 0.2);
}

.type-badge.type-debit {
    background: rgba(239, 68, 68, 0.1);
    color: #dc2626;
    border: 1px solid rgba(239, 68, 68, 0.2);
}

.description {
    max-width: 300px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    cursor: pointer;
    position: relative;
}

.description:hover {
    background: rgba(79, 70, 229, 0.05);
    border-radius: 4px;
}

.category-tag {
    display: inline-block;
    background: #f1f5f9;
    color: #64748b;
    padding: 0.125rem 0.375rem;
    border-radius: 12px;
    font-size: 0.7rem;
    font-weight: 500;
    margin-left: 0.5rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.amount-value {
    font-family: 'Courier New', 'Monaco', 'Menlo', monospace;
    font-weight: 700;
    font-size: 1rem;
    letter-spacing: 0.5px;
}

.amount-value.amount-credit {
    color: #16a34a;
}

.amount-value.amount-debit {
    color: #dc2626;
}

.date-time {
    font-weight: 500;
    color: var(--text-primary);
}

.time-display {
    display: block;
    color: var(--text-secondary);
    font-size: 0.8rem;
    margin-top: 0.25rem;
}

.ref-code {
    background: var(--background-light);
    padding: 0.25rem 0.5rem;
    border-radius: 4px;
    font-size: 0.8rem;
    color: var(--text-secondary);
    border: 1px solid var(--border-color);
}

.status-badge {
    display: inline-block;
    padding: 0.25rem 0.75rem;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.status-badge.status-completed {
    background: rgba(34, 197, 94, 0.1);
    color: #16a34a;
    border: 1px solid rgba(34, 197, 94, 0.2);
}

.status-badge.status-pending {
    background: rgba(245, 158, 11, 0.1);
    color: #d97706;
    border: 1px solid rgba(245, 158, 11, 0.2);
}

.status-badge.status-failed {
    background: rgba(239, 68, 68, 0.1);
    color: #dc2626;
    border: 1px solid rgba(239, 68, 68, 0.2);
}

.status-badge.status-cancelled {
    background: rgba(107, 114, 128, 0.1);
    color: #6b7280;
    border: 1px solid rgba(107, 114, 128, 0.2);
}

/* Pagination Styles */
.pagination-wrapper {
    padding: 1.5rem;
    background: var(--background-light);
    border-top: 1px solid var(--border-color);
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 1rem;
}

.pagination {
    margin: 0;
    display: flex;
    list-style: none;
    padding: 0;
    gap: 0.25rem;
}

.page-item .page-link {
    padding: 0.5rem 0.75rem;
    border: 1px solid var(--border-color);
    color: var(--text-primary);
    text-decoration: none;
    border-radius: 6px;
    transition: all 0.2s ease;
}

.page-item .page-link:hover {
    background: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

.page-item.active .page-link {
    background: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

.pagination-info {
    font-size: 0.9rem;
    color: var(--text-secondary);
    font-weight: 500;
}

/* Empty State */
.empty-state {
    text-align: center;
    padding: 3rem 2rem;
    color: var(--text-secondary);
}

.empty-state i {
    opacity: 0.5;
}

.empty-state h5 {
    margin-top: 1rem;
    margin-bottom: 0.5rem;
}

.empty-state p {
    margin-bottom: 1.5rem;
    max-width: 400px;
    margin-left: auto;
    margin-right: auto;
}

/* COMPACT TABLE DESIGN - FITS PERFECTLY */
.table-container {
    width: 100%;
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    overflow: hidden;
}

/* TABLE RESPONSIVE WRAPPER - Like Dashboard */
.table-responsive {
    width: 100%;
    overflow-x: auto;
    -webkit-overflow-scrolling: touch;
    padding: 0 0.5rem; /* Balanced horizontal padding */
}

/* SIMPLE RESPONSIVE TABLE */
.transactions-table {
    width: 100%;
    border-collapse: collapse;
    font-size: 0.85rem;
    margin: 0;
}

.transactions-table th,
.transactions-table td {
    padding: 0.75rem 0.75rem; /* Increased horizontal padding */
    text-align: left;
    vertical-align: middle;
    border-bottom: 1px solid #e5e7eb;
}

.transactions-table th {
    background: #f8fafc;
    font-weight: 600;
    color: #374151;
    font-size: 0.8rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

/* ULTRA COMPACT COLUMNS - ONLY ESSENTIALS */
.transactions-table th:nth-child(1), /* # */
.transactions-table td:nth-child(1) {
    width: 50px;
    text-align: center;
    font-weight: 600;
    color: #6b7280;
}

.transactions-table th:nth-child(2), /* Sender */
.transactions-table td:nth-child(2) {
    width: 180px;
    font-weight: 500;
}

.transactions-table th:nth-child(3), /* Type */
.transactions-table td:nth-child(3) {
    width: 100px;
    text-align: center;
}

.transactions-table th:nth-child(4), /* Description */
.transactions-table td:nth-child(4) {
    width: auto; /* Takes remaining space */
    cursor: pointer;
    position: relative;
    min-width: 200px;
}

.transactions-table th:nth-child(5), /* Amount */
.transactions-table td:nth-child(5) {
    width: 120px;
    text-align: right;
    font-weight: 600;
    font-family: 'Courier New', monospace;
}

.transactions-table th:nth-child(6), /* Date */
.transactions-table td:nth-child(6) {
    width: 90px;
    text-align: center;
    font-size: 0.8rem;
}

.transactions-table th:nth-child(7), /* Status */
.transactions-table td:nth-child(7) {
    width: 100px; /* Increased from 80px */
    text-align: center;
    padding-right: 1rem; /* Extra right padding for last column */
}

/* CLICKABLE DESCRIPTION CELLS */
.transactions-table td:nth-child(4) {
    transition: all 0.2s ease;
    border-radius: 4px;
    position: relative;
}

.transactions-table td:nth-child(4):hover {
    background-color: #f0f9ff;
    color: #0369a1;
    transform: translateX(2px);
    box-shadow: 0 2px 4px rgba(59, 130, 246, 0.1);
}

.transactions-table td:nth-child(4):hover::after {
    content: "👁 View Details";
    position: absolute;
    right: 8px;
    top: 50%;
    transform: translateY(-50%);
    font-size: 0.7rem;
    color: #0369a1;
    font-weight: 500;
    opacity: 0.8;
}

/* TRANSACTION TYPE BADGES */
.type-badge {
    display: inline-block;
    padding: 0.2rem 0.5rem;
    border-radius: 12px;
    font-size: 0.7rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.type-credit {
    background: linear-gradient(135deg, #d1fae5, #a7f3d0);
    color: #065f46;
    border: 1px solid #10b981;
}

.type-debit {
    background: linear-gradient(135deg, #fee2e2, #fecaca);
    color: #991b1b;
    border: 1px solid #ef4444;
}

/* AMOUNT STYLING */
.amount-positive {
    color: #059669;
    font-weight: 700;
}

.amount-negative {
    color: #dc2626;
    font-weight: 700;
}

/* STATUS BADGES */
.status-badge {
    display: inline-block;
    padding: 0.2rem 0.4rem;
    border-radius: 8px;
    font-size: 0.65rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.status-complete {
    background: #10b981;
    color: white;
}

.status-pending {
    background: #f59e0b;
    color: white;
}

.status-failed {
    background: #ef4444;
    color: white;
}

/* MOBILE RESPONSIVE */
@media (max-width: 768px) {
    .table-container {
        overflow-x: auto;
        -webkit-overflow-scrolling: touch;
    }

    .transactions-table {
        min-width: 600px;
    }

    .transactions-table th,
    .transactions-table td {
        padding: 0.5rem 0.3rem;
        font-size: 0.8rem;
    }
}

/* ADDITIONAL STYLES FOR CLEAN LAYOUT */
.main-content-wrapper {
    min-height: 100vh;
    display: flex;
    flex-direction: column;
}

.content-container {
    flex: 1;
    padding-bottom: 2rem;
}

.user-dashboard-footer {
    margin-top: auto;
    flex-shrink: 0;
}

/* Transaction type badge styles */
.type-badge.type-transfer_in {
    background: linear-gradient(135deg, #10b981 0%, #059669 100%);
    color: white;
}

.type-badge.type-transfer_out {
    background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
    color: white;
}

.type-badge.type-deposit {
    background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);
    color: white;
}

.type-badge.type-withdrawal {
    background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
    color: white;
}

/* Amount styling for different transaction types */
.amount-value.amount-transfer_in,
.amount-value.amount-deposit {
    color: #10b981;
    font-weight: 600;
}

.amount-value.amount-transfer_out,
.amount-value.amount-withdrawal {
    color: #ef4444;
    font-weight: 600;
}

/* Compact date styling like reference */
.date-code {
    background: #f1f5f9;
    color: #475569;
    padding: 0.25rem 0.5rem;
    border-radius: 4px;
    font-size: 0.75rem;
    font-family: 'Courier New', monospace;
    border: 1px solid #e2e8f0;
}

/* BALANCE CARDS STYLING */
.balance-overview-new {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
    margin-bottom: 1.5rem;
}

.balance-card-new {
    background: var(--background-white);
    border-radius: 8px;
    padding: 1rem;
    box-shadow: 0 2px 8px rgba(0,0,0,0.06);
    border: 1px solid var(--border-color);
    display: flex;
    align-items: center;
    gap: 0.75rem;
    transition: all 0.2s ease;
    min-height: 70px;
}

.balance-card-new:hover {
    box-shadow: 0 4px 12px rgba(0,0,0,0.1);
    transform: translateY(-1px);
}

.balance-icon {
    width: 36px;
    height: 36px;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
    color: white;
    font-size: 16px;
    flex-shrink: 0;
}

.balance-info {
    flex: 1;
    min-width: 0;
    overflow: hidden;
}

.balance-label {
    font-size: 0.75rem;
    font-weight: 500;
    color: var(--text-secondary);
    margin-bottom: 0.25rem;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.balance-amount {
    font-size: 1.1rem;
    font-weight: 700;
    color: var(--text-primary);
    margin-bottom: 0.25rem;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.balance-subtitle {
    font-size: 0.7rem;
    color: var(--text-muted);
    font-weight: 400;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

/* MODAL STYLING */
.custom-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 9999;
    display: flex;
    align-items: center;
    justify-content: center;
    background: rgba(0, 0, 0, 0.5);
    backdrop-filter: blur(4px);
    transition: opacity 0.2s ease;
}

.modal-backdrop {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    cursor: pointer;
}

.modal-container {
    position: relative;
    background: white;
    border-radius: 12px;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
    max-width: 700px;
    width: 90%;
    max-height: 80vh;
    overflow-y: auto;
    animation: modalSlideIn 0.3s ease-out;
}

@keyframes modalSlideIn {
    from {
        opacity: 0;
        transform: translateY(-50px) scale(0.9);
    }
    to {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

.modal-header {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
    color: white;
    padding: 1.5rem;
    border-radius: 12px 12px 0 0;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.modal-title {
    margin: 0;
    font-size: 1.25rem;
    font-weight: 600;
}

.modal-close {
    background: none;
    border: none;
    color: white;
    font-size: 1.5rem;
    cursor: pointer;
    padding: 0;
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    transition: background 0.2s ease;
}

.modal-close:hover {
    background: rgba(255, 255, 255, 0.2);
}

.modal-body {
    padding: 1.5rem;
}

.modal-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1.5rem;
    margin-bottom: 1rem;
}

.detail-group {
    margin-bottom: 1rem;
}

.detail-group.full-width {
    grid-column: 1 / -1;
    margin-top: 1rem;
    padding-top: 1rem;
    border-top: 1px solid #e5e7eb;
}

.detail-label {
    font-size: 0.75rem;
    font-weight: 600;
    color: #6b7280;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    margin-bottom: 0.5rem;
    display: block;
}

.detail-value {
    font-size: 0.9rem;
    color: #1f2937;
    font-weight: 500;
    padding: 0.75rem;
    background: #f9fafb;
    border-radius: 8px;
    border: 1px solid #e5e7eb;
    word-wrap: break-word;
}

.modal-footer {
    padding: 1rem 1.5rem;
    border-top: 1px solid #e5e7eb;
    display: flex;
    justify-content: flex-end;
}

.btn-close-modal {
    background: #6b7280;
    color: white;
    border: none;
    padding: 0.5rem 1.5rem;
    border-radius: 6px;
    cursor: pointer;
    font-weight: 500;
    transition: background 0.2s ease;
    font-size: 0.9rem;
}

.btn-close-modal:hover {
    background: #4b5563;
}

/* Ensure modal is always on top */
.custom-modal {
    z-index: 99999 !important;
}

/* Responsive Design */
@media (max-width: 768px) {
    .transaction-hero .hero-content {
        flex-direction: column;
        gap: 1rem;
        text-align: center;
    }

    .filters-form .row {
        gap: 1rem;
    }

    .filters-form .col-md-3,
    .filters-form .col-md-2 {
        flex: 0 0 100%;
        max-width: 100%;
    }

    /* Mobile table adjustments */
    .table-container {
        overflow-x: auto;
        -webkit-overflow-scrolling: touch;
    }

    .transactions-table {
        min-width: 700px; /* Minimum width for mobile scroll */
        font-size: 0.8rem;
    }

    .transactions-table th,
    .transactions-table td {
        padding: 0.5rem 0.375rem;
    }

    /* Adjust column widths for mobile */
    .transactions-table th:nth-child(1),
    .transactions-table td:nth-child(1) {
        width: 35px;
        min-width: 35px;
    }

    .transactions-table th:nth-child(2),
    .transactions-table td:nth-child(2) {
        width: 100px;
        min-width: 100px;
    }

    .transactions-table th:nth-child(3),
    .transactions-table td:nth-child(3) {
        width: 80px;
        min-width: 80px;
    }

    .transactions-table th:nth-child(4),
    .transactions-table td:nth-child(4) {
        width: 140px;
        min-width: 140px;
        max-width: 140px;
    }

    .transactions-table th:nth-child(5),
    .transactions-table td:nth-child(5) {
        width: 90px;
        min-width: 90px;
    }

    .transactions-table th:nth-child(6),
    .transactions-table td:nth-child(6) {
        width: 80px;
        min-width: 80px;
    }

    .transactions-table th:nth-child(7),
    .transactions-table td:nth-child(7) {
        width: 100px;
        min-width: 100px;
    }

    .transactions-table th:nth-child(8),
    .transactions-table td:nth-child(8) {
        width: 70px;
        min-width: 70px;
    }

    .pagination-wrapper {
        flex-direction: column;
        text-align: center;
    }

    .balance-overview-new {
        grid-template-columns: 1fr 1fr;
        gap: 0.5rem;
    }

    .balance-card-new {
        padding: 0.625rem;
        gap: 0.5rem;
        min-height: 55px;
    }

    .balance-icon {
        width: 28px;
        height: 28px;
        font-size: 12px;
    }

    .modal-grid {
        grid-template-columns: 1fr;
        gap: 1rem;
    }

    .modal-container {
        width: 95%;
        margin: 1rem;
    }
}

/* ===== BANK RECEIPT MODAL STYLES ===== */
.receipt-modal {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 9999;
    animation: fadeIn 0.3s ease-out;
}

.modal-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.7);
    backdrop-filter: blur(3px);
}

.receipt-container {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background: white;
    border: 1px solid #e0e0e0;
    box-shadow: 0 10px 40px rgba(0, 0, 0, 0.2);
    max-width: 900px;
    width: 95%;
    max-height: 85vh;
    overflow: hidden;
    scroll-behavior: smooth;
    animation: slideIn 0.3s ease-out;
    font-family: 'Courier New', 'Monaco', monospace;
}

/* Receipt Header */
.receipt-header {
    background: white;
    border-bottom: 1px solid #ddd;
    padding: 1.5rem 2rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
    position: relative;
}

.bank-logo {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    font-size: 1.5rem;
    font-weight: bold;
    color: #2c3e50;
}

.bank-logo i {
    font-size: 2rem;
    color: #3498db;
}

.bank-name {
    font-family: 'Arial', sans-serif;
    letter-spacing: 1px;
}

.receipt-title {
    position: absolute;
    left: 50%;
    transform: translateX(-50%);
    font-size: 1.25rem;
    font-weight: bold;
    letter-spacing: 2px;
    color: #2c3e50;
    font-family: 'Arial', sans-serif;
}

.receipt-close {
    background: none;
    border: 1px solid #ddd;
    color: #666;
    font-size: 1.2rem;
    cursor: pointer;
    padding: 0.5rem;
    border-radius: 4px;
    transition: all 0.2s;
    width: 35px;
    height: 35px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.receipt-close:hover {
    background: #f5f5f5;
    border-color: #999;
}

/* Receipt Body */
.receipt-body {
    padding: 2rem;
    max-height: 60vh;
    overflow-y: auto;
    background: white;
    scroll-behavior: smooth;
    /* Hide scrollbar for webkit browsers */
    scrollbar-width: none; /* Firefox */
    -ms-overflow-style: none; /* IE and Edge */
}

.receipt-body::-webkit-scrollbar {
    display: none; /* Chrome, Safari, Opera */
}

.receipt-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 2rem;
    font-size: 0.9rem;
    line-height: 1.6;
}

/* Receipt Sections */
.receipt-section {
    margin-bottom: 1.5rem;
}

.receipt-section-title {
    font-size: 1rem;
    font-weight: bold;
    color: #2c3e50;
    margin-bottom: 0.75rem;
    padding-bottom: 0.5rem;
    border-bottom: 1px solid #ddd;
    font-family: 'Arial', sans-serif;
    text-transform: uppercase;
    letter-spacing: 1px;
}

.receipt-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.4rem 0;
    border-bottom: 1px dotted #ddd;
}

.receipt-row:last-child {
    border-bottom: none;
}

.receipt-label {
    font-weight: 500;
    color: #555;
    font-size: 0.85rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    flex: 1;
}

.receipt-value {
    font-weight: 600;
    color: #2c3e50;
    text-align: right;
    font-family: 'Courier New', monospace;
    flex: 1;
    word-break: break-word;
}

/* Special styling for amounts */
.receipt-amount {
    font-size: 1.1rem;
    font-weight: bold;
    font-family: 'Courier New', monospace;
}

.receipt-amount.positive {
    color: #27ae60;
}

.receipt-amount.negative {
    color: #e74c3c;
}

/* Transaction status styling */
.receipt-status {
    display: inline-block;
    padding: 0.25rem 0.75rem;
    border-radius: 3px;
    font-size: 0.75rem;
    font-weight: bold;
    text-transform: uppercase;
    letter-spacing: 1px;
    font-family: 'Arial', sans-serif;
}

.receipt-status.completed {
    background: #d4edda;
    color: #155724;
    border: 1px solid #c3e6cb;
}

.receipt-status.pending {
    background: #fff3cd;
    color: #856404;
    border: 1px solid #ffeaa7;
}

.receipt-status.failed {
    background: #f8d7da;
    color: #721c24;
    border: 1px solid #f5c6cb;
}

/* Full width sections */
.receipt-section.full-width {
    grid-column: 1 / -1;
    margin-top: 1rem;
    padding-top: 1rem;
    border-top: 1px solid #ddd;
}

/* Receipt Footer */
.receipt-footer {
    background: #f8f9fa;
    border-top: 1px solid #ddd;
    padding: 1.5rem 2rem;
}

.receipt-actions {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
}

.btn-print,
.btn-close-receipt {
    padding: 0.75rem 1.5rem;
    border: 1px solid #ddd;
    background: white;
    color: #555;
    cursor: pointer;
    font-weight: 500;
    transition: all 0.2s;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    font-family: 'Arial', sans-serif;
    border-radius: 4px;
}

.btn-print:hover {
    background: #3498db;
    color: white;
    border-color: #3498db;
}

.btn-close-receipt:hover {
    background: #95a5a6;
    color: white;
    border-color: #95a5a6;
}

.receipt-disclaimer {
    text-align: center;
    color: #666;
    font-size: 0.8rem;
    font-family: 'Arial', sans-serif;
    font-style: italic;
}

/* Modal Animations */
@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

@keyframes slideIn {
    from {
        opacity: 0;
        transform: translate(-50%, -60%) scale(0.95);
    }
    to {
        opacity: 1;
        transform: translate(-50%, -50%) scale(1);
    }
}

/* Receipt Modal Responsive */
@media (max-width: 768px) {
    .receipt-container {
        width: 98%;
        max-height: 90vh;
        margin: 1rem;
    }

    .receipt-header {
        padding: 1rem;
        flex-direction: column;
        gap: 0.75rem;
        text-align: center;
    }

    .receipt-title {
        position: static;
        transform: none;
        font-size: 1rem;
    }

    .receipt-close {
        position: absolute;
        top: 1rem;
        right: 1rem;
    }

    .receipt-body {
        padding: 1rem;
    }

    .receipt-content {
        grid-template-columns: 1fr;
        gap: 1rem;
    }

    .receipt-row {
        flex-direction: column;
        align-items: flex-start;
        gap: 0.25rem;
    }

    .receipt-value {
        text-align: left;
        font-size: 0.9rem;
    }

    .receipt-actions {
        flex-direction: column;
        gap: 0.75rem;
    }

    .btn-print,
    .btn-close-receipt {
        width: 100%;
        justify-content: center;
    }
}
