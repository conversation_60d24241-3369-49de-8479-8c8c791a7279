<?php
session_start();

// Enable error reporting for debugging
error_reporting(E_ALL);
ini_set('display_errors', 1);

require_once 'config/config.php';
require_once 'config/database.php';

$page_title = 'User Registration';
$error = '';
$success = '';
$username = '';
$email = '';
$first_name = '';
$last_name = '';
$phone = '';

// Process registration form
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // Sanitize and validate input (same as admin/add-user.php)
    $username = sanitizeInput($_POST['username'] ?? '');
    $email = sanitizeInput($_POST['email'] ?? '');
    $first_name = sanitizeInput($_POST['first_name'] ?? '');
    $last_name = sanitizeInput($_POST['last_name'] ?? '');
    $phone = sanitizeInput($_POST['phone'] ?? '');
    $address = sanitizeInput($_POST['address'] ?? '');
    $date_of_birth = sanitizeInput($_POST['date_of_birth'] ?? '');
    $occupation = sanitizeInput($_POST['occupation'] ?? '');
    $marital_status = sanitizeInput($_POST['marital_status'] ?? 'single');
    $gender = sanitizeInput($_POST['gender'] ?? 'male');
    $currency = sanitizeInput($_POST['currency'] ?? 'USD');
    $account_type = sanitizeInput($_POST['account_type'] ?? 'savings');
    $password = $_POST['password'] ?? '';
    $confirm_password = $_POST['confirm_password'] ?? '';

    // Validation (same as admin/add-user.php)
    if (empty($username)) {
        $error = 'Username is required.';
    } elseif (strlen($username) < 3) {
        $error = 'Username must be at least 3 characters long.';
    } elseif (empty($email)) {
        $error = 'Email is required.';
    } elseif (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
        $error = 'Please enter a valid email address.';
    } elseif (empty($first_name)) {
        $error = 'First name is required.';
    } elseif (empty($last_name)) {
        $error = 'Last name is required.';
    } elseif (empty($password)) {
        $error = 'Password is required.';
    } elseif (strlen($password) < 6) {
        $error = 'Password must be at least 6 characters long.';
    } elseif ($password !== $confirm_password) {
        $error = 'Passwords do not match.';
    } elseif (!empty($date_of_birth)) {
        $dob = DateTime::createFromFormat('Y-m-d', $date_of_birth);
        if (!$dob || $dob->format('Y-m-d') !== $date_of_birth) {
            $error = 'Please enter a valid date of birth.';
        }
    }

    // Check for existing username/email
    if (empty($error)) {
        try {
            $db = getDB();
            $check_sql = "SELECT id FROM accounts WHERE username = ? OR email = ?";
            $check_result = $db->query($check_sql, [$username, $email]);

            if ($check_result && $check_result->num_rows > 0) {
                $error = 'Username or email already exists. Please choose different ones.';
            }
        } catch (Exception $e) {
            $error = 'Database error. Please try again. Error: ' . $e->getMessage();
            error_log("Database check error: " . $e->getMessage());
        }
    }

    // Create user if no errors
    if (empty($error)) {
        try {
            $db = getDB();
            $db->beginTransaction();

            // Generate account number
            $account_number = generateAccountNumber();

            // Hash password
            $hashed_password = hashPassword($password);

            // Insert user with pending status (for public registration)
            $sql = "INSERT INTO accounts (
                        account_number, username, password, email, first_name, last_name,
                        phone, address, date_of_birth, occupation, marital_status, gender,
                        currency, account_type, balance, status, kyc_status, is_admin
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, 0)";

            $params = [
                $account_number, $username, $hashed_password, $email, $first_name, $last_name,
                $phone, $address, $date_of_birth ?: null, $occupation, $marital_status, $gender,
                $currency, $account_type, 0.00, 'pending', 'pending'
            ];

            $user_id = $db->insert($sql, $params);

            $db->commit();

            // Prepare user data for pending approval email
            $user_data = [
                'first_name' => $first_name,
                'last_name' => $last_name,
                'username' => $username,
                'email' => $email,
                'account_number' => $account_number,
                'account_type' => $account_type,
                'currency' => $currency
            ];

            // Send pending approval email
            sendPendingApprovalEmail($email, $user_data);

            $success = 'Registration successful! Your account is pending admin approval. You will receive an email notification once approved.';

            // Clear form data on success
            $username = $email = $first_name = $last_name = $phone = $address = $date_of_birth = $occupation = '';
            $account_type = 'savings';
            $marital_status = 'single';
            $gender = 'male';
            $currency = 'USD';

        } catch (Exception $e) {
            if (isset($db)) {
                $db->rollback();
            }
            $error = 'Failed to create account. Please try again. Error: ' . $e->getMessage();
            error_log("Registration error: " . $e->getMessage());
        }
    }
}

// Get site settings for logo, name and favicon
function getSiteSettings() {
    try {
        $db = getDB();
        $settings = [];
        $result = $db->query("SELECT setting_key, setting_value FROM super_admin_settings 
                             WHERE setting_key IN ('site_name', 'site_logo', 'site_favicon')");

        if ($result) {
            while ($row = $result->fetch_assoc()) {
                $settings[$row['setting_key']] = $row['setting_value'];
            }
        }

        return [
            'site_name' => $settings['site_name'] ?? 'SecureBank Online Banking',
            'site_logo' => $settings['site_logo'] ?? '',
            'site_favicon' => $settings['site_favicon'] ?? ''
        ];
    } catch (Exception $e) {
        error_log("Error getting site settings: " . $e->getMessage());
        return [
            'site_name' => 'SecureBank Online Banking',
            'site_logo' => '',
            'site_favicon' => ''
        ];
    }
}

$site_settings = getSiteSettings();
$bank_name = $site_settings['site_name'];
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo htmlspecialchars($page_title . ' - ' . $site_settings['site_name']); ?></title>

    <!-- Favicon -->
    <?php if (!empty($site_settings['site_favicon']) && file_exists($site_settings['site_favicon'])): ?>
        <link rel="icon" type="image/x-icon" href="<?php echo htmlspecialchars($site_settings['site_favicon']); ?>">
    <?php else: ?>
        <link rel="icon" type="image/x-icon" href="assets/img/favicon.ico">
    <?php endif; ?>

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">

    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f8f9fa;
            min-height: 100vh;
            margin: 0;
            padding: 0;
            display: flex;
        }

        /* Icon-based Sidebar Navigation */
        .sidebar-nav {
            width: 80px;
            background: white;
            box-shadow: 2px 0 10px rgba(0, 0, 0, 0.1);
            position: fixed;
            top: 0;
            left: 0;
            height: 100vh;
            z-index: 1000;
            display: flex;
            flex-direction: column;
            align-items: center;
            padding: 20px 0;
        }

        .sidebar-logo {
            width: 40px;
            height: 40px;
            background: #4f46e5;
            border-radius: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 30px;
            color: white;
            font-size: 18px;
            font-weight: 700;
        }

        .nav-icon {
            width: 50px;
            height: 50px;
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 15px;
            color: #6b7280;
            text-decoration: none;
            transition: all 0.3s ease;
            position: relative;
            font-size: 20px;
        }

        .nav-icon:hover {
            background: #f3f4f6;
            color: #4f46e5;
            transform: translateX(5px);
        }

        .nav-icon.active {
            background: #4f46e5;
            color: white;
        }

        .nav-icon::after {
            content: attr(data-tooltip);
            position: absolute;
            left: 70px;
            top: 50%;
            transform: translateY(-50%);
            background: #1f2937;
            color: white;
            padding: 8px 12px;
            border-radius: 6px;
            font-size: 12px;
            white-space: nowrap;
            opacity: 0;
            visibility: hidden;
            transition: all 0.3s ease;
            z-index: 1001;
        }

        .nav-icon:hover::after {
            opacity: 1;
            visibility: visible;
            left: 75px;
        }

        /* Main Content Area */
        .main-content {
            flex: 1;
            margin-left: 80px;
            min-height: 100vh;
            background: white;
        }

        .registration-header {
            background: linear-gradient(135deg, #4f46e5 0%, #3b82f6 100%);
            color: white;
            padding: 40px 0;
            text-align: center;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }

        .registration-header h1 {
            font-size: 2.5rem;
            font-weight: 600;
            margin-bottom: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 15px;
        }

        .registration-header p {
            font-size: 1.1rem;
            opacity: 0.9;
            margin: 0;
        }

        .registration-form {
            max-width: 1000px;
            margin: 0 auto;
            padding: 50px 30px;
        }

        .form-section {
            margin-bottom: 30px;
        }

        .section-title {
            font-size: 1.3rem;
            font-weight: 600;
            color: #1f2937;
            margin-bottom: 20px;
            padding-bottom: 10px;
            border-bottom: 2px solid #e5e7eb;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .section-title i {
            color: #4361ee;
        }

        .form-label {
            font-weight: 500;
            color: #374151;
            margin-bottom: 8px;
            display: block;
            font-size: 14px;
        }

        .form-label.required::after {
            content: ' *';
            color: #dc2626;
        }

        .form-control {
            border: 2px solid #e5e7eb;
            border-radius: 12px;
            padding: 12px 16px;
            font-size: 16px;
            transition: all 0.2s ease;
            background: white;
            width: 100%;
        }

        .form-control:focus {
            border-color: #4361ee;
            box-shadow: 0 0 0 3px rgba(67, 97, 238, 0.1);
            outline: none;
        }

        .form-select {
            border: 2px solid #e5e7eb;
            border-radius: 12px;
            padding: 12px 16px;
            font-size: 16px;
            transition: all 0.2s ease;
            background: white;
            width: 100%;
        }

        .form-select:focus {
            border-color: #4361ee;
            box-shadow: 0 0 0 3px rgba(67, 97, 238, 0.1);
            outline: none;
        }

        .btn-register {
            background: linear-gradient(135deg, #4361ee 0%, #3651d4 100%);
            border: none;
            color: white;
            padding: 15px 40px;
            border-radius: 12px;
            font-weight: 600;
            font-size: 16px;
            width: 100%;
            transition: all 0.2s ease;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 10px;
        }

        .btn-register:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(67, 97, 238, 0.3);
        }

        .alert {
            border-radius: 12px;
            border: none;
            padding: 15px 20px;
            margin-bottom: 25px;
            font-weight: 500;
        }

        .alert-danger {
            background: #fef2f2;
            color: #dc2626;
            border-left: 4px solid #dc2626;
        }

        .alert-success {
            background: #f0fdf4;
            color: #166534;
            border: 1px solid #bbf7d0;
        }

        .success-container {
            max-width: 800px;
            margin: 20px auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .success-header {
            background: linear-gradient(135deg, #16a34a 0%, #15803d 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }

        .success-content {
            padding: 40px;
        }

        .user-info-card {
            background: #f8fafc;
            border: 1px solid #e2e8f0;
            border-radius: 12px;
            padding: 25px;
            margin: 20px 0;
        }

        .info-row {
            display: flex;
            justify-content: space-between;
            padding: 8px 0;
            border-bottom: 1px solid #e2e8f0;
        }

        .info-row:last-child {
            border-bottom: none;
        }

        .info-label {
            font-weight: 600;
            color: #374151;
        }

        .info-value {
            color: #6b7280;
        }

        .badge {
            padding: 4px 8px;
            border-radius: 6px;
            font-size: 12px;
            font-weight: 500;
        }

        .bg-warning {
            background-color: #fbbf24 !important;
        }

        .text-dark {
            color: #1f2937 !important;
        }

        .alert-info {
            background: #eff6ff;
            color: #1e40af;
            border: 1px solid #bfdbfe;
        }

        .btn {
            padding: 12px 24px;
            border-radius: 8px;
            font-weight: 600;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            border: none;
            cursor: pointer;
            transition: all 0.2s ease;
        }

        .btn-primary {
            background: linear-gradient(135deg, #4361ee 0%, #3651d4 100%);
            color: white;
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(67, 97, 238, 0.3);
            color: white;
            text-decoration: none;
        }

        .back-link {
            text-align: center;
            margin-top: 30px;
        }

        .back-link a {
            color: #4361ee;
            text-decoration: none;
            font-weight: 500;
            display: inline-flex;
            align-items: center;
            gap: 8px;
        }

        .back-link a:hover {
            text-decoration: underline;
        }

        .form-hint {
            font-size: 12px;
            color: #6b7280;
            margin-top: 5px;
        }

        /* Responsive Design */
        @media (max-width: 768px) {
            .sidebar-nav {
                width: 100%;
                height: 70px;
                flex-direction: row;
                justify-content: center;
                padding: 10px 0;
                position: fixed;
                bottom: 0;
                top: auto;
                box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.1);
            }

            .sidebar-logo {
                display: none;
            }

            .nav-icon {
                width: 45px;
                height: 45px;
                margin: 0 8px;
                font-size: 18px;
            }

            .nav-icon::after {
                display: none;
            }

            .main-content {
                margin-left: 0;
                margin-bottom: 70px;
            }

            .registration-header {
                padding: 30px 20px;
            }

            .registration-header h1 {
                font-size: 2rem;
            }

            .registration-form {
                padding: 30px 20px;
            }
        }

        @media (max-width: 576px) {
            .registration-header h1 {
                font-size: 1.8rem;
            }

            .nav-icon {
                width: 40px;
                height: 40px;
                margin: 0 5px;
                font-size: 16px;
            }
        }
    </style>
</head>
<body>
    <!-- Icon-based Sidebar Navigation -->
    <nav class="sidebar-nav">
        <div class="sidebar-logo">
            <?php echo strtoupper(substr($bank_name, 0, 1)); ?>
        </div>

        <a href="index.php" class="nav-icon" data-tooltip="Home">
            <i class="fas fa-home"></i>
        </a>

        <a href="login.php" class="nav-icon" data-tooltip="Login">
            <i class="fas fa-sign-in-alt"></i>
        </a>

        <a href="register.php" class="nav-icon active" data-tooltip="Register">
            <i class="fas fa-user-plus"></i>
        </a>

        <a href="#features" class="nav-icon" data-tooltip="Features">
            <i class="fas fa-star"></i>
        </a>

        <a href="#security" class="nav-icon" data-tooltip="Security">
            <i class="fas fa-shield-alt"></i>
        </a>

        <a href="#contact" class="nav-icon" data-tooltip="Contact">
            <i class="fas fa-envelope"></i>
        </a>
    </nav>

    <!-- Main Content -->
    <div class="main-content">
        <?php if (!empty($success)): ?>
            <!-- Success Page -->
            <div class="success-container">
                    <div class="success-header">
                        <h1><i class="fas fa-check-circle me-3"></i>Registration Successful!</h1>
                        <p>Welcome to <?php echo htmlspecialchars($bank_name); ?>! Your account has been created.</p>
                    </div>

                <div class="success-content">
                    <div class="alert alert-success text-center">
                        <i class="fas fa-envelope me-2"></i>
                        A confirmation email has been sent to <strong><?php echo htmlspecialchars($_POST['email'] ?? ''); ?></strong>
                    </div>

                    <h3><i class="fas fa-user me-2"></i>Your Registration Details</h3>
                    <div class="user-info-card">
                        <div class="info-row">
                            <span class="info-label">Full Name:</span>
                            <span class="info-value"><?php echo htmlspecialchars(($_POST['first_name'] ?? '') . ' ' . ($_POST['last_name'] ?? '')); ?></span>
                        </div>
                        <div class="info-row">
                            <span class="info-label">Email Address:</span>
                            <span class="info-value"><?php echo htmlspecialchars($_POST['email'] ?? ''); ?></span>
                        </div>
                        <div class="info-row">
                            <span class="info-label">Username:</span>
                            <span class="info-value"><?php echo htmlspecialchars($_POST['username'] ?? ''); ?></span>
                        </div>
                        <?php if (!empty($_POST['phone'])): ?>
                        <div class="info-row">
                            <span class="info-label">Phone Number:</span>
                            <span class="info-value"><?php echo htmlspecialchars($_POST['phone']); ?></span>
                        </div>
                        <?php endif; ?>
                        <div class="info-row">
                            <span class="info-label">Account Type:</span>
                            <span class="info-value"><?php echo htmlspecialchars(ucfirst($_POST['account_type'] ?? 'savings')); ?> Account</span>
                        </div>
                        <div class="info-row">
                            <span class="info-label">Currency:</span>
                            <span class="info-value"><?php echo htmlspecialchars($_POST['currency'] ?? 'USD'); ?></span>
                        </div>
                        <div class="info-row">
                            <span class="info-label">Status:</span>
                            <span class="info-value"><span class="badge bg-warning text-dark">Pending Admin Approval</span></span>
                        </div>
                    </div>

                    <div class="alert alert-info">
                        <i class="fas fa-info-circle me-2"></i>
                        <strong>What's Next?</strong><br>
                        Your account is currently pending admin approval. You will receive an email notification once your account has been approved and activated. This process typically takes 1-2 business days.
                    </div>

                    <div class="text-center mt-4">
                        <a href="login.php" class="btn btn-primary">
                            <i class="fas fa-sign-in-alt me-2"></i>Go to Login Page
                        </a>
                    </div>
                </div>
        <?php else: ?>
            <!-- Header Section -->
            <div class="registration-header">
                <h1><i class="fas fa-user-plus"></i>Create Account</h1>
                <p>Join <?php echo htmlspecialchars($bank_name); ?> - Secure Banking Registration</p>
            </div>

            <!-- Registration Form -->
            <div class="registration-form">
                <?php if (!empty($error)): ?>
                    <div class="alert alert-danger">
                        <i class="fas fa-exclamation-circle me-2"></i>
                        <?php echo htmlspecialchars($error); ?>
                    </div>
                <?php endif; ?>

                <form method="post" action="">
                    <!-- Personal Information Section -->
                    <div class="form-section">
                        <div class="section-title">
                            <i class="fas fa-user"></i>
                            Personal Information
                        </div>

                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label class="form-label required">First Name</label>
                                <input type="text" name="first_name" class="form-control"
                                       value="<?php echo htmlspecialchars($first_name ?? ''); ?>" required>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label class="form-label required">Last Name</label>
                                <input type="text" name="last_name" class="form-control"
                                       value="<?php echo htmlspecialchars($last_name ?? ''); ?>" required>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label class="form-label required">Email Address</label>
                                <input type="email" name="email" class="form-control"
                                       value="<?php echo htmlspecialchars($email ?? ''); ?>" required>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label class="form-label required">Username</label>
                                <input type="text" name="username" class="form-control"
                                       value="<?php echo htmlspecialchars($username ?? ''); ?>" required>
                                <div class="form-hint">Must be unique and at least 3 characters long</div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label class="form-label">Phone Number</label>
                                <input type="tel" name="phone" class="form-control"
                                       placeholder="e.g., 2341234567890"
                                       value="<?php echo htmlspecialchars($phone ?? ''); ?>">
                                <div class="form-hint">Enter without + (e.g., 2341234567890)</div>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label class="form-label">Date of Birth</label>
                                <input type="date" name="date_of_birth" class="form-control"
                                       value="<?php echo htmlspecialchars($date_of_birth ?? ''); ?>">
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-4 mb-3">
                                <label class="form-label">Gender</label>
                                <select name="gender" class="form-select">
                                    <option value="male" <?php echo ($gender ?? 'male') === 'male' ? 'selected' : ''; ?>>Male</option>
                                    <option value="female" <?php echo ($gender ?? '') === 'female' ? 'selected' : ''; ?>>Female</option>
                                    <option value="other" <?php echo ($gender ?? '') === 'other' ? 'selected' : ''; ?>>Other</option>
                                </select>
                            </div>
                            <div class="col-md-4 mb-3">
                                <label class="form-label">Marital Status</label>
                                <select name="marital_status" class="form-select">
                                    <option value="single" <?php echo ($marital_status ?? 'single') === 'single' ? 'selected' : ''; ?>>Single</option>
                                    <option value="married" <?php echo ($marital_status ?? '') === 'married' ? 'selected' : ''; ?>>Married</option>
                                    <option value="divorced" <?php echo ($marital_status ?? '') === 'divorced' ? 'selected' : ''; ?>>Divorced</option>
                                    <option value="widowed" <?php echo ($marital_status ?? '') === 'widowed' ? 'selected' : ''; ?>>Widowed</option>
                                </select>
                            </div>
                            <div class="col-md-4 mb-3">
                                <label class="form-label">Occupation</label>
                                <input type="text" name="occupation" class="form-control"
                                       placeholder="Your profession"
                                       value="<?php echo htmlspecialchars($occupation ?? ''); ?>">
                            </div>
                        </div>

                        <div class="mb-3">
                            <label class="form-label">Address</label>
                            <textarea name="address" class="form-control" rows="3"
                                      placeholder="Enter your full address"><?php echo htmlspecialchars($address ?? ''); ?></textarea>
                        </div>
                    </div>

                    <!-- Account Information Section -->
                    <div class="form-section">
                        <div class="section-title">
                            <i class="fas fa-credit-card"></i>
                            Account Information
                        </div>

                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label class="form-label">Account Type</label>
                                <select name="account_type" class="form-select">
                                    <option value="savings" <?php echo ($account_type ?? 'savings') === 'savings' ? 'selected' : ''; ?>>Savings Account</option>
                                    <option value="checking" <?php echo ($account_type ?? '') === 'checking' ? 'selected' : ''; ?>>Checking Account</option>
                                    <option value="business" <?php echo ($account_type ?? '') === 'business' ? 'selected' : ''; ?>>Business Account</option>
                                </select>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label class="form-label">Preferred Currency</label>
                                <select name="currency" class="form-select">
                                    <option value="USD" <?php echo ($currency ?? 'USD') === 'USD' ? 'selected' : ''; ?>>USD - US Dollar</option>
                                    <option value="EUR" <?php echo ($currency ?? '') === 'EUR' ? 'selected' : ''; ?>>EUR - Euro</option>
                                    <option value="GBP" <?php echo ($currency ?? '') === 'GBP' ? 'selected' : ''; ?>>GBP - British Pound</option>
                                    <option value="NGN" <?php echo ($currency ?? '') === 'NGN' ? 'selected' : ''; ?>>NGN - Nigerian Naira</option>
                                </select>
                            </div>
                        </div>
                    </div>

                    <!-- Security Section -->
                    <div class="form-section">
                        <div class="section-title">
                            <i class="fas fa-lock"></i>
                            Security Information
                        </div>

                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label class="form-label required">Password</label>
                                <input type="password" name="password" class="form-control" required>
                                <div class="form-hint">Minimum 6 characters</div>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label class="form-label required">Confirm Password</label>
                                <input type="password" name="confirm_password" class="form-control" required>
                            </div>
                        </div>
                    </div>

                    <!-- Submit Button -->
                    <div class="text-center">
                        <button type="submit" class="btn-register">
                            <i class="fas fa-user-plus"></i>
                            Create My Banking Account
                        </button>
                    </div>
                </form>

                <!-- Back to Login Link -->
                <div class="back-link">
                    <a href="login.php">
                        <i class="fas fa-arrow-left"></i>
                        Already have an account? Sign in here
                    </a>
                </div>
            </div>
        <?php endif; ?>
    </div>

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Form validation enhancement
        const form = document.querySelector('form');
        if (form) {
            const inputs = form.querySelectorAll('input[required]');

            inputs.forEach(input => {
                input.addEventListener('blur', function() {
                    if (this.value.trim() === '') {
                        this.style.borderColor = '#ef4444';
                    } else {
                        this.style.borderColor = '#4f46e5';
                    }
                });
            });
        }

        // Smooth scrolling for anchor links
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            });
        });
    </script>
</body>
</html>
