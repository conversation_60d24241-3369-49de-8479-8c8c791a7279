<?php
/**
 * CORRECTED Transfer Processing API
 * Processes money transfers with OTP verification
 * Fixed all database column mismatches and method errors
 */

// Start session and check authentication
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

if (!isset($_SESSION['user_id'])) {
    http_response_code(401);
    echo json_encode(['error' => 'Unauthorized']);
    exit();
}

// Enable error reporting for debugging
error_reporting(E_ALL);
ini_set('display_errors', 0);
ini_set('log_errors', 1);

// Add detailed logging for debugging
function logTransferDebug($message) {
    error_log("TRANSFER_DEBUG: " . $message);
}

// Use existing sanitizeInput function from config.php
// Function already declared in config.php

// Format currency (check if not already declared)
if (!function_exists('formatCurrency')) {
    function formatCurrency($amount, $currency = '$') {
        return $currency . number_format($amount, 2);
    }
}

// Include database connection
require_once '../../config/config.php';

// Set JSON response header and ensure clean output
header('Content-Type: application/json');
if (ob_get_level()) {
    ob_clean();
}

try {
    logTransferDebug("Starting transfer processing");

    // Get database connection
    $db = getDB();
    $user_id = $_SESSION['user_id'];

    logTransferDebug("User ID: " . $user_id);
    
    // Get user information
    $user_query = "SELECT * FROM accounts WHERE id = ?";
    $user_result = $db->query($user_query, [$user_id]);
    $user = $user_result->fetch_assoc();
    
    if (!$user) {
        throw new Exception('User not found');
    }
    
    // Get JSON input
    $raw_input = file_get_contents('php://input');
    logTransferDebug("Raw input: " . $raw_input);

    $input = json_decode($raw_input, true);

    if (!$input) {
        logTransferDebug("JSON decode failed: " . json_last_error_msg());
        throw new Exception('Invalid request data');
    }

    logTransferDebug("Parsed input: " . json_encode($input));
    
    // Extract transfer data
    $transfer_type = sanitizeInput($input['transfer_type'] ?? '');
    $source_account = sanitizeInput($input['source_account'] ?? 'main');
    $beneficiary_account = sanitizeInput($input['beneficiary_account'] ?? '');
    $beneficiary_name = sanitizeInput($input['beneficiary_name'] ?? '');
    $beneficiary_bank = sanitizeInput($input['beneficiary_bank'] ?? '');
    $routing_code = sanitizeInput($input['routing_code'] ?? '');
    $account_type = sanitizeInput($input['account_type'] ?? '');
    $amount = floatval($input['amount'] ?? 0);
    $narration = sanitizeInput($input['narration'] ?? 'Money Transfer');
    $currency = sanitizeInput($input['currency'] ?? $user['currency']);
    $otp_code = sanitizeInput($input['otp_code'] ?? '');

    logTransferDebug("Transfer type: " . $transfer_type);
    logTransferDebug("Amount: " . $amount);
    logTransferDebug("Beneficiary: " . $beneficiary_name . " (" . $beneficiary_account . ")");
    
    // Validate required fields
    if (empty($transfer_type) || empty($beneficiary_account) || empty($beneficiary_name) || $amount <= 0) {
        throw new Exception('Missing required transfer information');
    }
    
    // Validate transfer type
    if (!in_array($transfer_type, ['inter-bank', 'local-bank'])) {
        throw new Exception('Invalid transfer type');
    }
    
    // Validate amount
    if ($amount < 1) {
        throw new Exception('Minimum transfer amount is $1.00');
    }
    
    // Calculate transfer fee
    $transfer_fee = 0;
    if ($transfer_type === 'local-bank') {
        $transfer_fee = max(2.50, $amount * 0.001); // Minimum $2.50 or 0.1%
    }
    // Inter-bank transfers are free
    
    $total_debit = $amount + $transfer_fee;
    
    // Check if user has sufficient balance
    if ($user['balance'] < $total_debit) {
        throw new Exception('Insufficient balance for this transfer');
    }
    
    logTransferDebug("Transfer fee: " . $transfer_fee);
    logTransferDebug("Total debit: " . $total_debit);
    
    // Check if OTP verification is required (per-user setting for local-bank transfers)
    $otp_required = false;
    if ($transfer_type === 'local-bank') {
        // Check user's OTP setting
        $user_otp_query = "SELECT COALESCE(uss.otp_enabled, 1) as otp_enabled
                           FROM accounts a
                           LEFT JOIN user_security_settings uss ON a.id = uss.user_id
                           WHERE a.id = ?";
        $user_otp_result = $db->query($user_otp_query, [$user_id]);

        if ($user_otp_result && $user_otp_result->num_rows > 0) {
            $setting = $user_otp_result->fetch_assoc();
            $otp_required = ($setting['otp_enabled'] == 1);
        } else {
            $otp_required = true; // Default to requiring OTP if no setting found
        }

        logTransferDebug("OTP required for user $user_id: " . ($otp_required ? 'Yes' : 'No'));
    }

    if ($otp_required) {
        // Verify OTP for local bank transfers using CORRECT column names
        if (empty($otp_code)) {
            throw new Exception('OTP verification is required for local bank transfers');
        }
        
        // FIXED: Use correct column names (source instead of purpose, used instead of is_used)
        $verify_otp_sql = "SELECT * FROM user_otps 
                          WHERE user_id = ? AND otp_code = ? AND source = 'transfer' 
                          AND expires_at > NOW() AND used = 0";
        $otp_result = $db->query($verify_otp_sql, [$user_id, $otp_code]);
        
        if ($otp_result->num_rows === 0) {
            throw new Exception('Invalid or expired verification code');
        }
        
        // FIXED: Mark OTP as used with correct column name
        $mark_otp_used_sql = "UPDATE user_otps SET used = 1, used_at = NOW() WHERE user_id = ? AND otp_code = ?";
        $db->query($mark_otp_used_sql, [$user_id, $otp_code]);
        
        logTransferDebug("OTP verified and marked as used");
    }
    
    // For inter-bank transfers, validate recipient exists
    $recipient_id = null;
    if ($transfer_type === 'inter-bank') {
        logTransferDebug("Processing inter-bank transfer");

        $recipient_query = "SELECT id, first_name, last_name FROM accounts
                           WHERE account_number = ? AND is_admin = 0 AND status = 'active' AND id != ?";
        $recipient_result = $db->query($recipient_query, [$beneficiary_account, $user_id]);

        logTransferDebug("Recipient query executed, rows found: " . $recipient_result->num_rows);

        if ($recipient_result->num_rows === 0) {
            logTransferDebug("Recipient not found for account: " . $beneficiary_account);
            throw new Exception('Recipient account not found or invalid for inter-bank transfer');
        }

        $recipient = $recipient_result->fetch_assoc();
        $recipient_id = $recipient['id'];
        logTransferDebug("Recipient found: ID=" . $recipient_id . ", Name=" . $recipient['first_name'] . " " . $recipient['last_name']);
        
        // Credit recipient's account for inter-bank transfers
        $credit_recipient_sql = "UPDATE accounts SET balance = balance + ? WHERE id = ?";
        $db->query($credit_recipient_sql, [$amount, $recipient_id]);
        logTransferDebug("Recipient account credited with: " . $amount);
    }
    
    // FIXED: Start database transaction with correct method name
    logTransferDebug("Starting database transaction");
    $db->beginTransaction(); // FIXED: was begin_transaction()

    try {
        // Generate unique transfer reference
        $transfer_reference = 'TXN' . date('Ymd') . sprintf('%06d', mt_rand(100000, 999999));
        logTransferDebug("Transfer reference: " . $transfer_reference);
        
        // FIXED: Insert transfer record with CORRECT column names
        $insert_transfer_sql = "INSERT INTO transfers (
            transaction_id, sender_id, recipient_id, recipient_account, 
            recipient_name, amount, currency, transfer_type, status, 
            description, fee, created_at
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, 'completed', ?, ?, NOW())";
        
        // Map transfer_type to database enum values
        $db_transfer_type = ($transfer_type === 'inter-bank') ? 'local' : 'local';
        
        // FIXED: Use correct insert method
        $transfer_id = $db->insert($insert_transfer_sql, [
            $transfer_reference, $user_id, $recipient_id, $beneficiary_account,
            $beneficiary_name, $amount, $currency, $db_transfer_type,
            $narration, $transfer_fee
        ]);
        
        logTransferDebug("Transfer record inserted with ID: " . $transfer_id);
        
        // Debit sender's account
        if ($source_account === 'main') {
            $update_balance_sql = "UPDATE accounts SET balance = balance - ? WHERE id = ?";
            $db->query($update_balance_sql, [$total_debit, $user_id]);
            logTransferDebug("Sender account debited with: " . $total_debit);
        }
        
        // Commit transaction
        $db->commit();
        logTransferDebug("Transaction committed successfully");

        // Log successful transfer
        error_log("Transfer completed: {$transfer_reference} - {$user_id} sent " . formatCurrency($amount, $currency) . " to {$beneficiary_name}");

        // Return success response
        $response = [
            'success' => true,
            'transfer_id' => $transfer_id,
            'reference_number' => $transfer_reference,
            'amount' => $amount,
            'fee' => $transfer_fee,
            'total_debit' => $total_debit,
            'currency' => $currency,
            'beneficiary_name' => $beneficiary_name,
            'transfer_type' => $transfer_type,
            'status' => 'completed',
            'message' => 'Transfer completed successfully'
        ];

        logTransferDebug("Sending success response: " . json_encode($response));
        echo json_encode($response);
        
    } catch (Exception $e) {
        // Rollback transaction on error
        $db->rollback();
        throw $e;
    }
    
} catch (Exception $e) {
    error_log("Transfer processing error: " . $e->getMessage());
    
    http_response_code(400);
    echo json_encode([
        'success' => false,
        'error' => 'Transfer failed',
        'message' => $e->getMessage()
    ]);
}
?>
