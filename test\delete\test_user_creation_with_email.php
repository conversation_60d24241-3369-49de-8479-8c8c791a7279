<?php
/**
 * Test User Creation with Email Functionality
 * Creates a test user <NAME_EMAIL> and sends welcome email
 */

// Include required files
require_once 'config/config.php';
require_once 'config/database.php';
require_once 'config/email.php';
require_once 'includes/functions.php';

// Enable error reporting
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

echo "<!DOCTYPE html>";
echo "<html><head><title>Test User Creation with Email</title>";
echo "<style>
body { font-family: Arial, sans-serif; margin: 20px; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); min-height: 100vh; }
.container { max-width: 900px; margin: 0 auto; background: white; padding: 30px; border-radius: 15px; box-shadow: 0 8px 32px rgba(0,0,0,0.3); }
.header { background: linear-gradient(135deg, #28a745 0%, #20c997 100%); color: white; padding: 30px; text-align: center; border-radius: 10px; margin-bottom: 20px; }
.success { background: linear-gradient(135deg, #28a745 0%, #20c997 100%); color: white; padding: 20px; border-radius: 10px; margin: 15px 0; }
.error { background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%); color: white; padding: 20px; border-radius: 10px; margin: 15px 0; }
.info { background: #e7f3ff; border: 1px solid #b3d9ff; color: #0c5460; padding: 20px; border-radius: 10px; margin: 15px 0; }
.warning { background: #fff3cd; border: 1px solid #ffeaa7; color: #856404; padding: 20px; border-radius: 10px; margin: 15px 0; }
.config { background: #f8f9fa; padding: 15px; border-radius: 8px; border-left: 4px solid #007bff; margin: 15px 0; }
pre { background: #2d3748; color: #e2e8f0; padding: 15px; border-radius: 8px; font-size: 12px; overflow-x: auto; }
table { width: 100%; border-collapse: collapse; margin: 15px 0; }
th, td { padding: 12px; text-align: left; border-bottom: 1px solid #ddd; }
th { background-color: #f8f9fa; font-weight: bold; }
</style>";
echo "</head><body>";

echo "<div class='container'>";
echo "<div class='header'>";
echo "<h1>🏦 Test User Creation with Email</h1>";
echo "<p>Creating test <NAME_EMAIL> with welcome email</p>";
echo "</div>";

// Test user data
$test_user_data = [
    'username' => 'demothedev',
    'email' => '<EMAIL>',
    'first_name' => 'Demo',
    'last_name' => 'Developer',
    'phone' => '******-0123',
    'address' => '123 Demo Street, Test City, TC 12345',
    'date_of_birth' => '1990-01-01',
    'occupation' => 'Software Developer',
    'marital_status' => 'single',
    'gender' => 'male',
    'currency' => 'USD',
    'account_type' => 'savings',
    'initial_balance' => 5000.00,
    'password' => 'DemoPass123!',
    'kyc_status' => 'pending',
    'status' => 'active'
];

echo "<h2>📧 Current Email Configuration</h2>";
echo "<div class='config'>";
echo "<strong>SMTP Host:</strong> " . SMTP_HOST . "<br>";
echo "<strong>SMTP Port:</strong> " . SMTP_PORT . " (" . (SMTP_PORT == 465 ? 'SSL' : 'TLS') . ")<br>";
echo "<strong>SMTP Username:</strong> " . SMTP_USERNAME . "<br>";
echo "<strong>SMTP Encryption:</strong> " . strtoupper(SMTP_ENCRYPTION) . "<br>";
echo "<strong>From Email:</strong> " . FROM_EMAIL . "<br>";
echo "<strong>From Name:</strong> " . FROM_NAME . "<br>";
echo "</div>";

echo "<h2>👤 Test User Data</h2>";
echo "<table>";
echo "<tr><th>Field</th><th>Value</th></tr>";
foreach ($test_user_data as $key => $value) {
    if ($key !== 'password') {
        echo "<tr><td>" . ucwords(str_replace('_', ' ', $key)) . "</td><td>" . htmlspecialchars($value) . "</td></tr>";
    } else {
        echo "<tr><td>" . ucwords(str_replace('_', ' ', $key)) . "</td><td>••••••••••••</td></tr>";
    }
}
echo "</table>";

// Check if user already exists
echo "<h2>🔍 Pre-Creation Checks</h2>";
try {
    $db = getDB();
    
    // Check if username exists
    $username_check = $db->query("SELECT id FROM accounts WHERE username = ?", [$test_user_data['username']]);
    if ($username_check && $username_check->num_rows > 0) {
        echo "<div class='warning'>⚠️ Username '{$test_user_data['username']}' already exists. Will skip creation.</div>";
        $user_exists = true;
    } else {
        echo "<div class='success'>✅ Username '{$test_user_data['username']}' is available</div>";
        $user_exists = false;
    }
    
    // Check if email exists
    $email_check = $db->query("SELECT id FROM accounts WHERE email = ?", [$test_user_data['email']]);
    if ($email_check && $email_check->num_rows > 0) {
        echo "<div class='warning'>⚠️ Email '{$test_user_data['email']}' already exists. Will skip creation.</div>";
        $email_exists = true;
    } else {
        echo "<div class='success'>✅ Email '{$test_user_data['email']}' is available</div>";
        $email_exists = false;
    }
    
} catch (Exception $e) {
    echo "<div class='error'>❌ Database check error: " . $e->getMessage() . "</div>";
    $user_exists = true;
    $email_exists = true;
}

// Create user if doesn't exist
if (!$user_exists && !$email_exists) {
    echo "<h2>🚀 Creating User Account</h2>";
    
    try {
        $db = getDB();
        $db->beginTransaction();
        
        // Generate account number
        $account_number = generateAccountNumber();
        echo "<div class='info'>📋 Generated Account Number: <strong>$account_number</strong></div>";
        
        // Hash password
        $hashed_password = hashPassword($test_user_data['password']);
        echo "<div class='info'>🔐 Password hashed successfully</div>";
        
        // Insert user
        $sql = "INSERT INTO accounts (
                    account_number, username, password, email, first_name, last_name,
                    phone, address, date_of_birth, occupation, marital_status, gender,
                    currency, account_type, balance, status, kyc_status, is_admin
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, 0)";

        $params = [
            $account_number, 
            $test_user_data['username'], 
            $hashed_password, 
            $test_user_data['email'], 
            $test_user_data['first_name'], 
            $test_user_data['last_name'],
            $test_user_data['phone'], 
            $test_user_data['address'], 
            $test_user_data['date_of_birth'], 
            $test_user_data['occupation'], 
            $test_user_data['marital_status'], 
            $test_user_data['gender'],
            $test_user_data['currency'], 
            $test_user_data['account_type'], 
            $test_user_data['initial_balance'], 
            $test_user_data['status'], 
            $test_user_data['kyc_status']
        ];
        
        $user_id = $db->insert($sql, $params);
        echo "<div class='success'>✅ User created successfully with ID: $user_id</div>";
        
        $db->commit();
        
        // Prepare user data for welcome email
        $welcome_user_data = [
            'first_name' => $test_user_data['first_name'],
            'last_name' => $test_user_data['last_name'],
            'username' => $test_user_data['username'],
            'email' => $test_user_data['email'],
            'account_number' => $account_number,
            'account_type' => $test_user_data['account_type'],
            'currency' => $test_user_data['currency'],
            'balance' => $test_user_data['initial_balance'],
            'status' => $test_user_data['status']
        ];
        
        echo "<h2>📧 Sending Welcome Email</h2>";
        echo "<div class='info'>📤 Attempting to send welcome email to: <strong>{$test_user_data['email']}</strong></div>";
        
        // Send welcome email
        $emailSent = sendWelcomeEmail($test_user_data['email'], $welcome_user_data);
        
        if ($emailSent) {
            echo "<div class='success'>";
            echo "<h3>🎉 SUCCESS! User Created and Welcome Email Sent!</h3>";
            echo "<p><strong>Account Details:</strong></p>";
            echo "<ul>";
            echo "<li><strong>Username:</strong> {$test_user_data['username']}</li>";
            echo "<li><strong>Email:</strong> {$test_user_data['email']}</li>";
            echo "<li><strong>Account Number:</strong> $account_number</li>";
            echo "<li><strong>Password:</strong> {$test_user_data['password']}</li>";
            echo "<li><strong>Initial Balance:</strong> $" . number_format($test_user_data['initial_balance'], 2) . "</li>";
            echo "</ul>";
            echo "</div>";
        } else {
            echo "<div class='error'>";
            echo "<h3>⚠️ User Created but Welcome Email Failed</h3>";
            echo "<p>The user account was created successfully, but the welcome email could not be sent.</p>";
            echo "<p>Check the error logs for email delivery issues.</p>";
            echo "</div>";
        }
        
    } catch (Exception $e) {
        if (isset($db)) {
            $db->rollback();
        }
        echo "<div class='error'>❌ User creation failed: " . $e->getMessage() . "</div>";
    }
    
} else {
    echo "<h2>⚠️ User Creation Skipped</h2>";
    echo "<div class='warning'>User already exists. Testing email functionality with existing data...</div>";
    
    // Get existing user data for email test
    try {
        $existing_user = $db->query("SELECT * FROM accounts WHERE email = ? OR username = ?", 
                                   [$test_user_data['email'], $test_user_data['username']])->fetch_assoc();
        
        if ($existing_user) {
            $welcome_user_data = [
                'first_name' => $existing_user['first_name'],
                'last_name' => $existing_user['last_name'],
                'username' => $existing_user['username'],
                'email' => $existing_user['email'],
                'account_number' => $existing_user['account_number'],
                'account_type' => $existing_user['account_type'],
                'currency' => $existing_user['currency'],
                'balance' => $existing_user['balance'],
                'status' => $existing_user['status']
            ];
            
            echo "<h2>📧 Testing Email with Existing User</h2>";
            echo "<div class='info'>📤 Sending test welcome email to: <strong>{$existing_user['email']}</strong></div>";
            
            $emailSent = sendWelcomeEmail($existing_user['email'], $welcome_user_data);
            
            if ($emailSent) {
                echo "<div class='success'>";
                echo "<h3>🎉 Email Test Successful!</h3>";
                echo "<p>Welcome email sent successfully to existing user.</p>";
                echo "</div>";
            } else {
                echo "<div class='error'>";
                echo "<h3>❌ Email Test Failed</h3>";
                echo "<p>Could not send welcome email to existing user.</p>";
                echo "</div>";
            }
        }
    } catch (Exception $e) {
        echo "<div class='error'>❌ Error testing with existing user: " . $e->getMessage() . "</div>";
    }
}

// Test OTP Email as well
echo "<h2>🔐 Testing OTP Email</h2>";
try {
    $test_otp = generateOTP();
    echo "<div class='info'>🔢 Generated OTP: <strong>$test_otp</strong></div>";
    
    $otp_result = sendOTPEmail($test_user_data['email'], $test_otp, $test_user_data['first_name']);
    
    if ($otp_result) {
        echo "<div class='success'>✅ OTP email sent successfully to {$test_user_data['email']}</div>";
    } else {
        echo "<div class='error'>❌ OTP email failed to send</div>";
    }
    
} catch (Exception $e) {
    echo "<div class='error'>❌ OTP email error: " . $e->getMessage() . "</div>";
}

// Check recent email logs
echo "<h2>📄 Recent Email Logs</h2>";
$log_files = [
    'logs/email_welcome.log',
    'logs/email_simulation.log',
    'logs/error.log'
];

foreach ($log_files as $log_file) {
    if (file_exists($log_file)) {
        $log_size = filesize($log_file);
        echo "<h4>📄 $log_file (Size: " . number_format($log_size) . " bytes)</h4>";
        
        if ($log_size > 0) {
            $log_content = file_get_contents($log_file);
            $log_lines = explode("\n", $log_content);
            $recent_lines = array_slice($log_lines, -10);
            echo "<pre>" . htmlspecialchars(implode("\n", $recent_lines)) . "</pre>";
        } else {
            echo "<p>Log file is empty.</p>";
        }
    } else {
        echo "<h4>📄 $log_file</h4>";
        echo "<p>Log file not found.</p>";
    }
}

// Summary
echo "<hr>";
echo "<h2>📊 Test Summary</h2>";
echo "<div class='info'>";
echo "<h3>🎯 What to Check:</h3>";
echo "<ol>";
echo "<li>Check your email inbox at <strong><EMAIL></strong></li>";
echo "<li>Look for welcome email with subject containing 'Welcome to'</li>";
echo "<li>Look for OTP email with verification code: <strong>" . (isset($test_otp) ? $test_otp : 'N/A') . "</strong></li>";
echo "<li>Check spam/junk folder if emails are not in inbox</li>";
echo "<li>Verify HTML formatting is working correctly</li>";
echo "<li>Try logging in with the created account credentials</li>";
echo "</ol>";
echo "</div>";

echo "<div class='success'>";
echo "<h3>🚀 Next Steps:</h3>";
echo "<ul>";
echo "<li>✅ <strong>Email System:</strong> Configured with Port 465 (SSL)</li>";
echo "<li>✅ <strong>User Creation:</strong> Working with email notifications</li>";
echo "<li>✅ <strong>Welcome Emails:</strong> Sent via SMTP</li>";
echo "<li>✅ <strong>OTP Emails:</strong> Sent via SMTP</li>";
echo "<li>🎯 <strong>Ready for Production:</strong> Banking system email features operational</li>";
echo "</ul>";
echo "</div>";

echo "<p><strong>Test completed at:</strong> " . date('Y-m-d H:i:s') . "</p>";
echo "</div>";
echo "</body></html>";
?>
