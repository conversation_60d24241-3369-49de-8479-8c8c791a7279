<?php
require_once '../config/config.php';
requireAdmin();

$page_title = 'Billing Code Assignment';

// Define page actions
$page_actions = [
    [
        'url' => 'billing-code-settings.php',
        'label' => 'System Settings',
        'icon' => 'fas fa-cog'
    ],
    [
        'url' => 'wire-transfer-fields.php',
        'label' => 'Transfer Fields',
        'icon' => 'fas fa-list-alt'
    ]
];

// Handle form submissions
$success_message = '';
$error_message = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $action = $_POST['action'] ?? '';
    
    if ($action === 'save_billing_codes') {
        try {
            $db = getDB();
            $user_id = intval($_POST['user_id'] ?? 0);
            
            if ($user_id > 0) {
                // Delete existing billing codes for this user
                $db->query("DELETE FROM user_billing_codes WHERE user_id = ?", [$user_id]);
                
                // Insert new billing codes
                for ($i = 1; $i <= 4; $i++) {
                    $billing_name = sanitizeInput($_POST["billing_name_$i"] ?? '');
                    $billing_code = sanitizeInput($_POST["billing_code_$i"] ?? '');
                    $billing_description = sanitizeInput($_POST["billing_description_$i"] ?? '');
                    $is_active = isset($_POST["is_active_$i"]) ? 1 : 0;
                    
                    if (!empty($billing_name) && !empty($billing_code)) {
                        $sql = "INSERT INTO user_billing_codes (user_id, billing_position, billing_name, billing_code, billing_description, is_active, created_by) 
                                VALUES (?, ?, ?, ?, ?, ?, ?)";
                        $db->query($sql, [$user_id, $i, $billing_name, $billing_code, $billing_description, $is_active, $_SESSION['user_id']]);
                    }
                }
                
                $success_message = 'Billing codes updated successfully!';
            } else {
                $error_message = 'Please select a valid user.';
            }
            
        } catch (Exception $e) {
            $error_message = 'Error updating billing codes: ' . $e->getMessage();
        }
    }
}

// Get selected user ID
$selected_user_id = intval($_GET['user_id'] ?? $_POST['user_id'] ?? 0);

// Fetch all users
$users = [];
try {
    $db = getDB();
    $result = $db->query("SELECT id, first_name, last_name, email, account_number FROM accounts WHERE status = 'active' ORDER BY first_name, last_name");
    while ($row = $result->fetch_assoc()) {
        $users[] = $row;
    }
} catch (Exception $e) {
    $error_message = 'Error loading users: ' . $e->getMessage();
}

// Fetch billing codes for selected user
$user_billing_codes = [];
if ($selected_user_id > 0) {
    try {
        $result = $db->query("SELECT * FROM user_billing_codes WHERE user_id = ? ORDER BY billing_position", [$selected_user_id]);
        while ($row = $result->fetch_assoc()) {
            $user_billing_codes[$row['billing_position']] = $row;
        }
    } catch (Exception $e) {
        $error_message = 'Error loading billing codes: ' . $e->getMessage();
    }
}

include 'includes/admin-header.php';
?>

<!-- Breadcrumb -->
<nav aria-label="breadcrumb" style="margin-bottom: 1rem;">
    <ol class="breadcrumb">
        <li class="breadcrumb-item"><a href="index.php">Admin</a></li>
        <li class="breadcrumb-item active" aria-current="page">Billing Code Assignment</li>
    </ol>
</nav>

<?php if ($success_message): ?>
<div class="alert alert-success alert-dismissible fade show" role="alert">
    <i class="fas fa-check-circle me-2"></i><?php echo $success_message; ?>
    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
</div>
<?php endif; ?>

<?php if ($error_message): ?>
<div class="alert alert-danger alert-dismissible fade show" role="alert">
    <i class="fas fa-exclamation-triangle me-2"></i><?php echo $error_message; ?>
    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
</div>
<?php endif; ?>

<!-- User Selection Card -->
<div class="row row-cards">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h3 class="card-title">
                    <i class="fas fa-user-shield me-2"></i>
                    Billing Code Assignment - Enhanced Security
                </h3>
                <div class="card-actions">
                    <span class="badge" style="background-color: var(--primary-color); color: white;">
                        <i class="fas fa-users me-1"></i>
                        <?php echo count($users); ?> Users
                    </span>
                    <span class="badge bg-info ms-2">
                        <i class="fas fa-info-circle me-1"></i>
                        Simple Assignment
                    </span>
                </div>
            </div>
            <div class="card-body">
                <form method="GET" action="" class="mb-4">
                    <div class="row align-items-end">
                        <div class="col-md-8">
                            <label class="form-label">
                                <i class="fas fa-user me-1"></i>
                                Select User to Assign Billing Codes (Enhanced Security)
                            </label>
                            <select name="user_id" class="form-select" required>
                                <option value="">Choose a user...</option>
                                <?php foreach ($users as $user): ?>
                                <option value="<?php echo $user['id']; ?>" <?php echo $user['id'] == $selected_user_id ? 'selected' : ''; ?>>
                                    <?php echo htmlspecialchars($user['first_name'] . ' ' . $user['last_name']); ?> 
                                    (<?php echo htmlspecialchars($user['email']); ?>) 
                                    - Account: <?php echo htmlspecialchars($user['account_number']); ?>
                                </option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                        <div class="col-md-4">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-search me-2"></i>
                                Load User Codes
                            </button>
                        </div>
                    </div>
                </form>
                
                <?php if ($selected_user_id > 0): ?>
                <hr class="my-4">
                
                <!-- Selected User Info -->
                <?php 
                $selected_user = null;
                foreach ($users as $user) {
                    if ($user['id'] == $selected_user_id) {
                        $selected_user = $user;
                        break;
                    }
                }
                ?>
                
                <?php if ($selected_user): ?>
                <div class="card border-primary">
                    <div class="card-header" style="background-color: var(--primary-color); color: white;">
                        <h5 class="mb-0"><i class="fas fa-shield-alt me-2"></i>Assigning Billing Codes for Enhanced Security:</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-8">
                                <h6 class="text-primary mb-2">User Information</h6>
                                <p class="mb-1"><strong><?php echo htmlspecialchars($selected_user['first_name'] . ' ' . $selected_user['last_name']); ?></strong></p>
                                <p class="mb-1"><i class="fas fa-envelope me-2 text-muted"></i><?php echo htmlspecialchars($selected_user['email']); ?></p>
                                <p class="mb-0"><i class="fas fa-credit-card me-2 text-muted"></i>Account: <?php echo htmlspecialchars($selected_user['account_number']); ?></p>
                            </div>
                            <div class="col-md-4">
                                <h6 class="text-primary mb-2">Configuration</h6>
                                <div class="d-flex align-items-center">
                                    <i class="fas fa-cog me-2 text-info"></i>
                                    <span class="badge bg-info fs-6">
                                        Individual Billing Code Setup
                                    </span>
                                </div>
                                <small class="text-muted mt-2 d-block">If billing codes are assigned, user must enter them before OTP. If no codes assigned, user proceeds with OTP only.</small>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Billing Codes Form -->
                <div class="mt-4"></div>
                <form method="POST" action="">
                    <input type="hidden" name="action" value="save_billing_codes">
                    <input type="hidden" name="user_id" value="<?php echo $selected_user_id; ?>">
                    
                    <div class="row">
                        <?php for ($i = 1; $i <= 4; $i++): ?>
                        <?php $code_data = $user_billing_codes[$i] ?? null; ?>
                        <div class="col-md-6 mb-4">
                            <div class="card">
                                <div class="card-header">
                                    <h4 class="card-title">
                                        <i class="fas fa-key me-2"></i>
                                        Billing Code <?php echo $i; ?>
                                    </h4>
                                    <div class="card-actions">
                                        <div class="form-check">
                                            <input type="checkbox" name="is_active_<?php echo $i; ?>" class="form-check-input" 
                                                   id="is_active_<?php echo $i; ?>" <?php echo ($code_data && $code_data['is_active']) ? 'checked' : ''; ?>>
                                            <label class="form-check-label" for="is_active_<?php echo $i; ?>">
                                                Active
                                            </label>
                                        </div>
                                    </div>
                                </div>
                                <div class="card-body">
                                    <div class="mb-3">
                                        <label class="form-label">Billing Name</label>
                                        <input type="text" name="billing_name_<?php echo $i; ?>" class="form-control" 
                                               value="<?php echo htmlspecialchars($code_data['billing_name'] ?? ''); ?>" 
                                               placeholder="e.g., IRS RESTRICTION, Tax Code, etc.">
                                    </div>
                                    <div class="mb-3">
                                        <label class="form-label">Billing Code</label>
                                        <input type="text" name="billing_code_<?php echo $i; ?>" class="form-control" 
                                               value="<?php echo htmlspecialchars($code_data['billing_code'] ?? ''); ?>" 
                                               placeholder="Enter billing code">
                                    </div>
                                    <div class="mb-3">
                                        <label class="form-label">
                                            <i class="fas fa-comment-alt me-1"></i>
                                            Description (Popup Text for User)
                                        </label>
                                        <textarea name="billing_description_<?php echo $i; ?>" class="form-control" rows="3"
                                                  placeholder="e.g., Contact your bank for your IRS restriction code, Enter your tax verification code, etc."><?php echo htmlspecialchars($code_data['billing_description'] ?? ''); ?></textarea>
                                        <div class="form-text">
                                            <i class="fas fa-lightbulb me-1"></i>
                                            This exact message will be shown to the user in the billing code verification popup to guide them
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <?php endfor; ?>
                    </div>
                    
                    <div class="d-flex justify-content-between">
                        <button type="submit" class="btn btn-success">
                            <i class="fas fa-save me-2"></i>
                            Save Billing Codes
                        </button>
                        <div>
                            <a href="billing-code-settings.php" class="btn btn-outline-secondary me-2">
                                <i class="fas fa-cog me-2"></i>
                                System Settings
                            </a>
                            <button type="button" class="btn btn-outline-danger" onclick="clearAllCodes()">
                                <i class="fas fa-trash me-2"></i>
                                Clear All Codes
                            </button>
                        </div>
                    </div>
                </form>
                <?php endif; ?>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<?php if ($selected_user_id > 0): ?>
<!-- Usage Information -->
<div class="row row-cards mt-3">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h3 class="card-title">
                    <i class="fas fa-info-circle me-2"></i>
                    Simple Billing Code Assignment Guide
                </h3>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-4">
                        <h5><i class="fas fa-lightbulb text-warning me-2"></i>How It Works</h5>
                        <ul class="list-unstyled">
                            <li><i class="fas fa-check text-success me-1"></i> Default: OTP only (current system)</li>
                            <li><i class="fas fa-check text-success me-1"></i> Enhanced: Billing codes + OTP</li>
                            <li><i class="fas fa-check text-success me-1"></i> No codes assigned = OTP only</li>
                            <li><i class="fas fa-check text-success me-1"></i> Codes assigned = Codes + OTP</li>
                        </ul>
                    </div>
                    <div class="col-md-4">
                        <h5><i class="fas fa-comment-alt text-info me-2"></i>Description Examples</h5>
                        <ul class="list-unstyled">
                            <li><i class="fas fa-arrow-right text-info me-1"></i> "Contact bank for IRS code"</li>
                            <li><i class="fas fa-arrow-right text-info me-1"></i> "Enter your tax verification code"</li>
                            <li><i class="fas fa-arrow-right text-info me-1"></i> "Call support for compliance code"</li>
                            <li><i class="fas fa-arrow-right text-info me-1"></i> "Use your security PIN"</li>
                        </ul>
                    </div>
                    <div class="col-md-4">
                        <h5><i class="fas fa-shield-alt text-danger me-2"></i>Security Features</h5>
                        <ul class="list-unstyled">
                            <li><i class="fas fa-exclamation text-danger me-1"></i> Codes are case-sensitive</li>
                            <li><i class="fas fa-exclamation text-danger me-1"></i> Limited attempts (3 max)</li>
                            <li><i class="fas fa-exclamation text-danger me-1"></i> All attempts logged</li>
                            <li><i class="fas fa-exclamation text-danger me-1"></i> 5-minute timeout protection</li>
                        </ul>
                    </div>
                </div>

                <hr class="my-4">

                <div class="alert alert-info">
                    <h6><i class="fas fa-flow-chart me-2"></i>Transfer Flow:</h6>
                    <div class="row">
                        <div class="col-md-6">
                            <strong>User with NO billing codes:</strong><br>
                            <small>Fill form → OTP verification → Transfer processed</small>
                        </div>
                        <div class="col-md-6">
                            <strong>User with billing codes:</strong><br>
                            <small>Fill form → Billing codes → OTP verification → Transfer pending</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<?php endif; ?>

<script>
function clearAllCodes() {
    if (confirm('Are you sure you want to clear all billing codes for this user? This will remove their enhanced security requirement.')) {
        // Clear all form fields
        for (let i = 1; i <= 4; i++) {
            document.querySelector(`input[name="billing_name_${i}"]`).value = '';
            document.querySelector(`input[name="billing_code_${i}"]`).value = '';
            document.querySelector(`textarea[name="billing_description_${i}"]`).value = '';
            document.querySelector(`input[name="is_active_${i}"]`).checked = false;
        }

        // Show confirmation
        alert('All billing codes cleared. Click "Save Billing Codes" to apply changes.');
    }
}
</script>

<?php include 'includes/admin-footer.php'; ?>
