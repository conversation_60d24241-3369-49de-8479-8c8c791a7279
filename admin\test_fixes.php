<?php
/**
 * Test Script for Admin User Management Fixes
 * Tests both OTP generation and suspend user functionality
 */

require_once '../config/config.php';
requireAdmin();

// Test configuration
$test_results = [];

echo "<!DOCTYPE html>
<html>
<head>
    <title>Admin User Management - Fix Testing</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .success { background-color: #d4edda; border-color: #c3e6cb; color: #155724; }
        .error { background-color: #f8d7da; border-color: #f5c6cb; color: #721c24; }
        .info { background-color: #d1ecf1; border-color: #bee5eb; color: #0c5460; }
        .test-button { padding: 8px 16px; margin: 5px; border: none; border-radius: 4px; cursor: pointer; }
        .btn-primary { background-color: #007bff; color: white; }
        .btn-warning { background-color: #ffc107; color: black; }
        .btn-success { background-color: #28a745; color: white; }
        pre { background: #f8f9fa; padding: 10px; border-radius: 4px; overflow-x: auto; }
    </style>
</head>
<body>";

echo "<h1>🔧 Admin User Management Fix Testing</h1>";

// Test 1: Check if files exist
echo "<div class='test-section info'>
<h2>📁 File Existence Check</h2>";

$files_to_check = [
    'ajax/generate-otp.php' => 'AJAX OTP Generation Endpoint',
    'generate-otp.php' => 'Legacy OTP Generation Endpoint', 
    'suspend-user.php' => 'User Suspension Endpoint'
];

foreach ($files_to_check as $file => $description) {
    $exists = file_exists($file);
    $status = $exists ? "✅ EXISTS" : "❌ MISSING";
    echo "<p><strong>$description:</strong> $status ($file)</p>";
    $test_results[$file] = $exists;
}
echo "</div>";

// Test 2: Get sample users for testing
echo "<div class='test-section info'>
<h2>👥 Available Test Users</h2>";

try {
    $db = getDB();
    $users_query = "SELECT id, username, email, first_name, last_name, status 
                    FROM accounts 
                    WHERE is_admin = 0 
                    ORDER BY id 
                    LIMIT 5";
    $users_result = $db->query($users_query);
    
    if ($users_result->num_rows > 0) {
        echo "<table border='1' cellpadding='8' cellspacing='0' style='border-collapse: collapse; width: 100%;'>
        <tr style='background-color: #f8f9fa;'>
            <th>ID</th><th>Username</th><th>Email</th><th>Name</th><th>Status</th><th>Actions</th>
        </tr>";
        
        while ($user = $users_result->fetch_assoc()) {
            $status_class = $user['status'] === 'active' ? 'success' : 'warning';
            echo "<tr>
                <td>{$user['id']}</td>
                <td>{$user['username']}</td>
                <td>{$user['email']}</td>
                <td>{$user['first_name']} {$user['last_name']}</td>
                <td><span class='$status_class'>{$user['status']}</span></td>
                <td>
                    <button class='test-button btn-primary' onclick='testOTPGeneration({$user['id']})'>Test OTP</button>";
            
            if ($user['status'] === 'active') {
                echo "<button class='test-button btn-warning' onclick='testSuspendUser({$user['id']}, \"{$user['username']}\")'>Test Suspend</button>";
            }
            
            echo "</td></tr>";
        }
        echo "</table>";
        $test_results['users_available'] = true;
    } else {
        echo "<p class='error'>❌ No test users available</p>";
        $test_results['users_available'] = false;
    }
} catch (Exception $e) {
    echo "<p class='error'>❌ Database error: " . htmlspecialchars($e->getMessage()) . "</p>";
    $test_results['users_available'] = false;
}

echo "</div>";

// Test 3: OTP Generation Test Interface
echo "<div class='test-section'>
<h2>🔑 OTP Generation Testing</h2>
<p>This tests the fixed OTP generation functionality from users.php</p>
<div id='otp-test-results'></div>
</div>";

// Test 4: Suspend User Test Interface  
echo "<div class='test-section'>
<h2>🚫 Suspend User Testing</h2>
<p>This tests the suspend user functionality</p>
<div id='suspend-test-results'></div>
</div>";

// Test 5: Configuration Check
echo "<div class='test-section info'>
<h2>⚙️ Configuration Check</h2>";

// Check if required functions exist
$functions_to_check = ['generateOTP', 'storeOTP', 'sendOTPEmail', 'logActivity'];
foreach ($functions_to_check as $func) {
    $exists = function_exists($func);
    $status = $exists ? "✅ AVAILABLE" : "❌ MISSING";
    echo "<p><strong>Function $func:</strong> $status</p>";
}

// Check database connection
try {
    $db = getDB();
    echo "<p><strong>Database Connection:</strong> ✅ CONNECTED</p>";
} catch (Exception $e) {
    echo "<p><strong>Database Connection:</strong> ❌ FAILED - " . htmlspecialchars($e->getMessage()) . "</p>";
}

echo "</div>";

// JavaScript for testing
echo "<script>
// Test OTP Generation
function testOTPGeneration(userId) {
    const resultsDiv = document.getElementById('otp-test-results');
    resultsDiv.innerHTML = '<p>🔄 Testing OTP generation for user ID: ' + userId + '...</p>';
    
    // Use the same method as the fixed users.php
    fetch('ajax/generate-otp.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: 'user_id=' + userId
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            resultsDiv.innerHTML = `
                <div class='success'>
                    <h4>✅ OTP Generation Successful!</h4>
                    <p><strong>OTP Code:</strong> <code>${data.otp_code}</code></p>
                    <p><strong>Email Sent:</strong> ${data.email_sent ? 'Yes' : 'No'}</p>
                    <p><strong>User Email:</strong> ${data.user_email}</p>
                    <p><strong>Expires In:</strong> ${data.expires_in}</p>
                </div>
            `;
        } else {
            resultsDiv.innerHTML = `
                <div class='error'>
                    <h4>❌ OTP Generation Failed</h4>
                    <p><strong>Error:</strong> ${data.message}</p>
                </div>
            `;
        }
    })
    .catch(error => {
        resultsDiv.innerHTML = `
            <div class='error'>
                <h4>❌ Network Error</h4>
                <p><strong>Error:</strong> ${error.message}</p>
            </div>
        `;
    });
}

// Test Suspend User
function testSuspendUser(userId, username) {
    if (!confirm('This will actually suspend user \"' + username + '\". Continue?')) {
        return;
    }
    
    const resultsDiv = document.getElementById('suspend-test-results');
    resultsDiv.innerHTML = '<p>🔄 Testing user suspension for: ' + username + '...</p>';
    
    // Test by navigating to suspend-user.php
    window.location.href = 'suspend-user.php?id=' + userId;
}

// Auto-run basic tests
document.addEventListener('DOMContentLoaded', function() {
    // Test AJAX endpoint availability
    fetch('ajax/generate-otp.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: 'user_id=0' // Invalid ID to test endpoint response
    })
    .then(response => response.json())
    .then(data => {
        const otpResults = document.getElementById('otp-test-results');
        if (data.message && data.message.includes('Invalid user ID')) {
            otpResults.innerHTML = '<div class=\"success\">✅ AJAX OTP endpoint is responding correctly</div>';
        } else {
            otpResults.innerHTML = '<div class=\"error\">❌ AJAX OTP endpoint response unexpected</div>';
        }
    })
    .catch(error => {
        const otpResults = document.getElementById('otp-test-results');
        otpResults.innerHTML = '<div class=\"error\">❌ AJAX OTP endpoint not accessible: ' + error.message + '</div>';
    });
});
</script>";

echo "</body></html>";
?>
