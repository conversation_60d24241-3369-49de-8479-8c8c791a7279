<?php
/**
 * Update Primary Color to #206bc4
 */

require_once __DIR__ . '/database.php';

try {
    $db = getDB();
    
    // Update theme_color to #206bc4
    $result = $db->query("UPDATE super_admin_settings SET setting_value = '#206bc4' WHERE setting_key = 'theme_color'");
    
    if ($result) {
        echo "✅ Successfully updated primary theme color to #206bc4\n";
        
        // Verify the update
        $verify = $db->query("SELECT setting_value FROM super_admin_settings WHERE setting_key = 'theme_color'");
        $row = $verify->fetch_assoc();
        echo "🔍 Current theme_color value: " . $row['setting_value'] . "\n";
    } else {
        echo "❌ Failed to update theme color\n";
    }
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
}
?>
