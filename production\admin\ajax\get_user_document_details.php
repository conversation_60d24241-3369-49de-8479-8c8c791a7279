<?php
require_once '../../config/config.php';
requireAdmin();

header('Content-Type: application/json');

try {
    $document_id = intval($_GET['id'] ?? 0);

    if ($document_id <= 0) {
        throw new Exception('Invalid document ID');
    }

    $db = getDB();

    // Get document details using simpler approach
    $query = "SELECT * FROM user_documents WHERE id = ?";
    $result = $db->query($query, [$document_id]);

    if (!$result || $result->num_rows === 0) {
        throw new Exception('Document not found');
    }

    $document = $result->fetch_assoc();

    // Get user info separately
    $user_query = "SELECT first_name, last_name FROM accounts WHERE id = ?";
    $user_result = $db->query($user_query, [$document['user_id']]);
    $user = $user_result ? $user_result->fetch_assoc() : ['first_name' => 'Unknown', 'last_name' => 'User'];

    // Merge user info
    $document['first_name'] = $user['first_name'];
    $document['last_name'] = $user['last_name'];
    $document['mime_type'] = $document['file_type'];

    // Ensure uploaded_at exists
    if (!isset($document['uploaded_at']) || empty($document['uploaded_at'])) {
        $document['uploaded_at'] = $document['created_at'];
    }

    // Add file extension for easier handling in JS
    $document['file_extension'] = strtolower(pathinfo($document['file_path'], PATHINFO_EXTENSION));

    // Ensure file path is web-accessible
    $file_path = $document['file_path'];

    // Remove leading ../ if present
    if (strpos($file_path, '../') === 0) {
        $file_path = substr($file_path, 3);
    }

    // Ensure path starts with / for web access
    if (strpos($file_path, '/') !== 0) {
        $file_path = '/' . $file_path;
    }

    // For admin interface, we need to go up one level from admin folder
    $document['file_path'] = '../' . ltrim($file_path, '/');

    // Log the action (optional, but good practice)
    // error_log("User document details viewed: Document ID {$document_id}, Admin: {$_SESSION['user_id']}");

    echo json_encode([
        'success' => true,
        'document' => $document
    ]);

} catch (Exception $e) {
    error_log("Get user document details error: " . $e->getMessage());

    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ]);
}
?>
