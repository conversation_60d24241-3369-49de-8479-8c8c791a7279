<?php
/**
 * Simple User Creation Test with Email
 */

// Enable error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h1>🏦 User Creation Test</h1>";

// Include required files
require_once 'config/config.php';
require_once 'config/database.php';
require_once 'config/email.php';
require_once 'includes/functions.php';

echo "<h2>📧 Email Configuration</h2>";
echo "SMTP Host: " . SMTP_HOST . "<br>";
echo "SMTP Port: " . SMTP_PORT . "<br>";
echo "From Email: " . FROM_EMAIL . "<br>";

// Test user data
$username = 'demothedev';
$email = '<EMAIL>';
$first_name = 'Demo';
$last_name = 'Developer';
$password = 'DemoPass123!';

echo "<h2>👤 Test User Data</h2>";
echo "Username: $username<br>";
echo "Email: $email<br>";
echo "Name: $first_name $last_name<br>";

// Check if user exists
echo "<h2>🔍 Checking if User Exists</h2>";
try {
    $db = getDB();
    $existing = $db->query("SELECT id FROM accounts WHERE username = ? OR email = ?", [$username, $email]);
    
    if ($existing && $existing->num_rows > 0) {
        echo "⚠️ User already exists. Testing email with existing user...<br>";
        
        // Get existing user data
        $user = $db->query("SELECT * FROM accounts WHERE username = ? OR email = ?", [$username, $email])->fetch_assoc();
        
        $user_data = [
            'first_name' => $user['first_name'],
            'last_name' => $user['last_name'],
            'username' => $user['username'],
            'email' => $user['email'],
            'account_number' => $user['account_number'],
            'account_type' => $user['account_type'],
            'currency' => $user['currency'],
            'balance' => $user['balance'],
            'status' => $user['status']
        ];
        
        echo "<h2>📧 Testing Welcome Email</h2>";
        $result = sendWelcomeEmail($user['email'], $user_data);
        
        if ($result) {
            echo "<div style='background: #d4edda; padding: 15px; border-radius: 5px; color: #155724;'>";
            echo "✅ Welcome email sent successfully to $email<br>";
            echo "Check your inbox!";
            echo "</div>";
        } else {
            echo "<div style='background: #f8d7da; padding: 15px; border-radius: 5px; color: #721c24;'>";
            echo "❌ Welcome email failed to send";
            echo "</div>";
        }
        
    } else {
        echo "✅ User doesn't exist. Creating new user...<br>";
        
        echo "<h2>🚀 Creating User</h2>";
        
        // Generate account number
        $account_number = generateAccountNumber();
        echo "Account Number: $account_number<br>";
        
        // Hash password
        $hashed_password = hashPassword($password);
        echo "Password hashed successfully<br>";
        
        // Insert user
        $sql = "INSERT INTO accounts (
                    account_number, username, password, email, first_name, last_name,
                    phone, address, currency, account_type, balance, status, kyc_status, is_admin
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, 0)";

        $params = [
            $account_number, $username, $hashed_password, $email, $first_name, $last_name,
            '******-0123', '123 Demo Street', 'USD', 'savings', 5000.00, 'active', 'pending'
        ];
        
        $user_id = $db->insert($sql, $params);
        
        if ($user_id) {
            echo "✅ User created with ID: $user_id<br>";
            
            // Prepare welcome email data
            $user_data = [
                'first_name' => $first_name,
                'last_name' => $last_name,
                'username' => $username,
                'email' => $email,
                'account_number' => $account_number,
                'account_type' => 'savings',
                'currency' => 'USD',
                'balance' => 5000.00,
                'status' => 'active'
            ];
            
            echo "<h2>📧 Sending Welcome Email</h2>";
            $result = sendWelcomeEmail($email, $user_data);
            
            if ($result) {
                echo "<div style='background: #d4edda; padding: 20px; border-radius: 5px; color: #155724; margin: 20px 0;'>";
                echo "<h3>🎉 SUCCESS!</h3>";
                echo "<p>✅ User created successfully</p>";
                echo "<p>✅ Welcome email sent to $email</p>";
                echo "<p><strong>Login Details:</strong></p>";
                echo "<ul>";
                echo "<li>Username: $username</li>";
                echo "<li>Password: $password</li>";
                echo "<li>Account Number: $account_number</li>";
                echo "</ul>";
                echo "<p><strong>Check your <NAME_EMAIL>!</strong></p>";
                echo "</div>";
            } else {
                echo "<div style='background: #fff3cd; padding: 15px; border-radius: 5px; color: #856404;'>";
                echo "⚠️ User created but welcome email failed to send";
                echo "</div>";
            }
        } else {
            echo "❌ Failed to create user<br>";
        }
    }
    
} catch (Exception $e) {
    echo "<div style='background: #f8d7da; padding: 15px; border-radius: 5px; color: #721c24;'>";
    echo "❌ Error: " . $e->getMessage();
    echo "</div>";
}

// Test OTP email
echo "<h2>🔐 Testing OTP Email</h2>";
try {
    $otp = generateOTP();
    echo "Generated OTP: <strong>$otp</strong><br>";
    
    $otp_result = sendOTPEmail($email, $otp, $first_name);
    
    if ($otp_result) {
        echo "<div style='background: #d4edda; padding: 15px; border-radius: 5px; color: #155724;'>";
        echo "✅ OTP email sent successfully<br>";
        echo "OTP Code: <strong>$otp</strong>";
        echo "</div>";
    } else {
        echo "<div style='background: #f8d7da; padding: 15px; border-radius: 5px; color: #721c24;'>";
        echo "❌ OTP email failed to send";
        echo "</div>";
    }
} catch (Exception $e) {
    echo "❌ OTP Error: " . $e->getMessage() . "<br>";
}

echo "<hr>";
echo "<h2>📊 Summary</h2>";
echo "<p>✅ Email system configured with Port 465 (SSL)</p>";
echo "<p>✅ User creation process tested</p>";
echo "<p>✅ Welcome and OTP emails tested</p>";
echo "<p><strong>Check your <NAME_EMAIL> for test emails!</strong></p>";
echo "<p><strong>Test completed at:</strong> " . date('Y-m-d H:i:s') . "</p>";
?>
