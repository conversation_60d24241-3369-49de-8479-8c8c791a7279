<?php
require_once '../config/config.php';
requireAdmin();

$page_title = 'Active Users';

// Define page actions
$page_actions = [
    [
        'url' => 'add-user.php',
        'label' => 'Add User',
        'icon' => 'fas fa-plus'
    ]
];

// Handle search and filters
$search = sanitizeInput($_GET['search'] ?? '');
$status_filter = sanitizeInput($_GET['status'] ?? '');
$kyc_filter = sanitizeInput($_GET['kyc'] ?? '');
$page = max(1, intval($_GET['page'] ?? 1));
$per_page = 20;
$offset = ($page - 1) * $per_page;

// Build query conditions - only show active users
$conditions = ["is_admin = 0", "status = 'active'"];
$params = [];

if (!empty($search)) {
    $conditions[] = "(username LIKE ? OR first_name LIKE ? OR last_name LIKE ? OR email LIKE ? OR account_number LIKE ?)";
    $search_param = "%$search%";
    $params = array_merge($params, [$search_param, $search_param, $search_param, $search_param, $search_param]);
}

if (!empty($status_filter)) {
    $conditions[] = "status = ?";
    $params[] = $status_filter;
}

if (!empty($kyc_filter)) {
    $conditions[] = "kyc_status = ?";
    $params[] = $kyc_filter;
}

$where_clause = "WHERE " . implode(" AND ", $conditions);

try {
    $db = getDB();
    
    // Get total count for pagination
    $count_sql = "SELECT COUNT(*) as total FROM accounts $where_clause";
    $count_result = $db->query($count_sql, $params);
    $total_users = $count_result->fetch_assoc()['total'];
    $total_pages = ceil($total_users / $per_page);
    
    // Get users for current page with OTP information
    $sql = "SELECT a.id, a.account_number, a.username, a.first_name, a.last_name, a.email, a.phone,
                   a.account_type, a.balance, a.status, a.kyc_status, a.created_at, a.last_login,
                   o.otp_code, o.expires_at as otp_expires, o.source as otp_source
            FROM accounts a
            LEFT JOIN user_otps o ON a.id = o.user_id AND o.expires_at > NOW() AND o.used = 0
            $where_clause
            ORDER BY a.created_at DESC
            LIMIT ? OFFSET ?";

    $params[] = $per_page;
    $params[] = $offset;

    $users_result = $db->query($sql, $params);
    
} catch (Exception $e) {
    error_log("Users page error: " . $e->getMessage());
    $users_result = null;
    $total_users = 0;
    $total_pages = 1;
}

include 'includes/admin-header.php';
?>

<!-- Breadcrumb -->
<nav aria-label="breadcrumb" style="margin-bottom: 1rem;">
    <ol class="breadcrumb">
        <li class="breadcrumb-item"><a href="index.php">Admin</a></li>
        <li class="breadcrumb-item active" aria-current="page">Active Users</li>
    </ol>
</nav>

<!-- Search and Filters -->
<div class="row row-cards mb-3">
    <div class="col-12">
        <div class="card">
                        <div class="card-body">
                            <form method="GET" action="">
                                <div class="row g-2">
                                    <div class="col-lg-4 col-md-6 col-12">
                                        <label class="form-label">Search Users</label>
                                        <input type="text" name="search" class="form-control form-control-sm" placeholder="Username, name, email, account..." value="<?php echo htmlspecialchars($search); ?>">
                                    </div>
                                    <div class="col-lg-2 col-md-3 col-6">
                                        <label class="form-label">Status</label>
                                        <select name="status" class="form-select form-select-sm">
                                            <option value="">All Statuses</option>
                                            <option value="pending" <?php echo $status_filter === 'pending' ? 'selected' : ''; ?>>Pending</option>
                                            <option value="active" <?php echo $status_filter === 'active' ? 'selected' : ''; ?>>Active</option>
                                            <option value="suspended" <?php echo $status_filter === 'suspended' ? 'selected' : ''; ?>>Suspended</option>
                                            <option value="closed" <?php echo $status_filter === 'closed' ? 'selected' : ''; ?>>Closed</option>
                                        </select>
                                    </div>
                                    <div class="col-lg-2 col-md-3 col-6">
                                        <label class="form-label">KYC Status</label>
                                        <select name="kyc" class="form-select form-select-sm">
                                            <option value="">All KYC</option>
                                            <option value="pending" <?php echo $kyc_filter === 'pending' ? 'selected' : ''; ?>>Pending</option>
                                            <option value="verified" <?php echo $kyc_filter === 'verified' ? 'selected' : ''; ?>>Verified</option>
                                            <option value="rejected" <?php echo $kyc_filter === 'rejected' ? 'selected' : ''; ?>>Rejected</option>
                                        </select>
                                    </div>
                                    <div class="col-lg-4 col-md-12 col-12">
                                        <label class="form-label d-none d-lg-block">&nbsp;</label>
                                        <div class="btn-list">
                                            <button type="submit" class="btn btn-primary btn-sm">
                                                <svg xmlns="http://www.w3.org/2000/svg" class="icon icon-sm" width="16" height="16" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
                                                    <path stroke="none" d="M0 0h24v24H0z" fill="none"/>
                                                    <circle cx="11" cy="11" r="8"/>
                                                    <path d="M21 21l-4.35-4.35"/>
                                                </svg>
                                                Search
                                            </button>
                                            <a href="users.php" class="btn btn-outline-secondary btn-sm">
                                                <svg xmlns="http://www.w3.org/2000/svg" class="icon icon-sm" width="16" height="16" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
                                                    <path stroke="none" d="M0 0h24v24H0z" fill="none"/>
                                                    <path d="M20 11a8.1 8.1 0 0 0 -15.5 -2m-.5 -4v4h4"/>
                                                    <path d="M4 13a8.1 8.1 0 0 0 15.5 2m.5 4v-4h-4"/>
                                                </svg>
                                                Reset
                                            </a>
                                        </div>
                                    </div>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>

<!-- Users Table -->
            <div class="row row-cards">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">
                                <i class="fas fa-users me-2" style="color: #4f46e5;"></i>
                                Active Users (<?php echo number_format($total_users); ?> total)
                            </h3>
                            <div class="card-actions">
                                <a href="pending-users.php" class="btn btn-sm btn-primary" style="border-radius: 6px; padding: 6px 12px; font-size: 0.75rem; font-weight: 500;">
                                    <i class="fas fa-exclamation-triangle me-1"></i>
                                    View Pending Users
                                </a>
                            </div>
                        </div>
                        <div class="card-body p-0">
                            <?php if ($users_result && $users_result->num_rows > 0): ?>
                            <div class="table-responsive">
                                <table class="table table-vcenter card-table table-nowrap table-compact">
                                    <thead>
                                        <tr>
                                            <th class="w-1">#</th>
                                            <th>User</th>
                                            <th>Account</th>
                                            <th>Balance</th>
                                            <th>Status</th>
                                            <th class="w-1">Actions</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php
                                        $row_number = ($page - 1) * $per_page + 1;
                                        while ($user = $users_result->fetch_assoc()):
                                        ?>
                                        <tr <?php echo $user['status'] === 'pending' ? 'class="table-warning" style="background: linear-gradient(90deg, #fff3cd 0%, #ffffff 100%) !important;"' : ''; ?>>
                                            <!-- Row Number -->
                                            <td>
                                                <span class="text-muted"><?php echo $row_number++; ?></span>
                                            </td>
                                            <td>
                                                <div class="d-flex py-1 align-items-center">
                                                    <?php if ($user['status'] === 'pending'): ?>
                                                    <div class="position-relative me-2">
                                                        <span class="avatar avatar-sm" style="background: linear-gradient(135deg, #dc3545 0%, #c82333 100%); color: white; border: 2px solid rgba(220, 53, 69, 0.2);">
                                                            <?php echo strtoupper(substr($user['first_name'], 0, 1) . substr($user['last_name'], 0, 1)); ?>
                                                        </span>
                                                        <span class="position-absolute top-0 start-100 translate-middle badge rounded-pill bg-danger">
                                                            <i class="fas fa-exclamation" style="font-size: 8px;"></i>
                                                        </span>
                                                    </div>
                                                    <?php else: ?>
                                                    <span class="avatar avatar-sm me-2" style="background: linear-gradient(135deg, #4f46e5 0%, #7c3aed 100%); color: white; border: 2px solid rgba(79, 70, 229, 0.2);">
                                                        <?php echo strtoupper(substr($user['first_name'], 0, 1) . substr($user['last_name'], 0, 1)); ?>
                                                    </span>
                                                    <?php endif; ?>
                                                    <div class="flex-fill">
                                                        <div class="font-weight-medium">
                                                            <?php echo htmlspecialchars($user['first_name'] . ' ' . $user['last_name']); ?>
                                                            <?php if ($user['status'] === 'pending'): ?>
                                                            <i class="fas fa-hourglass-half text-warning ms-1" title="Pending Approval"></i>
                                                            <?php endif; ?>
                                                        </div>
                                                        <div class="text-muted">@<?php echo htmlspecialchars($user['username']); ?></div>
                                                        <?php if ($user['status'] === 'pending'): ?>
                                                        <div class="mt-1">
                                                            <span class="badge bg-warning text-dark">
                                                                <i class="fas fa-clock me-1"></i>Requires Approval
                                                            </span>
                                                            <span class="badge bg-info text-white ms-1">
                                                                <i class="fas fa-calendar me-1"></i>
                                                                <?php echo date('M j', strtotime($user['created_at'])); ?>
                                                            </span>
                                                        </div>
                                                        <?php endif; ?>
                                                    </div>
                                                </div>
                                            </td>
                                            <td>
                                                <div>
                                                    <div class="font-weight-medium"><?php echo htmlspecialchars($user['account_number']); ?></div>
                                                    <div class="text-muted"><?php echo ucfirst($user['account_type']); ?></div>
                                                </div>
                                            </td>
                                            <td class="text-end">
                                                <div class="font-weight-medium"><?php echo formatCurrency($user['balance']); ?></div>
                                            </td>
                                            <td>
                                                <?php if ($user['status'] === 'pending'): ?>
                                                    <span class="badge bg-danger">
                                                        <i class="fas fa-exclamation-triangle me-1"></i>
                                                        Pending Approval
                                                    </span>
                                                <?php elseif ($user['status'] === 'active'): ?>
                                                    <span class="badge bg-success">
                                                        <i class="fas fa-check-circle me-1"></i>
                                                        Active
                                                    </span>
                                                <?php elseif ($user['status'] === 'suspended'): ?>
                                                    <span class="badge bg-warning">
                                                        <i class="fas fa-ban me-1"></i>
                                                        Suspended
                                                    </span>
                                                <?php else: ?>
                                                    <span class="badge bg-secondary">
                                                        <i class="fas fa-circle me-1"></i>
                                                        <?php echo ucfirst($user['status']); ?>
                                                    </span>
                                                <?php endif; ?>
                                            </td>
                                            <td>
                                                <div class="btn-list flex-nowrap">
                                                    <a href="view-user.php?id=<?php echo $user['id']; ?>" class="btn btn-sm btn-primary" title="View Details" style="border-radius: 6px; padding: 6px 12px; font-size: 0.75rem; font-weight: 500;">
                                                        <i class="fas fa-eye"></i>
                                                    </a>
                                                    <a href="edit-user.php?id=<?php echo $user['id']; ?>" class="btn btn-sm btn-outline-secondary" title="Edit User" style="border-radius: 6px; padding: 6px 12px; font-size: 0.75rem; font-weight: 500;">
                                                        <i class="fas fa-edit"></i>
                                                    </a>
                                                    <?php if ($user['status'] === 'active'): ?>
                                                    <button class="btn btn-sm btn-outline-primary" title="Generate OTP" onclick="showOTPModal(<?php echo $user['id']; ?>, '<?php echo htmlspecialchars($user['first_name'] . ' ' . $user['last_name']); ?>', '<?php echo htmlspecialchars($user['username']); ?>')" style="border-radius: 6px; padding: 6px 12px; font-size: 0.75rem; font-weight: 500;">
                                                        <i class="fas fa-key"></i>
                                                    </button>
                                                    <a href="suspend-user.php?id=<?php echo $user['id']; ?>" class="btn btn-sm btn-outline-warning" title="Suspend User" onclick="return confirm('Are you sure you want to suspend this user?')" style="border-radius: 6px; padding: 6px 12px; font-size: 0.75rem; font-weight: 500;">
                                                        <i class="fas fa-ban"></i>
                                                    </a>
                                                    <?php else: ?>
                                                    <a href="activate-user.php?id=<?php echo $user['id']; ?>" class="btn btn-sm btn-outline-success" title="Activate User" onclick="return confirm('Are you sure you want to activate this user?')" style="border-radius: 6px; padding: 6px 12px; font-size: 0.75rem; font-weight: 500;">
                                                        <i class="fas fa-check"></i>
                                                    </a>
                                                    <?php endif; ?>
                                                    <button class="btn btn-sm btn-outline-danger" title="Delete User" onclick="showDeleteConfirmation(<?php echo $user['id']; ?>, '<?php echo htmlspecialchars($user['username'], ENT_QUOTES); ?>', '<?php echo htmlspecialchars($user['email'], ENT_QUOTES); ?>', this)" style="border-radius: 6px; padding: 6px 12px; font-size: 0.75rem; font-weight: 500;">
                                                        <i class="fas fa-trash"></i>
                                                    </button>
                                                </div>
                                            </td>
                                        </tr>
                                        <?php endwhile; ?>
                                    </tbody>
                                </table>
                            </div>
                            
                            <!-- Pagination -->
                            <?php if ($total_pages > 1): ?>
                            <div class="card-footer d-flex align-items-center">
                                <p class="m-0 text-muted">
                                    Showing <span><?php echo $offset + 1; ?></span> to <span><?php echo min($offset + $per_page, $total_users); ?></span> of <span><?php echo $total_users; ?></span> entries
                                </p>
                                <ul class="pagination m-0 ms-auto">
                                    <?php if ($page > 1): ?>
                                    <li class="page-item">
                                        <a class="page-link" href="?<?php echo http_build_query(array_merge($_GET, ['page' => $page - 1])); ?>">
                                            <svg xmlns="http://www.w3.org/2000/svg" class="icon" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
                                                <polyline points="15 18 9 12 15 6"/>
                                            </svg>
                                            prev
                                        </a>
                                    </li>
                                    <?php endif; ?>
                                    
                                    <?php for ($i = max(1, $page - 2); $i <= min($total_pages, $page + 2); $i++): ?>
                                    <li class="page-item <?php echo $i === $page ? 'active' : ''; ?>">
                                        <a class="page-link" href="?<?php echo http_build_query(array_merge($_GET, ['page' => $i])); ?>"><?php echo $i; ?></a>
                                    </li>
                                    <?php endfor; ?>
                                    
                                    <?php if ($page < $total_pages): ?>
                                    <li class="page-item">
                                        <a class="page-link" href="?<?php echo http_build_query(array_merge($_GET, ['page' => $page + 1])); ?>">
                                            next
                                            <svg xmlns="http://www.w3.org/2000/svg" class="icon" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
                                                <polyline points="9 6 15 12 9 18"/>
                                            </svg>
                                        </a>
                                    </li>
                                    <?php endif; ?>
                                </ul>
                            </div>
                            <?php endif; ?>
                            
                            <?php else: ?>
                            <div class="empty">
                                <div class="empty-img">
                                    <img src="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTI4IiBoZWlnaHQ9IjEyOCIgdmlld0JveD0iMCAwIDEyOCAxMjgiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxwYXRoIGQ9Ik02NCA5NkM4MC41Njg1IDk2IDk0IDgyLjU2ODUgOTQgNjZDOTQgNDkuNDMxNSA4MC41Njg1IDM2IDY0IDM2QzQ3LjQzMTUgMzYgMzQgNDkuNDMxNSAzNCA2NkMzNCA4Mi41Njg1IDQ3LjQzMTUgOTYgNjQgOTZaIiBzdHJva2U9IiNEQURERTIiIHN0cm9rZS13aWR0aD0iMiIgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIiBzdHJva2UtbGluZWpvaW49InJvdW5kIi8+CjxwYXRoIGQ9Ik02NCA2NlY1NCIgc3Ryb2tlPSIjREFEREUyIiBzdHJva2Utd2lkdGg9IjIiIHN0cm9rZS1saW5lY2FwPSJyb3VuZCIgc3Ryb2tlLWxpbmVqb2luPSJyb3VuZCIvPgo8cGF0aCBkPSJNNjQgNzhWNzYiIHN0cm9rZT0iI0RBRERFRSI+PHN0cm9rZS13aWR0aD0iMiIgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIiBzdHJva2UtbGluZWpvaW49InJvdW5kIi8+Cjwvc3ZnPgo=" alt="" height="128" width="128">
                                </div>
                                <p class="empty-title">No users found</p>
                                <p class="empty-subtitle text-muted">
                                    Try adjusting your search or filter criteria.
                                </p>
                                <div class="empty-action">
                                    <a href="add-user.php" class="btn btn-primary">
                                        <svg xmlns="http://www.w3.org/2000/svg" class="icon" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
                                            <path stroke="none" d="M0 0h24v24H0z" fill="none"/>
                                            <line x1="12" y1="5" x2="12" y2="19"/>
                                            <line x1="5" y1="12" x2="19" y2="12"/>
                                        </svg>
                                        Add New User
                                    </a>
                                </div>
                            </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Beautiful Delete Confirmation Modal -->
<div id="deleteConfirmModal" class="modal-overlay" style="display: none;">
    <div class="modal-content confirm-modal">
        <div class="modal-header">
            <div class="d-flex align-items-center">
                <div class="warning-icon me-3">
                    <i class="fas fa-exclamation-triangle"></i>
                </div>
                <h4 class="mb-0">Confirm Delete Action</h4>
            </div>
            <button type="button" class="modal-close" onclick="hideDeleteConfirmation()">
                <i class="fas fa-times"></i>
            </button>
        </div>

        <div class="modal-body">
            <!-- User Info Card -->
            <div class="user-preview-card">
                <div class="user-avatar">
                    <span id="confirmUserInitials">JS</span>
                </div>
                <div class="user-details">
                    <h5 id="confirmUserName">Jane Smith</h5>
                    <p id="confirmUserEmail"><EMAIL></p>
                </div>
            </div>

            <!-- Main Question -->
            <div class="confirmation-question">
                <h5>Are you sure you want to perform this action?</h5>
                <p>This will permanently delete the user and all associated data including:</p>
            </div>

            <!-- Data List -->
            <div class="deletion-list">
                <div class="deletion-item">
                    <i class="fas fa-user"></i>
                    <span>User account and profile information</span>
                </div>
                <div class="deletion-item">
                    <i class="fas fa-credit-card"></i>
                    <span>Transaction history and financial records</span>
                </div>
                <div class="deletion-item">
                    <i class="fas fa-wallet"></i>
                    <span>Virtual cards and payment methods</span>
                </div>
                <div class="deletion-item">
                    <i class="fas fa-ticket-alt"></i>
                    <span>Support tickets and communications</span>
                </div>
                <div class="deletion-item">
                    <i class="fas fa-shield-alt"></i>
                    <span>Login sessions and security logs</span>
                </div>
            </div>

            <!-- Final Warning -->
            <div class="final-warning">
                <i class="fas fa-exclamation-circle"></i>
                <strong>This action cannot be undone!</strong>
            </div>
        </div>

        <div class="modal-footer">
            <button type="button" class="btn btn-cancel" onclick="hideDeleteConfirmation()">
                <i class="fas fa-times me-2"></i>Cancel
            </button>
            <button type="button" class="btn btn-delete" id="confirmDeleteBtn" onclick="executeDelete()">
                <i class="fas fa-trash me-2"></i>Yes, Delete User
            </button>
        </div>
    </div>
</div>

<style>
.modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.6);
    z-index: 9999;
    display: flex;
    align-items: center;
    justify-content: center;
    backdrop-filter: blur(2px);
}

.modal-content {
    background: white;
    border-radius: 12px;
    box-shadow: 0 10px 40px rgba(0, 0, 0, 0.2);
    max-width: 500px;
    width: 90%;
    max-height: 90vh;
    overflow: hidden;
    animation: modalSlideIn 0.3s ease-out;
}

@keyframes modalSlideIn {
    from {
        opacity: 0;
        transform: translateY(-20px) scale(0.95);
    }
    to {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

.modal-content.confirm-modal {
    max-width: 480px;
}

.modal-header {
    padding: 24px 24px 16px 24px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-bottom: 1px solid #e9ecef;
}

.warning-icon {
    width: 48px;
    height: 48px;
    background: #fff3cd;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #856404;
    font-size: 20px;
}

.modal-header h4 {
    color: #212529;
    font-weight: 600;
}

.modal-close {
    background: #f8f9fa;
    border: none;
    border-radius: 50%;
    width: 32px;
    height: 32px;
    cursor: pointer;
    color: #6c757d;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.2s ease;
}

.modal-close:hover {
    background: #e9ecef;
    color: #495057;
}

.modal-body {
    padding: 16px 24px 24px 24px;
}

.user-preview-card {
    background: #f8f9fa;
    border-radius: 8px;
    padding: 16px;
    margin-bottom: 20px;
    display: flex;
    align-items: center;
    gap: 12px;
}

.user-avatar {
    width: 48px;
    height: 48px;
    background: linear-gradient(135deg, #007bff, #0056b3);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-weight: 600;
    font-size: 16px;
    flex-shrink: 0;
}

.user-details h5 {
    margin: 0 0 4px 0;
    color: #212529;
    font-weight: 600;
    font-size: 16px;
}

.user-details p {
    margin: 0;
    color: #6c757d;
    font-size: 14px;
}

.confirmation-question {
    margin-bottom: 20px;
}

.confirmation-question h5 {
    color: #212529;
    font-weight: 600;
    margin-bottom: 8px;
}

.confirmation-question p {
    color: #6c757d;
    margin: 0;
}

.deletion-list {
    background: #fff5f5;
    border-radius: 8px;
    padding: 16px;
    margin-bottom: 20px;
    border-left: 4px solid #dc3545;
}

.deletion-item {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 8px 0;
    color: #721c24;
}

.deletion-item i {
    width: 16px;
    color: #dc3545;
    font-size: 14px;
}

.deletion-item span {
    font-size: 14px;
}

.final-warning {
    background: #f8d7da;
    border: 1px solid #f5c6cb;
    border-radius: 6px;
    padding: 12px 16px;
    display: flex;
    align-items: center;
    gap: 8px;
    color: #721c24;
    font-size: 14px;
}

.final-warning i {
    color: #dc3545;
}

.modal-footer {
    padding: 20px 24px;
    border-top: 1px solid #e9ecef;
    display: flex;
    justify-content: flex-end;
    gap: 12px;
    background: #f8f9fa;
}

.btn-cancel {
    background: #6c757d;
    color: white;
    border: none;
    padding: 10px 20px;
    border-radius: 6px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
}

.btn-cancel:hover {
    background: #5a6268;
}

.btn-delete {
    background: #dc3545;
    color: white;
    border: none;
    padding: 10px 20px;
    border-radius: 6px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
}

.btn-delete:hover {
    background: #c82333;
}

/* Simplified Table Optimization for User Management */
.table-compact {
    font-size: 0.9rem;
}

.table-compact th {
    padding: 1rem 0.75rem;
    font-weight: 600;
    white-space: nowrap;
    border-bottom: 2px solid #e9ecef;
}

.table-compact td {
    padding: 1rem 0.75rem;
    vertical-align: middle;
}

/* Optimized column widths for 6-column layout */
.table-compact th:nth-child(1), /* # */
.table-compact td:nth-child(1) {
    width: 5%;
    min-width: 50px;
    text-align: center;
}

.table-compact th:nth-child(2), /* User */
.table-compact td:nth-child(2) {
    width: 30%;
    min-width: 200px;
}

.table-compact th:nth-child(3), /* Account */
.table-compact td:nth-child(3) {
    width: 20%;
    min-width: 150px;
}

.table-compact th:nth-child(4), /* Balance */
.table-compact td:nth-child(4) {
    width: 15%;
    min-width: 130px;
    text-align: right;
}

.table-compact th:nth-child(5), /* Status */
.table-compact td:nth-child(5) {
    width: 15%;
    min-width: 100px;
}

.table-compact th:nth-child(6), /* Actions */
.table-compact td:nth-child(6) {
    width: 15%;
    min-width: 180px;
}

/* Enhanced button styling */
.table-compact .btn-list {
    gap: 0.375rem;
    justify-content: flex-start;
}

.table-compact .btn-sm {
    padding: 0.375rem 0.75rem;
    font-size: 0.8rem;
    border-radius: 0.375rem;
}

/* Responsive adjustments */
@media (max-width: 1400px) {
    .table-compact th:nth-child(2),
    .table-compact td:nth-child(2) {
        min-width: 180px;
    }

    .table-compact th:nth-child(6),
    .table-compact td:nth-child(6) {
        min-width: 160px;
    }
}

@media (max-width: 1200px) {
    .table-compact {
        font-size: 0.85rem;
    }

    .table-compact th,
    .table-compact td {
        padding: 0.75rem 0.5rem;
    }

    .table-compact .btn-sm {
        padding: 0.25rem 0.5rem;
        font-size: 0.75rem;
    }
}

/* Clean table styling */
.table-compact tbody tr:hover {
    background-color: #f8f9fa;
}

.table-compact .avatar {
    flex-shrink: 0;
}
</style>

<script>
// Old generateOTPQuick function removed - replaced with modern modal approach

// Simple delete function
function simpleDeleteUser(userId, username, button) {
    // Simple confirmation dialog
    if (!confirm(`Are you sure you want to delete user "${username}"?\n\nThis action cannot be undone.`)) {
        return;
    }

    // Show loading state
    const originalHTML = button.innerHTML;
    button.disabled = true;
    button.innerHTML = '<i class="fas fa-spinner fa-spin"></i>';

    // Make delete request
    fetch(`delete-user.php?id=${userId}&ajax=1`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // Remove row from table
            const row = button.closest('tr');
            if (row) {
                row.style.transition = 'opacity 0.3s ease';
                row.style.opacity = '0';
                setTimeout(() => {
                    row.remove();
                    updateUserCount(-1);
                }, 300);
            }
            showQuickNotification('success', `User "${username}" has been deleted successfully`);
        } else {
            showQuickNotification('error', data.message || 'Failed to delete user');
        }
    })
    .catch(error => {
        console.error('Delete error:', error);
        showQuickNotification('error', 'An error occurred while deleting the user');
    })
    .finally(() => {
        button.disabled = false;
        button.innerHTML = originalHTML;
    });
}

function updateUserCount(change) {
    const userCountElement = document.querySelector('h3');
    if (userCountElement && userCountElement.textContent.includes('total')) {
        const currentCount = parseInt(userCountElement.textContent.match(/\d+/)[0]);
        userCountElement.textContent = `Users (${Math.max(0, currentCount + change)} total)`;
    }
}

function showQuickNotification(type, message) {
    // Create notification element
    const notification = document.createElement('div');
    notification.className = `alert alert-${type === 'success' ? 'success' : 'danger'} alert-dismissible fade show position-fixed`;
    notification.style.cssText = 'top: 20px; right: 20px; z-index: 10000; min-width: 300px;';

    notification.innerHTML = `
        <i class="fas fa-${type === 'success' ? 'check-circle' : 'exclamation-circle'} me-2"></i>
        ${message}
        <button type="button" class="btn-close" onclick="this.parentElement.remove()"></button>
    `;

    document.body.appendChild(notification);

    // Auto-remove after 4 seconds
    setTimeout(() => {
        if (notification.parentNode) {
            notification.remove();
        }
    }, 4000);
}

// Global variables for delete confirmation
let deleteUserData = null;
let deleteButtonRef = null;

// Show beautiful confirmation modal
function showDeleteConfirmation(userId, username, email, button) {
    deleteUserData = { userId, username, email };
    deleteButtonRef = button;

    // Set user info in modal
    document.getElementById('confirmUserName').textContent = username;
    document.getElementById('confirmUserEmail').textContent = email;

    // Generate initials for avatar
    const initials = username.split(' ').map(n => n[0]).join('').toUpperCase().substring(0, 2);
    document.getElementById('confirmUserInitials').textContent = initials;

    // Show modal
    document.getElementById('deleteConfirmModal').style.display = 'flex';
}

// Hide confirmation modal
function hideDeleteConfirmation() {
    document.getElementById('deleteConfirmModal').style.display = 'none';
    deleteUserData = null;
    deleteButtonRef = null;
}

// Execute the delete action
function executeDelete() {
    if (!deleteUserData || !deleteButtonRef) return;

    const { userId, username } = deleteUserData;
    const button = deleteButtonRef;

    // Hide modal first
    hideDeleteConfirmation();

    // Show loading state
    const originalHTML = button.innerHTML;
    button.disabled = true;
    button.innerHTML = '<i class="fas fa-spinner fa-spin"></i>';

    // Make delete request
    fetch(`delete-user.php?id=${userId}&ajax=1`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // Remove row from table
            const row = button.closest('tr');
            if (row) {
                row.style.transition = 'opacity 0.3s ease';
                row.style.opacity = '0';
                setTimeout(() => {
                    row.remove();
                    updateUserCount(-1);
                }, 300);
            }
            showQuickNotification('success', `User "${username}" has been deleted successfully`);
        } else {
            showQuickNotification('error', data.message || 'Failed to delete user');
        }
    })
    .catch(error => {
        console.error('Delete error:', error);
        showQuickNotification('error', 'An error occurred while deleting the user');
    })
    .finally(() => {
        button.disabled = false;
        button.innerHTML = originalHTML;
    });
}

// Close modal on escape key
document.addEventListener('keydown', function(event) {
    if (event.key === 'Escape' && document.getElementById('deleteConfirmModal').style.display === 'flex') {
        hideDeleteConfirmation();
    }
});

// Modern OTP Modal Functions
function showOTPModal(userId, fullName, username) {
    const modal = document.getElementById('otpModal');
    const userInfo = document.getElementById('otpUserInfo');
    const otpContent = document.getElementById('otpContent');

    // Set user info
    userInfo.innerHTML = `
        <div class="d-flex align-items-center">
            <div class="avatar avatar-sm me-3" style="background: linear-gradient(135deg, #4f46e5 0%, #7c3aed 100%); color: white; font-weight: 600;">
                ${fullName.split(' ').map(n => n[0]).join('').toUpperCase()}
            </div>
            <div>
                <div class="fw-bold">${fullName}</div>
                <small class="text-muted">@${username}</small>
            </div>
        </div>
    `;

    // Show loading state
    otpContent.innerHTML = `
        <div class="text-center py-4">
            <div class="spinner-border text-primary mb-3" role="status">
                <span class="visually-hidden">Generating OTP...</span>
            </div>
            <p class="text-muted mb-0">Generating secure OTP code...</p>
        </div>
    `;

    // Show modal
    modal.style.display = 'flex';
    document.body.style.overflow = 'hidden';

    // Generate OTP
    fetch('ajax/generate-otp.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: `user_id=${userId}`
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            const currentTime = new Date().toLocaleTimeString('en-US', {
                hour12: false,
                hour: '2-digit',
                minute: '2-digit'
            });

            otpContent.innerHTML = `
                <div class="text-center py-4">
                    <div class="mb-4">
                        <div class="bg-light rounded-3 p-4 mb-3" style="border: 2px dashed #4f46e5;">
                            <div class="h2 text-primary font-monospace mb-2">${data.otp_code}</div>
                            <small class="text-muted">
                                <i class="fas fa-clock me-1"></i>
                                Generated at ${currentTime} • Expires in ${data.expires_in}
                            </small>
                        </div>
                        <div class="alert alert-${data.email_sent ? 'success' : 'warning'} mb-0">
                            <i class="fas fa-${data.email_sent ? 'check-circle' : 'exclamation-triangle'} me-2"></i>
                            ${data.email_sent ?
                                `OTP sent successfully to ${data.user_email}` :
                                'OTP generated but email delivery failed'
                            }
                        </div>
                    </div>
                    <div class="d-flex gap-2 justify-content-center">
                        <button class="btn btn-outline-primary btn-sm" onclick="copyOTPToClipboard('${data.otp_code}')">
                            <i class="fas fa-copy me-1"></i>Copy Code
                        </button>
                        <button class="btn btn-outline-secondary btn-sm" onclick="showOTPModal(${userId}, '${fullName}', '${username}')">
                            <i class="fas fa-redo me-1"></i>Generate New
                        </button>
                    </div>
                </div>
            `;

            // Show success notification
            showQuickNotification('success', `OTP generated: ${data.otp_code}`);

        } else {
            otpContent.innerHTML = `
                <div class="text-center py-4">
                    <div class="text-danger mb-3">
                        <i class="fas fa-exclamation-triangle fa-2x"></i>
                    </div>
                    <h5 class="text-danger mb-2">Generation Failed</h5>
                    <p class="text-muted mb-3">${data.message}</p>
                    <button class="btn btn-primary btn-sm" onclick="showOTPModal(${userId}, '${fullName}', '${username}')">
                        <i class="fas fa-redo me-1"></i>Try Again
                    </button>
                </div>
            `;

            showQuickNotification('error', data.message);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        otpContent.innerHTML = `
            <div class="text-center py-4">
                <div class="text-danger mb-3">
                    <i class="fas fa-wifi fa-2x"></i>
                </div>
                <h5 class="text-danger mb-2">Connection Error</h5>
                <p class="text-muted mb-3">Failed to connect to server. Please check your connection.</p>
                <button class="btn btn-primary btn-sm" onclick="showOTPModal(${userId}, '${fullName}', '${username}')">
                    <i class="fas fa-redo me-1"></i>Try Again
                </button>
            </div>
        `;

        showQuickNotification('error', 'Connection error. Please try again.');
    });
}

function hideOTPModal() {
    const modal = document.getElementById('otpModal');
    modal.style.display = 'none';
    document.body.style.overflow = 'auto';
}

function copyOTPToClipboard(otpCode) {
    navigator.clipboard.writeText(otpCode).then(() => {
        showQuickNotification('success', 'OTP code copied to clipboard!');
    }).catch(() => {
        // Fallback for older browsers
        const textArea = document.createElement('textarea');
        textArea.value = otpCode;
        document.body.appendChild(textArea);
        textArea.select();
        document.execCommand('copy');
        document.body.removeChild(textArea);
        showQuickNotification('success', 'OTP code copied to clipboard!');
    });
}

// Close modal on escape key
document.addEventListener('keydown', function(event) {
    if (event.key === 'Escape' && document.getElementById('otpModal').style.display === 'flex') {
        hideOTPModal();
    }
});
</script>

<!-- Modern OTP Modal -->
<div id="otpModal" style="display: none; position: fixed; top: 0; left: 0; width: 100%; height: 100%; background: rgba(0,0,0,0.5); z-index: 1050; align-items: center; justify-content: center;">
    <div class="modal-dialog modal-dialog-centered" style="max-width: 400px;">
        <div class="modal-content" style="border: none; border-radius: 12px; box-shadow: 0 10px 40px rgba(0,0,0,0.2);">
            <div class="modal-header" style="border-bottom: 1px solid #e9ecef; border-radius: 12px 12px 0 0; background: linear-gradient(135deg, #4f46e5 0%, #7c3aed 100%); color: white;">
                <h5 class="modal-title">
                    <i class="fas fa-key me-2"></i>Generate OTP
                </h5>
                <button type="button" class="btn-close btn-close-white" onclick="hideOTPModal()" aria-label="Close"></button>
            </div>
            <div class="modal-body p-0">
                <div class="p-3 border-bottom bg-light">
                    <div id="otpUserInfo">
                        <!-- User info will be populated here -->
                    </div>
                </div>
                <div id="otpContent">
                    <!-- OTP content will be populated here -->
                </div>
            </div>
        </div>
    </div>
</div>

<?php include 'includes/admin-footer.php'; ?>
