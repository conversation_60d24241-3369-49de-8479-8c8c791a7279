<?php
/**
 * Test Account Verification System
 * Tests the account validation API and database queries
 */

require_once __DIR__ . '/../config/config.php';

echo "<h2>🧪 Testing Account Verification System</h2>";

try {
    $db = getDB();
    
    $test_account_number = '************';
    
    echo "<h3>1. Testing Database Connection</h3>";
    echo "✅ Database connection successful<br><br>";
    
    echo "<h3>2. Checking if Test Account Exists</h3>";
    $check_sql = "SELECT id, first_name, last_name, email, is_admin, status, account_number 
                  FROM accounts 
                  WHERE account_number = ?";
    $result = $db->query($check_sql, [$test_account_number]);
    
    if ($result->num_rows > 0) {
        $account = $result->fetch_assoc();
        echo "✅ Account found!<br>";
        echo "Account ID: {$account['id']}<br>";
        echo "Name: {$account['first_name']} {$account['last_name']}<br>";
        echo "Email: {$account['email']}<br>";
        echo "Is Admin: " . ($account['is_admin'] ? 'Yes' : 'No') . "<br>";
        echo "Status: {$account['status']}<br>";
        echo "Account Number: {$account['account_number']}<br>";
        
        // Test internal user logic
        $is_internal = ($account['is_admin'] == 0 && $account['status'] === 'active');
        echo "Is Internal User: " . ($is_internal ? 'Yes' : 'No') . "<br><br>";
        
    } else {
        echo "❌ Account not found. Creating test account...<br>";
        
        // Create test account
        $insert_sql = "INSERT INTO accounts (
            account_number, username, password, email, first_name, last_name,
            phone, address, currency, account_type, balance, status, kyc_status, is_admin
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";
        
        $params = [
            $test_account_number,
            'testbeneficiary',
            password_hash('test123', PASSWORD_DEFAULT),
            '<EMAIL>',
            'Test',
            'Beneficiary',
            '+**********',
            '123 Test Street, Test City',
            'USD',
            'savings',
            1000.00,
            'active',
            'verified',
            0
        ];
        
        $user_id = $db->insert($insert_sql, $params);
        
        if ($user_id) {
            echo "✅ Test account created successfully!<br>";
            echo "Account Number: {$test_account_number}<br>";
            echo "Username: testbeneficiary<br>";
            echo "Password: test123<br><br>";
        } else {
            echo "❌ Failed to create test account<br><br>";
        }
    }
    
    echo "<h3>3. Testing Account Validation Logic</h3>";

    // Test the same logic as the API
    $user_id = 1; // Simulate a different user
    $check_sql = "SELECT id, first_name, last_name, email, is_admin, status
                  FROM accounts
                  WHERE account_number = ? AND id != ?";
    $result = $db->query($check_sql, [$test_account_number, $user_id]);

    $account_data = null;
    if ($result->num_rows > 0) {
        $account_data = $result->fetch_assoc();
        $is_internal = ($account_data['is_admin'] == 0 && $account_data['status'] === 'active');

        echo "✅ Validation query successful<br>";
        echo "Account found: Yes<br>";
        echo "Is internal: " . ($is_internal ? 'Yes' : 'No') . "<br>";
        echo "Name: " . trim($account_data['first_name'] . ' ' . $account_data['last_name']) . "<br>";

        if ($is_internal) {
            echo "✅ Account should be available for inter-bank transfers<br>";
        } else {
            echo "⚠️ Account exists but not available for inter-bank transfers<br>";
            echo "Reason: is_admin={$account_data['is_admin']}, status={$account_data['status']}<br>";
        }
    } else {
        echo "❌ Account not found in validation query<br>";
    }

    echo "<br><h3>4. Testing API Response Format</h3>";

    // Simulate API response
    if ($account_data) {
        $is_internal = ($account_data['is_admin'] == 0 && $account_data['status'] === 'active');
        
        if ($is_internal) {
            $api_response = [
                'success' => true,
                'exists' => true,
                'is_internal' => true,
                'name' => trim($account_data['first_name'] . ' ' . $account_data['last_name']),
                'account_id' => $account_data['id'],
                'message' => 'Internal user found'
            ];
        } else {
            $api_response = [
                'success' => true,
                'exists' => true,
                'is_internal' => false,
                'message' => 'Account exists but not available for inter-bank transfer'
            ];
        }
    } else {
        $api_response = [
            'success' => true,
            'exists' => false,
            'is_internal' => false,
            'message' => 'Account format valid - External account'
        ];
    }
    
    echo "API Response:<br>";
    echo "<pre>" . json_encode($api_response, JSON_PRETTY_PRINT) . "</pre>";
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "<br>";
    echo "Stack trace:<br><pre>" . $e->getTraceAsString() . "</pre>";
}
?>
