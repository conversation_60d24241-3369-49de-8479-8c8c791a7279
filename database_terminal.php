<?php
/**
 * Database Terminal - Interactive SQL Query Tool
 * Online Banking System Database Access Terminal
 * 
 * This tool provides secure, interactive access to the database
 * with query execution, result formatting, and safety features.
 */

// Security check - only allow access from localhost or specific IPs
$allowed_ips = ['127.0.0.1', '::1', 'localhost'];
$client_ip = $_SERVER['REMOTE_ADDR'] ?? $_SERVER['HTTP_X_FORWARDED_FOR'] ?? 'unknown';

if (!in_array($client_ip, $allowed_ips) && !isset($_GET['allow_remote'])) {
    die("Access denied. Database terminal only accessible from localhost for security.");
}

// Include database configuration
require_once 'config/database.php';

// Initialize database connection
try {
    $db = Database::getInstance();
    $connection = $db->getConnection();
} catch (Exception $e) {
    die("Database connection failed: " . $e->getMessage());
}

// Handle AJAX requests
if (isset($_POST['action'])) {
    header('Content-Type: application/json');
    
    switch ($_POST['action']) {
        case 'execute_query':
            executeQuery($_POST['query'] ?? '');
            break;
        case 'get_table_info':
            getTableInfo($_POST['table'] ?? '');
            break;
        case 'get_database_stats':
            getDatabaseStats();
            break;
        case 'get_recent_queries':
            getRecentQueries();
            break;
        default:
            echo json_encode(['success' => false, 'message' => 'Invalid action']);
    }
    exit;
}

/**
 * Execute SQL query with safety checks
 */
function executeQuery($query) {
    global $db;
    
    if (empty(trim($query))) {
        echo json_encode(['success' => false, 'message' => 'Query cannot be empty']);
        return;
    }
    
    // Basic safety checks
    $query = trim($query);
    $dangerous_keywords = ['DROP', 'DELETE', 'TRUNCATE', 'ALTER', 'CREATE', 'INSERT', 'UPDATE'];
    $query_upper = strtoupper($query);
    
    $is_dangerous = false;
    foreach ($dangerous_keywords as $keyword) {
        if (strpos($query_upper, $keyword) !== false) {
            $is_dangerous = true;
            break;
        }
    }
    
    // Log query attempt
    logQuery($query, $is_dangerous);
    
    try {
        $start_time = microtime(true);
        $result = $db->query($query);
        $execution_time = round((microtime(true) - $start_time) * 1000, 2);
        
        if ($result === true) {
            // Non-SELECT query executed successfully
            echo json_encode([
                'success' => true,
                'message' => 'Query executed successfully',
                'execution_time' => $execution_time,
                'type' => 'non_select',
                'affected_rows' => $db->getConnection()->affected_rows
            ]);
        } elseif ($result) {
            // SELECT query - fetch results
            $rows = [];
            $columns = [];
            
            if ($result->num_rows > 0) {
                // Get column names
                $field_info = $result->fetch_fields();
                foreach ($field_info as $field) {
                    $columns[] = $field->name;
                }
                
                // Fetch all rows
                while ($row = $result->fetch_assoc()) {
                    $rows[] = $row;
                }
            }
            
            echo json_encode([
                'success' => true,
                'message' => 'Query executed successfully',
                'execution_time' => $execution_time,
                'type' => 'select',
                'columns' => $columns,
                'rows' => $rows,
                'row_count' => count($rows)
            ]);
        } else {
            echo json_encode([
                'success' => false,
                'message' => 'Query execution failed: ' . $db->getConnection()->error
            ]);
        }
    } catch (Exception $e) {
        echo json_encode([
            'success' => false,
            'message' => 'Query error: ' . $e->getMessage()
        ]);
    }
}

/**
 * Get detailed table information
 */
function getTableInfo($tableName) {
    global $db;
    
    if (empty($tableName)) {
        echo json_encode(['success' => false, 'message' => 'Table name required']);
        return;
    }
    
    try {
        // Get table structure
        $structure = $db->query("DESCRIBE `$tableName`");
        $columns = [];
        while ($row = $structure->fetch_assoc()) {
            $columns[] = $row;
        }
        
        // Get table stats
        $stats = $db->query("SELECT COUNT(*) as row_count FROM `$tableName`");
        $row_count = $stats->fetch_assoc()['row_count'];
        
        // Get table size
        $size_query = "SELECT 
            ROUND(((data_length + index_length) / 1024 / 1024), 2) AS size_mb
            FROM information_schema.TABLES 
            WHERE table_schema = DATABASE() AND table_name = '$tableName'";
        $size_result = $db->query($size_query);
        $size_mb = $size_result->fetch_assoc()['size_mb'] ?? 0;
        
        echo json_encode([
            'success' => true,
            'table_name' => $tableName,
            'columns' => $columns,
            'row_count' => $row_count,
            'size_mb' => $size_mb
        ]);
    } catch (Exception $e) {
        echo json_encode([
            'success' => false,
            'message' => 'Error getting table info: ' . $e->getMessage()
        ]);
    }
}

/**
 * Get database statistics
 */
function getDatabaseStats() {
    global $db;
    
    try {
        // Get table count
        $tables = $db->query("SHOW TABLES");
        $table_count = $tables->num_rows;
        
        // Get database size
        $size_query = "SELECT 
            ROUND(SUM(data_length + index_length) / 1024 / 1024, 2) AS size_mb
            FROM information_schema.TABLES 
            WHERE table_schema = DATABASE()";
        $size_result = $db->query($size_query);
        $size_mb = $size_result->fetch_assoc()['size_mb'] ?? 0;
        
        // Get user count
        $users = $db->query("SELECT COUNT(*) as count FROM accounts");
        $user_count = $users->fetch_assoc()['count'];
        
        // Get transaction count
        $transactions = $db->query("SELECT COUNT(*) as count FROM transfers");
        $transaction_count = $transactions->fetch_assoc()['count'];
        
        echo json_encode([
            'success' => true,
            'stats' => [
                'table_count' => $table_count,
                'size_mb' => $size_mb,
                'user_count' => $user_count,
                'transaction_count' => $transaction_count
            ]
        ]);
    } catch (Exception $e) {
        echo json_encode([
            'success' => false,
            'message' => 'Error getting database stats: ' . $e->getMessage()
        ]);
    }
}

/**
 * Get recent queries from log
 */
function getRecentQueries() {
    $log_file = 'logs/query_log.txt';
    $queries = [];
    
    if (file_exists($log_file)) {
        $lines = array_slice(file($log_file), -10); // Last 10 queries
        foreach ($lines as $line) {
            $parts = explode(' | ', trim($line));
            if (count($parts) >= 3) {
                $queries[] = [
                    'timestamp' => $parts[0],
                    'query' => $parts[1],
                    'status' => $parts[2] ?? 'unknown'
                ];
            }
        }
    }
    
    echo json_encode([
        'success' => true,
        'queries' => array_reverse($queries)
    ]);
}

/**
 * Log query execution
 */
function logQuery($query, $is_dangerous = false) {
    $log_dir = 'logs';
    if (!is_dir($log_dir)) {
        mkdir($log_dir, 0755, true);
    }
    
    $log_file = $log_dir . '/query_log.txt';
    $timestamp = date('Y-m-d H:i:s');
    $status = $is_dangerous ? 'DANGEROUS' : 'SAFE';
    $client_ip = $_SERVER['REMOTE_ADDR'] ?? 'unknown';
    
    $log_entry = "$timestamp | $query | $status | IP: $client_ip\n";
    file_put_contents($log_file, $log_entry, FILE_APPEND | LOCK_EX);
}

// Get list of tables for the interface
$tables_result = $db->query("SHOW TABLES");
$tables = [];
while ($row = $tables_result->fetch_array()) {
    $tables[] = $row[0];
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Database Terminal - Online Banking System</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
            background: #1a1a1a;
            color: #00ff00;
            line-height: 1.6;
        }
        
        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .header {
            text-align: center;
            margin-bottom: 30px;
            padding: 20px;
            background: #2a2a2a;
            border-radius: 8px;
            border: 2px solid #00ff00;
        }
        
        .header h1 {
            color: #00ff00;
            text-shadow: 0 0 10px #00ff00;
            margin-bottom: 10px;
        }
        
        .header .status {
            color: #ffff00;
        }
        
        .main-content {
            display: grid;
            grid-template-columns: 1fr 300px;
            gap: 20px;
        }
        
        .query-section {
            background: #2a2a2a;
            padding: 20px;
            border-radius: 8px;
            border: 1px solid #444;
        }
        
        .sidebar {
            background: #2a2a2a;
            padding: 20px;
            border-radius: 8px;
            border: 1px solid #444;
            height: fit-content;
        }
        
        .query-input {
            width: 100%;
            height: 200px;
            background: #1a1a1a;
            color: #00ff00;
            border: 1px solid #00ff00;
            border-radius: 4px;
            padding: 15px;
            font-family: inherit;
            font-size: 14px;
            resize: vertical;
        }
        
        .query-input:focus {
            outline: none;
            border-color: #00ffff;
            box-shadow: 0 0 10px rgba(0, 255, 255, 0.3);
        }
        
        .btn {
            background: #00ff00;
            color: #000;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            font-weight: bold;
            margin: 5px;
            transition: all 0.3s;
        }
        
        .btn:hover {
            background: #00ffff;
            box-shadow: 0 0 10px rgba(0, 255, 255, 0.5);
        }
        
        .btn-danger {
            background: #ff4444;
            color: #fff;
        }
        
        .btn-danger:hover {
            background: #ff6666;
        }
        
        .results {
            margin-top: 20px;
            background: #1a1a1a;
            border: 1px solid #444;
            border-radius: 4px;
            padding: 15px;
            max-height: 500px;
            overflow: auto;
        }
        
        .results table {
            width: 100%;
            border-collapse: collapse;
            font-size: 12px;
        }
        
        .results th,
        .results td {
            border: 1px solid #444;
            padding: 8px;
            text-align: left;
        }
        
        .results th {
            background: #333;
            color: #00ffff;
            position: sticky;
            top: 0;
        }
        
        .results tr:nth-child(even) {
            background: #2a2a2a;
        }
        
        .sidebar h3 {
            color: #00ffff;
            margin-bottom: 15px;
            border-bottom: 1px solid #444;
            padding-bottom: 5px;
        }
        
        .table-list {
            max-height: 300px;
            overflow-y: auto;
        }
        
        .table-item {
            padding: 5px 10px;
            margin: 2px 0;
            background: #1a1a1a;
            border-radius: 3px;
            cursor: pointer;
            transition: all 0.3s;
        }
        
        .table-item:hover {
            background: #333;
            color: #00ffff;
        }
        
        .stats {
            margin-top: 20px;
        }
        
        .stat-item {
            display: flex;
            justify-content: space-between;
            padding: 5px 0;
            border-bottom: 1px solid #333;
        }
        
        .message {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
        }
        
        .message.success {
            background: rgba(0, 255, 0, 0.1);
            border: 1px solid #00ff00;
            color: #00ff00;
        }
        
        .message.error {
            background: rgba(255, 68, 68, 0.1);
            border: 1px solid #ff4444;
            color: #ff4444;
        }
        
        .message.warning {
            background: rgba(255, 255, 0, 0.1);
            border: 1px solid #ffff00;
            color: #ffff00;
        }
        
        .query-examples {
            margin-top: 20px;
        }
        
        .example-query {
            background: #1a1a1a;
            padding: 10px;
            margin: 5px 0;
            border-radius: 3px;
            border-left: 3px solid #00ff00;
            cursor: pointer;
            font-size: 12px;
        }
        
        .example-query:hover {
            background: #333;
        }
        
        .loading {
            display: none;
            text-align: center;
            color: #ffff00;
            margin: 10px 0;
        }
        
        @keyframes blink {
            0%, 50% { opacity: 1; }
            51%, 100% { opacity: 0; }
        }
        
        .loading::after {
            content: '...';
            animation: blink 1s infinite;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🗄️ Database Terminal</h1>
            <div class="status">
                Connected to: <strong><?php echo DB_NAME; ?></strong> | 
                Host: <strong><?php echo DB_HOST; ?></strong> | 
                Tables: <strong><?php echo count($tables); ?></strong>
            </div>
        </div>
        
        <div class="main-content">
            <div class="query-section">
                <h2>SQL Query Executor</h2>
                <textarea id="queryInput" class="query-input" placeholder="Enter your SQL query here...&#10;&#10;Example:&#10;SELECT * FROM accounts LIMIT 10;"></textarea>
                
                <div style="margin: 15px 0;">
                    <button class="btn" onclick="executeQuery()">▶️ Execute Query</button>
                    <button class="btn" onclick="clearQuery()">🗑️ Clear</button>
                    <button class="btn" onclick="formatQuery()">✨ Format</button>
                    <button class="btn btn-danger" onclick="showDangerousWarning()">⚠️ Enable Dangerous Queries</button>
                </div>
                
                <div id="loading" class="loading">Executing query</div>
                <div id="messages"></div>
                <div id="results" class="results" style="display: none;"></div>
            </div>
            
            <div class="sidebar">
                <h3>📊 Database Stats</h3>
                <div id="stats" class="stats">
                    <div class="stat-item">
                        <span>Tables:</span>
                        <span id="tableCount">-</span>
                    </div>
                    <div class="stat-item">
                        <span>Size:</span>
                        <span id="dbSize">-</span>
                    </div>
                    <div class="stat-item">
                        <span>Users:</span>
                        <span id="userCount">-</span>
                    </div>
                    <div class="stat-item">
                        <span>Transactions:</span>
                        <span id="transactionCount">-</span>
                    </div>
                </div>
                
                <h3>📋 Tables</h3>
                <div class="table-list">
                    <?php foreach ($tables as $table): ?>
                        <div class="table-item" onclick="selectTable('<?php echo $table; ?>')" title="Click to describe table">
                            <?php echo $table; ?>
                        </div>
                    <?php endforeach; ?>
                </div>
                
                <div class="query-examples">
                    <h3>💡 Quick Queries</h3>
                    <div class="example-query" onclick="setQuery('SELECT * FROM accounts LIMIT 10;')">
                        Show 10 accounts
                    </div>
                    <div class="example-query" onclick="setQuery('SELECT COUNT(*) as total_users FROM accounts;')">
                        Count total users
                    </div>
                    <div class="example-query" onclick="setQuery('SELECT status, COUNT(*) as count FROM accounts GROUP BY status;')">
                        Users by status
                    </div>
                    <div class="example-query" onclick="setQuery('SELECT * FROM transfers ORDER BY created_at DESC LIMIT 10;')">
                        Recent transfers
                    </div>
                    <div class="example-query" onclick="setQuery('SELECT currency, SUM(balance) as total_balance FROM accounts GROUP BY currency;')">
                        Balance by currency
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Load database stats on page load
        document.addEventListener('DOMContentLoaded', function() {
            loadDatabaseStats();
        });
        
        // Execute SQL query
        function executeQuery() {
            const query = document.getElementById('queryInput').value.trim();
            
            if (!query) {
                showMessage('Please enter a SQL query.', 'error');
                return;
            }
            
            showLoading(true);
            clearMessages();
            
            const formData = new FormData();
            formData.append('action', 'execute_query');
            formData.append('query', query);
            
            fetch('database_terminal.php', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                showLoading(false);
                
                if (data.success) {
                    showMessage(`Query executed successfully in ${data.execution_time}ms`, 'success');
                    
                    if (data.type === 'select') {
                        displayResults(data.columns, data.rows);
                        showMessage(`Returned ${data.row_count} row(s)`, 'success');
                    } else {
                        showMessage(`Affected ${data.affected_rows} row(s)`, 'success');
                        hideResults();
                    }
                } else {
                    showMessage(data.message, 'error');
                    hideResults();
                }
            })
            .catch(error => {
                showLoading(false);
                showMessage('Network error: ' + error.message, 'error');
                hideResults();
            });
        }
        
        // Display query results in table format
        function displayResults(columns, rows) {
            const resultsDiv = document.getElementById('results');
            
            if (!columns || columns.length === 0) {
                resultsDiv.innerHTML = '<p>No results to display.</p>';
                resultsDiv.style.display = 'block';
                return;
            }
            
            let html = '<table><thead><tr>';
            columns.forEach(column => {
                html += `<th>${column}</th>`;
            });
            html += '</tr></thead><tbody>';
            
            rows.forEach(row => {
                html += '<tr>';
                columns.forEach(column => {
                    const value = row[column];
                    html += `<td>${value !== null ? value : '<em>NULL</em>'}</td>`;
                });
                html += '</tr>';
            });
            
            html += '</tbody></table>';
            resultsDiv.innerHTML = html;
            resultsDiv.style.display = 'block';
        }
        
        // Hide results table
        function hideResults() {
            document.getElementById('results').style.display = 'none';
        }
        
        // Show/hide loading indicator
        function showLoading(show) {
            document.getElementById('loading').style.display = show ? 'block' : 'none';
        }
        
        // Show message to user
        function showMessage(message, type = 'info') {
            const messagesDiv = document.getElementById('messages');
            const messageDiv = document.createElement('div');
            messageDiv.className = `message ${type}`;
            messageDiv.textContent = message;
            messagesDiv.appendChild(messageDiv);
            
            // Auto-remove success messages after 5 seconds
            if (type === 'success') {
                setTimeout(() => {
                    if (messageDiv.parentNode) {
                        messageDiv.parentNode.removeChild(messageDiv);
                    }
                }, 5000);
            }
        }
        
        // Clear all messages
        function clearMessages() {
            document.getElementById('messages').innerHTML = '';
        }
        
        // Clear query input
        function clearQuery() {
            document.getElementById('queryInput').value = '';
            hideResults();
            clearMessages();
        }
        
        // Set query in input
        function setQuery(query) {
            document.getElementById('queryInput').value = query;
        }
        
        // Basic query formatting
        function formatQuery() {
            const input = document.getElementById('queryInput');
            let query = input.value;
            
            // Basic SQL formatting
            query = query.replace(/\s+/g, ' '); // Remove extra spaces
            query = query.replace(/\s*,\s*/g, ', '); // Format commas
            query = query.replace(/\s*\(\s*/g, ' ('); // Format parentheses
            query = query.replace(/\s*\)\s*/g, ') ');
            query = query.replace(/\bSELECT\b/gi, 'SELECT');
            query = query.replace(/\bFROM\b/gi, '\nFROM');
            query = query.replace(/\bWHERE\b/gi, '\nWHERE');
            query = query.replace(/\bORDER BY\b/gi, '\nORDER BY');
            query = query.replace(/\bGROUP BY\b/gi, '\nGROUP BY');
            query = query.replace(/\bLIMIT\b/gi, '\nLIMIT');
            
            input.value = query.trim();
        }
        
        // Select table and show DESCRIBE query
        function selectTable(tableName) {
            setQuery(`DESCRIBE \`${tableName}\`;`);
        }
        
        // Load database statistics
        function loadDatabaseStats() {
            const formData = new FormData();
            formData.append('action', 'get_database_stats');
            
            fetch('database_terminal.php', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    document.getElementById('tableCount').textContent = data.stats.table_count;
                    document.getElementById('dbSize').textContent = data.stats.size_mb + ' MB';
                    document.getElementById('userCount').textContent = data.stats.user_count;
                    document.getElementById('transactionCount').textContent = data.stats.transaction_count;
                }
            })
            .catch(error => {
                console.error('Error loading database stats:', error);
            });
        }
        
        // Show warning for dangerous queries
        function showDangerousWarning() {
            if (confirm('⚠️ WARNING: This will enable dangerous SQL operations like DELETE, DROP, ALTER, etc.\n\nThese operations can permanently damage your database!\n\nAre you absolutely sure you want to continue?')) {
                showMessage('Dangerous query mode enabled. Use with extreme caution!', 'warning');
                // You could implement dangerous query mode here
            }
        }
        
        // Keyboard shortcuts
        document.addEventListener('keydown', function(e) {
            // Ctrl+Enter to execute query
            if (e.ctrlKey && e.key === 'Enter') {
                e.preventDefault();
                executeQuery();
            }
            
            // Ctrl+L to clear query
            if (e.ctrlKey && e.key === 'l') {
                e.preventDefault();
                clearQuery();
            }
        });
    </script>
</body>
</html>
