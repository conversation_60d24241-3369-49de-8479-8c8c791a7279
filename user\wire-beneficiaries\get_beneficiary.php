<?php
/**
 * Get Wire Beneficiary Data for Editing
 * Returns beneficiary data as <PERSON><PERSON><PERSON> for the edit modal
 */

session_start();
require_once '../../config/config.php';

// Set JSON header
header('Content-Type: application/json');

try {
    // Check if user is logged in
    if (!isset($_SESSION['user_id'])) {
        throw new Exception('User not authenticated');
    }

    $db = getDB();
    $user_id = $_SESSION['user_id'];
    $beneficiary_id = (int)($_GET['id'] ?? 0);

    if ($beneficiary_id <= 0) {
        throw new Exception('Invalid beneficiary ID');
    }

    // Get beneficiary data
    $sql = "SELECT * FROM wire_beneficiaries WHERE id = ? AND user_id = ?";
    $result = $db->query($sql, [$beneficiary_id, $user_id]);

    if ($result->num_rows === 0) {
        throw new Exception('Beneficiary not found or access denied');
    }

    $beneficiary = $result->fetch_assoc();

    // Return success response
    echo json_encode([
        'success' => true,
        'beneficiary' => $beneficiary
    ]);

} catch (Exception $e) {
    // Return error response
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ]);
}
?>
