/* Wire Transfer System Styles */

/* Hero Section */
.wire-transfer-hero {
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    border-radius: 12px;
    padding: 2rem;
    color: white;
    margin-bottom: 1.5rem;
}

.hero-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 1rem;
}

.hero-title {
    font-size: 1.8rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
}

.hero-subtitle {
    font-size: 1rem;
    opacity: 0.9;
    margin-bottom: 0.5rem;
}

.hero-stats {
    font-size: 0.9rem;
    opacity: 0.8;
}

.hero-actions .btn {
    border-color: rgba(255, 255, 255, 0.3);
}

.hero-actions .btn:hover {
    background-color: rgba(255, 255, 255, 0.1);
    border-color: rgba(255, 255, 255, 0.5);
}

/* Security Notice */
.security-notice .alert {
    border: none;
    border-radius: 8px;
    background-color: rgba(13, 110, 253, 0.1);
    border-left: 4px solid var(--primary-color);
}

/* Form Sections */
.form-section {
    border-bottom: 1px solid #e9ecef;
    padding-bottom: 1.5rem;
}

.form-section:last-child {
    border-bottom: none;
    padding-bottom: 0;
}

.form-section h6 {
    color: var(--primary-color);
    font-weight: 600;
    margin-bottom: 1rem;
    padding-bottom: 0.5rem;
    border-bottom: 2px solid #f8f9fa;
}

/* Source Account Display */
.source-account-display {
    background-color: #f8f9fa;
    border-radius: 8px;
    padding: 1rem;
    border: 1px solid #e9ecef;
}

.account-info .account-name {
    font-weight: 600;
    color: var(--primary-color);
    margin-bottom: 0.25rem;
}

.account-info .account-number {
    font-family: 'Courier New', monospace;
    color: #6c757d;
    font-size: 0.9rem;
    margin-bottom: 0.25rem;
}

.account-info .account-balance {
    font-weight: 600;
    color: #28a745;
    font-size: 1.1rem;
}

/* Form Controls */
.wire-transfer-form .form-control,
.wire-transfer-form .form-select {
    border-radius: 6px;
    border: 1px solid #ced4da;
    padding: 0.75rem;
    transition: all 0.2s ease;
}

.wire-transfer-form .form-control:focus,
.wire-transfer-form .form-select:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(13, 110, 253, 0.25);
}

.wire-transfer-form .input-group-text {
    background-color: #f8f9fa;
    border-color: #ced4da;
    font-weight: 600;
}

/* Required Field Indicator */
.text-danger {
    color: #dc3545 !important;
}

/* Form Help Text */
.form-text {
    font-size: 0.875rem;
    color: #6c757d;
    margin-top: 0.25rem;
}

/* Submit Button */
.wire-transfer-form .btn-lg {
    padding: 0.75rem 2rem;
    font-weight: 600;
    border-radius: 8px;
    transition: all 0.2s ease;
}

.wire-transfer-form .btn-primary {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
}

.wire-transfer-form .btn-primary:hover {
    background-color: var(--primary-dark);
    border-color: var(--primary-dark);
    transform: translateY(-1px);
}

.wire-transfer-form .btn-outline-secondary {
    border-color: #6c757d;
    color: #6c757d;
}

.wire-transfer-form .btn-outline-secondary:hover {
    background-color: #6c757d;
    border-color: #6c757d;
}

/* Modal Enhancements */
.modal-content {
    border-radius: 12px;
    border: none;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
}

.modal-header {
    background-color: #f8f9fa;
    border-bottom: 1px solid #e9ecef;
    border-radius: 12px 12px 0 0;
}

.modal-title {
    color: var(--primary-color);
    font-weight: 600;
}

.billing-code-info h6 {
    color: var(--primary-color);
    font-weight: 600;
    margin-bottom: 0.5rem;
}

.billing-code-info p {
    margin-bottom: 0;
    line-height: 1.5;
}

/* Progress Bar */
.progress {
    height: 8px;
    border-radius: 4px;
    background-color: #f8f9fa;
}

.progress-bar {
    background-color: var(--primary-color);
}

/* Error States */
.is-invalid {
    border-color: #dc3545;
}

.invalid-feedback {
    display: block;
    color: #dc3545;
    font-size: 0.875rem;
    margin-top: 0.25rem;
}

/* Success States */
.is-valid {
    border-color: #28a745;
}

.valid-feedback {
    display: block;
    color: #28a745;
    font-size: 0.875rem;
    margin-top: 0.25rem;
}

/* Responsive Design */
@media (max-width: 768px) {
    .hero-content {
        flex-direction: column;
        text-align: center;
    }
    
    .hero-actions {
        width: 100%;
    }
    
    .hero-actions .btn {
        width: 100%;
    }
    
    .wire-transfer-form .btn-lg {
        width: 100%;
        margin-bottom: 0.5rem;
    }
    
    .wire-transfer-form .btn-lg.ms-2 {
        margin-left: 0 !important;
    }
}

/* Loading States */
.loading {
    opacity: 0.6;
    pointer-events: none;
}

.loading .btn {
    position: relative;
}

.loading .btn::after {
    content: '';
    position: absolute;
    width: 16px;
    height: 16px;
    margin: auto;
    border: 2px solid transparent;
    border-top-color: #ffffff;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    top: 0;
    left: 0;
    bottom: 0;
    right: 0;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Card Enhancements */
.card {
    border: none;
    border-radius: 12px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.card-header {
    background-color: #f8f9fa;
    border-bottom: 1px solid #e9ecef;
    border-radius: 12px 12px 0 0;
    padding: 1.25rem;
}

.card-header h5 {
    color: var(--primary-color);
    font-weight: 600;
    margin-bottom: 0;
}

.card-body {
    padding: 2rem;
}

/* ===== WIRE TRANSFER RECEIPT MODAL STYLES ===== */

/* Modal Animations */
@keyframes fadeIn {
    from {
        opacity: 0;
        backdrop-filter: blur(0px);
    }
    to {
        opacity: 1;
        backdrop-filter: blur(3px);
    }
}

@keyframes fadeOut {
    from {
        opacity: 1;
        backdrop-filter: blur(3px);
    }
    to {
        opacity: 0;
        backdrop-filter: blur(0px);
    }
}

@keyframes slideIn {
    from {
        transform: translate(-50%, -60%);
        opacity: 0;
        scale: 0.9;
    }
    to {
        transform: translate(-50%, -50%);
        opacity: 1;
        scale: 1;
    }
}

/* Wire Transfer Receipt Modal */
.receipt-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 9999;
    animation: fadeIn 0.3s ease-out;
}

.receipt-modal .modal-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.7);
    backdrop-filter: blur(3px);
}

.receipt-modal .receipt-container {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background: white;
    border: 1px solid #e0e0e0;
    box-shadow: 0 10px 40px rgba(0, 0, 0, 0.2);
    max-width: 650px;
    width: 95%;
    max-height: 85vh;
    overflow: hidden;
    scroll-behavior: smooth;
    animation: slideIn 0.3s ease-out;
    border-radius: 12px;
    font-family: 'Courier New', 'Monaco', monospace;
}

/* Receipt Header */
.receipt-modal .receipt-header {
    background: linear-gradient(135deg, var(--primary-color, #206bc4) 0%, var(--primary-dark, #1a5490) 100%);
    color: white;
    padding: 1.5rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
    position: relative;
    border-bottom: 1px solid #ddd;
}

.receipt-modal .bank-logo {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    font-weight: 600;
    font-size: 1.1rem;
}

.receipt-modal .bank-logo i {
    font-size: 1.5rem;
}

.receipt-modal .receipt-title {
    position: absolute;
    left: 50%;
    transform: translateX(-50%);
    font-weight: 700;
    font-size: 1rem;
    letter-spacing: 1px;
}

.receipt-modal .receipt-close {
    background: rgba(255, 255, 255, 0.2);
    border: none;
    color: white;
    width: 32px;
    height: 32px;
    border-radius: 50%;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.2s ease;
}

.receipt-modal .receipt-close:hover {
    background: rgba(255, 255, 255, 0.3);
    transform: scale(1.1);
}

/* Receipt Body */
.receipt-modal .receipt-body {
    padding: 2rem;
    max-height: 60vh;
    overflow-y: auto;
    background: white;
    scroll-behavior: smooth;
    /* Hide scrollbar for webkit browsers */
    scrollbar-width: none; /* Firefox */
    -ms-overflow-style: none; /* IE and Edge */
}

.receipt-modal .receipt-body::-webkit-scrollbar {
    display: none; /* Chrome, Safari, Opera */
}

.receipt-modal .receipt-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 2rem;
    font-size: 0.9rem;
    line-height: 1.6;
}

/* Receipt Sections */
.receipt-modal .receipt-section {
    margin-bottom: 1.5rem;
}

.receipt-modal .receipt-section-title {
    font-size: 1rem;
    font-weight: bold;
    color: #2c3e50;
    margin-bottom: 0.75rem;
    padding-bottom: 0.5rem;
    border-bottom: 1px solid #ddd;
    font-family: 'Arial', sans-serif;
    text-transform: uppercase;
    letter-spacing: 1px;
}

.receipt-modal .receipt-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.4rem 0;
    border-bottom: 1px dotted #ddd;
}

.receipt-modal .receipt-row:last-child {
    border-bottom: none;
}

.receipt-modal .receipt-label {
    font-weight: 500;
    color: #555;
    font-size: 0.85rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    flex: 1;
}

.receipt-modal .receipt-value {
    font-weight: 600;
    color: #2c3e50;
    text-align: right;
    font-family: 'Courier New', monospace;
    flex: 1;
    word-break: break-word;
}

/* Special styling for amounts */
.receipt-modal .receipt-amount {
    font-size: 1.1rem;
    font-weight: bold;
    font-family: 'Courier New', monospace;
}

.receipt-modal .receipt-amount.positive {
    color: #27ae60;
}

.receipt-modal .receipt-amount.negative {
    color: #e74c3c;
}

/* Transaction status styling */
.receipt-modal .receipt-status {
    display: inline-block;
    padding: 0.25rem 0.75rem;
    border-radius: 3px;
    font-size: 0.75rem;
    font-weight: bold;
    text-transform: uppercase;
    letter-spacing: 1px;
    font-family: 'Arial', sans-serif;
}

.receipt-modal .receipt-status.completed {
    background: #d4edda;
    color: #155724;
    border: 1px solid #c3e6cb;
}

.receipt-modal .receipt-status.pending {
    background: #fff3cd;
    color: #856404;
    border: 1px solid #ffeaa7;
}

.receipt-modal .receipt-status.failed {
    background: #f8d7da;
    color: #721c24;
    border: 1px solid #f5c6cb;
}

/* Full width sections */
.receipt-modal .receipt-section.full-width {
    grid-column: 1 / -1;
    margin-top: 1rem;
    padding-top: 1rem;
    border-top: 1px solid #ddd;
}

/* Receipt Actions */
.receipt-modal .receipt-actions {
    display: flex;
    justify-content: center;
    gap: 1rem;
    margin-top: 2rem;
    padding-top: 1.5rem;
    border-top: 1px solid #ddd;
}

.receipt-modal .btn-print {
    background: linear-gradient(135deg, var(--primary-color, #206bc4) 0%, var(--primary-dark, #1a5490) 100%);
    color: white;
    border: none;
    padding: 0.75rem 1.5rem;
    border-radius: 6px;
    font-size: 0.875rem;
    font-weight: 600;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    transition: all 0.2s ease;
    text-decoration: none;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.receipt-modal .btn-print:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(32, 107, 196, 0.3);
    color: white;
    text-decoration: none;
}

.receipt-modal .btn-close-receipt {
    background: linear-gradient(135deg, #6b7280 0%, #4b5563 100%);
    color: white;
    border: none;
    padding: 0.75rem 1.5rem;
    border-radius: 6px;
    font-size: 0.875rem;
    font-weight: 600;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    transition: all 0.2s ease;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.receipt-modal .btn-close-receipt:hover {
    background: linear-gradient(135deg, #4b5563 0%, #374151 100%);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(107, 114, 128, 0.3);
}

/* ===== RESPONSIVE DESIGN FOR RECEIPT MODAL ===== */

/* 14-inch screens and smaller laptops */
@media (max-width: 1366px) {
    .receipt-modal .receipt-container {
        max-width: 600px;
        width: 90%;
    }

    .receipt-modal .receipt-body {
        padding: 1.5rem;
    }

    .receipt-modal .receipt-content {
        gap: 1.5rem;
    }
}

/* Tablet and Mobile Responsive */
@media (max-width: 768px) {
    .receipt-modal .receipt-container {
        width: 95%;
        max-height: 90vh;
        margin: 1rem;
        max-width: none;
    }

    .receipt-modal .receipt-header {
        padding: 1rem;
        flex-direction: column;
        gap: 0.75rem;
        text-align: center;
    }

    .receipt-modal .receipt-title {
        position: static;
        transform: none;
        font-size: 0.9rem;
    }

    .receipt-modal .receipt-close {
        position: absolute;
        top: 1rem;
        right: 1rem;
    }

    .receipt-modal .receipt-body {
        padding: 1rem;
    }

    .receipt-modal .receipt-content {
        grid-template-columns: 1fr;
        gap: 1rem;
    }

    .receipt-modal .receipt-row {
        flex-direction: column;
        align-items: flex-start;
        gap: 0.25rem;
    }

    .receipt-modal .receipt-value {
        text-align: left;
        font-size: 0.9rem;
    }

    .receipt-modal .receipt-actions {
        flex-direction: column;
        gap: 0.75rem;
    }

    .receipt-modal .btn-print,
    .receipt-modal .btn-close-receipt {
        width: 100%;
        justify-content: center;
    }

    /* Success icon smaller on mobile */
    .receipt-modal .receipt-body i.fa-check-circle {
        font-size: 2.5rem;
    }

    /* Adjust section titles */
    .receipt-modal .receipt-section-title {
        font-size: 0.9rem;
    }

    /* Compact notice on mobile */
    .receipt-modal .receipt-section.full-width div[style*="background: #f0f9ff"] {
        padding: 0.75rem;
    }

    .receipt-modal .receipt-section.full-width div[style*="background: #f0f9ff"] div {
        font-size: 0.8rem;
    }
}

/* Small mobile devices */
@media (max-width: 480px) {
    .receipt-modal .receipt-container {
        width: 98%;
        margin: 0.5rem;
    }

    .receipt-modal .receipt-header {
        padding: 0.75rem;
    }

    .receipt-modal .bank-logo {
        font-size: 1rem;
    }

    .receipt-modal .bank-logo i {
        font-size: 1.25rem;
    }

    .receipt-modal .receipt-title {
        font-size: 0.8rem;
    }

    .receipt-modal .receipt-body {
        padding: 0.75rem;
    }

    .receipt-modal .receipt-section-title {
        font-size: 0.85rem;
        margin-bottom: 0.5rem;
    }

    .receipt-modal .receipt-label {
        font-size: 0.8rem;
    }

    .receipt-modal .receipt-value {
        font-size: 0.85rem;
    }

    .receipt-modal .receipt-amount {
        font-size: 1rem;
    }
}

/* Print styles for receipt */
@media print {
    .receipt-modal {
        position: static !important;
        background: white !important;
        animation: none !important;
    }

    .receipt-modal .modal-overlay {
        display: none !important;
    }

    .receipt-modal .receipt-container {
        position: static !important;
        transform: none !important;
        box-shadow: none !important;
        border: 1px solid #000 !important;
        max-width: none !important;
        width: 100% !important;
        max-height: none !important;
        border-radius: 0 !important;
    }

    .receipt-modal .receipt-close,
    .receipt-modal .receipt-actions {
        display: none !important;
    }

    .receipt-modal .receipt-header {
        background: #f8f9fa !important;
        color: #000 !important;
        border-bottom: 2px solid #000 !important;
    }

    .receipt-modal .receipt-body {
        max-height: none !important;
        overflow: visible !important;
        padding: 1rem !important;
    }

    .receipt-modal .receipt-content {
        grid-template-columns: 1fr 1fr !important;
        gap: 1rem !important;
    }

    .receipt-modal .receipt-section-title {
        color: #000 !important;
    }

    .receipt-modal .receipt-value {
        color: #000 !important;
    }

    .receipt-modal .receipt-amount.positive {
        color: #000 !important;
    }

    .receipt-modal .receipt-status {
        border: 1px solid #000 !important;
        color: #000 !important;
        background: #f8f9fa !important;
    }
}
