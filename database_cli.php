<?php
/**
 * Database CLI Tool - Command Line Interface for Database Operations
 * Online Banking System Database Command Line Access
 * 
 * Usage: php database_cli.php [command] [options]
 * 
 * Commands:
 *   query "SQL"     - Execute SQL query
 *   tables          - List all tables
 *   describe TABLE  - Show table structure
 *   stats           - Show database statistics
 *   backup          - Create database backup
 *   users           - List user accounts
 *   help            - Show this help
 */

// Include database configuration
require_once 'config/database.php';

// ANSI color codes for terminal output
class Colors {
    const RESET = "\033[0m";
    const RED = "\033[31m";
    const GREEN = "\033[32m";
    const YELLOW = "\033[33m";
    const BLUE = "\033[34m";
    const MAGENTA = "\033[35m";
    const CYAN = "\033[36m";
    const WHITE = "\033[37m";
    const BOLD = "\033[1m";
}

class DatabaseCLI {
    private $db;
    private $connection;
    
    public function __construct() {
        try {
            $this->db = Database::getInstance();
            $this->connection = $this->db->getConnection();
            $this->printHeader();
        } catch (Exception $e) {
            $this->error("Database connection failed: " . $e->getMessage());
            exit(1);
        }
    }
    
    /**
     * Print CLI header
     */
    private function printHeader() {
        echo Colors::CYAN . Colors::BOLD;
        echo "╔══════════════════════════════════════════════════════════════╗\n";
        echo "║                    Database CLI Tool                        ║\n";
        echo "║                Online Banking System                        ║\n";
        echo "╚══════════════════════════════════════════════════════════════╝\n";
        echo Colors::RESET;
        echo Colors::GREEN . "Connected to: " . Colors::BOLD . DB_NAME . "@" . DB_HOST . Colors::RESET . "\n\n";
    }
    
    /**
     * Main command dispatcher
     */
    public function run($args) {
        if (count($args) < 2) {
            $this->showHelp();
            return;
        }
        
        $command = strtolower($args[1]);
        
        switch ($command) {
            case 'query':
            case 'q':
                if (isset($args[2])) {
                    $this->executeQuery($args[2]);
                } else {
                    $this->error("Usage: php database_cli.php query \"SQL STATEMENT\"");
                }
                break;
                
            case 'tables':
            case 't':
                $this->listTables();
                break;
                
            case 'describe':
            case 'desc':
            case 'd':
                if (isset($args[2])) {
                    $this->describeTable($args[2]);
                } else {
                    $this->error("Usage: php database_cli.php describe TABLE_NAME");
                }
                break;
                
            case 'stats':
            case 's':
                $this->showStats();
                break;
                
            case 'backup':
            case 'b':
                $this->createBackup();
                break;
                
            case 'users':
            case 'u':
                $this->listUsers();
                break;
                
            case 'interactive':
            case 'i':
                $this->interactiveMode();
                break;
                
            case 'help':
            case 'h':
            case '--help':
                $this->showHelp();
                break;
                
            default:
                $this->error("Unknown command: $command");
                $this->showHelp();
        }
    }
    
    /**
     * Execute SQL query
     */
    private function executeQuery($sql) {
        $this->info("Executing query: " . $sql);
        echo str_repeat("-", 60) . "\n";
        
        try {
            $start_time = microtime(true);
            $result = $this->db->query($sql);
            $execution_time = round((microtime(true) - $start_time) * 1000, 2);
            
            if ($result === true) {
                $this->success("Query executed successfully in {$execution_time}ms");
                $affected = $this->connection->affected_rows;
                if ($affected > 0) {
                    echo "Affected rows: " . Colors::YELLOW . $affected . Colors::RESET . "\n";
                }
            } elseif ($result && $result->num_rows > 0) {
                $this->success("Query executed successfully in {$execution_time}ms");
                $this->displayResults($result);
            } else {
                $this->warning("Query executed but returned no results");
            }
        } catch (Exception $e) {
            $this->error("Query failed: " . $e->getMessage());
        }
    }
    
    /**
     * List all tables
     */
    private function listTables() {
        $this->info("Database Tables:");
        echo str_repeat("-", 60) . "\n";
        
        try {
            $result = $this->db->query("SHOW TABLES");
            $count = 0;
            
            while ($row = $result->fetch_array()) {
                $count++;
                echo sprintf("%2d. %s\n", $count, Colors::CYAN . $row[0] . Colors::RESET);
            }
            
            echo str_repeat("-", 60) . "\n";
            $this->success("Total tables: $count");
        } catch (Exception $e) {
            $this->error("Failed to list tables: " . $e->getMessage());
        }
    }
    
    /**
     * Describe table structure
     */
    private function describeTable($tableName) {
        $this->info("Table structure: $tableName");
        echo str_repeat("-", 80) . "\n";
        
        try {
            $result = $this->db->query("DESCRIBE `$tableName`");
            
            // Print header
            printf("%-20s %-20s %-8s %-8s %-8s %-10s\n",
                Colors::BOLD . "Field" . Colors::RESET,
                Colors::BOLD . "Type" . Colors::RESET,
                Colors::BOLD . "Null" . Colors::RESET,
                Colors::BOLD . "Key" . Colors::RESET,
                Colors::BOLD . "Default" . Colors::RESET,
                Colors::BOLD . "Extra" . Colors::RESET
            );
            echo str_repeat("-", 80) . "\n";
            
            while ($row = $result->fetch_assoc()) {
                printf("%-20s %-20s %-8s %-8s %-8s %-10s\n",
                    Colors::CYAN . $row['Field'] . Colors::RESET,
                    $row['Type'],
                    $row['Null'],
                    Colors::YELLOW . $row['Key'] . Colors::RESET,
                    $row['Default'] ?? 'NULL',
                    $row['Extra']
                );
            }
            
            // Get row count
            $count_result = $this->db->query("SELECT COUNT(*) as count FROM `$tableName`");
            $count = $count_result->fetch_assoc()['count'];
            
            echo str_repeat("-", 80) . "\n";
            $this->success("Total rows: $count");
            
        } catch (Exception $e) {
            $this->error("Failed to describe table: " . $e->getMessage());
        }
    }
    
    /**
     * Show database statistics
     */
    private function showStats() {
        $this->info("Database Statistics:");
        echo str_repeat("-", 60) . "\n";
        
        try {
            // Table count
            $tables = $this->db->query("SHOW TABLES");
            $table_count = $tables->num_rows;
            
            // Database size
            $size_query = "SELECT 
                ROUND(SUM(data_length + index_length) / 1024 / 1024, 2) AS size_mb
                FROM information_schema.TABLES 
                WHERE table_schema = DATABASE()";
            $size_result = $this->db->query($size_query);
            $size_mb = $size_result->fetch_assoc()['size_mb'] ?? 0;
            
            // User count
            $users = $this->db->query("SELECT COUNT(*) as count FROM accounts");
            $user_count = $users->fetch_assoc()['count'];
            
            // Transaction count
            $transactions = $this->db->query("SELECT COUNT(*) as count FROM transfers");
            $transaction_count = $transactions->fetch_assoc()['count'];
            
            // Active users
            $active_users = $this->db->query("SELECT COUNT(*) as count FROM accounts WHERE status = 'active'");
            $active_count = $active_users->fetch_assoc()['count'];
            
            // Recent transactions (last 24 hours)
            $recent_transactions = $this->db->query("SELECT COUNT(*) as count FROM transfers WHERE created_at >= DATE_SUB(NOW(), INTERVAL 24 HOUR)");
            $recent_count = $recent_transactions->fetch_assoc()['count'];
            
            printf("%-25s: %s\n", "Database Name", Colors::CYAN . DB_NAME . Colors::RESET);
            printf("%-25s: %s\n", "Host", Colors::CYAN . DB_HOST . Colors::RESET);
            printf("%-25s: %s\n", "Tables", Colors::YELLOW . $table_count . Colors::RESET);
            printf("%-25s: %s MB\n", "Database Size", Colors::YELLOW . $size_mb . Colors::RESET);
            printf("%-25s: %s\n", "Total Users", Colors::GREEN . $user_count . Colors::RESET);
            printf("%-25s: %s\n", "Active Users", Colors::GREEN . $active_count . Colors::RESET);
            printf("%-25s: %s\n", "Total Transactions", Colors::BLUE . $transaction_count . Colors::RESET);
            printf("%-25s: %s\n", "Recent Transactions", Colors::BLUE . $recent_count . Colors::RESET);
            
        } catch (Exception $e) {
            $this->error("Failed to get statistics: " . $e->getMessage());
        }
    }
    
    /**
     * Create database backup
     */
    private function createBackup() {
        $this->info("Creating database backup...");
        
        $filename = 'backup_' . date('Y-m-d_H-i-s') . '.sql';
        $backup_dir = 'sql/';
        
        if (!is_dir($backup_dir)) {
            mkdir($backup_dir, 0755, true);
        }
        
        $command = sprintf(
            'mysqldump -u %s -p%s --single-transaction --routines --triggers %s > "%s"',
            DB_USERNAME,
            DB_PASSWORD,
            DB_NAME,
            $backup_dir . $filename
        );
        
        exec($command, $output, $return_code);
        
        if ($return_code === 0) {
            $this->success("Backup created: " . $backup_dir . $filename);
        } else {
            $this->error("Backup failed. Make sure mysqldump is available in PATH.");
        }
    }
    
    /**
     * List user accounts
     */
    private function listUsers() {
        $this->info("User Accounts:");
        echo str_repeat("-", 100) . "\n";
        
        try {
            $result = $this->db->query("SELECT id, username, email, first_name, last_name, status, balance, created_at FROM accounts ORDER BY id LIMIT 20");
            
            // Print header
            printf("%-4s %-15s %-25s %-20s %-10s %-12s %-15s\n",
                Colors::BOLD . "ID" . Colors::RESET,
                Colors::BOLD . "Username" . Colors::RESET,
                Colors::BOLD . "Email" . Colors::RESET,
                Colors::BOLD . "Name" . Colors::RESET,
                Colors::BOLD . "Status" . Colors::RESET,
                Colors::BOLD . "Balance" . Colors::RESET,
                Colors::BOLD . "Created" . Colors::RESET
            );
            echo str_repeat("-", 100) . "\n";
            
            while ($row = $result->fetch_assoc()) {
                $status_color = $row['status'] === 'active' ? Colors::GREEN : Colors::RED;
                
                printf("%-4s %-15s %-25s %-20s %s%-10s%s $%-11s %-15s\n",
                    $row['id'],
                    Colors::CYAN . $row['username'] . Colors::RESET,
                    $row['email'],
                    $row['first_name'] . ' ' . $row['last_name'],
                    $status_color,
                    $row['status'],
                    Colors::RESET,
                    number_format($row['balance'], 2),
                    date('Y-m-d', strtotime($row['created_at']))
                );
            }
            
            echo str_repeat("-", 100) . "\n";
            $this->info("Showing first 20 users. Use 'query' command for more specific results.");
            
        } catch (Exception $e) {
            $this->error("Failed to list users: " . $e->getMessage());
        }
    }
    
    /**
     * Interactive mode
     */
    private function interactiveMode() {
        $this->info("Entering interactive mode. Type 'exit' to quit.");
        echo str_repeat("-", 60) . "\n";
        
        while (true) {
            echo Colors::CYAN . "sql> " . Colors::RESET;
            $input = trim(fgets(STDIN));
            
            if (strtolower($input) === 'exit' || strtolower($input) === 'quit') {
                $this->info("Goodbye!");
                break;
            }
            
            if (empty($input)) {
                continue;
            }
            
            $this->executeQuery($input);
            echo "\n";
        }
    }
    
    /**
     * Display query results in table format
     */
    private function displayResults($result) {
        $fields = $result->fetch_fields();
        $rows = [];
        
        // Fetch all rows
        while ($row = $result->fetch_assoc()) {
            $rows[] = $row;
        }
        
        if (empty($rows)) {
            $this->warning("No results found");
            return;
        }
        
        // Calculate column widths
        $widths = [];
        foreach ($fields as $field) {
            $widths[$field->name] = max(strlen($field->name), 10);
        }
        
        foreach ($rows as $row) {
            foreach ($row as $key => $value) {
                $widths[$key] = max($widths[$key], strlen($value ?? 'NULL'));
            }
        }
        
        // Print header
        foreach ($fields as $field) {
            printf("%-{$widths[$field->name]}s ", Colors::BOLD . $field->name . Colors::RESET);
        }
        echo "\n";
        
        $total_width = array_sum($widths) + count($widths) - 1;
        echo str_repeat("-", $total_width) . "\n";
        
        // Print rows
        foreach ($rows as $row) {
            foreach ($fields as $field) {
                $value = $row[$field->name] ?? 'NULL';
                if ($value === 'NULL') {
                    $value = Colors::YELLOW . 'NULL' . Colors::RESET;
                }
                printf("%-{$widths[$field->name]}s ", $value);
            }
            echo "\n";
        }
        
        echo str_repeat("-", $total_width) . "\n";
        $this->success("Rows returned: " . count($rows));
    }
    
    /**
     * Show help information
     */
    private function showHelp() {
        echo Colors::YELLOW . Colors::BOLD . "Database CLI Tool - Help\n" . Colors::RESET;
        echo str_repeat("=", 60) . "\n\n";
        
        echo Colors::CYAN . "USAGE:\n" . Colors::RESET;
        echo "  php database_cli.php [command] [options]\n\n";
        
        echo Colors::CYAN . "COMMANDS:\n" . Colors::RESET;
        echo "  query \"SQL\"     Execute SQL query\n";
        echo "  tables           List all tables\n";
        echo "  describe TABLE   Show table structure\n";
        echo "  stats            Show database statistics\n";
        echo "  backup           Create database backup\n";
        echo "  users            List user accounts\n";
        echo "  interactive      Enter interactive SQL mode\n";
        echo "  help             Show this help\n\n";
        
        echo Colors::CYAN . "EXAMPLES:\n" . Colors::RESET;
        echo "  php database_cli.php query \"SELECT * FROM accounts LIMIT 5\"\n";
        echo "  php database_cli.php describe accounts\n";
        echo "  php database_cli.php stats\n";
        echo "  php database_cli.php interactive\n\n";
        
        echo Colors::CYAN . "SHORTCUTS:\n" . Colors::RESET;
        echo "  q = query, t = tables, d = describe, s = stats\n";
        echo "  b = backup, u = users, i = interactive, h = help\n\n";
    }
    
    /**
     * Print success message
     */
    private function success($message) {
        echo Colors::GREEN . "✓ " . $message . Colors::RESET . "\n";
    }
    
    /**
     * Print info message
     */
    private function info($message) {
        echo Colors::BLUE . "ℹ " . $message . Colors::RESET . "\n";
    }
    
    /**
     * Print warning message
     */
    private function warning($message) {
        echo Colors::YELLOW . "⚠ " . $message . Colors::RESET . "\n";
    }
    
    /**
     * Print error message
     */
    private function error($message) {
        echo Colors::RED . "✗ " . $message . Colors::RESET . "\n";
    }
}

// Check if running from command line
if (php_sapi_name() !== 'cli') {
    die("This script must be run from the command line.\n");
}

// Create and run CLI
$cli = new DatabaseCLI();
$cli->run($argv);
?>
