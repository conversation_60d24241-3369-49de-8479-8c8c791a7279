<?php
require_once '../config/config.php';
requireAdmin();

$page_title = 'Wire Transfer Fields';

// Define page actions
$page_actions = [
    [
        'url' => 'billing-code-settings.php',
        'label' => 'Global Settings',
        'icon' => 'fas fa-key'
    ],
    [
        'url' => 'edit-billing-code.php',
        'label' => 'User Codes',
        'icon' => 'fas fa-user-edit'
    ]
];

// Handle form submissions
$success_message = '';
$error_message = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $action = $_POST['action'] ?? '';
    
    if ($action === 'add_field') {
        try {
            $db = getDB();
            
            $field_name = sanitizeInput($_POST['field_name'] ?? '');
            $field_label = sanitizeInput($_POST['field_label'] ?? '');
            $field_type = sanitizeInput($_POST['field_type'] ?? 'text');
            $field_placeholder = sanitizeInput($_POST['field_placeholder'] ?? '');
            $field_group = sanitizeInput($_POST['field_group'] ?? 'general');
            $is_required = isset($_POST['is_required']) ? 1 : 0;
            $is_active = isset($_POST['is_active']) ? 1 : 0;
            $help_text = sanitizeInput($_POST['help_text'] ?? '');
            
            if (!empty($field_name) && !empty($field_label)) {
                $sql = "INSERT INTO wire_transfer_fields (field_name, field_label, field_type, field_placeholder, field_group, is_required, is_active, help_text, created_by) 
                        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)";
                $db->query($sql, [$field_name, $field_label, $field_type, $field_placeholder, $field_group, $is_required, $is_active, $help_text, $_SESSION['user_id']]);
                
                $success_message = 'Wire transfer field added successfully!';
            } else {
                $error_message = 'Field name and label are required.';
            }
            
        } catch (Exception $e) {
            $error_message = 'Error adding field: ' . $e->getMessage();
        }
        
    } elseif ($action === 'update_field') {
        try {
            $db = getDB();
            
            $field_id = intval($_POST['field_id'] ?? 0);
            $field_label = sanitizeInput($_POST['field_label'] ?? '');
            $field_placeholder = sanitizeInput($_POST['field_placeholder'] ?? '');
            $field_group = sanitizeInput($_POST['field_group'] ?? 'general');
            $is_required = isset($_POST['is_required']) ? 1 : 0;
            $is_active = isset($_POST['is_active']) ? 1 : 0;
            $help_text = sanitizeInput($_POST['help_text'] ?? '');
            
            if ($field_id > 0 && !empty($field_label)) {
                $sql = "UPDATE wire_transfer_fields SET field_label = ?, field_placeholder = ?, field_group = ?, is_required = ?, is_active = ?, help_text = ?, updated_by = ? WHERE id = ?";
                $db->query($sql, [$field_label, $field_placeholder, $field_group, $is_required, $is_active, $help_text, $_SESSION['user_id'], $field_id]);
                
                $success_message = 'Wire transfer field updated successfully!';
            } else {
                $error_message = 'Invalid field data.';
            }
            
        } catch (Exception $e) {
            $error_message = 'Error updating field: ' . $e->getMessage();
        }
        
    } elseif ($action === 'delete_field') {
        try {
            $db = getDB();
            $field_id = intval($_POST['field_id'] ?? 0);
            
            if ($field_id > 0) {
                $db->query("DELETE FROM wire_transfer_fields WHERE id = ?", [$field_id]);
                $success_message = 'Wire transfer field deleted successfully!';
            }
            
        } catch (Exception $e) {
            $error_message = 'Error deleting field: ' . $e->getMessage();
        }
    }
}

// Fetch all wire transfer fields
$fields = [];
try {
    $db = getDB();
    $result = $db->query("SELECT * FROM wire_transfer_fields ORDER BY field_group, display_order, field_label");
    while ($row = $result->fetch_assoc()) {
        $fields[] = $row;
    }
} catch (Exception $e) {
    $error_message = 'Error loading fields: ' . $e->getMessage();
}

// Group fields by category
$grouped_fields = [];
foreach ($fields as $field) {
    $grouped_fields[$field['field_group']][] = $field;
}

include 'includes/admin-header.php';
?>

<!-- Breadcrumb -->
<nav aria-label="breadcrumb" style="margin-bottom: 1rem;">
    <ol class="breadcrumb">
        <li class="breadcrumb-item"><a href="index.php">Admin</a></li>
        <li class="breadcrumb-item active" aria-current="page">Wire Transfer Fields</li>
    </ol>
</nav>

<?php if ($success_message): ?>
<div class="alert alert-success alert-dismissible fade show" role="alert">
    <i class="fas fa-check-circle me-2"></i><?php echo $success_message; ?>
    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
</div>
<?php endif; ?>

<?php if ($error_message): ?>
<div class="alert alert-danger alert-dismissible fade show" role="alert">
    <i class="fas fa-exclamation-triangle me-2"></i><?php echo $error_message; ?>
    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
</div>
<?php endif; ?>

<!-- Add New Field Card -->
<div class="row row-cards">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h3 class="card-title">
                    <i class="fas fa-plus me-2"></i>
                    Add New Wire Transfer Field
                </h3>
                <div class="card-actions">
                    <span class="badge bg-info">
                        <i class="fas fa-list me-1"></i>
                        <?php echo count($fields); ?> Fields
                    </span>
                </div>
            </div>
            <div class="card-body">
                <form method="POST" action="">
                    <input type="hidden" name="action" value="add_field">
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label class="form-label">
                                <i class="fas fa-tag me-1"></i>
                                Field Name (Internal)
                            </label>
                            <input type="text" name="field_name" class="form-control" 
                                   placeholder="e.g., beneficiary_phone" required>
                            <div class="form-text">Internal field name (lowercase, underscores only)</div>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label class="form-label">
                                <i class="fas fa-text-width me-1"></i>
                                Field Label (Display)
                            </label>
                            <input type="text" name="field_label" class="form-control" 
                                   placeholder="e.g., Beneficiary Phone Number" required>
                            <div class="form-text">Label shown to users</div>
                        </div>
                        <div class="col-md-4 mb-3">
                            <label class="form-label">
                                <i class="fas fa-list me-1"></i>
                                Field Type
                            </label>
                            <select name="field_type" class="form-select" required>
                                <option value="text">Text</option>
                                <option value="email">Email</option>
                                <option value="number">Number</option>
                                <option value="tel">Phone</option>
                                <option value="textarea">Textarea</option>
                                <option value="select">Select Dropdown</option>
                                <option value="url">URL</option>
                            </select>
                        </div>
                        <div class="col-md-4 mb-3">
                            <label class="form-label">
                                <i class="fas fa-layer-group me-1"></i>
                                Field Group
                            </label>
                            <select name="field_group" class="form-select" required>
                                <option value="beneficiary">Beneficiary Information</option>
                                <option value="bank">Bank Information</option>
                                <option value="amount">Amount & Currency</option>
                                <option value="details">Transfer Details</option>
                                <option value="compliance">Compliance & Verification</option>
                                <option value="general">General</option>
                            </select>
                        </div>
                        <div class="col-md-4 mb-3">
                            <label class="form-label">
                                <i class="fas fa-keyboard me-1"></i>
                                Placeholder Text
                            </label>
                            <input type="text" name="field_placeholder" class="form-control" 
                                   placeholder="Enter placeholder text">
                        </div>
                        <div class="col-12 mb-3">
                            <label class="form-label">
                                <i class="fas fa-question-circle me-1"></i>
                                Help Text
                            </label>
                            <textarea name="help_text" class="form-control" rows="2" 
                                      placeholder="Optional help text to guide users"></textarea>
                        </div>
                        <div class="col-12 mb-3">
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-check">
                                        <input type="checkbox" name="is_required" class="form-check-input" id="is_required">
                                        <label class="form-check-label" for="is_required">
                                            <i class="fas fa-asterisk text-danger me-1"></i>
                                            Required Field
                                        </label>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-check">
                                        <input type="checkbox" name="is_active" class="form-check-input" id="is_active" checked>
                                        <label class="form-check-label" for="is_active">
                                            <i class="fas fa-toggle-on text-success me-1"></i>
                                            Active Field
                                        </label>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-plus me-2"></i>
                        Add Field
                    </button>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- Existing Fields -->
<div class="row row-cards mt-3">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h3 class="card-title">
                    <i class="fas fa-list-alt me-2"></i>
                    Existing Wire Transfer Fields
                </h3>
            </div>
            <div class="card-body">
                <?php if (empty($fields)): ?>
                <div class="text-center py-4">
                    <i class="fas fa-inbox fa-3x text-muted mb-3"></i>
                    <h4 class="text-muted">No Fields Configured</h4>
                    <p class="text-muted">Add your first wire transfer field using the form above.</p>
                </div>
                <?php else: ?>
                <?php foreach ($grouped_fields as $group => $group_fields): ?>
                <div class="mb-4">
                    <h4 class="mb-3">
                        <i class="fas fa-folder me-2"></i>
                        <?php echo ucwords(str_replace('_', ' ', $group)); ?> Fields
                        <span class="badge bg-secondary ms-2"><?php echo count($group_fields); ?></span>
                    </h4>
                    <div class="table-responsive">
                        <table class="table table-striped">
                            <thead>
                                <tr>
                                    <th>Field Name</th>
                                    <th>Label</th>
                                    <th>Type</th>
                                    <th>Status</th>
                                    <th>Required</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($group_fields as $field): ?>
                                <tr>
                                    <td><code><?php echo htmlspecialchars($field['field_name']); ?></code></td>
                                    <td><?php echo htmlspecialchars($field['field_label']); ?></td>
                                    <td>
                                        <span class="badge bg-info">
                                            <?php echo ucfirst($field['field_type']); ?>
                                        </span>
                                    </td>
                                    <td>
                                        <?php if ($field['is_active']): ?>
                                        <span class="badge bg-success">
                                            <i class="fas fa-check me-1"></i>Active
                                        </span>
                                        <?php else: ?>
                                        <span class="badge bg-secondary">
                                            <i class="fas fa-times me-1"></i>Inactive
                                        </span>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <?php if ($field['is_required']): ?>
                                        <span class="badge bg-warning">
                                            <i class="fas fa-asterisk me-1"></i>Required
                                        </span>
                                        <?php else: ?>
                                        <span class="badge bg-light text-dark">Optional</span>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <div class="btn-group btn-group-sm">
                                            <button type="button" class="btn btn-outline-primary" 
                                                    onclick="editField(<?php echo htmlspecialchars(json_encode($field)); ?>)">
                                                <i class="fas fa-edit"></i>
                                            </button>
                                            <form method="POST" style="display: inline;" 
                                                  onsubmit="return confirm('Are you sure you want to delete this field?')">
                                                <input type="hidden" name="action" value="delete_field">
                                                <input type="hidden" name="field_id" value="<?php echo $field['id']; ?>">
                                                <button type="submit" class="btn btn-outline-danger">
                                                    <i class="fas fa-trash"></i>
                                                </button>
                                            </form>
                                        </div>
                                    </td>
                                </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
                <?php endforeach; ?>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<!-- Edit Field Modal -->
<div class="modal fade" id="editFieldModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-edit me-2"></i>
                    Edit Wire Transfer Field
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form method="POST" action="">
                <input type="hidden" name="action" value="update_field">
                <input type="hidden" name="field_id" id="edit_field_id">
                <div class="modal-body">
                    <div class="mb-3">
                        <label class="form-label">Field Label</label>
                        <input type="text" name="field_label" id="edit_field_label" class="form-control" required>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">Placeholder Text</label>
                        <input type="text" name="field_placeholder" id="edit_field_placeholder" class="form-control">
                    </div>
                    <div class="mb-3">
                        <label class="form-label">Field Group</label>
                        <select name="field_group" id="edit_field_group" class="form-select">
                            <option value="beneficiary">Beneficiary Information</option>
                            <option value="bank">Bank Information</option>
                            <option value="amount">Amount & Currency</option>
                            <option value="details">Transfer Details</option>
                            <option value="compliance">Compliance & Verification</option>
                            <option value="general">General</option>
                        </select>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">Help Text</label>
                        <textarea name="help_text" id="edit_help_text" class="form-control" rows="2"></textarea>
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-check">
                                <input type="checkbox" name="is_required" id="edit_is_required" class="form-check-input">
                                <label class="form-check-label" for="edit_is_required">Required Field</label>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-check">
                                <input type="checkbox" name="is_active" id="edit_is_active" class="form-check-input">
                                <label class="form-check-label" for="edit_is_active">Active Field</label>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save me-2"></i>
                        Save Changes
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
function editField(field) {
    document.getElementById('edit_field_id').value = field.id;
    document.getElementById('edit_field_label').value = field.field_label;
    document.getElementById('edit_field_placeholder').value = field.field_placeholder || '';
    document.getElementById('edit_field_group').value = field.field_group;
    document.getElementById('edit_help_text').value = field.help_text || '';
    document.getElementById('edit_is_required').checked = field.is_required == 1;
    document.getElementById('edit_is_active').checked = field.is_active == 1;
    
    new bootstrap.Modal(document.getElementById('editFieldModal')).show();
}
</script>

<?php include 'includes/admin-footer.php'; ?>
