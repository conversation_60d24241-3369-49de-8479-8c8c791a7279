/**
 * Transfer Statements Page JavaScript
 * Handles modal interactions, filtering, and export functionality
 */

// Global variables
let currentTransfer = null;

// Main initialization function
document.addEventListener('DOMContentLoaded', function() {
    console.log('🚀 Statements page initializing...');

    // Check for modal element
    const modal = document.getElementById('transferModal');
    if (modal) {
        console.log('✅ Modal element found');
    } else {
        console.error('❌ Modal element NOT found');
    }

    // Initialize all components
    initializeModal();
    initializeFilters();
    initializeTableInteractions();
    initializeExportFunctionality();
    animateBalanceCards();

    // Make functions globally available
    window.showTransferDetails = showTransferDetails;
    window.closeModal = closeModal;
    window.printReceipt = printReceipt;
    window.downloadReceipt = downloadReceipt;
    window.exportToPDF = exportToPDF;
    window.clearFilters = clearFilters;

    console.log('✅ Statements page fully initialized');
});

/**
 * Show transfer details modal
 */
function showTransferDetails(row) {
    console.log('🧾 Opening transfer receipt for:', row);

    const modal = document.getElementById('transferModal');
    const content = document.getElementById('modalContent');

    if (!modal || !content) {
        console.error('❌ Modal elements not found');
        alert('Receipt not available. Please refresh the page.');
        return;
    }

    try {
        // Get transfer data from row
        const transferData = JSON.parse(row.dataset.transfer);
        currentTransfer = transferData;

        // Generate transfer receipt content
        content.innerHTML = generateTransferReceiptContent(transferData);

        // Show modal with animation
        modal.style.display = 'block';
        document.body.style.overflow = 'hidden';

        console.log('✅ Transfer receipt opened successfully');
    } catch (error) {
        console.error('❌ Error opening transfer details:', error);
        alert('Error loading transfer details. Please try again.');
    }
}

/**
 * Generate transfer receipt content
 */
function generateTransferReceiptContent(transfer) {
    const currentDate = new Date().toLocaleString();
    const transferDate = new Date(transfer.created_at).toLocaleString();
    const amount = parseFloat(transfer.amount) || 0;
    const fee = parseFloat(transfer.fee) || 0;

    return `
        <!-- Transfer Information Section -->
        <div class="receipt-section">
            <div class="receipt-section-title">Transfer Information</div>
            <div class="receipt-row">
                <span class="receipt-label">Transaction ID:</span>
                <span class="receipt-value">${transfer.id || 'N/A'}</span>
            </div>
            <div class="receipt-row">
                <span class="receipt-label">Reference Number:</span>
                <span class="receipt-value">${transfer.transaction_id}</span>
            </div>
            <div class="receipt-row">
                <span class="receipt-label">Transfer Type:</span>
                <span class="receipt-value">${formatTransferType(transfer.transfer_type)}</span>
            </div>
            <div class="receipt-row">
                <span class="receipt-label">Status:</span>
                <span class="receipt-value">
                    <span class="receipt-status ${transfer.status || 'completed'}">${(transfer.status || 'Completed').toUpperCase()}</span>
                </span>
            </div>
        </div>

        <!-- Amount Details Section -->
        <div class="receipt-section">
            <div class="receipt-section-title">Amount Details</div>
            <div class="receipt-row">
                <span class="receipt-label">Transfer Amount:</span>
                <span class="receipt-value receipt-amount negative">
                    -$${amount.toFixed(2)}
                </span>
            </div>
            <div class="receipt-row">
                <span class="receipt-label">Processing Fee:</span>
                <span class="receipt-value">${fee > 0 ? '$' + fee.toFixed(2) : 'Free'}</span>
            </div>
            <div class="receipt-row">
                <span class="receipt-label">Total Debited:</span>
                <span class="receipt-value receipt-amount negative">
                    -$${(amount + fee).toFixed(2)}
                </span>
            </div>
        </div>

        <!-- Account Information Section -->
        <div class="receipt-section full-width">
            <div class="receipt-section-title">Account & Recipient Information</div>
            <div class="receipt-content" style="grid-template-columns: 1fr 1fr;">
                <div>
                    <div class="receipt-row">
                        <span class="receipt-label">Account Holder:</span>
                        <span class="receipt-value">James Bong</span>
                    </div>
                    <div class="receipt-row">
                        <span class="receipt-label">Account Number:</span>
                        <span class="receipt-value">****7588</span>
                    </div>
                    <div class="receipt-row">
                        <span class="receipt-label">Account Type:</span>
                        <span class="receipt-value">Savings Account</span>
                    </div>
                </div>
                <div>
                    <div class="receipt-row">
                        <span class="receipt-label">Recipient Name:</span>
                        <span class="receipt-value">${transfer.recipient_display_name || transfer.recipient_name}</span>
                    </div>
                    <div class="receipt-row">
                        <span class="receipt-label">Recipient Account:</span>
                        <span class="receipt-value">****${transfer.recipient_account.slice(-4)}</span>
                    </div>
                    <div class="receipt-row">
                        <span class="receipt-label">Institution:</span>
                        <span class="receipt-value">${getInstitutionName(transfer.transfer_category)}</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- Transfer Details Section -->
        <div class="receipt-section full-width">
            <div class="receipt-section-title">Transfer Details</div>
            <div class="receipt-row">
                <span class="receipt-label">Description:</span>
                <span class="receipt-value">${transfer.description || 'No additional description provided'}</span>
            </div>
            <div class="receipt-row">
                <span class="receipt-label">Transfer Date:</span>
                <span class="receipt-value">${transferDate}</span>
            </div>
            <div class="receipt-row">
                <span class="receipt-label">Processing Time:</span>
                <span class="receipt-value">${getProcessingTime(transfer.transfer_type)}</span>
            </div>
            <div class="receipt-row">
                <span class="receipt-label">Receipt Generated:</span>
                <span class="receipt-value">${currentDate}</span>
            </div>
        </div>

        <!-- Balance Information Section -->
        <div class="receipt-section full-width">
            <div class="receipt-section-title">Account Balance Information</div>
            <div class="receipt-content" style="grid-template-columns: 1fr 1fr;">
                <div>
                    <div class="receipt-row">
                        <span class="receipt-label">Previous Balance:</span>
                        <span class="receipt-value">$${(4841300.00 + amount + fee).toFixed(2)}</span>
                    </div>
                </div>
                <div>
                    <div class="receipt-row">
                        <span class="receipt-label">Current Balance:</span>
                        <span class="receipt-value receipt-amount positive">$4,841,300.00</span>
                    </div>
                </div>
            </div>
        </div>
    `;
}

/**
 * Helper functions for receipt generation
 */
function formatTransferType(type) {
    const types = {
        'local': 'Local Bank Transfer',
        'inter-bank': 'Inter-Bank Transfer',
        'international': 'International Wire Transfer',
        'wire': 'Wire Transfer'
    };
    return types[type] || type.charAt(0).toUpperCase() + type.slice(1) + ' Transfer';
}

function getInstitutionName(category) {
    const institutions = {
        'External Bank': 'External Banking Institution',
        'Internal Bank': 'PremierBank Pro',
        'Local Bank': 'Local Banking Network',
        'Wire Transfer': 'International Wire Network'
    };
    return institutions[category] || category || 'Banking Institution';
}

function getProcessingTime(type) {
    const times = {
        'local': 'Same business day',
        'inter-bank': 'Instant',
        'international': '1-3 business days',
        'wire': '1-2 business days'
    };
    return times[type] || 'Standard processing time';
}

/**
 * Close modal
 */
function closeModal() {
    const modal = document.getElementById('transferModal');
    if (modal) {
        modal.style.display = 'none';
        document.body.style.overflow = 'auto';
        currentTransfer = null;
    }
}

/**
 * Print receipt
 */
function printReceipt() {
    if (!currentTransfer) {
        alert('No transfer data available for printing.');
        return;
    }

    const printWindow = window.open('', '_blank');
    const receiptContent = generateTransferReceiptContent(currentTransfer);

    printWindow.document.write(`
        <!DOCTYPE html>
        <html>
        <head>
            <title>Transfer Receipt - ${currentTransfer.transaction_id}</title>
            <style>
                body { font-family: 'Courier New', monospace; margin: 20px; background: white; }
                .receipt-section { margin-bottom: 20px; border-bottom: 1px solid #eee; padding-bottom: 15px; }
                .receipt-row { display: flex; justify-content: space-between; margin-bottom: 8px; }
                .label { font-weight: bold; }
                .value { text-align: right; }
                .total { border-top: 2px solid #333; padding-top: 8px; margin-top: 8px; }
                .receipt-description { background: #f5f5f5; padding: 10px; border-radius: 4px; }
                h4 { color: #333; border-bottom: 2px solid #007bff; padding-bottom: 5px; }
            </style>
        </head>
        <body>
            <h2>PremierBank Pro - Transfer Receipt</h2>
            ${receiptContent}
            <p style="text-align: center; margin-top: 30px; font-size: 12px; color: #666;">
                This is a computer-generated receipt. Please retain for your records.
            </p>
        </body>
        </html>
    `);

    printWindow.document.close();
    printWindow.print();
}

/**
 * Download receipt as PDF
 */
function downloadReceipt() {
    if (!currentTransfer) {
        alert('No transfer data available for download.');
        return;
    }

    console.log('📥 Downloading transfer receipt...');

    // Create a temporary form to submit transfer data for PDF generation
    const form = document.createElement('form');
    form.method = 'POST';
    form.action = 'generate-receipt-pdf.php';
    form.target = '_blank';

    // Add transfer data as hidden inputs
    const transferData = document.createElement('input');
    transferData.type = 'hidden';
    transferData.name = 'transfer_data';
    transferData.value = JSON.stringify(currentTransfer);
    form.appendChild(transferData);

    // Add to body, submit, and remove
    document.body.appendChild(form);
    form.submit();
    document.body.removeChild(form);

    console.log('✅ PDF download initiated');
}

/**
 * Initialize modal functionality
 */
function initializeModal() {
    // Add escape key handler
    document.addEventListener('keydown', function(e) {
        if (e.key === 'Escape') {
            closeModal();
        }
    });

    // Add backdrop click handler
    const modal = document.getElementById('transferModal');
    if (modal) {
        modal.addEventListener('click', function(e) {
            if (e.target === modal || e.target.classList.contains('modal-overlay')) {
                closeModal();
            }
        });
    }

    console.log('✅ Modal initialized successfully!');
}

/**
 * Initialize filter functionality
 */
function initializeFilters() {
    const filterForm = document.querySelector('.filters-form');
    
    if (filterForm) {
        // Auto-submit form when date inputs change
        const dateInputs = filterForm.querySelectorAll('input[type="date"]');
        dateInputs.forEach(input => {
            input.addEventListener('change', function() {
                // Add small delay to allow user to set both dates
                setTimeout(() => {
                    if (this.value) {
                        filterForm.submit();
                    }
                }, 500);
            });
        });
        
        // Auto-submit when select changes
        const selectInputs = filterForm.querySelectorAll('select');
        selectInputs.forEach(select => {
            select.addEventListener('change', function() {
                filterForm.submit();
            });
        });
    }

    console.log('✅ Filters initialized');
}

/**
 * Initialize table interactions
 */
function initializeTableInteractions() {
    // Add click listeners to table rows
    const tableRows = document.querySelectorAll('.statements-table tbody tr[data-transfer]');
    console.log(`🔍 Found ${tableRows.length} transfer rows`);

    tableRows.forEach((row, index) => {
        row.addEventListener('click', function(e) {
            console.log(`🖱️ Transfer row ${index + 1} clicked`);
            showTransferDetails(this);
        });
    });

    console.log('✅ Table interactions initialized');
}

/**
 * Initialize export functionality
 */
function initializeExportFunctionality() {
    // Export functionality will be implemented here
    console.log('✅ Export functionality initialized');
}

/**
 * Animate balance cards
 */
function animateBalanceCards() {
    const cards = document.querySelectorAll('.balance-card-new');
    cards.forEach((card, index) => {
        setTimeout(() => {
            card.style.opacity = '0';
            card.style.transform = 'translateY(20px)';
            card.style.transition = 'all 0.5s ease';
            
            setTimeout(() => {
                card.style.opacity = '1';
                card.style.transform = 'translateY(0)';
            }, 50);
        }, index * 100);
    });
}

/**
 * Clear all filters
 */
function clearFilters() {
    window.location.href = window.location.pathname;
}

/**
 * Print bank statement
 */
function exportToPDF() {
    console.log('🖨️ Opening print-friendly bank statement...');

    // Open print-friendly statement in new tab
    window.open('export-statement-pdf.php', '_blank');

    console.log('✅ Print statement opened');
}

/**
 * Format date and time
 */
function formatDateTime(dateString) {
    if (!dateString) return 'N/A';

    try {
        const date = new Date(dateString);
        return date.toLocaleDateString('en-US', {
            year: 'numeric',
            month: 'short',
            day: 'numeric',
            hour: '2-digit',
            minute: '2-digit'
        });
    } catch (e) {
        return dateString;
    }
}

/**
 * Format currency values
 */
function formatCurrency(amount, currency = 'USD') {
    return new Intl.NumberFormat('en-US', {
        style: 'currency',
        currency: currency
    }).format(amount);
}
