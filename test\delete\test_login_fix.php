<?php
/**
 * Test Login Fix - Verify the corrected 2FA logic
 */

require_once 'config/config.php';
require_once 'includes/user-2fa-functions.php';

echo "<h2>🔧 Login Fix Verification</h2>";

// Test the corrected logic
$test_scenarios = [
    [
        'name' => 'OTP ON, 2FA OFF',
        'otp_enabled' => 1,
        'require_2fa' => 0,
        'google_2fa_enabled' => 0,
        'expected' => 'OTP Verification',
        'your_observation' => 'Works correctly'
    ],
    [
        'name' => 'OTP OFF, 2FA ON',
        'otp_enabled' => 0,
        'require_2fa' => 1,
        'google_2fa_enabled' => 0,
        'expected' => 'Direct Login',
        'your_observation' => 'Was going to OTP (SHOULD BE FIXED)'
    ],
    [
        'name' => 'OTP OFF, 2FA OFF',
        'otp_enabled' => 0,
        'require_2fa' => 0,
        'google_2fa_enabled' => 0,
        'expected' => 'Direct Login',
        'your_observation' => 'Was looping (SHOULD BE FIXED)'
    ],
    [
        'name' => 'OTP ON, 2FA ON',
        'otp_enabled' => 1,
        'require_2fa' => 1,
        'google_2fa_enabled' => 0,
        'expected' => 'OTP Verification',
        'your_observation' => 'Should work'
    ],
    [
        'name' => 'Google 2FA ON',
        'otp_enabled' => 0,
        'require_2fa' => 0,
        'google_2fa_enabled' => 1,
        'expected' => 'OTP Verification',
        'your_observation' => 'Should work'
    ]
];

echo "<h3>✅ Corrected Logic Test Results</h3>";
echo "<table border='1' cellpadding='8' cellspacing='0' style='border-collapse: collapse; width: 100%;'>";
echo "<tr style='background-color: #e8f5e8;'>";
echo "<th>Scenario</th><th>OTP</th><th>2FA Req</th><th>Google 2FA</th><th>New Logic Result</th><th>Expected</th><th>Status</th>";
echo "</tr>";

foreach ($test_scenarios as $scenario) {
    // Test the corrected logic: OTP verification if OTP enabled OR Google 2FA enabled
    $new_logic_result = ($scenario['otp_enabled'] == 1) || ($scenario['google_2fa_enabled'] == 1);
    $new_behavior = $new_logic_result ? 'OTP Verification' : 'Direct Login';
    
    // Check if corrected logic matches expected
    $is_correct = ($new_behavior === $scenario['expected']);
    $status_color = $is_correct ? 'green' : 'red';
    $status_text = $is_correct ? '✅ FIXED' : '❌ Still Wrong';
    
    echo "<tr>";
    echo "<td><strong>" . htmlspecialchars($scenario['name']) . "</strong></td>";
    echo "<td>" . ($scenario['otp_enabled'] ? 'ON' : 'OFF') . "</td>";
    echo "<td>" . ($scenario['require_2fa'] ? 'ON' : 'OFF') . "</td>";
    echo "<td>" . ($scenario['google_2fa_enabled'] ? 'ON' : 'OFF') . "</td>";
    echo "<td><strong>" . htmlspecialchars($new_behavior) . "</strong></td>";
    echo "<td>" . htmlspecialchars($scenario['expected']) . "</td>";
    echo "<td style='color: $status_color;'><strong>$status_text</strong></td>";
    echo "</tr>";
}

echo "</table>";

echo "<h3>🔍 Test with Real User (if available)</h3>";

try {
    $db = getDB();
    
    // Find a test user
    $sql = "SELECT a.id, a.username, a.first_name, uss.otp_enabled, uss.require_2fa, uss.google_2fa_enabled 
            FROM accounts a 
            LEFT JOIN user_security_settings uss ON a.id = uss.user_id 
            WHERE a.is_admin = 0 
            LIMIT 1";
    $result = $db->query($sql);
    
    if ($result && $result->num_rows > 0) {
        $user = $result->fetch_assoc();
        $user_id = $user['id'];
        
        echo "<div style='background-color: #f8f9fa; padding: 15px; border: 1px solid #dee2e6; border-radius: 5px;'>";
        echo "<h4>Testing with User: " . htmlspecialchars($user['username']) . "</h4>";
        
        // Get current settings
        $settings = getUserSecuritySettings($user_id);
        if ($settings) {
            echo "<p><strong>Current Settings:</strong></p>";
            echo "<ul>";
            echo "<li>OTP Enabled: " . ($settings['otp_enabled'] ? 'Yes' : 'No') . "</li>";
            echo "<li>2FA Required: " . ($settings['require_2fa'] ? 'Yes' : 'No') . "</li>";
            echo "<li>Google 2FA: " . ($settings['google_2fa_enabled'] ? 'Yes' : 'No') . "</li>";
            echo "</ul>";
            
            // Test the function
            $should_verify = shouldUserVerifyOTP($user_id);
            echo "<p><strong>Login Decision:</strong> ";
            if ($should_verify) {
                echo "<span style='color: orange;'>🔒 User will be redirected to OTP verification</span>";
            } else {
                echo "<span style='color: green;'>🚀 User will be logged in directly to dashboard</span>";
            }
            echo "</p>";
            
            // Show the logic reasoning
            echo "<p><strong>Reasoning:</strong> ";
            if ($settings['otp_enabled']) {
                echo "OTP is enabled";
            } elseif ($settings['google_2fa_enabled']) {
                echo "Google 2FA is enabled";
            } else {
                echo "No 2FA methods are enabled";
            }
            echo "</p>";
        } else {
            echo "<p style='color: red;'>❌ Could not retrieve user security settings</p>";
        }
        echo "</div>";
        
    } else {
        echo "<p>No users found to test with. <a href='create_test_user.php'>Create a test user</a></p>";
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'>Error: " . htmlspecialchars($e->getMessage()) . "</p>";
}

echo "<h3>🛠️ Changes Made</h3>";
echo "<div style='background-color: #d4edda; padding: 15px; border: 1px solid #c3e6cb; border-radius: 5px;'>";
echo "<h4>Fixed Issues:</h4>";
echo "<ol>";
echo "<li><strong>Corrected Logic:</strong> Changed from <code>(OTP enabled AND 2FA required) OR Google 2FA</code> to <code>OTP enabled OR Google 2FA</code></li>";
echo "<li><strong>Default Settings:</strong> New users now get OTP and 2FA disabled by default instead of enabled</li>";
echo "<li><strong>Fallback Behavior:</strong> If no settings found, default to NOT requiring OTP (better UX)</li>";
echo "</ol>";
echo "</div>";

echo "<h3>🧪 Next Steps</h3>";
echo "<div style='background-color: #cce5ff; padding: 15px; border: 1px solid #99ccff; border-radius: 5px;'>";
echo "<h4>Test the Fix:</h4>";
echo "<ol>";
echo "<li><strong>Test Scenario 1:</strong> Go to admin panel, set a user's OTP to ON and 2FA to OFF → Should go to OTP verification</li>";
echo "<li><strong>Test Scenario 2:</strong> Set OTP to OFF and 2FA to ON → Should skip OTP and go directly to dashboard</li>";
echo "<li><strong>Test Scenario 3:</strong> Set both OTP and 2FA to OFF → Should skip OTP and go directly to dashboard</li>";
echo "</ol>";
echo "</div>";

echo "<hr>";
echo "<p><strong>Quick Links:</strong></p>";
echo "<p>";
echo "<a href='login.php' style='margin-right: 15px;'>🔐 Test Login</a>";
echo "<a href='admin/user-security-management.php' style='margin-right: 15px;'>⚙️ User Security Management</a>";
echo "<a href='debug_login_logic.php' style='margin-right: 15px;'>🔍 Debug Logic</a>";
echo "<a href='create_test_user.php'>👤 Create Test User</a>";
echo "</p>";

?>
