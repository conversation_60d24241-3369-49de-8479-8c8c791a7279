<?php
/**
 * Super Admin Authentication Functions
 * Handles session management and access control for super administrators
 */

require_once __DIR__ . '/2fa-functions.php';

/**
 * Check if user is logged in as super admin
 */
function isSuperAdminLoggedIn() {
    return isset($_SESSION['super_admin_logged_in']) && $_SESSION['super_admin_logged_in'] === true;
}

/**
 * Check if super admin has completed 2FA verification
 */
function isSuperAdmin2FAVerified() {
    return isset($_SESSION['super_admin_2fa_verified']) && $_SESSION['super_admin_2fa_verified'] === true;
}

/**
 * Check if super admin needs 2FA verification
 */
function requiresSuperAdmin2FA() {
    // Check if 2FA is required system-wide first
    if (!isSuperAdmin2FARequired()) {
        // If system-wide 2FA is not required, then 2FA is optional
        return false;
    }

    // If system-wide 2FA is required, check if user has it enabled
    $username = $_SESSION['super_admin_username'] ?? 'superadmin';
    return isSuperAdmin2FAEnabled($username);
}

/**
 * Require super admin authentication (redirect if not logged in)
 */
function requireSuperAdminAuth() {
    if (!isSuperAdminLoggedIn()) {
        header('Location: login.php');
        exit;
    }

    // Check if 2FA is required and not yet verified
    if (requiresSuperAdmin2FA() && !isSuperAdmin2FAVerified()) {
        header('Location: verify-2fa.php');
        exit;
    }

    // Check session timeout (30 minutes)
    $timeout = 30 * 60; // 30 minutes in seconds
    if (isset($_SESSION['super_admin_login_time']) && (time() - $_SESSION['super_admin_login_time']) > $timeout) {
        logSuperAdminAction('session_timeout', 'Super admin session expired');
        destroySuperAdminSession();
        header('Location: login.php?timeout=1');
        exit;
    }

    // Update last activity time
    $_SESSION['super_admin_last_activity'] = time();
}

/**
 * Get current super admin username
 */
function getSuperAdminUsername() {
    return $_SESSION['super_admin_username'] ?? 'Unknown';
}

/**
 * Get super admin session info
 */
function getSuperAdminSessionInfo() {
    if (!isSuperAdminLoggedIn()) {
        return null;
    }
    
    return [
        'username' => $_SESSION['super_admin_username'] ?? 'Unknown',
        'login_time' => $_SESSION['super_admin_login_time'] ?? time(),
        'last_activity' => $_SESSION['super_admin_last_activity'] ?? time(),
        'ip_address' => $_SESSION['super_admin_ip'] ?? 'Unknown',
        'session_duration' => time() - ($_SESSION['super_admin_login_time'] ?? time())
    ];
}

/**
 * Destroy super admin session
 */
function destroySuperAdminSession() {
    // Log logout
    if (isSuperAdminLoggedIn()) {
        logSuperAdminAction('logout', 'Super admin logged out');
    }

    // Clear super admin session variables
    unset($_SESSION['super_admin_logged_in']);
    unset($_SESSION['super_admin_username']);
    unset($_SESSION['super_admin_login_time']);
    unset($_SESSION['super_admin_last_activity']);
    unset($_SESSION['super_admin_ip']);

    // Clear 2FA session variables
    unset($_SESSION['super_admin_2fa_verified']);
    unset($_SESSION['super_admin_2fa_pending']);
    unset($_SESSION['temp_super_admin_2fa_secret']);
}

/**
 * Log super admin actions for audit trail
 */
function logSuperAdminAction($action, $description, $details = []) {
    try {
        require_once __DIR__ . '/../../config/database.php';
        $db = getDB();
        
        // Create audit log table if it doesn't exist
        $create_table_sql = "CREATE TABLE IF NOT EXISTS `super_admin_audit_log` (
            `id` int(11) NOT NULL AUTO_INCREMENT,
            `action` varchar(100) NOT NULL,
            `description` text NOT NULL,
            `details` json DEFAULT NULL,
            `username` varchar(100) DEFAULT NULL,
            `ip_address` varchar(45) DEFAULT NULL,
            `user_agent` text DEFAULT NULL,
            `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
            PRIMARY KEY (`id`),
            KEY `idx_action` (`action`),
            KEY `idx_username` (`username`),
            KEY `idx_created_at` (`created_at`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
        
        $db->query($create_table_sql);
        
        // Insert audit log entry
        $sql = "INSERT INTO super_admin_audit_log (action, description, details, username, ip_address, user_agent) 
                VALUES (?, ?, ?, ?, ?, ?)";
        
        $db->query($sql, [
            $action,
            $description,
            json_encode($details),
            getSuperAdminUsername(),
            $_SERVER['REMOTE_ADDR'] ?? 'unknown',
            $_SERVER['HTTP_USER_AGENT'] ?? 'unknown'
        ]);
    } catch (Exception $e) {
        error_log("Failed to log super admin action: " . $e->getMessage());
    }
}

/**
 * Get super admin audit logs
 */
function getSuperAdminAuditLogs($limit = 100, $offset = 0) {
    try {
        require_once __DIR__ . '/../../config/database.php';
        $db = getDB();
        
        $sql = "SELECT * FROM super_admin_audit_log 
                ORDER BY created_at DESC 
                LIMIT ? OFFSET ?";
        
        $result = $db->query($sql, [$limit, $offset]);
        $logs = [];
        
        while ($row = $result->fetch_assoc()) {
            $row['details'] = json_decode($row['details'], true);
            $logs[] = $row;
        }
        
        return $logs;
    } catch (Exception $e) {
        error_log("Failed to get super admin audit logs: " . $e->getMessage());
        return [];
    }
}

/**
 * Check if IP address is allowed (basic security)
 */
function isIPAllowed($ip) {
    // In production, you might want to restrict access to specific IP ranges
    // For now, allow all IPs
    return true;
}

/**
 * Generate CSRF token for forms
 */
function generateCSRFToken() {
    if (!isset($_SESSION['super_admin_csrf_token'])) {
        $_SESSION['super_admin_csrf_token'] = bin2hex(random_bytes(32));
    }
    return $_SESSION['super_admin_csrf_token'];
}

/**
 * Verify CSRF token
 */
function verifyCSRFToken($token) {
    return isset($_SESSION['super_admin_csrf_token']) && 
           hash_equals($_SESSION['super_admin_csrf_token'], $token);
}

/**
 * Get super admin dashboard stats
 */
function getSuperAdminStats() {
    try {
        require_once __DIR__ . '/../../config/database.php';
        $db = getDB();
        
        $stats = [];

        // Total regular users (excluding admins)
        $result = $db->query("SELECT COUNT(*) as count FROM accounts WHERE (is_admin = 0 OR is_admin IS NULL) AND (role = 'user' OR role IS NULL)");
        $stats['total_users'] = $result ? $result->fetch_assoc()['count'] : 0;

        // Active regular users (logged in within last 30 days, excluding admins)
        $result = $db->query("SELECT COUNT(*) as count FROM accounts WHERE (is_admin = 0 OR is_admin IS NULL) AND (role = 'user' OR role IS NULL) AND last_login >= DATE_SUB(NOW(), INTERVAL 30 DAY)");
        $stats['active_users'] = $result ? $result->fetch_assoc()['count'] : 0;

        // Total admin accounts (from both tables)
        $result1 = $db->query("SELECT COUNT(*) as count FROM accounts WHERE is_admin = 1 OR role IN ('admin', 'super_admin')");
        $admin_count_accounts = $result1 ? $result1->fetch_assoc()['count'] : 0;

        $result2 = $db->query("SELECT COUNT(*) as count FROM admin_users");
        $admin_count_admin_users = $result2 ? $result2->fetch_assoc()['count'] : 0;

        $stats['total_admins'] = $admin_count_accounts + $admin_count_admin_users;
        
        // Total transactions today (from account_transactions table)
        $result = $db->query("SELECT COUNT(*) as count FROM account_transactions WHERE DATE(created_at) = CURDATE()");
        $stats['transactions_today'] = $result ? $result->fetch_assoc()['count'] : 0;
        
        // System settings count
        $result = $db->query("SELECT COUNT(*) as count FROM super_admin_settings");
        $stats['system_settings'] = $result ? $result->fetch_assoc()['count'] : 0;
        
        // Recent audit log entries
        $result = $db->query("SELECT COUNT(*) as count FROM super_admin_audit_log WHERE created_at >= DATE_SUB(NOW(), INTERVAL 24 HOUR)");
        $stats['audit_entries_24h'] = $result ? $result->fetch_assoc()['count'] : 0;
        
        return $stats;
    } catch (Exception $e) {
        error_log("Failed to get super admin stats: " . $e->getMessage());
        return [
            'total_users' => 0,
            'active_users' => 0,
            'total_admins' => 0,
            'transactions_today' => 0,
            'system_settings' => 0,
            'audit_entries_24h' => 0
        ];
    }
}

/**
 * Format time duration for display
 */
function formatDuration($seconds) {
    if ($seconds < 60) {
        return $seconds . ' seconds';
    } elseif ($seconds < 3600) {
        return floor($seconds / 60) . ' minutes';
    } else {
        return floor($seconds / 3600) . ' hours, ' . floor(($seconds % 3600) / 60) . ' minutes';
    }
}

/**
 * Get super admin menu items
 */
function getSuperAdminMenuItems() {
    return [
        [
            'title' => 'Dashboard',
            'icon' => 'fas fa-tachometer-alt',
            'url' => 'dashboard.php',
            'active' => basename($_SERVER['PHP_SELF']) === 'dashboard.php'
        ],
        [
            'title' => 'System Settings',
            'icon' => 'fas fa-cogs',
            'url' => 'system-settings.php',
            'active' => basename($_SERVER['PHP_SELF']) === 'system-settings.php'
        ],
        [
            'title' => 'Appearance Settings',
            'icon' => 'fas fa-palette',
            'url' => 'appearance-settings.php',
            'active' => basename($_SERVER['PHP_SELF']) === 'appearance-settings.php'
        ],
        [
            'title' => 'Email Templates',
            'icon' => 'fas fa-envelope',
            'url' => 'email-templates.php',
            'active' => basename($_SERVER['PHP_SELF']) === 'email-templates.php'
        ],
        [
            'title' => 'SMTP Configuration',
            'icon' => 'fas fa-server',
            'url' => 'smtp-config.php',
            'active' => basename($_SERVER['PHP_SELF']) === 'smtp-config.php'
        ],
        [
            'title' => 'Admin Management',
            'icon' => 'fas fa-users-cog',
            'url' => 'user-management.php',
            'active' => basename($_SERVER['PHP_SELF']) === 'user-management.php'
        ],
        [
            'title' => 'Audit Logs',
            'icon' => 'fas fa-clipboard-list',
            'url' => 'audit-logs.php',
            'active' => basename($_SERVER['PHP_SELF']) === 'audit-logs.php'
        ],
        [
            'title' => 'Security',
            'icon' => 'fas fa-shield-alt',
            'url' => 'security.php',
            'active' => basename($_SERVER['PHP_SELF']) === 'security.php'
        ],
        [
            'title' => '2FA Setup',
            'icon' => 'fas fa-mobile-alt',
            'url' => 'setup-2fa.php',
            'active' => in_array(basename($_SERVER['PHP_SELF']), ['setup-2fa.php', '2fa-setup.php'])
        ]
    ];
}

/**
 * Generate page breadcrumbs
 */
function generateBreadcrumbs($current_page) {
    $breadcrumbs = [
        ['title' => 'Super Admin', 'url' => 'dashboard.php']
    ];
    
    $page_titles = [
        'dashboard.php' => 'Dashboard',
        'system-settings.php' => 'System Settings',
        'appearance-settings.php' => 'Appearance Settings',
        'email-templates.php' => 'Email Templates',
        'smtp-config.php' => 'SMTP Configuration',
        'user-management.php' => 'Admin Management',
        'audit-logs.php' => 'Audit Logs',
        'security.php' => 'Security',
        'setup-2fa.php' => '2FA Setup',
        '2fa-setup.php' => '2FA Configuration'
    ];
    
    if (isset($page_titles[$current_page]) && $current_page !== 'dashboard.php') {
        $breadcrumbs[] = ['title' => $page_titles[$current_page], 'url' => null];
    }
    
    return $breadcrumbs;
}
?>
