<?php
require_once '../config/config.php';
requireAdmin();

$page_title = 'Email Delivery Debug Tool';

// Include PHPMailer
require_once '../vendor/autoload.php';
use <PERSON><PERSON>Mailer\PHPMailer\PHPMailer;
use <PERSON><PERSON><PERSON>ailer\PHPMailer\Exception;

$debug_results = [];

if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action']) && $_POST['action'] === 'debug_email') {
    try {
        $test_email = trim($_POST['test_email']);
        $debug_level = intval($_POST['debug_level']);
        
        if (empty($test_email) || !filter_var($test_email, FILTER_VALIDATE_EMAIL)) {
            throw new Exception("Please provide a valid email address for testing.");
        }
        
        $db = getDB();
        
        // Get email settings
        $settings_query = "SELECT * FROM email_settings WHERE is_active = 1 ORDER BY created_at DESC LIMIT 1";
        $settings_result = $db->query($settings_query);
        $email_settings = $settings_result->fetch_assoc();
        
        if (!$email_settings) {
            throw new Exception("No email settings found. Please configure email settings first.");
        }
        
        $debug_results['settings'] = $email_settings;
        
        // Test SMTP connection
        $mail = new PHPMailer(true);
        $mail->SMTPDebug = $debug_level; // Enable verbose debug output
        $mail->isSMTP();
        $mail->Host = $email_settings['smtp_host'];
        $mail->SMTPAuth = true;
        $mail->Username = $email_settings['smtp_username'];
        $mail->Password = base64_decode($email_settings['smtp_password']);
        $mail->SMTPSecure = $email_settings['smtp_encryption'];
        $mail->Port = $email_settings['smtp_port'];
        
        // Capture debug output
        ob_start();
        
        // Test connection
        $debug_results['connection_test'] = $mail->smtpConnect();
        
        if ($debug_results['connection_test']) {
            $debug_results['connection_status'] = 'SUCCESS';
            $debug_results['connection_message'] = 'SMTP connection established successfully';
            
            // Send test email
            $mail->setFrom($email_settings['sender_email'], $email_settings['sender_name']);
            $mail->addAddress($test_email);
            $mail->isHTML(true);
            $mail->Subject = 'Email Debug Test - ' . date('Y-m-d H:i:s');
            
            $mail->Body = '
            <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px;">
                <div style="background-color: #e8f5e8; padding: 20px; border-radius: 8px; border-left: 4px solid #28a745;">
                    <h2 style="color: #155724; margin: 0 0 15px 0;">✅ Email Debug Test Successful!</h2>
                    <p style="color: #155724; margin: 0; line-height: 1.6;">
                        This email was sent successfully through the SMTP configuration debug test.
                    </p>
                </div>
                
                <div style="margin: 20px 0; padding: 15px; background-color: #f8f9fa; border-radius: 5px;">
                    <h3 style="margin: 0 0 10px 0;">Debug Information:</h3>
                    <ul style="margin: 0; padding-left: 20px;">
                        <li><strong>SMTP Host:</strong> ' . htmlspecialchars($email_settings['smtp_host']) . '</li>
                        <li><strong>SMTP Port:</strong> ' . $email_settings['smtp_port'] . '</li>
                        <li><strong>Encryption:</strong> ' . strtoupper($email_settings['smtp_encryption']) . '</li>
                        <li><strong>Sender:</strong> ' . htmlspecialchars($email_settings['sender_email']) . '</li>
                        <li><strong>Test Time:</strong> ' . date('Y-m-d H:i:s') . '</li>
                        <li><strong>Debug Level:</strong> ' . $debug_level . '</li>
                    </ul>
                </div>
                
                <div style="background-color: #fff3cd; padding: 15px; border-radius: 5px; border-left: 4px solid #ffc107;">
                    <h3 style="color: #856404; margin: 0 0 10px 0;">📧 Email Delivery Tips:</h3>
                    <ul style="color: #856404; margin: 0; padding-left: 20px; font-size: 0.9rem;">
                        <li>Check your spam/junk folder if you don\'t see this email in your inbox</li>
                        <li>Add ' . htmlspecialchars($email_settings['sender_email']) . ' to your contacts</li>
                        <li>Some email providers may delay delivery by a few minutes</li>
                        <li>Corporate email systems may have additional filtering</li>
                    </ul>
                </div>
                
                <div style="text-align: center; margin-top: 20px; padding-top: 15px; border-top: 1px solid #dee2e6;">
                    <p style="color: #6c757d; margin: 0; font-size: 0.85rem;">
                        This email was sent automatically by the Online Banking System Email Debug Tool.<br>
                        If you received this email unexpectedly, please contact your system administrator.
                    </p>
                </div>
            </div>';
            
            $send_result = $mail->send();
            
            if ($send_result) {
                $debug_results['send_status'] = 'SUCCESS';
                $debug_results['send_message'] = 'Email sent successfully to ' . $test_email;
                
                // Log successful test
                $log_email = "INSERT INTO email_logs (
                    recipient_email, subject, email_type, method_used, status, sent_by
                ) VALUES (?, ?, ?, ?, ?, ?)";
                $db->query($log_email, [
                    $test_email, 'Email Debug Test', 'debug', 'smtp', 'sent', $_SESSION['user_id']
                ]);
            } else {
                $debug_results['send_status'] = 'FAILED';
                $debug_results['send_message'] = 'Failed to send email';
            }
            
        } else {
            $debug_results['connection_status'] = 'FAILED';
            $debug_results['connection_message'] = 'Failed to establish SMTP connection';
        }
        
        $debug_results['smtp_debug_output'] = ob_get_clean();
        
        // Additional diagnostics
        $debug_results['php_mail_enabled'] = function_exists('mail');
        $debug_results['openssl_enabled'] = extension_loaded('openssl');
        $debug_results['socket_enabled'] = function_exists('fsockopen');
        $debug_results['dns_lookup'] = gethostbyname($email_settings['smtp_host']);
        
        // Test DNS resolution
        $debug_results['dns_test'] = ($debug_results['dns_lookup'] !== $email_settings['smtp_host']);
        
        // Test port connectivity
        $debug_results['port_test'] = @fsockopen($email_settings['smtp_host'], $email_settings['smtp_port'], $errno, $errstr, 10);
        if ($debug_results['port_test']) {
            fclose($debug_results['port_test']);
            $debug_results['port_test'] = true;
            $debug_results['port_message'] = 'Port ' . $email_settings['smtp_port'] . ' is accessible';
        } else {
            $debug_results['port_test'] = false;
            $debug_results['port_message'] = 'Port ' . $email_settings['smtp_port'] . ' is not accessible: ' . $errstr;
        }
        
    } catch (Exception $e) {
        $debug_results['error'] = $e->getMessage();
        $debug_results['smtp_debug_output'] = ob_get_clean();
        
        // Log failed test
        if (isset($test_email) && isset($db)) {
            $log_email = "INSERT INTO email_logs (
                recipient_email, subject, email_type, method_used, status, error_message, sent_by
            ) VALUES (?, ?, ?, ?, ?, ?, ?)";
            $db->query($log_email, [
                $test_email, 'Email Debug Test', 'debug', 'smtp', 'failed', $e->getMessage(), $_SESSION['user_id']
            ]);
        }
    }
}

include 'includes/admin-header.php';
?>

<!-- Breadcrumb -->
<nav aria-label="breadcrumb" style="margin-bottom: 1rem;">
    <ol class="breadcrumb">
        <li class="breadcrumb-item"><a href="index.php">Admin</a></li>
        <li class="breadcrumb-item"><a href="email-settings.php">Email Settings</a></li>
        <li class="breadcrumb-item active" aria-current="page">Email Debug</li>
    </ol>
</nav>

<!-- Debug Results -->
<?php if (!empty($debug_results)): ?>
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h3 class="card-title">
                    <i class="fas fa-bug me-2"></i>
                    Email Debug Results
                </h3>
            </div>
            <div class="card-body">
                <?php if (isset($debug_results['error'])): ?>
                <div class="alert alert-danger">
                    <h4 class="alert-title">❌ Debug Test Failed</h4>
                    <p class="mb-0"><?php echo htmlspecialchars($debug_results['error']); ?></p>
                </div>
                <?php else: ?>
                
                <!-- Connection Status -->
                <div class="alert alert-<?php echo $debug_results['connection_status'] === 'SUCCESS' ? 'success' : 'danger'; ?>">
                    <h4 class="alert-title">
                        <?php echo $debug_results['connection_status'] === 'SUCCESS' ? '✅' : '❌'; ?> 
                        SMTP Connection Test
                    </h4>
                    <p class="mb-0"><?php echo htmlspecialchars($debug_results['connection_message']); ?></p>
                </div>
                
                <!-- Send Status -->
                <?php if (isset($debug_results['send_status'])): ?>
                <div class="alert alert-<?php echo $debug_results['send_status'] === 'SUCCESS' ? 'success' : 'danger'; ?>">
                    <h4 class="alert-title">
                        <?php echo $debug_results['send_status'] === 'SUCCESS' ? '✅' : '❌'; ?> 
                        Email Send Test
                    </h4>
                    <p class="mb-0"><?php echo htmlspecialchars($debug_results['send_message']); ?></p>
                </div>
                <?php endif; ?>
                
                <?php endif; ?>
                
                <!-- System Diagnostics -->
                <div class="row mt-4">
                    <div class="col-md-6">
                        <h5>System Diagnostics</h5>
                        <table class="table table-sm">
                            <tr>
                                <td>PHP Mail Function</td>
                                <td>
                                    <span class="badge bg-<?php echo $debug_results['php_mail_enabled'] ? 'success' : 'danger'; ?> badge-sm">
                                        <?php echo $debug_results['php_mail_enabled'] ? 'Enabled' : 'Disabled'; ?>
                                    </span>
                                </td>
                            </tr>
                            <tr>
                                <td>OpenSSL Extension</td>
                                <td>
                                    <span class="badge bg-<?php echo $debug_results['openssl_enabled'] ? 'success' : 'danger'; ?> badge-sm">
                                        <?php echo $debug_results['openssl_enabled'] ? 'Enabled' : 'Disabled'; ?>
                                    </span>
                                </td>
                            </tr>
                            <tr>
                                <td>Socket Functions</td>
                                <td>
                                    <span class="badge bg-<?php echo $debug_results['socket_enabled'] ? 'success' : 'danger'; ?> badge-sm">
                                        <?php echo $debug_results['socket_enabled'] ? 'Enabled' : 'Disabled'; ?>
                                    </span>
                                </td>
                            </tr>
                            <tr>
                                <td>DNS Resolution</td>
                                <td>
                                    <span class="badge bg-<?php echo $debug_results['dns_test'] ? 'success' : 'danger'; ?> badge-sm">
                                        <?php echo $debug_results['dns_test'] ? 'Working' : 'Failed'; ?>
                                    </span>
                                    <br><small class="text-muted"><?php echo htmlspecialchars($debug_results['dns_lookup']); ?></small>
                                </td>
                            </tr>
                            <tr>
                                <td>Port Connectivity</td>
                                <td>
                                    <span class="badge bg-<?php echo $debug_results['port_test'] ? 'success' : 'danger'; ?> badge-sm">
                                        <?php echo $debug_results['port_test'] ? 'Accessible' : 'Blocked'; ?>
                                    </span>
                                    <br><small class="text-muted"><?php echo htmlspecialchars($debug_results['port_message']); ?></small>
                                </td>
                            </tr>
                        </table>
                    </div>
                    
                    <div class="col-md-6">
                        <h5>SMTP Configuration</h5>
                        <?php if (isset($debug_results['settings'])): ?>
                        <table class="table table-sm">
                            <tr>
                                <td>Host</td>
                                <td><code><?php echo htmlspecialchars($debug_results['settings']['smtp_host']); ?></code></td>
                            </tr>
                            <tr>
                                <td>Port</td>
                                <td><code><?php echo $debug_results['settings']['smtp_port']; ?></code></td>
                            </tr>
                            <tr>
                                <td>Encryption</td>
                                <td><code><?php echo strtoupper($debug_results['settings']['smtp_encryption']); ?></code></td>
                            </tr>
                            <tr>
                                <td>Username</td>
                                <td><code><?php echo htmlspecialchars($debug_results['settings']['smtp_username']); ?></code></td>
                            </tr>
                            <tr>
                                <td>Sender Email</td>
                                <td><code><?php echo htmlspecialchars($debug_results['settings']['sender_email']); ?></code></td>
                            </tr>
                        </table>
                        <?php endif; ?>
                    </div>
                </div>
                
                <!-- SMTP Debug Output -->
                <?php if (!empty($debug_results['smtp_debug_output'])): ?>
                <div class="mt-4">
                    <h5>SMTP Debug Output</h5>
                    <pre class="bg-dark text-light p-3 rounded" style="max-height: 300px; overflow-y: auto; font-size: 0.8rem;"><?php echo htmlspecialchars($debug_results['smtp_debug_output']); ?></pre>
                </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>
<?php endif; ?>

<!-- Debug Form -->
<div class="row">
    <div class="col-md-8">
        <div class="card">
            <div class="card-header">
                <h3 class="card-title">
                    <i class="fas fa-envelope-open-text me-2"></i>
                    Email Debug Test
                </h3>
            </div>
            <div class="card-body">
                <form method="POST" action="">
                    <input type="hidden" name="action" value="debug_email">

                    <div class="mb-3">
                        <label class="form-label">Test Email Address <span class="text-danger">*</span></label>
                        <input type="email" name="test_email" class="form-control form-control-lg"
                               placeholder="<EMAIL>"
                               value="<?php echo htmlspecialchars($_SESSION['email'] ?? ''); ?>" required>
                        <small class="form-hint">Email address to send debug test message to</small>
                    </div>

                    <div class="mb-3">
                        <label class="form-label">Debug Level</label>
                        <select name="debug_level" class="form-select form-select-lg">
                            <option value="0">0 - No debug output</option>
                            <option value="1" selected>1 - Client messages</option>
                            <option value="2">2 - Client and server messages</option>
                            <option value="3">3 - Client, server, and connection status</option>
                            <option value="4">4 - Low-level data output (very verbose)</option>
                        </select>
                        <small class="form-hint">Higher levels provide more detailed debugging information</small>
                    </div>

                    <div class="form-footer">
                        <button type="submit" class="btn btn-primary btn-lg">
                            <i class="fas fa-bug me-2"></i>
                            Run Email Debug Test
                        </button>
                        <a href="email-settings.php" class="btn btn-secondary btn-lg">
                            <i class="fas fa-cog me-2"></i>
                            Email Settings
                        </a>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <div class="col-md-4">
        <div class="card">
            <div class="card-header">
                <h3 class="card-title">
                    <i class="fas fa-info-circle me-2"></i>
                    Debug Information
                </h3>
            </div>
            <div class="card-body">
                <div class="alert alert-info">
                    <h4 class="alert-title">What This Tool Does</h4>
                    <ul class="mb-0" style="font-size: 0.9rem;">
                        <li>Tests SMTP connection to your email server</li>
                        <li>Verifies authentication credentials</li>
                        <li>Sends a test email with debug information</li>
                        <li>Checks system requirements and connectivity</li>
                        <li>Provides detailed error messages for troubleshooting</li>
                    </ul>
                </div>

                <div class="alert alert-warning">
                    <h4 class="alert-title">Common Issues</h4>
                    <ul class="mb-0" style="font-size: 0.9rem;">
                        <li><strong>Emails in Spam:</strong> Check spam/junk folders</li>
                        <li><strong>Port Blocked:</strong> Firewall may block SMTP ports</li>
                        <li><strong>Authentication Failed:</strong> Check username/password</li>
                        <li><strong>DNS Issues:</strong> Server cannot resolve SMTP host</li>
                        <li><strong>SSL/TLS Problems:</strong> Encryption settings mismatch</li>
                    </ul>
                </div>

                <div class="alert alert-success">
                    <h4 class="alert-title">Troubleshooting Tips</h4>
                    <ul class="mb-0" style="font-size: 0.9rem;">
                        <li>Try different debug levels for more information</li>
                        <li>Test with different email addresses</li>
                        <li>Check your email provider's SMTP settings</li>
                        <li>Verify server firewall allows outbound SMTP</li>
                        <li>Contact your hosting provider if issues persist</li>
                    </ul>
                </div>

                <div class="alert alert-secondary">
                    <h4 class="alert-title">Current SMTP Settings</h4>
                    <?php
                    try {
                        $db = getDB();
                        $settings_query = "SELECT * FROM email_settings WHERE is_active = 1 ORDER BY created_at DESC LIMIT 1";
                        $settings_result = $db->query($settings_query);
                        $current_settings = $settings_result->fetch_assoc();

                        if ($current_settings) {
                            echo '<ul class="mb-0" style="font-size: 0.9rem;">';
                            echo '<li><strong>Host:</strong> ' . htmlspecialchars($current_settings['smtp_host']) . '</li>';
                            echo '<li><strong>Port:</strong> ' . $current_settings['smtp_port'] . '</li>';
                            echo '<li><strong>Encryption:</strong> ' . strtoupper($current_settings['smtp_encryption']) . '</li>';
                            echo '<li><strong>Username:</strong> ' . htmlspecialchars($current_settings['smtp_username']) . '</li>';
                            echo '</ul>';
                        } else {
                            echo '<p class="mb-0 text-muted">No SMTP settings configured</p>';
                        }
                    } catch (Exception $e) {
                        echo '<p class="mb-0 text-danger">Error loading settings</p>';
                    }
                    ?>
                </div>
            </div>
        </div>
    </div>
</div>

<?php include 'includes/admin-footer.php'; ?>
