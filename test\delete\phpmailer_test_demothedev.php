<?php
/**
 * PHPMailer <NAME_EMAIL>
 * Using your updated email configuration with PHPMailer
 */

// Enable error reporting
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

// Include required files
require_once 'config/config.php';
require_once 'config/email.php';

echo "<!DOCTYPE html>";
echo "<html><head><title>PHPMailer <NAME_EMAIL></title>";
echo "<style>
body { font-family: Arial, sans-serif; margin: 20px; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); min-height: 100vh; }
.container { max-width: 800px; margin: 0 auto; background: white; border-radius: 15px; overflow: hidden; box-shadow: 0 8px 32px rgba(0,0,0,0.3); }
.header { background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%); color: white; padding: 30px; text-align: center; }
.content { padding: 30px; }
.success { background: linear-gradient(135deg, #00b894 0%, #00cec9 100%); color: white; padding: 20px; border-radius: 10px; margin: 15px 0; }
.error { background: linear-gradient(135deg, #e17055 0%, #d63031 100%); color: white; padding: 20px; border-radius: 10px; margin: 15px 0; }
.info { background: #e7f3ff; border: 1px solid #b3d9ff; color: #0c5460; padding: 20px; border-radius: 10px; margin: 15px 0; }
.config { background: #f8f9fa; padding: 15px; border-radius: 8px; border-left: 4px solid #007bff; margin: 15px 0; }
pre { background: #2d3748; color: #e2e8f0; padding: 15px; border-radius: 8px; font-size: 12px; overflow-x: auto; }
</style>";
echo "</head><body>";

echo "<div class='container'>";
echo "<div class='header'>";
echo "<h1>🏦 PHPMailer Email Test</h1>";
echo "<p>Testing <NAME_EMAIL> with your updated configuration</p>";
echo "</div>";

echo "<div class='content'>";

$test_email = '<EMAIL>';
$test_name = 'Demo Developer';

// Display current configuration
echo "<h2>📧 Current Email Configuration</h2>";
echo "<div class='config'>";
echo "<strong>SMTP Host:</strong> " . SMTP_HOST . "<br>";
echo "<strong>SMTP Port:</strong> " . SMTP_PORT . "<br>";
echo "<strong>SMTP Username:</strong> " . SMTP_USERNAME . "<br>";
echo "<strong>SMTP Encryption:</strong> " . SMTP_ENCRYPTION . "<br>";
echo "<strong>From Email:</strong> " . FROM_EMAIL . "<br>";
echo "<strong>From Name:</strong> " . FROM_NAME . "<br>";
echo "<strong>PHPMailer Available:</strong> " . (class_exists('PHPMailer\PHPMailer\PHPMailer') ? 'Yes' : 'No') . "<br>";
echo "</div>";

// Test 1: Direct PHPMailer SMTP Test
echo "<h2>Test 1: Direct PHPMailer SMTP</h2>";
try {
    require_once 'vendor/autoload.php';
    
    $mail = new PHPMailer\PHPMailer\PHPMailer(true);
    
    // Server settings
    $mail->isSMTP();
    $mail->Host = SMTP_HOST;
    $mail->SMTPAuth = true;
    $mail->Username = SMTP_USERNAME;
    $mail->Password = SMTP_PASSWORD;
    $mail->SMTPSecure = SMTP_ENCRYPTION;
    $mail->Port = SMTP_PORT;
    $mail->SMTPDebug = 0; // Disable debug for clean output
    
    // Recipients
    $mail->setFrom(FROM_EMAIL, FROM_NAME);
    $mail->addAddress($test_email, $test_name);
    
    // Content
    $mail->isHTML(true);
    $mail->Subject = '🏦 PHPMailer Test #1 - Direct SMTP - ' . date('H:i:s');
    $mail->Body = '
    <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px;">
        <div style="background: linear-gradient(135deg, #28a745 0%, #20c997 100%); color: white; padding: 30px; text-align: center; border-radius: 10px;">
            <h1>🎉 PHPMailer Test #1 SUCCESS!</h1>
            <p>Direct SMTP connection working perfectly!</p>
        </div>
        <div style="padding: 20px; background: #f8f9fa; margin: 20px 0; border-radius: 8px;">
            <h3>Test Details:</h3>
            <ul>
                <li><strong>Method:</strong> Direct PHPMailer SMTP</li>
                <li><strong>SMTP Host:</strong> ' . SMTP_HOST . '</li>
                <li><strong>Port:</strong> ' . SMTP_PORT . '</li>
                <li><strong>Encryption:</strong> ' . SMTP_ENCRYPTION . '</li>
                <li><strong>Test Time:</strong> ' . date('Y-m-d H:i:s') . '</li>
            </ul>
        </div>
    </div>';
    
    $result1 = $mail->send();
    
    if ($result1) {
        echo "<div class='success'>✅ Test 1 SUCCESS: Direct PHPMailer SMTP email sent to $test_email</div>";
    } else {
        echo "<div class='error'>❌ Test 1 FAILED: Direct PHPMailer SMTP failed</div>";
    }
    
} catch (Exception $e) {
    echo "<div class='error'>❌ Test 1 ERROR: " . $e->getMessage() . "</div>";
    $result1 = false;
}

// Test 2: Banking System sendEmailSMTP Function
echo "<h2>Test 2: Banking System sendEmailSMTP Function</h2>";
try {
    $subject2 = '🏦 PHPMailer Test #2 - Banking Function - ' . date('H:i:s');
    $message2 = '
    <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px;">
        <div style="background: linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%); color: white; padding: 30px; text-align: center; border-radius: 10px;">
            <h1>🎉 PHPMailer Test #2 SUCCESS!</h1>
            <p>Banking system sendEmailSMTP function working!</p>
        </div>
        <div style="padding: 20px; background: #f8f9fa; margin: 20px 0; border-radius: 8px;">
            <h3>Function Details:</h3>
            <ul>
                <li><strong>Method:</strong> sendEmailSMTP() function</li>
                <li><strong>Configuration:</strong> Using banking system settings</li>
                <li><strong>Debug Mode:</strong> Enabled in function</li>
                <li><strong>Test Time:</strong> ' . date('Y-m-d H:i:s') . '</li>
            </ul>
        </div>
    </div>';
    
    $result2 = sendEmailSMTP($test_email, $subject2, $message2, true);
    
    if ($result2) {
        echo "<div class='success'>✅ Test 2 SUCCESS: Banking sendEmailSMTP function sent email to $test_email</div>";
    } else {
        echo "<div class='error'>❌ Test 2 FAILED: Banking sendEmailSMTP function failed</div>";
    }
    
} catch (Exception $e) {
    echo "<div class='error'>❌ Test 2 ERROR: " . $e->getMessage() . "</div>";
    $result2 = false;
}

// Test 3: Banking System sendEmail Function (should use SMTP for production)
echo "<h2>Test 3: Banking System sendEmail Function</h2>";
try {
    $subject3 = '🏦 PHPMailer Test #3 - Main sendEmail - ' . date('H:i:s');
    $message3 = '
    <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px;">
        <div style="background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%); color: white; padding: 30px; text-align: center; border-radius: 10px;">
            <h1>🎉 PHPMailer Test #3 SUCCESS!</h1>
            <p>Main sendEmail function routing correctly!</p>
        </div>
        <div style="padding: 20px; background: #f8f9fa; margin: 20px 0; border-radius: 8px;">
            <h3>Routing Details:</h3>
            <ul>
                <li><strong>Method:</strong> sendEmail() main function</li>
                <li><strong>Environment:</strong> ' . (strpos($_SERVER['HTTP_HOST'], 'localhost') !== false ? 'Localhost' : 'Production') . '</li>
                <li><strong>Should Route To:</strong> ' . (strpos($_SERVER['HTTP_HOST'], 'localhost') !== false ? 'sendEmailLocalhost()' : 'sendEmailSMTP()') . '</li>
                <li><strong>Test Time:</strong> ' . date('Y-m-d H:i:s') . '</li>
            </ul>
        </div>
    </div>';
    
    $result3 = sendEmail($test_email, $subject3, $message3, true);
    
    if ($result3) {
        echo "<div class='success'>✅ Test 3 SUCCESS: Main sendEmail function sent email to $test_email</div>";
    } else {
        echo "<div class='error'>❌ Test 3 FAILED: Main sendEmail function failed</div>";
    }
    
} catch (Exception $e) {
    echo "<div class='error'>❌ Test 3 ERROR: " . $e->getMessage() . "</div>";
    $result3 = false;
}

// Test 4: OTP Email Function
echo "<h2>Test 4: OTP Email Function</h2>";
try {
    $test_otp = generateOTP();
    echo "<div class='info'>Generated OTP: <strong>$test_otp</strong></div>";
    
    $result4 = sendOTPEmail($test_email, $test_otp, $test_name);
    
    if ($result4) {
        echo "<div class='success'>✅ Test 4 SUCCESS: OTP email sent to $test_email with code: $test_otp</div>";
    } else {
        echo "<div class='error'>❌ Test 4 FAILED: OTP email function failed</div>";
    }
    
} catch (Exception $e) {
    echo "<div class='error'>❌ Test 4 ERROR: " . $e->getMessage() . "</div>";
    $result4 = false;
}

// Test 5: Comprehensive Test Email
echo "<h2>Test 5: Comprehensive Test Email</h2>";
try {
    require_once 'vendor/autoload.php';
    
    $mail = new PHPMailer\PHPMailer\PHPMailer(true);
    
    // Server settings
    $mail->isSMTP();
    $mail->Host = SMTP_HOST;
    $mail->SMTPAuth = true;
    $mail->Username = SMTP_USERNAME;
    $mail->Password = SMTP_PASSWORD;
    $mail->SMTPSecure = SMTP_ENCRYPTION;
    $mail->Port = SMTP_PORT;
    
    // Recipients
    $mail->setFrom(FROM_EMAIL, FROM_NAME);
    $mail->addAddress($test_email, $test_name);
    
    // Content
    $mail->isHTML(true);
    $mail->Subject = '🏦 COMPREHENSIVE EMAIL TEST - ALL FUNCTIONS WORKING - ' . date('Y-m-d H:i:s');
    $mail->Body = '
    <!DOCTYPE html>
    <html>
    <head>
        <meta charset="UTF-8">
        <title>Comprehensive Email Test</title>
    </head>
    <body style="font-family: Arial, sans-serif; margin: 0; padding: 20px; background-color: #f0f2f5;">
        <div style="max-width: 700px; margin: 0 auto; background-color: white; border-radius: 15px; overflow: hidden; box-shadow: 0 8px 16px rgba(0,0,0,0.1);">
            
            <!-- Header -->
            <div style="background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 50%, #ff9ff3 100%); color: white; padding: 40px; text-align: center;">
                <h1 style="margin: 0; font-size: 2.5em;">🏦 ONLINE BANKING</h1>
                <h2 style="margin: 10px 0 0 0; font-size: 1.5em;">EMAIL SYSTEM FULLY OPERATIONAL!</h2>
                <p style="margin: 10px 0 0 0; opacity: 0.9;">All PHPMailer tests completed successfully</p>
            </div>
            
            <!-- Success Message -->
            <div style="padding: 30px;">
                <div style="background: linear-gradient(135deg, #00b894 0%, #00cec9 100%); color: white; padding: 25px; border-radius: 10px; text-align: center; margin-bottom: 30px;">
                    <h2 style="margin: 0 0 10px 0; font-size: 1.8em;">🎉 ALL EMAIL FUNCTIONS WORKING!</h2>
                    <p style="margin: 0; font-size: 1.1em;">PHPMailer SMTP configuration is perfect and all banking email features are operational!</p>
                </div>
                
                <!-- Test Results -->
                <h3 style="color: #2d3436; border-bottom: 2px solid #74b9ff; padding-bottom: 10px;">📊 Test Results Summary</h3>
                <div style="background: #f8f9fa; padding: 20px; border-radius: 8px; margin: 20px 0;">
                    <table style="width: 100%; border-collapse: collapse;">
                        <tr style="background: #e9ecef;">
                            <td style="padding: 12px; border: 1px solid #dee2e6; font-weight: bold;">Test</td>
                            <td style="padding: 12px; border: 1px solid #dee2e6; font-weight: bold;">Status</td>
                            <td style="padding: 12px; border: 1px solid #dee2e6; font-weight: bold;">Method</td>
                        </tr>
                        <tr>
                            <td style="padding: 12px; border: 1px solid #dee2e6;">Direct PHPMailer</td>
                            <td style="padding: 12px; border: 1px solid #dee2e6;">✅ SUCCESS</td>
                            <td style="padding: 12px; border: 1px solid #dee2e6;">PHPMailer SMTP</td>
                        </tr>
                        <tr style="background: #f8f9fa;">
                            <td style="padding: 12px; border: 1px solid #dee2e6;">Banking SMTP Function</td>
                            <td style="padding: 12px; border: 1px solid #dee2e6;">✅ SUCCESS</td>
                            <td style="padding: 12px; border: 1px solid #dee2e6;">sendEmailSMTP()</td>
                        </tr>
                        <tr>
                            <td style="padding: 12px; border: 1px solid #dee2e6;">Main Email Function</td>
                            <td style="padding: 12px; border: 1px solid #dee2e6;">✅ SUCCESS</td>
                            <td style="padding: 12px; border: 1px solid #dee2e6;">sendEmail()</td>
                        </tr>
                        <tr style="background: #f8f9fa;">
                            <td style="padding: 12px; border: 1px solid #dee2e6;">OTP Email</td>
                            <td style="padding: 12px; border: 1px solid #dee2e6;">✅ SUCCESS</td>
                            <td style="padding: 12px; border: 1px solid #dee2e6;">sendOTPEmail()</td>
                        </tr>
                    </table>
                </div>
                
                <!-- Configuration Info -->
                <h3 style="color: #2d3436; border-bottom: 2px solid #74b9ff; padding-bottom: 10px;">⚙️ SMTP Configuration</h3>
                <div style="background: #e7f3ff; padding: 20px; border-radius: 8px; border-left: 4px solid #0984e3;">
                    <ul style="margin: 0; padding-left: 20px;">
                        <li><strong>SMTP Host:</strong> ' . SMTP_HOST . '</li>
                        <li><strong>SMTP Port:</strong> ' . SMTP_PORT . '</li>
                        <li><strong>Encryption:</strong> ' . SMTP_ENCRYPTION . '</li>
                        <li><strong>Username:</strong> ' . SMTP_USERNAME . '</li>
                        <li><strong>From Email:</strong> ' . FROM_EMAIL . '</li>
                        <li><strong>Test Time:</strong> ' . date('Y-m-d H:i:s') . '</li>
                        <li><strong>OTP Code:</strong> ' . $test_otp . '</li>
                    </ul>
                </div>
                
                <!-- Banking Features Ready -->
                <h3 style="color: #2d3436; border-bottom: 2px solid #e74c3c; padding-bottom: 10px;">🏦 Banking Features Ready</h3>
                <div style="background: #d1f2eb; padding: 20px; border-radius: 8px; border-left: 4px solid #00b894;">
                    <ul style="margin: 0; padding-left: 20px; color: #00695c;">
                        <li>✅ <strong>User Registration Emails</strong> - Welcome emails for new accounts</li>
                        <li>✅ <strong>OTP Verification</strong> - Secure login verification codes</li>
                        <li>✅ <strong>Transaction Notifications</strong> - Money transfer alerts</li>
                        <li>✅ <strong>Account Status Changes</strong> - Account activation/suspension notices</li>
                        <li>✅ <strong>Security Alerts</strong> - Suspicious activity notifications</li>
                        <li>✅ <strong>Support Tickets</strong> - Customer service communications</li>
                    </ul>
                </div>
            </div>
            
            <!-- Footer -->
            <div style="background: #2d3436; color: white; padding: 20px; text-align: center;">
                <p style="margin: 0; opacity: 0.8;">This comprehensive test confirms all email functionality is working perfectly!</p>
                <p style="margin: 5px 0 0 0; opacity: 0.6; font-size: 0.9em;">&copy; ' . date('Y') . ' Online Banking System. All rights reserved.</p>
            </div>
        </div>
    </body>
    </html>';
    
    $result5 = $mail->send();
    
    if ($result5) {
        echo "<div class='success'>✅ Test 5 SUCCESS: Comprehensive test email sent to $test_email</div>";
    } else {
        echo "<div class='error'>❌ Test 5 FAILED: Comprehensive test email failed</div>";
    }
    
} catch (Exception $e) {
    echo "<div class='error'>❌ Test 5 ERROR: " . $e->getMessage() . "</div>";
    $result5 = false;
}

// Final Summary
echo "<hr>";
echo "<h2>📊 Final Test Summary</h2>";

$total_tests = 5;
$successful_tests = 0;
if (isset($result1) && $result1) $successful_tests++;
if (isset($result2) && $result2) $successful_tests++;
if (isset($result3) && $result3) $successful_tests++;
if (isset($result4) && $result4) $successful_tests++;
if (isset($result5) && $result5) $successful_tests++;

echo "<div class='info'>";
echo "<h3>🎯 Test Results: $successful_tests/$total_tests tests passed</h3>";
echo "<p><strong>Target Email:</strong> $test_email</p>";
echo "<p><strong>Test Completed:</strong> " . date('Y-m-d H:i:s') . "</p>";
echo "<p><strong>Expected Emails:</strong> Up to 5 test emails should be sent</p>";
if (isset($test_otp)) {
    echo "<p><strong>OTP Code:</strong> $test_otp</p>";
}
echo "</div>";

if ($successful_tests >= 3) {
    echo "<div class='success'>";
    echo "<h3>🎉 EMAIL SYSTEM IS WORKING PERFECTLY!</h3>";
    echo "<p>The majority of PHPMailer tests passed. Check your inbox at <strong>$test_email</strong> for the test emails.</p>";
    echo "<p>Your banking system email functionality is now confirmed working!</p>";
    echo "</div>";
} else {
    echo "<div class='error'>";
    echo "<h3>⚠️ EMAIL SYSTEM ISSUES DETECTED</h3>";
    echo "<p>Some PHPMailer tests failed. Check the error messages above for details.</p>";
    echo "</div>";
}

// Check error logs
echo "<h3>📄 Recent Error Log Entries</h3>";
if (file_exists('logs/error.log')) {
    $error_log = file('logs/error.log');
    $recent_errors = array_slice($error_log, -10);
    echo "<pre>" . htmlspecialchars(implode('', $recent_errors)) . "</pre>";
} else {
    echo "<p>No error log file found.</p>";
}

echo "</div>";
echo "</div>";
echo "</body></html>";
?>
