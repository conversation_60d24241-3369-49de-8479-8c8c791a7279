<?php
/**
 * Test Improved Email Templates
 * Tests the new CSS design and dynamic contact information
 */

// Enable error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<!DOCTYPE html>";
echo "<html><head><title>Improved Email Templates Test</title>";
echo "<style>
body { font-family: Arial, sans-serif; margin: 20px; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); min-height: 100vh; }
.container { max-width: 1000px; margin: 0 auto; background: white; padding: 30px; border-radius: 15px; box-shadow: 0 8px 32px rgba(0,0,0,0.3); }
.header { background: linear-gradient(135deg, #1e40af 0%, #3b82f6 100%); color: white; padding: 30px; text-align: center; border-radius: 10px; margin-bottom: 20px; }
.success { background: linear-gradient(135deg, #059669 0%, #10b981 100%); color: white; padding: 15px; border-radius: 8px; margin: 10px 0; }
.error { background: linear-gradient(135deg, #dc2626 0%, #ef4444 100%); color: white; padding: 15px; border-radius: 8px; margin: 10px 0; }
.info { background: #e7f3ff; border: 1px solid #b3d9ff; color: #0c5460; padding: 15px; border-radius: 8px; margin: 10px 0; }
.test-section { background: #f8f9fa; padding: 20px; border-radius: 8px; margin: 20px 0; border-left: 4px solid #1e40af; }
</style>";
echo "</head><body>";

echo "<div class='container'>";
echo "<div class='header'>";
echo "<h1>🎨 Improved Email Templates Test</h1>";
echo "<p>Testing new CSS design and dynamic contact information</p>";
echo "</div>";

try {
    // Include required files
    require_once 'config/config.php';
    require_once 'config/email.php';
    require_once 'config/email_templates.php';
    require_once 'config/super_admin_settings.php';
    
    echo "<div class='test-section'>";
    echo "<h2>1. 🔧 Testing Dynamic Contact Information</h2>";
    
    $contact_info = getEmailContactInfo();
    echo "<div class='info'>";
    echo "<h3>📞 Dynamic Contact Information Loaded:</h3>";
    echo "<ul>";
    echo "<li><strong>Site Name:</strong> " . htmlspecialchars($contact_info['site_name']) . "</li>";
    echo "<li><strong>Support Email:</strong> " . htmlspecialchars($contact_info['support_email']) . "</li>";
    echo "<li><strong>Support Phone:</strong> " . htmlspecialchars($contact_info['support_phone']) . "</li>";
    echo "<li><strong>Security Email:</strong> " . htmlspecialchars($contact_info['security_email']) . "</li>";
    echo "<li><strong>Security Phone:</strong> " . htmlspecialchars($contact_info['security_phone']) . "</li>";
    echo "<li><strong>Footer Text:</strong> " . htmlspecialchars($contact_info['email_footer_text']) . "</li>";
    echo "</ul>";
    echo "</div>";
    echo "</div>";
    
    echo "<div class='test-section'>";
    echo "<h2>2. 📧 Testing Welcome Email with New Design</h2>";
    
    $user_data = [
        'first_name' => 'Demo',
        'last_name' => 'Developer',
        'username' => 'demothedev',
        'email' => '<EMAIL>',
        'account_number' => '**********',
        'account_type' => 'savings',
        'currency' => 'USD',
        'balance' => 5000.00,
        'status' => 'active'
    ];
    
    $welcome_html = generateWelcomeEmailTemplate($user_data);
    $subject = "Welcome Email Test - New Design";
    $result = sendEmailSMTP('<EMAIL>', $subject, $welcome_html, true);
    
    if ($result) {
        echo "<div class='success'>✅ Welcome email sent successfully with new design!</div>";
        echo "<div class='info'>📧 Features tested: New CSS design, dynamic contact info, improved styling</div>";
    } else {
        echo "<div class='error'>❌ Welcome email failed to send</div>";
    }
    echo "</div>";
    
    echo "<div class='test-section'>";
    echo "<h2>3. 🔐 Testing OTP Email with Improved Design</h2>";
    
    $otp_code = generateOTP();
    $otp_html = generateOTPEmailTemplate($user_data, $otp_code, 10);
    $subject = "OTP Test - Improved Design";
    $result = sendEmailSMTP('<EMAIL>', $subject, $otp_html, true);
    
    if ($result) {
        echo "<div class='success'>✅ OTP email sent successfully with improved design!</div>";
        echo "<div class='info'>🔢 <strong>OTP Code:</strong> $otp_code</div>";
        echo "<div class='info'>🎨 Features: Subtle top borders instead of thick left borders, gradient backgrounds</div>";
    } else {
        echo "<div class='error'>❌ OTP email failed to send</div>";
    }
    echo "</div>";
    
    echo "<div class='test-section'>";
    echo "<h2>4. ⚠️ Testing Account Suspension with Dynamic Contact</h2>";
    
    $suspension_html = generateAccountSuspensionEmailTemplate($user_data, 'Testing new contact system');
    $subject = "Suspension Test - Dynamic Contact Info";
    $result = sendEmailSMTP('<EMAIL>', $subject, $suspension_html, true);
    
    if ($result) {
        echo "<div class='success'>✅ Suspension email sent with dynamic contact information!</div>";
        echo "<div class='info'>📞 Contact info now pulled from database settings</div>";
    } else {
        echo "<div class='error'>❌ Suspension email failed to send</div>";
    }
    echo "</div>";
    
    echo "<div class='test-section'>";
    echo "<h2>5. 🔧 Testing SMTP Configuration</h2>";
    
    $smtp_config = getSMTPConfig();
    echo "<div class='info'>";
    echo "<h3>📡 SMTP Configuration (from database):</h3>";
    echo "<ul>";
    echo "<li><strong>Host:</strong> " . htmlspecialchars($smtp_config['host']) . "</li>";
    echo "<li><strong>Port:</strong> " . htmlspecialchars($smtp_config['port']) . "</li>";
    echo "<li><strong>Username:</strong> " . htmlspecialchars($smtp_config['username']) . "</li>";
    echo "<li><strong>Encryption:</strong> " . htmlspecialchars($smtp_config['encryption']) . "</li>";
    echo "<li><strong>From Email:</strong> " . htmlspecialchars($smtp_config['from_email']) . "</li>";
    echo "<li><strong>From Name:</strong> " . htmlspecialchars($smtp_config['from_name']) . "</li>";
    echo "</ul>";
    echo "</div>";
    echo "</div>";
    
    echo "<div class='success'>";
    echo "<h2>🎉 Improvements Successfully Implemented!</h2>";
    echo "<h3>✅ CSS Design Changes:</h3>";
    echo "<ul>";
    echo "<li>Removed thick left borders from email boxes</li>";
    echo "<li>Added subtle top gradient borders</li>";
    echo "<li>Improved box shadows and gradients</li>";
    echo "<li>Enhanced visual hierarchy</li>";
    echo "</ul>";
    
    echo "<h3>✅ Dynamic Contact Information:</h3>";
    echo "<ul>";
    echo "<li>Contact info now pulled from database</li>";
    echo "<li>No more hardcoded values in templates</li>";
    echo "<li>Super admin can modify all contact details</li>";
    echo "<li>Audit trail for all changes</li>";
    echo "</ul>";
    
    echo "<h3>✅ Super Admin System:</h3>";
    echo "<ul>";
    echo "<li>Database tables created successfully</li>";
    echo "<li>26 default settings configured</li>";
    echo "<li>SMTP settings moved to secure panel</li>";
    echo "<li>Role-based access control implemented</li>";
    echo "</ul>";
    echo "</div>";
    
    echo "<div class='info'>";
    echo "<h3>📧 Check Your Email!</h3>";
    echo "<p>Three test emails have been sent to <strong><EMAIL></strong>:</p>";
    echo "<ol>";
    echo "<li><strong>Welcome Email</strong> - New design with dynamic contact info</li>";
    echo "<li><strong>OTP Email</strong> - Improved styling with code: <strong>$otp_code</strong></li>";
    echo "<li><strong>Suspension Email</strong> - Dynamic contact information</li>";
    echo "</ol>";
    echo "<p><strong>Look for the improved design elements:</strong></p>";
    echo "<ul>";
    echo "<li>Subtle top borders instead of thick left borders</li>";
    echo "<li>Gradient backgrounds on info boxes</li>";
    echo "<li>Dynamic contact information in footers</li>";
    echo "<li>Professional banking appearance</li>";
    echo "</ul>";
    echo "</div>";
    
} catch (Exception $e) {
    echo "<div class='error'>";
    echo "<h3>❌ Test Failed</h3>";
    echo "<p>Error: " . htmlspecialchars($e->getMessage()) . "</p>";
    echo "</div>";
}

echo "<div class='info'>";
echo "<h3>🔗 Next Steps:</h3>";
echo "<ul>";
echo "<li><a href='admin/super-admin-settings.php' style='color: #1e40af;'>Access Super Admin Settings Panel</a></li>";
echo "<li><a href='admin/smtp-settings.php' style='color: #1e40af;'>Configure SMTP Settings</a></li>";
echo "<li><a href='admin/dashboard.php' style='color: #1e40af;'>Return to Admin Dashboard</a></li>";
echo "</ul>";
echo "</div>";

echo "<p><strong>Test completed at:</strong> " . date('Y-m-d H:i:s') . "</p>";
echo "</div>";
echo "</body></html>";
?>
