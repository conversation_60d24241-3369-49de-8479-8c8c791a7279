<?php
/**
 * Generate Wire Transfer Receipt
 * Creates a printable receipt for wire transfers
 */

// Enable error reporting for debugging
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Start session and check authentication
session_start();
if (!isset($_SESSION['user_id'])) {
    header('Location: ../../auth/login.php');
    exit();
}

// Include database connection
require_once '../../config/config.php';

// Get database connection
$db = getDB();
$user_id = $_SESSION['user_id'];

// Get transfer ID from URL
$transfer_id = intval($_GET['id'] ?? 0);

if (!$transfer_id) {
    die('Invalid transfer ID');
}

// Get wire transfer details with sender information
$transfer_sql = "SELECT t.*, 
                        a.first_name as sender_first_name, 
                        a.last_name as sender_last_name,
                        a.account_number as sender_account,
                        a.email as sender_email
                 FROM transfers t 
                 JOIN accounts a ON t.sender_id = a.id 
                 WHERE t.id = ? AND t.sender_id = ? AND t.transfer_type = 'international'";

$transfer = $db->query($transfer_sql, [$transfer_id, $user_id]);

if (!$transfer || $transfer->num_rows === 0) {
    die('Wire transfer not found or access denied');
}

$transfer_data = $transfer->fetch_assoc();
$sender_name = $transfer_data['sender_first_name'] . ' ' . $transfer_data['sender_last_name'];

// Note: formatCurrency() function is already defined in config.php

// Get status badge color
function getStatusBadgeColor($status) {
    switch (strtolower($status)) {
        case 'completed': return '#28a745';
        case 'pending': return '#ffc107';
        case 'failed': return '#dc3545';
        case 'cancelled': return '#6c757d';
        default: return '#17a2b8';
    }
}

// Get status display text
function getStatusDisplayText($status) {
    switch (strtolower($status)) {
        case 'pending': return 'Pending Review';
        case 'completed': return 'Completed';
        case 'failed': return 'Failed';
        case 'cancelled': return 'Cancelled';
        default: return ucfirst($status);
    }
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Wire Transfer Receipt - <?php echo htmlspecialchars($transfer_data['transaction_id']); ?></title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Courier New', monospace;
            background: #f8f9fa;
            padding: 20px;
            line-height: 1.4;
        }
        
        .receipt-container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border: 2px solid #000;
            padding: 30px;
            box-shadow: 0 0 20px rgba(0,0,0,0.1);
        }
        
        .print-date {
            text-align: right;
            font-size: 12px;
            margin-bottom: 20px;
            color: #666;
        }
        
        .bank-header {
            text-align: center;
            border-bottom: 2px solid #000;
            padding-bottom: 20px;
            margin-bottom: 30px;
        }
        
        .bank-name {
            font-size: 24px;
            font-weight: bold;
            margin-bottom: 5px;
        }
        
        .bank-address {
            font-size: 12px;
            color: #666;
            margin-bottom: 15px;
        }
        
        .receipt-title {
            font-size: 18px;
            font-weight: bold;
            background: #000;
            color: white;
            padding: 8px 20px;
            display: inline-block;
        }
        
        .account-info {
            display: flex;
            justify-content: space-between;
            margin-bottom: 30px;
            padding: 15px;
            background: #f8f9fa;
            border: 1px solid #ddd;
        }
        
        .receipt-section {
            margin-bottom: 25px;
            border: 1px solid #ddd;
            padding: 15px;
        }
        
        .section-title {
            font-weight: bold;
            font-size: 14px;
            margin-bottom: 15px;
            padding-bottom: 5px;
            border-bottom: 1px solid #ddd;
        }
        
        .receipt-row {
            display: flex;
            justify-content: space-between;
            margin-bottom: 8px;
            padding: 3px 0;
        }
        
        .receipt-label {
            font-weight: bold;
            width: 40%;
        }
        
        .receipt-value {
            width: 60%;
            text-align: right;
        }
        
        .amount-section {
            background: #f8f9fa;
            border: 2px solid #000;
            padding: 20px;
            margin: 20px 0;
        }
        
        .total-amount {
            font-size: 18px;
            font-weight: bold;
            text-align: center;
        }
        
        .status-badge {
            display: inline-block;
            padding: 4px 12px;
            border-radius: 4px;
            color: white;
            font-weight: bold;
            font-size: 12px;
        }
        
        .receipt-footer {
            margin-top: 40px;
            padding-top: 20px;
            border-top: 2px solid #000;
            text-align: center;
        }
        
        .footer-note {
            font-size: 11px;
            margin-bottom: 5px;
            color: #666;
        }
        
        /* Loading Screen Styles */
        .loading-screen {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: #f8f9fa;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            z-index: 9999;
        }

        .loading-content {
            text-align: center;
            max-width: 400px;
            padding: 2rem;
        }

        .loading-logo {
            font-size: 2rem;
            font-weight: bold;
            margin-bottom: 1rem;
            color: #206bc4;
        }

        .loading-title {
            font-size: 1.2rem;
            font-weight: 600;
            margin-bottom: 0.5rem;
            color: #333;
        }

        .loading-subtitle {
            font-size: 0.9rem;
            color: #666;
            margin-bottom: 2rem;
        }

        .progress-container {
            width: 100%;
            height: 8px;
            background: #e9ecef;
            border-radius: 4px;
            overflow: hidden;
            margin-bottom: 1rem;
        }

        .progress-bar {
            height: 100%;
            background: linear-gradient(90deg, #206bc4, #1a5490);
            border-radius: 4px;
            transition: width 0.3s ease;
            width: 0%;
        }

        .loading-message {
            font-size: 0.85rem;
            color: #666;
            min-height: 1.2rem;
        }

        .receipt-content {
            display: none;
        }

        @media print {
            body { background: white; padding: 0; }
            .receipt-container { box-shadow: none; border: 1px solid #000; }
            .loading-screen { display: none !important; }
            .receipt-content { display: block !important; }
        }
    </style>
</head>
<body>
    <!-- Loading Screen -->
    <div class="loading-screen" id="loadingScreen">
        <div class="loading-content">
            <div class="loading-logo">🏦 PremierBank Pro</div>
            <div class="loading-title">Generating Wire Transfer Receipt</div>
            <div class="loading-subtitle">Please wait while we prepare your receipt...</div>

            <div class="progress-container">
                <div class="progress-bar" id="progressBar"></div>
            </div>

            <div class="loading-message" id="loadingMessage">Initializing...</div>
        </div>
    </div>

    <!-- Receipt Content -->
    <div class="receipt-content" id="receiptContent">
        <div class="receipt-container">
        <!-- Print Date -->
        <div class="print-date">
            Generated on: <?php echo date('F j, Y g:i A'); ?>
        </div>
        
        <!-- Bank Header -->
        <div class="bank-header">
            <div class="bank-name">PremierBank Pro</div>
            <div class="bank-address">
                International Wire Transfer Services<br>
                Phone: +**************** | Email: <EMAIL>
            </div>
            <div class="receipt-title">WIRE TRANSFER RECEIPT</div>
        </div>
        
        <!-- Account Information -->
        <div class="account-info">
            <div>
                <strong>Account Holder:</strong> <?php echo htmlspecialchars($sender_name); ?>
            </div>
            <div>
                <strong>Account Number:</strong> ****<?php echo substr($transfer_data['sender_account'], -4); ?>
            </div>
        </div>
        
        <!-- Transaction Details -->
        <div class="receipt-section">
            <div class="section-title">🔄 TRANSACTION DETAILS</div>
            
            <div class="receipt-row">
                <span class="receipt-label">Reference Number:</span>
                <span class="receipt-value"><?php echo htmlspecialchars($transfer_data['transaction_id']); ?></span>
            </div>
            
            <div class="receipt-row">
                <span class="receipt-label">Transfer Type:</span>
                <span class="receipt-value">International Wire Transfer</span>
            </div>
            
            <div class="receipt-row">
                <span class="receipt-label">Date & Time:</span>
                <span class="receipt-value"><?php echo date('F j, Y g:i A', strtotime($transfer_data['created_at'])); ?></span>
            </div>
            
            <div class="receipt-row">
                <span class="receipt-label">Status:</span>
                <span class="receipt-value">
                    <span class="status-badge" style="background-color: <?php echo getStatusBadgeColor($transfer_data['processing_status'] ?? $transfer_data['status']); ?>">
                        <?php echo getStatusDisplayText($transfer_data['processing_status'] ?? $transfer_data['status']); ?>
                    </span>
                </span>
            </div>
        </div>
        
        <!-- Recipient Information -->
        <div class="receipt-section">
            <div class="section-title">🎯 BENEFICIARY INFORMATION</div>
            
            <div class="receipt-row">
                <span class="receipt-label">Name:</span>
                <span class="receipt-value"><?php echo htmlspecialchars($transfer_data['recipient_name']); ?></span>
            </div>
            
            <div class="receipt-row">
                <span class="receipt-label">Account Number:</span>
                <span class="receipt-value"><?php echo htmlspecialchars($transfer_data['recipient_account']); ?></span>
            </div>
            
            <div class="receipt-row">
                <span class="receipt-label">Transfer Method:</span>
                <span class="receipt-value">SWIFT Wire Transfer</span>
            </div>
        </div>
        
        <!-- Amount Section -->
        <div class="amount-section">
            <div class="total-amount">
                TRANSFER AMOUNT: <?php echo formatCurrency($transfer_data['amount'], $transfer_data['currency']); ?>
            </div>
            <?php if ($transfer_data['fee'] > 0): ?>
            <div style="text-align: center; margin-top: 10px; font-size: 14px;">
                Processing Fee: <?php echo formatCurrency($transfer_data['fee'], $transfer_data['currency']); ?>
            </div>
            <?php endif; ?>
        </div>
        
        <!-- Description -->
        <?php if (!empty($transfer_data['description'])): ?>
        <div class="receipt-section">
            <div class="section-title">📝 TRANSFER DESCRIPTION</div>
            <div style="padding: 10px; background: #f8f9fa;">
                <?php echo htmlspecialchars($transfer_data['description']); ?>
            </div>
        </div>
        <?php endif; ?>
        
        <!-- Important Notice -->
        <div class="receipt-section">
            <div class="section-title">⚠️ IMPORTANT NOTICE</div>
            <div style="font-size: 12px; line-height: 1.6;">
                <p><strong>Processing Time:</strong> International wire transfers typically take 1-3 business days to complete.</p>
                <p><strong>Status Updates:</strong> You will receive email notifications when your transfer status changes.</p>
                <p><strong>Tracking:</strong> Use reference number <?php echo htmlspecialchars($transfer_data['transaction_id']); ?> for all inquiries.</p>
            </div>
        </div>
        
        <!-- Receipt Footer -->
        <div class="receipt-footer">
            <div class="footer-note">
                <strong>*** COMPUTER GENERATED RECEIPT ***</strong>
            </div>
            <div class="footer-note">
                This receipt is valid without signature and serves as proof of your wire transfer request.
            </div>
            <div class="footer-note">
                For any queries, please contact our customer support at +**************** or visit your nearest branch.
            </div>
            <div class="footer-note">
                <strong>Transaction Reference:</strong> <?php echo htmlspecialchars($transfer_data['transaction_id']); ?>
            </div>
        </div>
        </div>
    </div>

    <script>
        // Loading animation with progress messages
        const loadingMessages = [
            'Authenticating user credentials...',
            'Connecting to transfer server...',
            'Retrieving transaction details...',
            'Validating transfer information...',
            'Formatting receipt data...',
            'Generating receipt document...',
            'Finalizing receipt...'
        ];

        let currentStep = 0;
        const progressBar = document.getElementById('progressBar');
        const loadingMessage = document.getElementById('loadingMessage');
        const loadingScreen = document.getElementById('loadingScreen');
        const receiptContent = document.getElementById('receiptContent');

        function updateProgress() {
            if (currentStep < loadingMessages.length) {
                const progress = ((currentStep + 1) / loadingMessages.length) * 100;
                progressBar.style.width = progress + '%';
                loadingMessage.textContent = loadingMessages[currentStep];
                currentStep++;

                // Vary timing for realism
                const delay = currentStep === 1 ? 800 : (currentStep === loadingMessages.length ? 1200 : 600);
                setTimeout(updateProgress, delay);
            } else {
                // Show receipt and hide loading
                setTimeout(() => {
                    loadingScreen.style.display = 'none';
                    receiptContent.style.display = 'block';

                    // Auto-print after showing receipt
                    setTimeout(() => {
                        window.print();
                    }, 300);
                }, 500);
            }
        }

        // Start loading animation when page loads
        window.onload = function() {
            setTimeout(updateProgress, 500);
        };
    </script>
</body>
</html>
