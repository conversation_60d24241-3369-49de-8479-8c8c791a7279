/* Fundwise Dashboard Styles */

/* CSS Variables - Now loaded dynamically from database via dynamic-css.php */
/* Fallback variables for when dynamic CSS fails to load */
:root {
    --primary-color: #206bc4;
    --primary-dark: #164a73;
    --accent-color: #10b981;
    --warning-color: #f59e0b;
    --danger-color: #ef4444;
    --text-primary: #1f2937;
    --text-secondary: #6b7280;
    --text-muted: #9ca3af;
    --border-color: #e5e7eb;
    --sidebar-bg: #f8fafc;
    --card-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
    --card-shadow-hover: 0 4px 12px 0 rgba(0, 0, 0, 0.15);
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
    color: var(--text-primary);
    min-height: 100vh;
}

/* Dashboard Layout */
.dashboard-layout {
    display: flex;
    min-height: 100vh;
}

/* Sidebar */
.dashboard-sidebar {
    width: 280px;
    background: linear-gradient(180deg, #ffffff 0%, #f8fafc 100%);
    border-right: 1px solid var(--border-color);
    padding: 2rem 0;
    position: fixed;
    height: 100vh;
    overflow-y: auto;
    box-shadow: 4px 0 20px rgba(0, 0, 0, 0.08);
}

/* Hide scrollbar for sidebar */
.dashboard-sidebar::-webkit-scrollbar {
    width: 0px;
    background: transparent;
}

.dashboard-sidebar {
    scrollbar-width: none; /* Firefox */
    -ms-overflow-style: none; /* IE and Edge */
}

/* Banking Sidebar Styles */
.banking-sidebar {
    width: 280px;
    background: linear-gradient(180deg, #ffffff 0%, #f8fafc 100%);
    border-right: 1px solid var(--border-color);
    padding: 0;
    position: fixed;
    height: 100vh;
    overflow-y: auto;
    overflow-x: hidden;
    box-shadow: 4px 0 20px rgba(0, 0, 0, 0.08);
    scrollbar-width: none; /* Firefox */
    -ms-overflow-style: none; /* IE and Edge */
    scroll-behavior: smooth; /* Smooth scrolling */
}

.banking-sidebar::-webkit-scrollbar {
    width: 0px;
    background: transparent;
    display: none; /* Completely hide scrollbar */
}

.sidebar-header {
    padding: 2rem 1.5rem;
    border-bottom: 1px solid var(--border-color);
    background: linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%);
    color: white;
}

.bank-logo {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    margin-bottom: 1.5rem;
}

.logo-icon {
    width: 40px;
    height: 40px;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
}

.bank-name {
    font-size: 1.25rem;
    font-weight: 700;
    color: white;
}

.user-info {
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.user-avatar {
    width: 40px;
    height: 40px;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 600;
    color: white;
}

.user-details {
    flex: 1;
}

.user-name {
    font-weight: 600;
    color: white;
    font-size: 0.875rem;
}

.account-number {
    font-size: 0.75rem;
    color: rgba(255, 255, 255, 0.8);
}

/* Banking Sidebar Navigation */
.sidebar-nav {
    padding: 1.5rem 0;
}

.nav-section {
    margin-bottom: 2rem;
}

.nav-section-title {
    font-size: 0.75rem;
    font-weight: 600;
    color: var(--text-muted);
    text-transform: uppercase;
    letter-spacing: 0.05em;
    padding: 0 1.5rem;
    margin-bottom: 0.75rem;
}

.nav-list {
    list-style: none;
    padding: 0;
    margin: 0;
}

.nav-item {
    margin-bottom: 0.25rem;
}

.nav-link {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 0.75rem 1.5rem;
    color: var(--text-secondary);
    text-decoration: none;
    font-weight: 500;
    font-size: 0.875rem;
    transition: all 0.3s ease;
    border-radius: 0;
    position: relative;
}

.nav-link:hover {
    background: linear-gradient(90deg, rgba(99, 102, 241, 0.1) 0%, transparent 100%);
    color: var(--primary-color);
    text-decoration: none;
}

.nav-link.active {
    background: linear-gradient(90deg, rgba(99, 102, 241, 0.15) 0%, transparent 100%);
    color: var(--primary-color);
    border-right: 3px solid var(--primary-color);
}

.nav-icon {
    width: 20px;
    height: 20px;
    flex-shrink: 0;
}

/* Sidebar Footer */
.sidebar-footer {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    padding: 1.5rem;
    border-top: 1px solid var(--border-color);
    background: rgba(248, 250, 252, 0.8);
}

.logout-link {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 0.75rem;
    color: var(--danger-color);
    text-decoration: none;
    font-weight: 500;
    font-size: 0.875rem;
    border-radius: 8px;
    transition: all 0.3s ease;
}

.logout-link:hover {
    background: rgba(239, 68, 68, 0.1);
    color: var(--danger-color);
    text-decoration: none;
}

.logout-icon {
    width: 20px;
    height: 20px;
}

/* Main Content Area */
.main-content {
    margin-left: 280px;
    padding: 2rem;
    background: transparent;
    min-height: 100vh;
    overflow-x: hidden;
    scroll-behavior: smooth;
}

/* Banking Container */
.banking-container {
    max-width: 1400px;
    margin: 0 auto;
    width: 100%;
}

.banking-dashboard {
    display: flex;
    flex-direction: column;
    gap: 2rem;
}

/* Page Header */
.page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;
    padding: 1.5rem 2rem;
    background: white;
    border-radius: 16px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    border: 1px solid var(--border-color);
}

.page-title h1 {
    font-size: 2rem;
    font-weight: 700;
    color: var(--text-primary);
    margin: 0 0 0.5rem 0;
}

.page-title p {
    font-size: 0.875rem;
    color: var(--text-secondary);
    margin: 0;
}

.page-actions {
    display: flex;
    align-items: center;
    gap: 1rem;
}

/* Top Bar */
.top-bar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;
    padding: 1.5rem 2rem;
    background: white;
    border-radius: 16px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    border: 1px solid var(--border-color);
}

.top-bar h1 {
    font-size: 2rem;
    font-weight: 700;
    color: var(--text-primary);
    margin: 0;
}

.top-bar-actions {
    display: flex;
    align-items: center;
    gap: 1rem;
}

/* Dashboard Grid */
.dashboard-grid {
    display: grid;
    grid-template-columns: 1fr 320px;
    gap: 2rem;
    align-items: start;
}

.main-section {
    display: flex;
    flex-direction: column;
    gap: 2rem;
}

.sidebar-section {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
}

/* Account Overview Section */
.account-overview-section {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
}

.account-summary-card {
    background: linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%);
    border-radius: 20px;
    padding: 2rem;
    color: white;
    box-shadow: 0 8px 32px rgba(99, 102, 241, 0.3);
    position: relative;
    overflow: hidden;
}

.account-summary-card::before {
    content: '';
    position: absolute;
    top: -50%;
    right: -50%;
    width: 100%;
    height: 100%;
    background: radial-gradient(circle, rgba(255, 255, 255, 0.1) 0%, transparent 70%);
    pointer-events: none;
}

.account-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 2rem;
}

.account-info {
    flex: 1;
}

.account-title h2 {
    font-size: 1.5rem;
    font-weight: 700;
    margin: 0 0 0.5rem 0;
    color: white;
}

.account-subtitle {
    font-size: 0.875rem;
    opacity: 0.8;
    margin: 0;
}

.account-status {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
    align-items: flex-end;
}

.account-avatar {
    width: 60px;
    height: 60px;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 700;
    font-size: 1.25rem;
    color: white;
    backdrop-filter: blur(10px);
}

/* Balance Display */
.balance-display {
    display: flex;
    justify-content: space-between;
    align-items: flex-end;
    gap: 2rem;
}

.balance-main {
    flex: 1;
}

.balance-label {
    font-size: 0.875rem;
    opacity: 0.8;
    margin: 0 0 0.5rem 0;
}

.balance-amount {
    font-size: 3rem;
    font-weight: 700;
    margin: 0 0 0.5rem 0;
    color: white;
    line-height: 1;
}

.balance-details {
    font-size: 0.875rem;
    opacity: 0.8;
    margin: 0;
}

.balance-actions {
    display: flex;
    gap: 0.75rem;
    flex-wrap: wrap;
}

.action-btn {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.75rem 1rem;
    border-radius: 12px;
    text-decoration: none;
    font-weight: 500;
    font-size: 0.875rem;
    transition: all 0.3s ease;
    backdrop-filter: blur(10px);
}

.action-btn.primary {
    background: rgba(255, 255, 255, 0.2);
    color: white;
    border: 1px solid rgba(255, 255, 255, 0.3);
}

.action-btn.primary:hover {
    background: rgba(255, 255, 255, 0.3);
    color: white;
    text-decoration: none;
    transform: translateY(-2px);
}

.action-btn.secondary {
    background: rgba(255, 255, 255, 0.1);
    color: white;
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.action-btn.secondary:hover {
    background: rgba(255, 255, 255, 0.2);
    color: white;
    text-decoration: none;
    transform: translateY(-2px);
}

/* Account Details Grid */
.account-details-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.5rem;
}

.detail-card {
    background: white;
    border-radius: 16px;
    padding: 1.5rem;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    border: 1px solid var(--border-color);
    display: flex;
    align-items: center;
    gap: 1rem;
    transition: all 0.3s ease;
}

.detail-card:hover {
    transform: translateY(-4px);
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);
}

.detail-icon {
    width: 48px;
    height: 48px;
    border-radius: 12px;
    background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--primary-color);
    flex-shrink: 0;
}

.detail-content h4 {
    font-size: 0.875rem;
    font-weight: 600;
    color: var(--text-secondary);
    margin: 0 0 0.25rem 0;
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

.detail-content p {
    font-size: 1rem;
    font-weight: 600;
    color: var(--text-primary);
    margin: 0;
}

/* Status Badges */
.status-badge {
    display: inline-flex;
    align-items: center;
    padding: 0.375rem 0.75rem;
    border-radius: 20px;
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

.status-badge.status-active,
.status-badge.active {
    background: #dcfce7;
    color: #166534;
}

.status-badge.status-pending,
.status-badge.pending {
    background: #fef3c7;
    color: #92400e;
}

.status-badge.status-suspended,
.status-badge.suspended {
    background: #fee2e2;
    color: #991b1b;
}

.kyc-badge {
    display: inline-flex;
    align-items: center;
    padding: 0.375rem 0.75rem;
    border-radius: 20px;
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

.kyc-badge.kyc-verified {
    background: rgba(255, 255, 255, 0.2);
    color: white;
    border: 1px solid rgba(255, 255, 255, 0.3);
}

.kyc-badge.kyc-pending {
    background: rgba(245, 158, 11, 0.2);
    color: white;
    border: 1px solid rgba(245, 158, 11, 0.3);
}

.kyc-badge.kyc-rejected {
    background: rgba(239, 68, 68, 0.2);
    color: white;
    border: 1px solid rgba(239, 68, 68, 0.3);
}

/* Quick Actions */
.quick-actions {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
    gap: 1rem;
    margin-top: 1.5rem;
}

.quick-action {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 1.5rem 1rem;
    background: white;
    border-radius: 16px;
    text-decoration: none;
    color: var(--text-primary);
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    border: 1px solid var(--border-color);
    transition: all 0.3s ease;
    text-align: center;
    font-size: 0.875rem;
    font-weight: 500;
}

.quick-action:hover {
    transform: translateY(-4px);
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);
    text-decoration: none;
    color: var(--text-primary);
}

.quick-action-icon {
    width: 48px;
    height: 48px;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 0.75rem;
    color: white;
    font-weight: 600;
    font-size: 0.875rem;
}

/* Financial Summary Grid */
.financial-summary-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 1.5rem;
    margin-top: 1.5rem;
}

.summary-card {
    background: white;
    border-radius: 16px;
    padding: 1.5rem;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    border: 1px solid var(--border-color);
    transition: all 0.3s ease;
}

.summary-card:hover {
    transform: translateY(-4px);
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);
}

.summary-header {
    display: flex;
    align-items: center;
    gap: 1rem;
    margin-bottom: 1rem;
}

.summary-icon {
    width: 48px;
    height: 48px;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
}

.summary-card.income .summary-icon {
    background: linear-gradient(135deg, #dcfce7 0%, #bbf7d0 100%);
    color: var(--accent-color);
}

.summary-card.expense .summary-icon {
    background: linear-gradient(135deg, #fee2e2 0%, #fecaca 100%);
    color: var(--danger-color);
}

.summary-card.net .summary-icon {
    background: linear-gradient(135deg, #dbeafe 0%, #bfdbfe 100%);
    color: var(--primary-color);
}

.summary-info h4 {
    font-size: 0.875rem;
    font-weight: 600;
    color: var(--text-secondary);
    margin: 0 0 0.25rem 0;
}

.summary-info p {
    font-size: 0.75rem;
    color: var(--text-muted);
    margin: 0;
}

.summary-amount {
    font-size: 1.75rem;
    font-weight: 700;
    color: var(--text-primary);
    margin-bottom: 0.5rem;
}

.summary-change {
    font-size: 0.875rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
}

.summary-change.positive {
    color: var(--accent-color);
}

.summary-change.negative {
    color: var(--danger-color);
}

.summary-details {
    font-size: 0.75rem;
    color: var(--text-muted);
}

/* Transaction History Section */
.transaction-history-section {
    background: white;
    border-radius: 16px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    border: 1px solid var(--border-color);
    overflow: hidden;
}

.section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1.5rem 2rem;
    border-bottom: 1px solid var(--border-color);
    background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
}

.section-title h3 {
    font-size: 1.25rem;
    font-weight: 700;
    color: var(--text-primary);
    margin: 0 0 0.25rem 0;
}

.section-title p {
    font-size: 0.875rem;
    color: var(--text-secondary);
    margin: 0;
}

.section-actions {
    display: flex;
    gap: 0.75rem;
}

/* Transaction List */
.transaction-list-container {
    width: 100%;
}

.transaction-list {
    display: flex;
    flex-direction: column;
}

.enhanced-transaction-item {
    display: flex;
    align-items: center;
    padding: 1.5rem 2rem;
    border-bottom: 1px solid var(--border-color);
    cursor: pointer;
    transition: all 0.3s ease;
    background: white;
}

.enhanced-transaction-item:hover {
    background: #f8fafc;
}

.enhanced-transaction-item:last-child {
    border-bottom: none;
}

.transaction-main {
    display: flex;
    align-items: center;
    gap: 1rem;
    flex: 1;
}

.transaction-avatar {
    flex-shrink: 0;
}

.avatar-icon {
    width: 48px;
    height: 48px;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
}

.avatar-icon.sent {
    background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
}

.avatar-icon.received {
    background: linear-gradient(135deg, #10b981 0%, #059669 100%);
}

.transaction-details {
    flex: 1;
    min-width: 0;
}

.transaction-primary {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    margin-bottom: 0.25rem;
}

.transaction-primary h4 {
    font-size: 1rem;
    font-weight: 600;
    color: var(--text-primary);
    margin: 0;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.transaction-type {
    font-size: 0.75rem;
    font-weight: 600;
    color: var(--text-muted);
    text-transform: uppercase;
    letter-spacing: 0.05em;
    background: #f1f5f9;
    padding: 0.25rem 0.5rem;
    border-radius: 6px;
}

.transaction-secondary {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.transaction-description {
    font-size: 0.875rem;
    color: var(--text-secondary);
}

.transaction-id {
    font-size: 0.75rem;
    color: var(--text-muted);
    font-family: 'Monaco', 'Menlo', monospace;
}

.transaction-amount-section {
    text-align: right;
    flex-shrink: 0;
}

.amount {
    font-size: 1.125rem;
    font-weight: 700;
    margin-bottom: 0.25rem;
}

.amount.positive {
    color: var(--accent-color);
}

.amount.negative {
    color: var(--danger-color);
}

.transaction-time {
    font-size: 0.75rem;
    color: var(--text-muted);
    margin-bottom: 0.5rem;
}

.transaction-status {
    display: flex;
    justify-content: flex-end;
}

.transaction-expand-icon {
    margin-left: 1rem;
    color: var(--text-muted);
    transition: all 0.3s ease;
}

.enhanced-transaction-item:hover .transaction-expand-icon {
    color: var(--primary-color);
    transform: translateX(4px);
}

/* Transaction Footer */
.transaction-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1.5rem 2rem;
    background: #f8fafc;
    border-top: 1px solid var(--border-color);
}

.transaction-summary {
    font-size: 0.875rem;
    color: var(--text-secondary);
}

.view-all-link {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    color: var(--primary-color);
    text-decoration: none;
    font-weight: 600;
    font-size: 0.875rem;
    transition: all 0.3s ease;
}

.view-all-link:hover {
    color: var(--primary-dark);
    text-decoration: none;
    transform: translateX(4px);
}

/* Empty States */
.empty-transactions {
    padding: 4rem 2rem;
    text-align: center;
}

.empty-icon {
    width: 80px;
    height: 80px;
    margin: 0 auto 1.5rem;
    color: var(--text-muted);
    opacity: 0.5;
}

.empty-transactions h4 {
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--text-primary);
    margin: 0 0 0.5rem 0;
}

.empty-transactions p {
    font-size: 0.875rem;
    color: var(--text-secondary);
    margin: 0 0 2rem 0;
    max-width: 400px;
    margin-left: auto;
    margin-right: auto;
}

.empty-actions {
    display: flex;
    justify-content: center;
    gap: 1rem;
    flex-wrap: wrap;
}

/* Card Styles */
.card {
    background: white;
    border-radius: 16px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    border: 1px solid var(--border-color);
    overflow: hidden;
    transition: all 0.3s ease;
}

.card:hover {
    transform: translateY(-4px);
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);
}

.card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1.5rem 2rem;
    border-bottom: 1px solid var(--border-color);
    background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
}

.card-title {
    font-size: 1.125rem;
    font-weight: 700;
    color: var(--text-primary);
    margin: 0;
}

/* Virtual Card */
.virtual-card {
    background: linear-gradient(135deg, #1f2937 0%, #374151 100%);
    border-radius: 16px;
    padding: 1.5rem;
    color: white;
    position: relative;
    overflow: hidden;
}

.virtual-card::before {
    content: '';
    position: absolute;
    top: -50%;
    right: -50%;
    width: 100%;
    height: 100%;
    background: radial-gradient(circle, rgba(255, 255, 255, 0.1) 0%, transparent 70%);
    pointer-events: none;
}

.card-brand {
    font-size: 0.875rem;
    font-weight: 700;
    text-transform: uppercase;
    letter-spacing: 0.1em;
    margin-bottom: 1rem;
    opacity: 0.8;
}

.card-number {
    font-family: 'Monaco', 'Menlo', monospace;
    font-size: 1.125rem;
    font-weight: 600;
    letter-spacing: 0.1em;
    margin-bottom: 1rem;
}

.card-details {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

/* Statistics Cards Row */
.stats-cards-row {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.5rem;
    margin-bottom: 2rem;
}

/* Cards Statistics Grid */
.cards-stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.5rem;
    margin-bottom: 2rem;
}

.cards-stats-grid .stat-card {
    background: white;
    border-radius: 16px;
    padding: 1.5rem;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    border: 1px solid var(--border-color);
    transition: all 0.3s ease;
}

.cards-stats-grid .stat-card:hover {
    transform: translateY(-4px);
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);
}

.cards-stats-grid .stat-content {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.cards-stats-grid .stat-content h3 {
    font-size: 1.75rem;
    font-weight: 700;
    color: var(--text-primary);
    margin: 0;
}

.cards-stats-grid .stat-content p {
    font-size: 0.875rem;
    font-weight: 600;
    color: var(--text-secondary);
    margin: 0;
}

.cards-stats-grid .stat-details {
    display: flex;
    gap: 1rem;
    margin-top: 0.5rem;
}

.cards-stats-grid .stat-details span {
    font-size: 0.75rem;
    color: var(--text-muted);
}

.cards-stats-grid .active-count {
    color: var(--accent-color);
    font-weight: 600;
}

.cards-stats-grid .blocked-count {
    color: var(--danger-color);
    font-weight: 600;
}

.cards-stats-grid .stat-card {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.cards-stats-grid .stat-icon {
    width: 48px;
    height: 48px;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
    background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
    color: var(--primary-color);
}

.cards-stats-grid .stat-icon.balance {
    background: linear-gradient(135deg, #dcfce7 0%, #bbf7d0 100%);
    color: var(--accent-color);
}

.cards-stats-grid .stat-icon.limit {
    background: linear-gradient(135deg, #fef3c7 0%, #fde68a 100%);
    color: var(--warning-color);
}

.cards-stats-grid .stat-icon.spending {
    background: linear-gradient(135deg, #fee2e2 0%, #fecaca 100%);
    color: var(--danger-color);
}

.stat-card {
    background: white;
    border-radius: 16px;
    padding: 1.5rem;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    border: 1px solid var(--border-color);
    transition: all 0.3s ease;
}

.stat-card:hover {
    transform: translateY(-4px);
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);
}

.stat-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
}

.stat-icon {
    width: 48px;
    height: 48px;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
}

.stat-icon.income {
    background: linear-gradient(135deg, #dcfce7 0%, #bbf7d0 100%);
    color: var(--accent-color);
}

.stat-icon.expense {
    background: linear-gradient(135deg, #fee2e2 0%, #fecaca 100%);
    color: var(--danger-color);
}

.stat-icon.balance {
    background: linear-gradient(135deg, #dbeafe 0%, #bfdbfe 100%);
    color: var(--primary-color);
}

.stat-icon.activity {
    background: linear-gradient(135deg, #fef3c7 0%, #fde68a 100%);
    color: var(--warning-color);
}

.stat-trend {
    font-size: 0.75rem;
    font-weight: 600;
    padding: 0.25rem 0.5rem;
    border-radius: 6px;
}

.stat-trend.positive {
    background: #dcfce7;
    color: var(--accent-color);
}

.stat-trend.negative {
    background: #fee2e2;
    color: var(--danger-color);
}

.stat-value {
    font-size: 1.75rem;
    font-weight: 700;
    color: var(--text-primary);
    margin-bottom: 0.5rem;
}

.stat-label {
    font-size: 0.875rem;
    font-weight: 600;
    color: var(--text-secondary);
    margin-bottom: 0.25rem;
}

.stat-sublabel {
    font-size: 0.75rem;
    color: var(--text-muted);
}

/* Quick Actions Panel */
.quick-actions-panel {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
}

.quick-action-item {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1rem;
    background: #f8fafc;
    border-radius: 12px;
    text-decoration: none;
    color: var(--text-primary);
    transition: all 0.3s ease;
    border: 1px solid transparent;
}

.quick-action-item:hover {
    background: white;
    border-color: var(--border-color);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
    text-decoration: none;
    color: var(--text-primary);
    transform: translateY(-2px);
}

.quick-action-item .quick-action-icon {
    width: 40px;
    height: 40px;
    border-radius: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
}

.action-title {
    font-size: 0.875rem;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 0.25rem;
}

.action-subtitle {
    font-size: 0.75rem;
    color: var(--text-muted);
}

/* Account Summary Items */
.account-summary-items {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.summary-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.75rem 0;
    border-bottom: 1px solid #f1f5f9;
}

.summary-item:last-child {
    border-bottom: none;
}

.summary-label {
    font-size: 0.875rem;
    color: var(--text-secondary);
    font-weight: 500;
}

.summary-value {
    font-size: 0.875rem;
    font-weight: 600;
    color: var(--text-primary);
}

/* Full Width Transaction History */
.transaction-history-section.full-width {
    margin-top: 2rem;
}

/* Account Details Section */
.account-details-section {
    margin-top: 2rem;
}

.details-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
    gap: 2rem;
}

.detail-card {
    background: white;
    border-radius: 16px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    border: 1px solid var(--border-color);
    overflow: hidden;
}

.detail-header {
    padding: 1.5rem 2rem;
    background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
    border-bottom: 1px solid var(--border-color);
}

.detail-header h4 {
    font-size: 1.125rem;
    font-weight: 700;
    color: var(--text-primary);
    margin: 0;
}

.detail-content {
    padding: 2rem;
}

.detail-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem 0;
    border-bottom: 1px solid #f1f5f9;
}

.detail-row:last-child {
    border-bottom: none;
}

.detail-row .label {
    font-size: 0.875rem;
    color: var(--text-secondary);
    font-weight: 500;
}

.detail-row .value {
    font-size: 0.875rem;
    font-weight: 600;
    color: var(--text-primary);
}

.detail-row .value.status-active {
    color: var(--accent-color);
}

.detail-row .value.status-pending {
    color: var(--warning-color);
}

.detail-row .value.status-suspended {
    color: var(--danger-color);
}

/* Summary Stats */
.summary-stats {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 2rem;
    margin-bottom: 2rem;
}

.stat-item {
    text-align: center;
    padding: 1.5rem;
    background: #f8fafc;
    border-radius: 12px;
    border: 1px solid var(--border-color);
}

.stat-item .stat-value {
    font-size: 2rem;
    font-weight: 700;
    color: var(--text-primary);
    margin-bottom: 0.5rem;
}

.stat-item .stat-label {
    font-size: 0.875rem;
    color: var(--text-secondary);
    font-weight: 600;
    margin-bottom: 0.5rem;
}

.stat-amount {
    font-size: 0.875rem;
    font-weight: 600;
}

.stat-amount.positive {
    color: var(--accent-color);
}

.stat-amount.negative {
    color: var(--danger-color);
}

.summary-actions {
    text-align: center;
}

.summary-link {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    color: var(--primary-color);
    text-decoration: none;
    font-weight: 600;
    font-size: 0.875rem;
    transition: all 0.3s ease;
}

.summary-link:hover {
    color: var(--primary-dark);
    text-decoration: none;
    transform: translateX(4px);
}

.sidebar-brand {
    padding: 0 2rem 2rem 2rem;
    border-bottom: 1px solid var(--border-color);
    margin-bottom: 2rem;
}

.brand-name {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--text-primary);
}

.sidebar-nav {
    padding: 0 1rem;
}

.nav-section {
    margin-bottom: 2rem;
}

.nav-section-title {
    font-size: 0.75rem;
    font-weight: 600;
    color: var(--text-muted);
    text-transform: uppercase;
    letter-spacing: 0.05em;
    padding: 0 1rem;
    margin-bottom: 0.5rem;
}

.nav-item {
    margin-bottom: 0.25rem;
}

.nav-link {
    display: flex;
    align-items: center;
    padding: 0.75rem 1rem;
    color: var(--text-secondary);
    text-decoration: none;
    border-radius: 8px;
    transition: all 0.2s ease;
    font-weight: 500;
    font-size: 0.875rem;
}

.nav-link:hover {
    background: rgba(99, 102, 241, 0.1);
    color: var(--primary-color);
    text-decoration: none;
}

.nav-link.active {
    background: var(--primary-color);
    color: white;
}

.nav-link i {
    width: 20px;
    margin-right: 0.75rem;
    font-size: 1rem;
}

/* Main Content */
.dashboard-main {
    flex: 1;
    margin-left: 280px;
    padding: 2rem;
    background: white;
}

/* Header */
.dashboard-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;
}

.header-title {
    font-size: 2rem;
    font-weight: 700;
    color: var(--text-primary);
}

.header-actions {
    display: flex;
    gap: 1rem;
    align-items: center;
}

.btn {
    padding: 0.75rem 1.5rem;
    border-radius: 8px;
    font-weight: 600;
    font-size: 0.875rem;
    text-decoration: none;
    border: none;
    cursor: pointer;
    transition: all 0.2s ease;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
}

.btn-primary {
    background: var(--primary-color);
    color: white;
}

.btn-primary:hover {
    background: var(--primary-dark);
    text-decoration: none;
    color: white;
}

.btn-outline {
    background: white;
    color: var(--text-primary);
    border: 1px solid var(--border-color);
}

.btn-outline:hover {
    background: var(--sidebar-bg);
    text-decoration: none;
    color: var(--text-primary);
}

.user-avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: var(--primary-color);
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 600;
    font-size: 0.875rem;
}

/* Dashboard Grid */
.dashboard-grid {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: 2rem;
    margin-bottom: 2rem;
}

/* Balance Section */
.balance-section {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 2rem;
    margin-bottom: 2rem;
}

.balance-card {
    background: white;
    border-radius: 12px;
    padding: 2rem;
    box-shadow: var(--card-shadow);
    border: 1px solid var(--border-color);
}

.balance-header {
    margin-bottom: 1rem;
}

.balance-title {
    font-size: 0.875rem;
    color: var(--text-secondary);
    margin-bottom: 0.5rem;
    font-weight: 500;
}

.balance-amount {
    font-size: 2.5rem;
    font-weight: 700;
    color: var(--text-primary);
    margin-bottom: 1rem;
}

.balance-actions {
    display: flex;
    gap: 0.75rem;
}

.balance-action {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.5rem 1rem;
    background: var(--sidebar-bg);
    border: 1px solid var(--border-color);
    border-radius: 8px;
    color: var(--text-primary);
    text-decoration: none;
    font-size: 0.875rem;
    font-weight: 500;
    transition: all 0.2s ease;
}

.balance-action:hover {
    background: var(--primary-color);
    color: white;
    text-decoration: none;
}

.balance-action i {
    font-size: 0.875rem;
}

/* Quick Transaction */
.quick-transaction {
    background: white;
    border-radius: 12px;
    padding: 2rem;
    box-shadow: var(--card-shadow);
    border: 1px solid var(--border-color);
}

.quick-transaction-header {
    margin-bottom: 1.5rem;
}

.quick-transaction-title {
    font-size: 1.125rem;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 0.5rem;
}

.quick-contacts {
    display: flex;
    gap: 1rem;
    margin-bottom: 1.5rem;
}

.contact-avatar {
    width: 48px;
    height: 48px;
    border-radius: 50%;
    background: var(--sidebar-bg);
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 600;
    color: var(--text-primary);
    cursor: pointer;
    transition: all 0.2s ease;
    border: 2px solid transparent;
}

.contact-avatar:hover {
    border-color: var(--primary-color);
    transform: scale(1.05);
}

.contact-avatar.add-contact {
    background: var(--primary-color);
    color: white;
}

/* Cards Section */
.cards-section {
    margin-bottom: 2rem;
}

.section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1.5rem;
}

.section-title {
    font-size: 1.125rem;
    font-weight: 600;
    color: var(--text-primary);
}

.section-action {
    color: var(--primary-color);
    text-decoration: none;
    font-weight: 500;
    font-size: 0.875rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.section-action:hover {
    text-decoration: none;
    color: var(--primary-dark);
}

.credit-card {
    background: linear-gradient(135deg, #1f2937 0%, #374151 100%);
    border-radius: 16px;
    padding: 2rem;
    color: white;
    position: relative;
    overflow: hidden;
    min-height: 200px;
}

.credit-card::before {
    content: '';
    position: absolute;
    top: -50%;
    right: -50%;
    width: 100%;
    height: 100%;
    background: radial-gradient(circle, rgba(255,255,255,0.1) 0%, transparent 70%);
    pointer-events: none;
}

.card-brand {
    position: absolute;
    top: 2rem;
    right: 2rem;
    font-size: 1.5rem;
    font-weight: 700;
}

.card-holder {
    margin-top: 2rem;
    font-size: 0.875rem;
    opacity: 0.8;
}

.card-number {
    font-size: 1.25rem;
    font-weight: 600;
    letter-spacing: 0.1em;
    margin: 1rem 0;
}

.card-details {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 1rem;
}

.card-expiry {
    font-size: 0.875rem;
    opacity: 0.8;
}

.banking-welcome {
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 1rem;
}

.welcome-text h1 {
    font-size: 2rem;
    font-weight: 700;
    color: var(--text-primary);
    margin: 0;
}

.welcome-text p {
    color: var(--text-secondary);
    margin: 0.5rem 0 0 0;
    font-size: 1rem;
}

.quick-actions {
    display: flex;
    gap: 1rem;
    flex-wrap: wrap;
}

.quick-action-btn {
    background: var(--primary-color);
    color: white;
    border: none;
    border-radius: 12px;
    padding: 0.75rem 1.5rem;
    font-weight: 600;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    transition: all 0.3s ease;
    font-size: 0.875rem;
}

.quick-action-btn:hover {
    background: var(--primary-dark);
    transform: translateY(-2px);
    box-shadow: var(--card-shadow-hover);
    color: white;
    text-decoration: none;
}

.quick-action-btn.secondary {
    background: white;
    color: var(--primary-color);
    border: 2px solid var(--primary-color);
}

.quick-action-btn.secondary:hover {
    background: var(--primary-color);
    color: white;
}

/* Balance Card */
.balance-card {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
    border-radius: 20px;
    padding: 2.5rem;
    color: white;
    margin-bottom: 2rem;
    position: relative;
    overflow: hidden;
    border: none;
    box-shadow: 0 20px 25px -5px rgba(99, 102, 241, 0.3);
}

.balance-card::before {
    content: '';
    position: absolute;
    top: -50%;
    right: -50%;
    width: 100%;
    height: 100%;
    background: radial-gradient(circle, rgba(255,255,255,0.1) 0%, transparent 70%);
    pointer-events: none;
}

.balance-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 1.5rem;
}

.balance-title {
    font-size: 1rem;
    opacity: 0.9;
    margin: 0;
    font-weight: 500;
}

.balance-amount {
    font-size: 3rem;
    font-weight: 700;
    margin: 0;
    line-height: 1;
}

.account-info {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 1.5rem;
    padding-top: 1.5rem;
    border-top: 1px solid rgba(255,255,255,0.2);
}

.account-number {
    font-size: 0.875rem;
    opacity: 0.9;
}

.balance-actions {
    display: flex;
    gap: 0.75rem;
}

.balance-action {
    background: rgba(255,255,255,0.2);
    border: 1px solid rgba(255,255,255,0.3);
    color: white;
    border-radius: 10px;
    padding: 0.5rem 1rem;
    font-size: 0.875rem;
    text-decoration: none;
    transition: all 0.3s ease;
    backdrop-filter: blur(10px);
}

.balance-action:hover {
    background: rgba(255,255,255,0.3);
    color: white;
    text-decoration: none;
    transform: translateY(-1px);
}

/* Dashboard Grid */
.dashboard-grid {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: 2rem;
    margin-bottom: 2rem;
}

/* Transaction Section */
.transaction-section {
    background: white;
    border-radius: 16px;
    padding: 2rem;
    box-shadow: var(--card-shadow);
    border: 1px solid var(--border-color);
}

.section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1.5rem;
    padding-bottom: 1rem;
    border-bottom: 1px solid var(--border-color);
}

.section-title {
    font-size: 1.25rem;
    font-weight: 700;
    color: var(--text-primary);
    margin: 0;
}

.section-action {
    color: var(--primary-color);
    text-decoration: none;
    font-weight: 600;
    font-size: 0.875rem;
    transition: color 0.3s ease;
}

.section-action:hover {
    color: var(--primary-dark);
    text-decoration: none;
}

/* Transaction List */
.transaction-list {
    list-style: none;
    padding: 0;
    margin: 0;
}

.transaction-item {
    display: flex;
    align-items: center;
    padding: 1rem 0;
    border-bottom: 1px solid #f3f4f6;
    transition: all 0.3s ease;
}

.transaction-item:last-child {
    border-bottom: none;
}

.transaction-item:hover {
    background: #f9fafb;
    margin: 0 -1rem;
    padding: 1rem;
    border-radius: 12px;
}

.transaction-avatar {
    width: 48px;
    height: 48px;
    border-radius: 12px;
    background: var(--secondary-color);
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 1rem;
    font-weight: 600;
    color: var(--text-primary);
    font-size: 1.125rem;
}

.transaction-avatar.sent {
    background: #fee2e2;
    color: var(--danger-color);
}

.transaction-avatar.received {
    background: #dcfce7;
    color: var(--accent-color);
}

.transaction-details {
    flex: 1;
}

.transaction-name {
    font-weight: 600;
    color: var(--text-primary);
    margin: 0 0 0.25rem 0;
    font-size: 0.875rem;
}

.transaction-description {
    color: var(--text-secondary);
    font-size: 0.75rem;
    margin: 0;
}

.transaction-amount {
    text-align: right;
}

.amount-value {
    font-weight: 700;
    font-size: 0.875rem;
    margin: 0 0 0.25rem 0;
}

.amount-value.positive {
    color: var(--accent-color);
}

.amount-value.negative {
    color: var(--danger-color);
}

.transaction-date {
    color: var(--text-muted);
    font-size: 0.75rem;
    margin: 0;
}

/* Quick Stats */
.quick-stats {
    display: grid;
    gap: 1.5rem;
}

.stat-card {
    background: white;
    border-radius: 16px;
    padding: 1.5rem;
    box-shadow: var(--card-shadow);
    border: 1px solid var(--border-color);
    transition: all 0.3s ease;
}

.stat-card:hover {
    transform: translateY(-2px);
    box-shadow: var(--card-shadow-hover);
}

.stat-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 1rem;
}

.stat-icon {
    width: 40px;
    height: 40px;
    border-radius: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.25rem;
}

.stat-icon.income {
    background: #dcfce7;
    color: var(--accent-color);
}

.stat-icon.expense {
    background: #fee2e2;
    color: var(--danger-color);
}

.stat-icon.transactions {
    background: #dbeafe;
    color: var(--primary-color);
}

.stat-value {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--text-primary);
    margin: 0;
}

.stat-label {
    color: var(--text-secondary);
    font-size: 0.875rem;
    margin: 0.25rem 0 0 0;
}

.stat-change {
    font-size: 0.75rem;
    font-weight: 600;
    padding: 0.25rem 0.5rem;
    border-radius: 6px;
    margin-top: 0.5rem;
    display: inline-block;
}

.stat-change.positive {
    background: #dcfce7;
    color: var(--accent-color);
}

.stat-change.negative {
    background: #fee2e2;
    color: var(--danger-color);
}

/* Empty State */
.empty-state {
    text-align: center;
    padding: 3rem 1rem;
    color: var(--text-secondary);
}

.empty-state-icon {
    font-size: 3rem;
    margin-bottom: 1rem;
    opacity: 0.5;
}

.empty-state h3 {
    font-size: 1.125rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
    color: var(--text-primary);
}

.empty-state p {
    margin: 0;
    font-size: 0.875rem;
}

/* Account Overview Section */
.account-overview-section {
    margin-bottom: 2rem;
}

.account-summary-card {
    background: linear-gradient(135deg, #1e293b 0%, #334155 100%);
    border-radius: 20px;
    padding: 2.5rem;
    color: white;
    margin-bottom: 2rem;
    position: relative;
    overflow: hidden;
    box-shadow: 0 20px 25px -5px rgba(30, 41, 59, 0.3);
}

.account-summary-card::before {
    content: '';
    position: absolute;
    top: -50%;
    right: -50%;
    width: 100%;
    height: 100%;
    background: radial-gradient(circle, rgba(255,255,255,0.1) 0%, transparent 70%);
    pointer-events: none;
}

.account-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 2rem;
}

.account-info h2 {
    font-size: 1.5rem;
    font-weight: 700;
    margin: 0 0 0.5rem 0;
    line-height: 1.2;
}

.account-subtitle {
    font-size: 0.875rem;
    opacity: 0.8;
    margin: 0;
    font-weight: 500;
}

.account-status {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
    align-items: flex-end;
}

.status-badge, .kyc-badge {
    padding: 0.25rem 0.75rem;
    border-radius: 20px;
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

.status-badge.status-active {
    background: rgba(16, 185, 129, 0.2);
    color: #10b981;
    border: 1px solid rgba(16, 185, 129, 0.3);
}

.status-badge.status-suspended {
    background: rgba(239, 68, 68, 0.2);
    color: #ef4444;
    border: 1px solid rgba(239, 68, 68, 0.3);
}

.kyc-badge.kyc-verified {
    background: rgba(16, 185, 129, 0.2);
    color: #10b981;
    border: 1px solid rgba(16, 185, 129, 0.3);
}

.kyc-badge.kyc-pending {
    background: rgba(245, 158, 11, 0.2);
    color: #f59e0b;
    border: 1px solid rgba(245, 158, 11, 0.3);
}

.kyc-badge.kyc-rejected {
    background: rgba(239, 68, 68, 0.2);
    color: #ef4444;
    border: 1px solid rgba(239, 68, 68, 0.3);
}

.account-avatar {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.2);
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 700;
    font-size: 1.25rem;
    border: 2px solid rgba(255, 255, 255, 0.3);
}

.balance-display {
    margin-bottom: 2rem;
}

.balance-label {
    font-size: 0.875rem;
    opacity: 0.8;
    margin: 0 0 0.5rem 0;
    font-weight: 500;
}

.balance-amount {
    font-size: 3rem;
    font-weight: 700;
    margin: 0 0 0.5rem 0;
    line-height: 1;
}

.balance-details {
    font-size: 0.875rem;
    opacity: 0.8;
    margin: 0;
}

.balance-actions {
    display: flex;
    gap: 1rem;
    flex-wrap: wrap;
}

.action-btn {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.75rem 1.5rem;
    border-radius: 12px;
    text-decoration: none;
    font-weight: 600;
    font-size: 0.875rem;
    transition: all 0.3s ease;
    border: none;
    cursor: pointer;
}

.action-btn.primary {
    background: rgba(255, 255, 255, 0.2);
    color: white;
    border: 1px solid rgba(255, 255, 255, 0.3);
    backdrop-filter: blur(10px);
}

.action-btn.primary:hover {
    background: rgba(255, 255, 255, 0.3);
    color: white;
    text-decoration: none;
    transform: translateY(-2px);
}

.action-btn.secondary {
    background: rgba(255, 255, 255, 0.1);
    color: white;
    border: 1px solid rgba(255, 255, 255, 0.2);
    backdrop-filter: blur(10px);
}

.action-btn.secondary:hover {
    background: rgba(255, 255, 255, 0.2);
    color: white;
    text-decoration: none;
    transform: translateY(-2px);
}

/* Account Details Grid */
.account-details-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.5rem;
    margin-bottom: 2rem;
}

.detail-card {
    background: white;
    border-radius: 16px;
    padding: 1.5rem;
    box-shadow: var(--card-shadow);
    border: 1px solid var(--border-color);
    display: flex;
    align-items: center;
    gap: 1rem;
    transition: all 0.3s ease;
}

.detail-card:hover {
    transform: translateY(-2px);
    box-shadow: var(--card-shadow-hover);
}

.detail-icon {
    width: 48px;
    height: 48px;
    border-radius: 12px;
    background: var(--sidebar-bg);
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--primary-color);
    flex-shrink: 0;
}

.detail-content h4 {
    font-size: 0.875rem;
    font-weight: 600;
    color: var(--text-secondary);
    margin: 0 0 0.25rem 0;
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

.detail-content p {
    font-size: 1rem;
    font-weight: 600;
    color: var(--text-primary);
    margin: 0;
}

/* Financial Summary Grid */
.financial-summary-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 1.5rem;
    margin-bottom: 2rem;
}

.summary-card {
    background: white;
    border-radius: 16px;
    padding: 2rem;
    box-shadow: var(--card-shadow);
    border: 1px solid var(--border-color);
    transition: all 0.3s ease;
}

.summary-card:hover {
    transform: translateY(-2px);
    box-shadow: var(--card-shadow-hover);
}

.summary-header {
    display: flex;
    align-items: center;
    gap: 1rem;
    margin-bottom: 1.5rem;
}

.summary-icon {
    width: 48px;
    height: 48px;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
}

.summary-card.income .summary-icon {
    background: #dcfce7;
    color: var(--accent-color);
}

.summary-card.expense .summary-icon {
    background: #fee2e2;
    color: var(--danger-color);
}

.summary-card.net .summary-icon {
    background: #dbeafe;
    color: var(--primary-color);
}

.summary-info h4 {
    font-size: 1rem;
    font-weight: 600;
    color: var(--text-primary);
    margin: 0 0 0.25rem 0;
}

.summary-info p {
    font-size: 0.875rem;
    color: var(--text-secondary);
    margin: 0;
}

.summary-amount {
    font-size: 2rem;
    font-weight: 700;
    color: var(--text-primary);
    margin-bottom: 0.5rem;
    line-height: 1;
}

.summary-change {
    font-size: 0.875rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
}

.summary-change.positive {
    color: var(--accent-color);
}

.summary-change.negative {
    color: var(--danger-color);
}

.summary-details {
    font-size: 0.75rem;
    color: var(--text-muted);
    margin: 0;
}

/* Enhanced Transaction History Styles */
.transaction-history-section {
    background: white;
    border-radius: 16px;
    box-shadow: var(--card-shadow);
    border: 1px solid var(--border-color);
    overflow: hidden;
}

.section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 2rem;
    border-bottom: 1px solid var(--border-color);
}

.section-title h3 {
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--text-primary);
    margin: 0 0 0.25rem 0;
}

.section-title p {
    font-size: 0.875rem;
    color: var(--text-secondary);
    margin: 0;
}

.section-actions {
    display: flex;
    gap: 1rem;
}

.btn-secondary, .btn-outline {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.75rem 1rem;
    border-radius: 8px;
    text-decoration: none;
    font-size: 0.875rem;
    font-weight: 500;
    transition: all 0.3s ease;
}

.btn-secondary {
    background: var(--sidebar-bg);
    color: var(--text-primary);
    border: 1px solid var(--border-color);
}

.btn-secondary:hover {
    background: var(--primary-color);
    color: white;
    text-decoration: none;
}

.btn-outline {
    background: transparent;
    color: var(--text-secondary);
    border: 1px solid var(--border-color);
}

.btn-outline:hover {
    background: var(--border-color);
    color: var(--text-primary);
    text-decoration: none;
}

.transaction-list-container {
    padding: 0;
}

.enhanced-transaction-item {
    display: flex;
    align-items: center;
    padding: 1.5rem 2rem;
    border-bottom: 1px solid var(--border-color);
    cursor: pointer;
    transition: all 0.3s ease;
}

.enhanced-transaction-item:hover {
    background: var(--sidebar-bg);
}

.enhanced-transaction-item:last-child {
    border-bottom: none;
}

.transaction-main {
    display: flex;
    align-items: center;
    flex: 1;
    gap: 1rem;
}

.transaction-avatar {
    flex-shrink: 0;
}

.avatar-icon {
    width: 48px;
    height: 48px;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
}

.avatar-icon.sent {
    background: var(--danger-color);
}

.avatar-icon.received {
    background: var(--accent-color);
}

.transaction-details {
    flex: 1;
    min-width: 0;
}

.transaction-primary {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    margin-bottom: 0.5rem;
}

.transaction-primary h4 {
    font-size: 1rem;
    font-weight: 600;
    color: var(--text-primary);
    margin: 0;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.transaction-type {
    font-size: 0.75rem;
    font-weight: 500;
    padding: 0.25rem 0.5rem;
    border-radius: 4px;
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

.transaction-secondary {
    display: flex;
    align-items: center;
    gap: 1rem;
    margin-bottom: 0.25rem;
}

.transaction-description {
    font-size: 0.875rem;
    color: var(--text-secondary);
}

.transaction-id {
    font-size: 0.75rem;
    color: var(--text-muted);
    font-family: 'Courier New', monospace;
}

.transaction-amount-section {
    text-align: right;
    flex-shrink: 0;
}

.amount {
    font-size: 1.125rem;
    font-weight: 700;
    margin-bottom: 0.25rem;
}

.amount.positive {
    color: var(--accent-color);
}

.amount.negative {
    color: var(--danger-color);
}

.transaction-time {
    font-size: 0.75rem;
    color: var(--text-muted);
    margin-bottom: 0.25rem;
}

.transaction-status .status-badge {
    font-size: 0.75rem;
    font-weight: 500;
    padding: 0.25rem 0.5rem;
    border-radius: 4px;
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

.status-badge.status-completed {
    background: #dcfce7;
    color: var(--accent-color);
}

.status-badge.status-pending {
    background: #fef3c7;
    color: #f59e0b;
}

.status-badge.status-failed {
    background: #fee2e2;
    color: var(--danger-color);
}

.status-badge.status-cancelled {
    background: #f3f4f6;
    color: var(--text-muted);
}

.transaction-expand-icon {
    margin-left: 1rem;
    color: var(--text-muted);
    transition: transform 0.3s ease;
}

.enhanced-transaction-item:hover .transaction-expand-icon {
    transform: translateX(4px);
}

.transaction-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1.5rem 2rem;
    border-top: 1px solid var(--border-color);
    background: var(--sidebar-bg);
}

.transaction-summary {
    font-size: 0.875rem;
    color: var(--text-secondary);
}

.view-all-link {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    color: var(--primary-color);
    text-decoration: none;
    font-weight: 500;
    font-size: 0.875rem;
    transition: all 0.3s ease;
}

.view-all-link:hover {
    color: var(--primary-color);
    text-decoration: none;
    transform: translateX(4px);
}

.empty-transactions {
    text-align: center;
    padding: 4rem 2rem;
}

.empty-icon {
    margin-bottom: 1.5rem;
    color: var(--text-muted);
    opacity: 0.5;
}

.empty-transactions h4 {
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--text-primary);
    margin: 0 0 0.5rem 0;
}

.empty-transactions p {
    font-size: 0.875rem;
    color: var(--text-secondary);
    margin: 0 0 2rem 0;
    max-width: 400px;
    margin-left: auto;
    margin-right: auto;
}

.empty-actions {
    display: flex;
    gap: 1rem;
    justify-content: center;
    flex-wrap: wrap;
}

.btn-primary {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.75rem 1.5rem;
    background: var(--primary-color);
    color: white;
    border: none;
    border-radius: 8px;
    text-decoration: none;
    font-weight: 500;
    font-size: 0.875rem;
    transition: all 0.3s ease;
}

.btn-primary:hover {
    background: #5a67d8;
    color: white;
    text-decoration: none;
    transform: translateY(-2px);
}

/* Responsive Design */

/* Large Desktop (1920px+) */
@media (min-width: 1920px) {
    .main-content {
        padding: 3rem;
    }

    .stats-cards-row {
        grid-template-columns: repeat(4, 1fr);
    }

    .dashboard-grid {
        grid-template-columns: 2fr 400px;
        gap: 3rem;
    }

    .account-details-grid {
        grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    }
}

/* Desktop (1200px - 1919px) */
@media (min-width: 1200px) and (max-width: 1919px) {
    .stats-cards-row {
        grid-template-columns: repeat(4, 1fr);
    }

    .dashboard-grid {
        grid-template-columns: 2fr 350px;
    }
}

/* Tablet Landscape (1024px - 1199px) */
@media (min-width: 1024px) and (max-width: 1199px) {
    .main-content {
        padding: 1.5rem;
    }

    .stats-cards-row {
        grid-template-columns: repeat(2, 1fr);
    }

    .dashboard-grid {
        grid-template-columns: 1fr;
        gap: 2rem;
    }

    .sidebar-section {
        grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
        display: grid;
        gap: 1.5rem;
    }

    .account-details-grid {
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    }

    .financial-summary-grid {
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    }

    .details-grid {
        grid-template-columns: 1fr;
    }
}

/* Tablet Portrait (768px - 1023px) */
@media (min-width: 768px) and (max-width: 1023px) {
    .banking-sidebar,
    .dashboard-sidebar {
        width: 260px;
    }

    .main-content {
        margin-left: 260px;
        padding: 1.5rem;
    }

    .stats-cards-row {
        grid-template-columns: repeat(2, 1fr);
        gap: 1rem;
    }

    .dashboard-grid {
        grid-template-columns: 1fr;
        gap: 1.5rem;
    }

    .sidebar-section {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
        gap: 1.5rem;
    }

    .account-details-grid {
        grid-template-columns: repeat(auto-fit, minmax(220px, 1fr));
        gap: 1rem;
    }

    .financial-summary-grid {
        grid-template-columns: 1fr;
        gap: 1rem;
    }

    .quick-actions {
        grid-template-columns: repeat(auto-fit, minmax(100px, 1fr));
        gap: 0.75rem;
    }

    .balance-actions {
        flex-wrap: wrap;
        gap: 0.5rem;
    }

    .action-btn {
        padding: 0.5rem 0.75rem;
        font-size: 0.8125rem;
    }

    .details-grid {
        grid-template-columns: 1fr;
        gap: 1.5rem;
    }

    .summary-stats {
        grid-template-columns: 1fr;
        gap: 1rem;
    }
}

/* Mobile Landscape (480px - 767px) */
@media (min-width: 480px) and (max-width: 767px) {
    .banking-sidebar,
    .dashboard-sidebar {
        transform: translateX(-100%);
        transition: transform 0.3s ease;
        width: 280px;
        z-index: 1000;
    }

    .banking-sidebar.mobile-open,
    .dashboard-sidebar.mobile-open {
        transform: translateX(0);
    }

    .main-content {
        margin-left: 0;
        padding: 1rem;
    }

    .top-bar {
        flex-direction: column;
        align-items: flex-start;
        gap: 1rem;
        padding: 1rem 1.5rem;
    }

    .top-bar-actions {
        width: 100%;
        justify-content: space-between;
    }

    .stats-cards-row {
        grid-template-columns: 1fr;
        gap: 1rem;
    }

    .stat-card {
        padding: 1rem;
    }

    .stat-header {
        margin-bottom: 0.75rem;
    }

    .stat-value {
        font-size: 1.5rem;
    }

    .dashboard-grid {
        grid-template-columns: 1fr;
        gap: 1rem;
    }

    .account-summary-card {
        padding: 1.5rem;
    }

    .account-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 1rem;
        margin-bottom: 1.5rem;
    }

    .balance-display {
        flex-direction: column;
        align-items: flex-start;
        gap: 1rem;
    }

    .balance-amount {
        font-size: 2.5rem;
    }

    .balance-actions {
        width: 100%;
        justify-content: space-between;
    }

    .account-details-grid {
        grid-template-columns: 1fr;
        gap: 1rem;
    }

    .detail-card {
        padding: 1rem;
    }

    .financial-summary-grid {
        grid-template-columns: 1fr;
        gap: 1rem;
    }

    .quick-actions {
        grid-template-columns: repeat(3, 1fr);
        gap: 0.5rem;
    }

    .quick-action {
        padding: 1rem 0.5rem;
        font-size: 0.8125rem;
    }

    .quick-action-icon {
        width: 40px;
        height: 40px;
        margin-bottom: 0.5rem;
    }

    .sidebar-section {
        display: flex;
        flex-direction: column;
        gap: 1rem;
    }

    .quick-actions-panel {
        gap: 0.5rem;
    }

    .quick-action-item {
        padding: 0.75rem;
    }

    .details-grid {
        grid-template-columns: 1fr;
        gap: 1rem;
    }

    .detail-content {
        padding: 1.5rem;
    }

    .summary-stats {
        grid-template-columns: 1fr;
        gap: 1rem;
    }

    .transaction-list-container {
        overflow-x: auto;
    }

    .enhanced-transaction-item {
        padding: 1rem;
        min-width: 400px;
    }

    .section-header {
        padding: 1rem 1.5rem;
        flex-direction: column;
        align-items: flex-start;
        gap: 1rem;
    }

    .section-actions {
        width: 100%;
        justify-content: space-between;
    }
}

/* Mobile Portrait (< 480px) */
@media (max-width: 479px) {
    .banking-sidebar,
    .dashboard-sidebar {
        width: 100%;
        transform: translateX(-100%);
        transition: transform 0.3s ease;
    }

    .banking-sidebar.mobile-open,
    .dashboard-sidebar.mobile-open {
        transform: translateX(0);
    }

    .main-content {
        margin-left: 0;
        padding: 0.75rem;
    }

    .top-bar {
        flex-direction: column;
        align-items: flex-start;
        gap: 1rem;
        padding: 1rem;
    }

    .top-bar h1 {
        font-size: 1.5rem;
    }

    .top-bar-actions {
        width: 100%;
        flex-wrap: wrap;
        gap: 0.5rem;
    }

    .btn-outline,
    .btn-primary {
        padding: 0.5rem 1rem;
        font-size: 0.8125rem;
    }

    .stats-cards-row {
        grid-template-columns: 1fr;
        gap: 0.75rem;
    }

    .stat-card {
        padding: 1rem;
    }

    .stat-icon {
        width: 40px;
        height: 40px;
    }

    .stat-value {
        font-size: 1.25rem;
    }

    .stat-label {
        font-size: 0.8125rem;
    }

    .account-summary-card {
        padding: 1rem;
    }

    .account-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 0.75rem;
        margin-bottom: 1rem;
    }

    .account-title h2 {
        font-size: 1.25rem;
    }

    .balance-display {
        flex-direction: column;
        align-items: flex-start;
        gap: 1rem;
    }

    .balance-amount {
        font-size: 2rem;
    }

    .balance-actions {
        width: 100%;
        flex-direction: column;
        gap: 0.5rem;
    }

    .action-btn {
        width: 100%;
        justify-content: center;
        padding: 0.75rem;
    }

    .account-details-grid {
        grid-template-columns: 1fr;
        gap: 0.75rem;
    }

    .detail-card {
        flex-direction: column;
        align-items: flex-start;
        gap: 0.75rem;
        padding: 1rem;
    }

    .detail-icon {
        width: 40px;
        height: 40px;
    }

    .financial-summary-grid {
        grid-template-columns: 1fr;
        gap: 0.75rem;
    }

    .summary-card {
        padding: 1rem;
    }

    .summary-icon {
        width: 40px;
        height: 40px;
    }

    .summary-amount {
        font-size: 1.5rem;
    }

    .quick-actions {
        grid-template-columns: repeat(2, 1fr);
        gap: 0.5rem;
    }

    .quick-action {
        padding: 0.75rem 0.5rem;
        font-size: 0.75rem;
    }

    .quick-action-icon {
        width: 36px;
        height: 36px;
        margin-bottom: 0.5rem;
    }

    .sidebar-section {
        gap: 0.75rem;
    }

    .card {
        margin-bottom: 0.75rem;
    }

    .card-header {
        padding: 1rem;
    }

    .card-title {
        font-size: 1rem;
    }

    .quick-actions-panel {
        gap: 0.5rem;
    }

    .quick-action-item {
        padding: 0.75rem;
        flex-direction: column;
        text-align: center;
        gap: 0.5rem;
    }

    .quick-action-item .quick-action-icon {
        width: 36px;
        height: 36px;
    }

    .action-title {
        font-size: 0.8125rem;
    }

    .action-subtitle {
        font-size: 0.6875rem;
    }

    .account-summary-items {
        gap: 0.75rem;
    }

    .summary-item {
        padding: 0.5rem 0;
        flex-direction: column;
        align-items: flex-start;
        gap: 0.25rem;
    }

    .details-grid {
        grid-template-columns: 1fr;
        gap: 0.75rem;
    }

    .detail-header {
        padding: 1rem;
    }

    .detail-content {
        padding: 1rem;
    }

    .detail-row {
        flex-direction: column;
        align-items: flex-start;
        gap: 0.25rem;
        padding: 0.75rem 0;
    }

    .summary-stats {
        grid-template-columns: 1fr;
        gap: 0.75rem;
    }

    .stat-item {
        padding: 1rem;
    }

    .stat-item .stat-value {
        font-size: 1.5rem;
    }

    .transaction-history-section {
        margin-top: 1rem;
    }

    .section-header {
        padding: 1rem;
        flex-direction: column;
        align-items: flex-start;
        gap: 0.75rem;
    }

    .section-title h3 {
        font-size: 1rem;
    }

    .section-actions {
        width: 100%;
        flex-wrap: wrap;
        gap: 0.5rem;
    }

    .enhanced-transaction-item {
        padding: 1rem;
        flex-direction: column;
        align-items: flex-start;
        gap: 0.75rem;
        min-width: auto;
    }

    .transaction-main {
        width: 100%;
        gap: 0.75rem;
    }

    .avatar-icon {
        width: 40px;
        height: 40px;
    }

    .transaction-amount-section {
        text-align: left;
        width: 100%;
    }

    .transaction-expand-icon {
        margin-left: 0;
        align-self: flex-end;
    }

    .transaction-footer {
        padding: 1rem;
        flex-direction: column;
        align-items: flex-start;
        gap: 0.75rem;
    }

    .view-all-link {
        width: 100%;
        justify-content: center;
    }
}

/* Form Styles */
.form-card {
    background: white;
    border-radius: 16px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    border: 1px solid var(--border-color);
    overflow: hidden;
}

.form-header {
    padding: 1.5rem 2rem;
    background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
    border-bottom: 1px solid var(--border-color);
}

.form-header h3 {
    font-size: 1.25rem;
    font-weight: 700;
    color: var(--text-primary);
    margin: 0 0 0.5rem 0;
}

.form-header p {
    font-size: 0.875rem;
    color: var(--text-secondary);
    margin: 0;
}

.form-group {
    margin-bottom: 1.5rem;
}

.form-group label {
    display: block;
    font-size: 0.875rem;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 0.5rem;
}

.form-control {
    width: 100%;
    padding: 0.75rem 1rem;
    border: 1px solid var(--border-color);
    border-radius: 8px;
    font-size: 0.875rem;
    transition: all 0.3s ease;
    background: white;
}

.form-control:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
}

.amount-input {
    position: relative;
    display: flex;
    align-items: center;
}

.currency-symbol {
    position: absolute;
    left: 1rem;
    font-weight: 600;
    color: var(--text-secondary);
    z-index: 1;
}

.amount-input .form-control {
    padding-left: 3rem;
}

.form-actions {
    display: flex;
    gap: 1rem;
    justify-content: flex-end;
    margin-top: 2rem;
}

/* Alert Styles */
.alert {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 1rem 1.5rem;
    border-radius: 12px;
    margin-bottom: 1.5rem;
    font-size: 0.875rem;
    font-weight: 500;
}

.alert-success {
    background: #dcfce7;
    color: #166534;
    border: 1px solid #bbf7d0;
}

.alert-error {
    background: #fee2e2;
    color: #991b1b;
    border: 1px solid #fecaca;
}

.alert svg {
    flex-shrink: 0;
}

/* Page Header */
.page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;
    padding: 1.5rem 2rem;
    background: white;
    border-radius: 16px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    border: 1px solid var(--border-color);
}

.page-title h1 {
    font-size: 2rem;
    font-weight: 700;
    color: var(--text-primary);
    margin: 0 0 0.5rem 0;
}

.page-title p {
    font-size: 0.875rem;
    color: var(--text-secondary);
    margin: 0;
}

.page-actions {
    display: flex;
    gap: 1rem;
}

/* Filters Section */
.filters-section {
    margin-bottom: 2rem;
}

.filter-card {
    background: white;
    border-radius: 16px;
    padding: 1.5rem 2rem;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    border: 1px solid var(--border-color);
}

.filter-group {
    display: flex;
    flex-direction: column;
    min-width: 150px;
}

.filter-group label {
    font-size: 0.875rem;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 0.5rem;
}

/* Pagination */
.pagination {
    display: flex;
    gap: 0.5rem;
    align-items: center;
}

.pagination-btn {
    padding: 0.5rem 0.75rem;
    border: 1px solid var(--border-color);
    border-radius: 6px;
    text-decoration: none;
    color: var(--text-primary);
    font-size: 0.875rem;
    font-weight: 500;
    transition: all 0.3s ease;
}

.pagination-btn:hover {
    background: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
    text-decoration: none;
}

.pagination-btn.active {
    background: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

/* Account Overview */
.account-overview {
    margin-bottom: 2rem;
}

.primary-account-card {
    background: linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%);
    border-radius: 20px;
    padding: 2rem;
    color: white;
    box-shadow: 0 8px 32px rgba(99, 102, 241, 0.3);
    position: relative;
    overflow: hidden;
}

.primary-account-card::before {
    content: '';
    position: absolute;
    top: -50%;
    right: -50%;
    width: 100%;
    height: 100%;
    background: radial-gradient(circle, rgba(255, 255, 255, 0.1) 0%, transparent 70%);
    pointer-events: none;
}

.balance-section {
    margin-top: 1.5rem;
}

.current-balance h3 {
    font-size: 0.875rem;
    opacity: 0.8;
    margin: 0 0 0.5rem 0;
}

.balance-amount {
    font-size: 3rem;
    font-weight: 700;
    margin: 0 0 1.5rem 0;
    color: white;
    line-height: 1;
}

@media (max-width: 768px) {
    .banking-container {
        padding: 0 0.75rem;
    }

    .banking-dashboard {
        padding: 1rem 0;
    }

    .banking-header {
        padding: 1.5rem;
        margin-bottom: 1.5rem;
    }

    .banking-welcome {
        flex-direction: column;
        align-items: flex-start;
        gap: 1.5rem;
    }

    .welcome-text h1 {
        font-size: 1.5rem;
    }

    .account-summary-card {
        padding: 2rem;
        margin-bottom: 1.5rem;
    }

    .account-header {
        flex-direction: column;
        gap: 1rem;
        align-items: flex-start;
    }

    .account-status {
        align-items: flex-start;
    }

    .balance-amount {
        font-size: 2.5rem;
    }

    .balance-actions {
        flex-direction: column;
        width: 100%;
    }

    .action-btn {
        justify-content: center;
        width: 100%;
    }

    .account-details-grid {
        grid-template-columns: 1fr;
        gap: 1rem;
    }

    .financial-summary-grid {
        grid-template-columns: 1fr;
        gap: 1rem;
    }

    .summary-card {
        padding: 1.5rem;
    }

    .summary-amount {
        font-size: 1.5rem;
    }

    .transaction-section {
        padding: 1.5rem;
    }

    .transaction-item:hover {
        margin: 0 -1.5rem;
        padding: 1rem 1.5rem;
    }

    .quick-actions {
        width: 100%;
        justify-content: flex-start;
    }

    .quick-action-btn {
        flex: 1;
        justify-content: center;
        min-width: 120px;
    }
}

@media (max-width: 480px) {
    .account-summary-card {
        padding: 1.5rem;
    }

    .account-info h2 {
        font-size: 1.25rem;
    }

    .balance-amount {
        font-size: 2rem;
    }

    .balance-actions {
        flex-direction: column;
        width: 100%;
        gap: 0.75rem;
    }

    .action-btn {
        width: 100%;
        text-align: center;
        justify-content: center;
    }

    .detail-card {
        padding: 1rem;
        flex-direction: column;
        text-align: center;
        gap: 0.75rem;
    }

    .summary-card {
        padding: 1rem;
    }

    .summary-header {
        flex-direction: column;
        text-align: center;
        gap: 0.75rem;
    }

    .summary-amount {
        font-size: 1.25rem;
    }

    .quick-actions {
        flex-direction: column;
    }

    .quick-action-btn {
        width: 100%;
    }
}
