/**
 * Wire Transfer Beneficiaries Page CSS
 * Dedicated styles for wire transfer beneficiaries management page
 * Extends dashboard patterns with wire beneficiaries-specific styling
 */

/* Import dashboard base styles */
@import url('../dashboard/dashboard.css');

/* Wire Beneficiaries Hero Section - Mini version of dashboard hero */
.wire-beneficiaries-hero {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
    border-radius: 16px;
    padding: 1.5rem 2rem;
    color: white;
    margin-bottom: 2rem;
    box-shadow: 0 8px 25px rgba(0,0,0,0.15);
    position: relative;
    overflow: hidden;
}

.wire-beneficiaries-hero::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, rgba(255,255,255,0.1) 0%, transparent 50%);
    pointer-events: none;
}

.wire-beneficiaries-hero .hero-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    position: relative;
    z-index: 1;
}

.wire-beneficiaries-hero .hero-main {
    flex: 1;
}

.wire-beneficiaries-hero .hero-title {
    font-size: 1.75rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
    text-shadow: 0 2px 4px rgba(0,0,0,0.3);
}

.wire-beneficiaries-hero .hero-subtitle {
    font-size: 1rem;
    opacity: 0.9;
    margin-bottom: 0.5rem;
}

.wire-beneficiaries-hero .hero-stats {
    font-size: 0.9rem;
    opacity: 0.8;
    font-weight: 500;
}

.wire-beneficiaries-hero .hero-actions {
    display: flex;
    gap: 0.75rem;
}

/* Wire Beneficiaries Section */
.wire-beneficiaries-section {
    background: var(--background-white);
    border-radius: 12px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.08);
    border: 1px solid var(--border-color);
    overflow: hidden;
}

.wire-beneficiaries-header {
    background: var(--background-light);
    padding: 1rem 1.5rem;
    border-bottom: 1px solid var(--border-color);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.wire-beneficiaries-header h3 {
    margin: 0;
    color: var(--text-primary);
    font-size: 1.25rem;
    font-weight: 600;
}

.wire-beneficiaries-summary {
    color: var(--text-secondary);
    font-size: 0.9rem;
}

/* Wire Beneficiaries Grid */
.wire-beneficiaries-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
    gap: 1.5rem;
    padding: 1.5rem;
}

/* Wire Beneficiary Card */
.wire-beneficiary-card {
    background: var(--background-white);
    border: 1px solid var(--border-color);
    border-radius: 12px;
    padding: 1.25rem;
    transition: all 0.3s ease;
    box-shadow: 0 2px 8px rgba(0,0,0,0.05);
}

.wire-beneficiary-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.15);
    border-color: var(--primary-color);
}

.wire-beneficiary-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 1rem;
}

.wire-beneficiary-info {
    flex: 1;
}

.wire-beneficiary-name {
    font-size: 1.1rem;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 0.5rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.favorite-star {
    color: #f59e0b;
    font-size: 0.9rem;
}

.international-badge {
    background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
    color: white;
    padding: 0.2rem 0.5rem;
    border-radius: 6px;
    font-size: 0.7rem;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.wire-beneficiary-account {
    margin-bottom: 0.5rem;
}

.account-number {
    font-family: 'Courier New', monospace;
    font-weight: 600;
    color: var(--text-primary);
    background: var(--background-light);
    padding: 0.25rem 0.5rem;
    border-radius: 4px;
    font-size: 0.9rem;
}

.wire-beneficiary-bank {
    color: var(--text-secondary);
    font-size: 0.9rem;
}

.swift-code {
    color: var(--text-muted);
    font-size: 0.8rem;
    font-family: 'Courier New', monospace;
    background: var(--background-light);
    padding: 0.2rem 0.4rem;
    border-radius: 4px;
    display: inline-block;
}

.wire-beneficiary-actions .dropdown-toggle {
    border: none;
    background: transparent;
    color: var(--text-secondary);
    padding: 0.25rem 0.5rem;
}

.wire-beneficiary-actions .dropdown-toggle:hover {
    color: var(--primary-color);
    background: var(--background-light);
}

/* Wire Beneficiary Details */
.wire-beneficiary-details {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
    gap: 0.75rem;
    padding-top: 1rem;
    border-top: 1px solid var(--border-color);
}

.detail-item {
    display: flex;
    flex-direction: column;
    gap: 0.25rem;
}

.detail-label {
    font-size: 0.75rem;
    color: var(--text-muted);
    text-transform: uppercase;
    letter-spacing: 0.5px;
    font-weight: 500;
}

.detail-value {
    font-size: 0.9rem;
    color: var(--text-primary);
    font-weight: 500;
}

/* Country Flag Icons */
.country-flag {
    width: 20px;
    height: 15px;
    border-radius: 2px;
    margin-right: 0.5rem;
    display: inline-block;
}

/* Empty State */
.empty-state {
    text-align: center;
    padding: 3rem 2rem;
    color: var(--text-muted);
}

.empty-state i {
    opacity: 0.5;
}

.empty-state h5 {
    margin-top: 1rem;
    margin-bottom: 0.5rem;
}

.empty-state p {
    margin-bottom: 1.5rem;
    max-width: 400px;
    margin-left: auto;
    margin-right: auto;
}

/* Modal Enhancements */
.modal-content {
    border: none;
    border-radius: 12px;
    box-shadow: 0 20px 60px rgba(0,0,0,0.2);
}

.modal-header {
    background: var(--background-light);
    border-bottom: 1px solid var(--border-color);
    border-radius: 12px 12px 0 0;
}

.modal-title {
    color: var(--text-primary);
    font-weight: 600;
}

.modal-body {
    padding: 1.5rem;
}

.modal-footer {
    background: var(--background-light);
    border-top: 1px solid var(--border-color);
    border-radius: 0 0 12px 12px;
}

/* Form Enhancements */
.form-label {
    font-weight: 500;
    color: var(--text-primary);
    margin-bottom: 0.5rem;
}

.form-control {
    border: 1px solid var(--border-color);
    border-radius: 8px;
    padding: 0.75rem;
    transition: all 0.3s ease;
}

.form-control:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(var(--primary-rgb), 0.25);
}

.form-text {
    color: var(--text-muted);
    font-size: 0.8rem;
    margin-top: 0.25rem;
}

.form-check-input:checked {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
}
