// Transactions Page JavaScript

document.addEventListener('DOMContentLoaded', function() {
    // Initialize transactions page functionality
    initializeTransactionsPage();
    
    // Initialize filters
    initializeFilters();
    
    // Initialize transaction interactions
    initializeTransactionInteractions();
    
    // Initialize pagination
    initializePagination();
});

// Transactions page initialization
function initializeTransactionsPage() {
    console.log('Transactions page initialized');
    
    // Add loading states to action buttons
    const actionButtons = document.querySelectorAll('.btn-primary, .btn-outline');
    actionButtons.forEach(button => {
        button.addEventListener('click', function(e) {
            if (this.classList.contains('btn-loading')) {
                e.preventDefault();
                return;
            }
        });
    });
    
    // Initialize transaction list animations
    animateTransactionItems();
}

// Filters functionality
function initializeFilters() {
    const filterForm = document.querySelector('.filters-section form');
    if (filterForm) {
        filterForm.addEventListener('submit', function(e) {
            e.preventDefault();
            applyFilters();
        });
        
        // Auto-apply filters on change
        const filterSelects = filterForm.querySelectorAll('select');
        filterSelects.forEach(select => {
            select.addEventListener('change', function() {
                // Debounce the filter application
                clearTimeout(this.filterTimeout);
                this.filterTimeout = setTimeout(() => {
                    applyFilters();
                }, 500);
            });
        });
    }
}

// Transaction interactions
function initializeTransactionInteractions() {
    const transactionItems = document.querySelectorAll('.enhanced-transaction-item');
    
    transactionItems.forEach(item => {
        item.addEventListener('click', function() {
            expandTransactionDetails(this);
        });
        
        // Add hover effects
        item.addEventListener('mouseenter', function() {
            this.style.transform = 'translateX(4px)';
        });
        
        item.addEventListener('mouseleave', function() {
            this.style.transform = 'translateX(0)';
        });
    });
}

// Pagination functionality
function initializePagination() {
    const paginationButtons = document.querySelectorAll('.pagination-btn');
    
    paginationButtons.forEach(button => {
        button.addEventListener('click', function(e) {
            if (this.classList.contains('active')) {
                e.preventDefault();
                return;
            }
            
            // Show loading state
            showPageLoading();
        });
    });
}

// Apply filters
function applyFilters() {
    const filterForm = document.querySelector('.filters-section form');
    if (!filterForm) return;
    
    const formData = new FormData(filterForm);
    const params = new URLSearchParams(formData);
    
    // Show loading state
    showTransactionLoading();
    
    // Update URL with filter parameters
    const newUrl = window.location.pathname + '?' + params.toString();
    
    // In a real application, you would fetch filtered data
    // For now, we'll simulate the loading and reload
    setTimeout(() => {
        window.location.href = newUrl;
    }, 1000);
}

// Expand transaction details
function expandTransactionDetails(transactionItem) {
    const transactionId = transactionItem.dataset.transactionId;
    
    // Check if details are already expanded
    const existingDetails = transactionItem.nextElementSibling;
    if (existingDetails && existingDetails.classList.contains('transaction-details-expanded')) {
        // Collapse details
        existingDetails.style.maxHeight = '0';
        setTimeout(() => {
            existingDetails.remove();
        }, 300);
        return;
    }
    
    // Create expanded details
    const detailsElement = createTransactionDetails(transactionId);
    transactionItem.insertAdjacentElement('afterend', detailsElement);
    
    // Animate expansion
    setTimeout(() => {
        detailsElement.style.maxHeight = detailsElement.scrollHeight + 'px';
    }, 10);
}

// Create transaction details
function createTransactionDetails(transactionId) {
    const detailsElement = document.createElement('div');
    detailsElement.className = 'transaction-details-expanded';
    detailsElement.style.cssText = `
        max-height: 0;
        overflow: hidden;
        transition: max-height 0.3s ease;
        background: var(--background-light);
        border-bottom: 1px solid var(--border-light);
    `;
    
    detailsElement.innerHTML = `
        <div style="padding: 1.5rem 2rem;">
            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 1rem;">
                <div>
                    <h5 style="font-size: 0.875rem; font-weight: 600; color: var(--text-primary); margin: 0 0 0.5rem 0;">Transaction Details</h5>
                    <p style="font-size: 0.75rem; color: var(--text-secondary); margin: 0;">ID: ${transactionId}</p>
                    <p style="font-size: 0.75rem; color: var(--text-secondary); margin: 0;">Reference: TXN-${transactionId}</p>
                </div>
                <div>
                    <h5 style="font-size: 0.875rem; font-weight: 600; color: var(--text-primary); margin: 0 0 0.5rem 0;">Processing Info</h5>
                    <p style="font-size: 0.75rem; color: var(--text-secondary); margin: 0;">Processed instantly</p>
                    <p style="font-size: 0.75rem; color: var(--text-secondary); margin: 0;">No fees applied</p>
                </div>
                <div>
                    <h5 style="font-size: 0.875rem; font-weight: 600; color: var(--text-primary); margin: 0 0 0.5rem 0;">Actions</h5>
                    <div style="display: flex; gap: 0.5rem; flex-wrap: wrap;">
                        <button onclick="downloadReceipt('${transactionId}')" class="btn btn-sm btn-outline">Download Receipt</button>
                        <button onclick="reportIssue('${transactionId}')" class="btn btn-sm btn-outline">Report Issue</button>
                    </div>
                </div>
            </div>
        </div>
    `;
    
    return detailsElement;
}

// Animate transaction items
function animateTransactionItems() {
    const transactionItems = document.querySelectorAll('.enhanced-transaction-item');
    
    transactionItems.forEach((item, index) => {
        item.style.opacity = '0';
        item.style.transform = 'translateY(20px)';
        
        setTimeout(() => {
            item.style.transition = 'all 0.3s ease';
            item.style.opacity = '1';
            item.style.transform = 'translateY(0)';
        }, index * 50);
    });
}

// Show transaction loading
function showTransactionLoading() {
    const transactionList = document.querySelector('.transaction-list');
    if (transactionList) {
        transactionList.style.opacity = '0.5';
        transactionList.style.pointerEvents = 'none';
        
        // Add loading overlay
        const loadingOverlay = document.createElement('div');
        loadingOverlay.className = 'loading-overlay';
        loadingOverlay.style.cssText = `
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(255, 255, 255, 0.8);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 10;
        `;
        loadingOverlay.innerHTML = '<span class="loading"></span>';
        
        const container = transactionList.closest('.transaction-list-container');
        if (container) {
            container.style.position = 'relative';
            container.appendChild(loadingOverlay);
        }
    }
}

// Show page loading
function showPageLoading() {
    const pageContent = document.querySelector('.main-content');
    if (pageContent) {
        pageContent.style.opacity = '0.7';
        pageContent.style.pointerEvents = 'none';
    }
}

// Export CSV functionality
function exportTransactions() {
    showAlert('Preparing CSV export...', 'info');
    
    // Simulate CSV generation
    setTimeout(() => {
        // In a real application, you would generate and download the CSV
        const csvContent = generateCSVContent();
        downloadCSV(csvContent, 'transactions.csv');
        showAlert('Transactions exported successfully!', 'success');
    }, 2000);
}

// Generate CSV content
function generateCSVContent() {
    const transactions = document.querySelectorAll('.enhanced-transaction-item');
    let csvContent = 'Date,Description,Amount,Status\n';
    
    transactions.forEach(item => {
        const date = item.querySelector('.transaction-time').textContent.trim();
        const description = item.querySelector('.transaction-primary h4').textContent.trim();
        const amount = item.querySelector('.amount').textContent.trim();
        const status = item.querySelector('.status-badge').textContent.trim();
        
        csvContent += `"${date}","${description}","${amount}","${status}"\n`;
    });
    
    return csvContent;
}

// Download CSV
function downloadCSV(content, filename) {
    const blob = new Blob([content], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement('a');
    
    if (link.download !== undefined) {
        const url = URL.createObjectURL(blob);
        link.setAttribute('href', url);
        link.setAttribute('download', filename);
        link.style.visibility = 'hidden';
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
    }
}

// Download receipt
function downloadReceipt(transactionId) {
    showAlert('Preparing receipt download...', 'info');
    
    // Simulate receipt generation
    setTimeout(() => {
        showAlert('Receipt download functionality coming soon!', 'info');
    }, 1000);
}

// Report issue
function reportIssue(transactionId) {
    const message = `Report issue for transaction ${transactionId}`;
    showAlert(message, 'info');
    
    // In a real application, you would open a support form or modal
    setTimeout(() => {
        showAlert('Issue reporting functionality coming soon!', 'info');
    }, 1000);
}

// Utility functions
function showAlert(message, type = 'info') {
    const alertContainer = document.querySelector('.alert-container') || createAlertContainer();
    
    const alert = document.createElement('div');
    alert.className = `alert alert-${type}`;
    alert.textContent = message;
    
    alertContainer.appendChild(alert);
    
    // Auto remove after 5 seconds
    setTimeout(() => {
        alert.remove();
    }, 5000);
}

function createAlertContainer() {
    const container = document.createElement('div');
    container.className = 'alert-container';
    container.style.cssText = `
        position: fixed;
        top: 1rem;
        right: 1rem;
        z-index: 1002;
        max-width: 400px;
    `;
    
    document.body.appendChild(container);
    return container;
}

// Format currency
function formatCurrency(amount, currency = 'USD') {
    return new Intl.NumberFormat('en-US', {
        style: 'currency',
        currency: currency
    }).format(amount);
}

// Export functions for global use
window.TransactionsPage = {
    applyFilters,
    expandTransactionDetails,
    exportTransactions,
    downloadReceipt,
    reportIssue,
    showAlert,
    formatCurrency
};
