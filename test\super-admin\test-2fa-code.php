<?php
/**
 * Test script to generate a valid 2FA code for super admin
 */

session_start();

// Include required files
require_once '../super-admin/includes/auth.php';

echo "<h1>Super Admin 2FA Code Generator</h1>";

try {
    // Get the super admin's 2FA secret
    $username = 'superadmin';
    $settings = getSuperAdmin2FASettings($username);
    
    if (!$settings || !$settings['google_2fa_secret']) {
        echo "❌ No 2FA secret found for super admin. Please set up 2FA first.<br>";
        echo "<a href='setup-2fa.php'>Setup 2FA</a>";
        exit;
    }
    
    echo "✅ 2FA secret found for super admin<br>";
    echo "Secret: " . substr($settings['google_2fa_secret'], 0, 4) . "..." . substr($settings['google_2fa_secret'], -4) . "<br>";
    
    // Generate current valid codes
    $google2fa = getGoogle2FA();
    $secret = $settings['google_2fa_secret'];
    
    // Get current time window
    $currentTime = time();
    $timeWindow = floor($currentTime / 30);
    
    echo "<h2>Valid 2FA Codes (Current Time Window)</h2>";
    
    // Generate codes for current and adjacent time windows
    for ($i = -1; $i <= 1; $i++) {
        $testTime = ($timeWindow + $i) * 30;
        $code = $google2fa->getCurrentOtp($secret, $testTime);
        $status = ($i === 0) ? " (CURRENT)" : ($i === -1 ? " (PREVIOUS)" : " (NEXT)");
        echo "Time window $i: <strong>$code</strong>$status<br>";
    }
    
    echo "<h2>Test Verification</h2>";
    $currentCode = $google2fa->getCurrentOtp($secret);
    echo "Current code: <strong>$currentCode</strong><br>";
    
    // Test verification
    $isValid = $google2fa->verifyKey($secret, $currentCode);
    echo "Verification test: " . ($isValid ? "✅ VALID" : "❌ INVALID") . "<br>";
    
    echo "<h2>Manual Test</h2>";
    echo "<p>Use one of the codes above to test 2FA verification:</p>";
    echo "<a href='verify-2fa.php'>Go to 2FA Verification</a><br>";
    echo "<a href='login.php'>Back to Login</a>";
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "<br>";
    echo "Stack trace:<br><pre>" . $e->getTraceAsString() . "</pre>";
}
?>
