/* ===== CLEAN DASHBOARD STYLES ===== */

/* CSS Variables */
:root {
    --primary-color: #2563eb;
    --primary-hover: #1d4ed8;
    --success-color: #10b981;
    --warning-color: #f59e0b;
    --danger-color: #ef4444;
    --text-primary: #1f2937;
    --text-secondary: #6b7280;
    --text-muted: #9ca3af;
    --border-color: #e5e7eb;
    --background-light: #f8fafc;
    --background-white: #ffffff;
    --shadow-sm: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    --radius-md: 0.5rem;
    --radius-lg: 0.75rem;
}

/* Base Styles */
body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
    background-color: var(--background-light);
    color: var(--text-primary);
    line-height: 1.6;
}

/* Dashboard Layout - Use existing structure */
.main-content {
    padding: 2rem;
    background-color: var(--background-light);
    min-height: 100vh;
}

/* Remove sidebar styles - using existing template */

/* Cards - NO THICK BORDERS */
.card {
    background: var(--background-white);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-sm);
    transition: all 0.2s ease;
}

.card:hover {
    box-shadow: var(--shadow-md);
}

.card-body {
    padding: 1.5rem;
}

/* Stats Cards */
.stats-card {
    background: var(--background-white);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-lg);
    padding: 1.5rem;
    text-align: center;
    transition: all 0.2s ease;
}

.stats-card:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
}

.stats-icon {
    width: 48px;
    height: 48px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1rem;
    font-size: 1.25rem;
}

.stats-icon.success {
    background: rgba(16, 185, 129, 0.1);
    color: var(--success-color);
}

.stats-icon.warning {
    background: rgba(245, 158, 11, 0.1);
    color: var(--warning-color);
}

.stats-icon.primary {
    background: rgba(37, 99, 235, 0.1);
    color: var(--primary-color);
}

.stats-value {
    font-size: 1.875rem;
    font-weight: 700;
    color: var(--text-primary);
}

/* ===== ENHANCED VIRTUAL CARD STYLES ===== */
.virtual-card-container {
    height: 100%;
}

.virtual-card {
    width: 100%;
    height: 200px;
    border-radius: 16px;
    padding: 1.5rem;
    color: white;
    position: relative;
    overflow: hidden;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
    transition: all 0.3s ease;
    cursor: pointer;
}

.virtual-card:hover {
    transform: translateY(-4px);
    box-shadow: 0 12px 40px rgba(0, 0, 0, 0.4);
}

.card-pattern {
    position: absolute;
    top: -50px;
    right: -50px;
    width: 150px;
    height: 150px;
    background: radial-gradient(circle, rgba(255,255,255,0.1) 0%, transparent 70%);
    border-radius: 50%;
}

.card-pattern::before {
    content: '';
    position: absolute;
    bottom: -80px;
    left: -80px;
    width: 120px;
    height: 120px;
    background: radial-gradient(circle, rgba(255,255,255,0.05) 0%, transparent 70%);
    border-radius: 50%;
}

.card-header-section {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 1rem;
    position: relative;
    z-index: 2;
}

.card-brand-logo {
    font-size: 2rem;
    opacity: 0.9;
}

.card-chip {
    width: 32px;
    height: 24px;
    background: linear-gradient(135deg, #ffd700 0%, #ffed4e 100%);
    border-radius: 4px;
    position: relative;
    overflow: hidden;
}

.chip-lines {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 20px;
    height: 16px;
    background: repeating-linear-gradient(
        90deg,
        rgba(0,0,0,0.1) 0px,
        rgba(0,0,0,0.1) 1px,
        transparent 1px,
        transparent 3px
    );
}

.card-number-section {
    margin: 1.5rem 0;
    position: relative;
    z-index: 2;
}

.card-number {
    font-family: 'Courier New', monospace;
    font-size: 1.1rem;
    font-weight: 600;
    letter-spacing: 2px;
    text-align: center;
}

.card-footer-section {
    display: flex;
    justify-content: space-between;
    align-items: flex-end;
    position: relative;
    z-index: 2;
    margin-top: auto;
}

.card-holder, .card-expiry, .card-balance {
    text-align: left;
}

.card-balance {
    text-align: right;
}

.card-footer-section .label {
    font-size: 0.65rem;
    opacity: 0.8;
    font-weight: 500;
    margin-bottom: 0.25rem;
    letter-spacing: 0.5px;
}

.card-footer-section .name,
.card-footer-section .date,
.card-footer-section .amount {
    font-size: 0.85rem;
    font-weight: 600;
    line-height: 1;
}

/* No Card Placeholder */
.no-card-placeholder {
    height: 200px;
    border: 2px dashed var(--border-color);
    border-radius: 16px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: var(--background-white);
}

.placeholder-content {
    text-align: center;
    color: var(--text-muted);
}

.placeholder-content i {
    font-size: 2.5rem;
    margin-bottom: 1rem;
    opacity: 0.5;
}

.placeholder-content h6 {
    font-weight: 600;
    color: var(--text-secondary);
    margin-bottom: 0.5rem;
}

.placeholder-content p {
    font-size: 0.875rem;
    margin-bottom: 1rem;
}

/* ===== ACCOUNT OVERVIEW CARD ===== */
.account-overview-card {
    background: var(--background-white);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-lg);
    padding: 1.5rem;
    height: 100%;
    box-shadow: var(--shadow-sm);
    transition: all 0.2s ease;
}

.account-overview-card:hover {
    box-shadow: var(--shadow-md);
}

.overview-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
}

.overview-header h5 {
    font-size: 1.125rem;
    font-weight: 600;
    color: var(--text-primary);
    margin: 0;
}

.account-type {
    font-size: 0.75rem;
    font-weight: 600;
    color: var(--primary-color);
    background: rgba(37, 99, 235, 0.1);
    padding: 0.25rem 0.75rem;
    border-radius: 12px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.balance-display {
    margin-bottom: 1.5rem;
}

.balance-amount {
    font-size: 2.25rem;
    font-weight: 700;
    color: var(--text-primary);
    line-height: 1;
    margin-bottom: 0.5rem;
}

.balance-details small {
    color: var(--text-muted);
    font-size: 0.875rem;
}

.last-transaction {
    padding-top: 1rem;
    border-top: 1px solid var(--border-color);
}

.transaction-label {
    font-size: 0.75rem;
    font-weight: 600;
    color: var(--text-secondary);
    text-transform: uppercase;
    letter-spacing: 0.5px;
    margin-bottom: 0.5rem;
}

.transaction-info {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 0.25rem;
}

.transaction-info .amount {
    font-size: 1rem;
    font-weight: 600;
    color: var(--danger-color);
}

.transaction-info .date {
    font-size: 0.75rem;
    color: var(--text-muted);
}

.transaction-desc {
    font-size: 0.875rem;
    color: var(--text-secondary);
    margin: 0;
}

/* ===== ENHANCED TRANSACTION ITEMS ===== */
.transaction-item {
    display: flex;
    align-items: center;
    padding: 1rem 0;
    border-bottom: 1px solid var(--border-color);
    transition: all 0.2s ease;
}

.transaction-item:last-child {
    border-bottom: none;
}

.transaction-item:hover {
    background: var(--background-light);
    margin: 0 -1rem;
    padding: 1rem;
    border-radius: var(--radius-md);
}

.transaction-icon {
    width: 48px;
    height: 48px;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 1rem;
    flex-shrink: 0;
}

.transaction-details {
    flex: 1;
    min-width: 0;
}

.transaction-title {
    font-size: 0.875rem;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 0.25rem;
    line-height: 1.3;
}

.transaction-date {
    font-size: 0.75rem;
    color: var(--text-muted);
    margin-bottom: 0.25rem;
}

.transaction-status {
    margin-top: 0.25rem;
}

.transaction-amount {
    text-align: right;
    flex-shrink: 0;
}

.transaction-amount .amount {
    font-size: 1rem;
    font-weight: 700;
    line-height: 1;
    margin-bottom: 0.25rem;
    display: block;
}

.transaction-amount .amount.positive {
    color: var(--success-color);
}

.transaction-amount .amount.negative {
    color: var(--danger-color);
}

.transfer-type {
    text-align: right;
}

.transfer-type small {
    font-size: 0.75rem;
    color: var(--text-muted);
}

/* Badge Styles */
.badge {
    font-size: 0.65rem;
    font-weight: 600;
    padding: 0.25rem 0.5rem;
    border-radius: 6px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.bg-success {
    background-color: rgba(16, 185, 129, 0.1) !important;
    color: var(--success-color) !important;
}

.bg-warning {
    background-color: rgba(245, 158, 11, 0.1) !important;
    color: var(--warning-color) !important;
}

.bg-danger {
    background-color: rgba(239, 68, 68, 0.1) !important;
    color: var(--danger-color) !important;
}

/* Button Group Styles */
.btn-group .btn {
    font-size: 0.875rem;
    padding: 0.5rem 1rem;
    border-radius: var(--radius-md);
    font-weight: 500;
    transition: all 0.2s ease;
}

.btn-outline-primary {
    color: var(--primary-color);
    border-color: var(--primary-color);
    background: transparent;
}

.btn-outline-primary:hover {
    background: var(--primary-color);
    color: white;
}

.btn-outline-secondary {
    color: var(--text-secondary);
    border-color: var(--border-color);
    background: transparent;
}

.btn-outline-secondary:hover {
    background: var(--text-secondary);
    color: white;
}

.btn-primary {
    background: var(--primary-color);
    border-color: var(--primary-color);
    color: white;
}

.btn-primary:hover {
    background: var(--primary-hover);
    border-color: var(--primary-hover);
}

/* ===== WALLET DETAILS PAGE STYLES ===== */
.wallet-overview-card {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-hover) 100%);
    border-radius: var(--radius-lg);
    padding: 2rem;
    color: white;
    box-shadow: 0 8px 32px rgba(37, 99, 235, 0.3);
    position: relative;
    overflow: hidden;
}

.wallet-overview-card::before {
    content: '';
    position: absolute;
    top: -50%;
    right: -50%;
    width: 100%;
    height: 100%;
    background: radial-gradient(circle, rgba(255,255,255,0.1) 0%, transparent 70%);
    pointer-events: none;
}

.wallet-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    position: relative;
    z-index: 2;
}

.wallet-header .account-info h2 {
    font-size: 1.75rem;
    font-weight: 700;
    margin: 0 0 0.5rem 0;
}

.wallet-header .account-details {
    font-size: 0.875rem;
    opacity: 0.9;
    margin: 0;
}

.wallet-header .account-details .status-active {
    color: #10b981;
    font-weight: 600;
}

.wallet-header .account-details .status-suspended {
    color: #ef4444;
    font-weight: 600;
}

.balance-section {
    text-align: right;
}

.balance-label {
    font-size: 0.875rem;
    opacity: 0.8;
    margin-bottom: 0.5rem;
}

.balance-amount {
    font-size: 2.5rem;
    font-weight: 700;
    line-height: 1;
    margin-bottom: 0.25rem;
}

.balance-currency {
    font-size: 0.875rem;
    opacity: 0.8;
}

/* Detail Rows */
.detail-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.75rem 0;
    border-bottom: 1px solid var(--border-color);
}

.detail-row:last-child {
    border-bottom: none;
}

.detail-row .label {
    font-size: 0.875rem;
    color: var(--text-secondary);
    font-weight: 500;
}

.detail-row .value {
    font-size: 0.875rem;
    font-weight: 600;
    color: var(--text-primary);
    text-align: right;
}

.detail-row .value.status-verified {
    color: var(--success-color);
}

.detail-row .value.status-pending {
    color: var(--warning-color);
}

.detail-row .value.status-rejected {
    color: var(--danger-color);
}

/* Card Summary Items */
.card-summary-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem 0;
    border-bottom: 1px solid var(--border-color);
}

.card-summary-item:last-child {
    border-bottom: none;
}

.card-info .card-name {
    font-size: 0.875rem;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 0.25rem;
}

.card-info .card-number {
    font-size: 0.75rem;
    color: var(--text-muted);
    font-family: 'Courier New', monospace;
    margin-bottom: 0.25rem;
}

.card-details {
    display: flex;
    gap: 0.5rem;
}

.card-type {
    font-size: 0.65rem;
    font-weight: 600;
    color: var(--primary-color);
    background: rgba(37, 99, 235, 0.1);
    padding: 0.125rem 0.5rem;
    border-radius: 6px;
    text-transform: uppercase;
}

.card-status {
    font-size: 0.65rem;
    font-weight: 600;
    padding: 0.125rem 0.5rem;
    border-radius: 6px;
    text-transform: uppercase;
}

.card-status.status-active {
    color: var(--success-color);
    background: rgba(16, 185, 129, 0.1);
}

.card-status.status-blocked {
    color: var(--danger-color);
    background: rgba(239, 68, 68, 0.1);
}

.card-balance {
    text-align: right;
}

.card-balance .balance {
    font-size: 1rem;
    font-weight: 700;
    color: var(--text-primary);
    margin-bottom: 0.25rem;
}

.card-balance .limit {
    font-size: 0.75rem;
    color: var(--text-muted);
}

/* Activity List */
.activity-list {
    max-height: 400px;
    overflow-y: auto;
}

.activity-item {
    display: flex;
    align-items: center;
    padding: 0.75rem 0;
    border-bottom: 1px solid var(--border-color);
}

.activity-item:last-child {
    border-bottom: none;
}

.activity-icon {
    width: 32px;
    height: 32px;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 0.75rem;
    flex-shrink: 0;
}

.activity-icon.success {
    background: rgba(16, 185, 129, 0.1);
    color: var(--success-color);
}

.activity-icon.warning {
    background: rgba(245, 158, 11, 0.1);
    color: var(--warning-color);
}

.activity-details {
    flex: 1;
    min-width: 0;
}

.activity-title {
    font-size: 0.875rem;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 0.25rem;
    line-height: 1.3;
}

.activity-date {
    font-size: 0.75rem;
    color: var(--text-muted);
    margin-bottom: 0.25rem;
}

.activity-type {
    margin-top: 0.25rem;
}

.activity-amount {
    text-align: right;
    flex-shrink: 0;
}

.activity-amount .amount {
    font-size: 0.875rem;
    font-weight: 700;
}

.activity-amount .amount.positive {
    color: var(--success-color);
}

.activity-amount .amount.negative {
    color: var(--danger-color);
}

/* Stats Detail */
.stats-detail {
    font-size: 0.75rem;
    color: var(--text-muted);
    margin-top: 0.25rem;
}

/* Responsive Design */
@media (max-width: 768px) {
    .virtual-card {
        height: 180px;
        padding: 1.25rem;
    }

    .card-number {
        font-size: 1rem;
    }

    .balance-amount {
        font-size: 1.875rem;
    }

    .transaction-item {
        flex-direction: column;
        align-items: flex-start;
        gap: 0.75rem;
    }

    .transaction-amount {
        text-align: left;
        width: 100%;
    }

    .btn-group {
        flex-direction: column;
        width: 100%;
    }

    .btn-group .btn {
        margin-bottom: 0.5rem;
    }

    .wallet-header {
        flex-direction: column;
        text-align: center;
        gap: 1.5rem;
    }

    .balance-section {
        text-align: center;
    }

    .card-summary-item {
        flex-direction: column;
        align-items: flex-start;
        gap: 0.75rem;
    }

    .card-balance {
        text-align: left;
        width: 100%;
    }

    .activity-item {
        flex-direction: column;
        align-items: flex-start;
        gap: 0.5rem;
    }

    .activity-amount {
        text-align: left;
        width: 100%;
    }
}
    margin-bottom: 0.25rem;
}

.stats-label {
    font-size: 0.875rem;
    color: var(--text-muted);
    margin-bottom: 0.5rem;
}

.stats-change {
    font-size: 0.75rem;
    font-weight: 600;
}

.stats-change.positive {
    color: var(--success-color);
}

.stats-change.negative {
    color: var(--danger-color);
}

/* Account Balance Card */
.balance-card {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-hover) 100%);
    color: white;
    border: none;
    border-radius: var(--radius-lg);
    padding: 2rem;
    position: relative;
    overflow: hidden;
}

.balance-card::before {
    content: '';
    position: absolute;
    top: -50%;
    right: -50%;
    width: 100%;
    height: 100%;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 50%;
}

.balance-amount {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
}

.balance-label {
    font-size: 1rem;
    opacity: 0.9;
}

/* Quick Actions */
.quick-actions {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
    margin-top: 2rem;
}

.action-btn {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 1rem;
    background: var(--background-white);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-lg);
    text-decoration: none;
    color: var(--text-primary);
    transition: all 0.2s ease;
}

.action-btn:hover {
    background: var(--background-light);
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
    color: var(--primary-color);
    text-decoration: none;
}

.action-icon {
    width: 40px;
    height: 40px;
    border-radius: var(--radius-md);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.125rem;
}

/* Recent Transactions */
.transaction-item {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1rem 0;
    border-bottom: 1px solid var(--border-color);
}

.transaction-item:last-child {
    border-bottom: none;
}

.transaction-icon {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1rem;
}

.transaction-details {
    flex: 1;
}

.transaction-title {
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 0.25rem;
}

.transaction-date {
    font-size: 0.875rem;
    color: var(--text-muted);
}

.transaction-amount {
    font-weight: 600;
    font-size: 1rem;
}

.transaction-amount.credit {
    color: var(--success-color);
}

.transaction-amount.debit {
    color: var(--danger-color);
}

/* Responsive Design */
@media (max-width: 768px) {
    .banking-sidebar {
        transform: translateX(-100%);
        transition: transform 0.3s ease;
    }
    
    .banking-sidebar.show {
        transform: translateX(0);
    }
    
    .main-content {
        margin-left: 0;
        padding: 1rem;
    }
}

/* Utilities */
.text-primary { color: var(--text-primary) !important; }
.text-secondary { color: var(--text-secondary) !important; }
.text-muted { color: var(--text-muted) !important; }
.text-success { color: var(--success-color) !important; }
.text-warning { color: var(--warning-color) !important; }
.text-danger { color: var(--danger-color) !important; }

.bg-light { background-color: var(--background-light) !important; }
.bg-white { background-color: var(--background-white) !important; }

.border-0 { border: none !important; }
.rounded-lg { border-radius: var(--radius-lg) !important; }
.shadow-sm { box-shadow: var(--shadow-sm) !important; }
