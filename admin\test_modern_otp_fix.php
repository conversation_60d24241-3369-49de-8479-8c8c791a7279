<?php
/**
 * Test Script for Modern OTP Modal Fix
 * Tests the new OTP modal implementation and primary color styling
 */

require_once '../config/config.php';
requireAdmin();

echo "<!DOCTYPE html>
<html>
<head>
    <title>Modern OTP Modal - Fix Testing</title>
    <link href='https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css' rel='stylesheet'>
    <link href='https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css' rel='stylesheet'>
    <script src='https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js'></script>
    <script src='https://code.jquery.com/jquery-3.6.0.min.js'></script>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background-color: #f8f9fa; }
        .test-section { margin: 20px 0; padding: 20px; border: 1px solid #ddd; border-radius: 8px; background: white; }
        .success { background-color: #d4edda; border-color: #c3e6cb; color: #155724; }
        .error { background-color: #f8d7da; border-color: #f5c6cb; color: #721c24; }
        .info { background-color: #d1ecf1; border-color: #bee5eb; color: #0c5460; }
        .avatar-demo { 
            background: linear-gradient(135deg, #4f46e5 0%, #7c3aed 100%); 
            color: white; 
            width: 40px; 
            height: 40px; 
            border-radius: 50%; 
            display: inline-flex; 
            align-items: center; 
            justify-content: center; 
            font-weight: 600; 
            font-size: 0.85rem;
            margin-right: 15px;
            border: 2px solid rgba(79, 70, 229, 0.2);
        }
        .btn-primary { 
            background: linear-gradient(135deg, #4f46e5 0%, #7c3aed 100%); 
            border: none; 
            border-radius: 6px; 
            padding: 8px 16px; 
            font-size: 0.85rem; 
            font-weight: 500; 
        }
        .btn-outline-primary { 
            border: 1px solid #4f46e5; 
            color: #4f46e5; 
            border-radius: 6px; 
            padding: 6px 12px; 
            font-size: 0.75rem; 
            font-weight: 500; 
        }
        .btn-outline-primary:hover { 
            background: linear-gradient(135deg, #4f46e5 0%, #7c3aed 100%); 
            border-color: #4f46e5; 
        }
    </style>
</head>
<body>";

echo "<div class='container-fluid'>
<h1>🔧 Modern OTP Modal - Fix Testing</h1>
<p class='text-muted'>Testing the new modern OTP modal implementation with primary color styling</p>";

// Test 1: OTP Modal Demo
echo "<div class='test-section'>
<h2>🔑 Modern OTP Modal Demo</h2>
<p>This demonstrates the new modern OTP modal that replaces the old table-based approach</p>";

try {
    $db = getDB();
    
    // Get sample users
    $users_query = "SELECT id, first_name, last_name, username, email, status 
                    FROM accounts 
                    WHERE is_admin = 0 AND status = 'active'
                    ORDER BY id 
                    LIMIT 6";
    $users_result = $db->query($users_query);
    
    if ($users_result && $users_result->num_rows > 0) {
        echo "<div class='row'>";
        while ($user = $users_result->fetch_assoc()) {
            $initials = strtoupper(substr($user['first_name'], 0, 1) . substr($user['last_name'], 0, 1));
            $fullName = $user['first_name'] . ' ' . $user['last_name'];
            
            echo "<div class='col-md-6 col-lg-4 mb-3'>
                <div class='card h-100'>
                    <div class='card-body'>
                        <div class='d-flex align-items-center mb-3'>
                            <div class='avatar-demo'>$initials</div>
                            <div>
                                <div class='fw-bold'>$fullName</div>
                                <small class='text-muted'>@{$user['username']}</small>
                            </div>
                        </div>
                        <div class='mb-2'>
                            <small class='text-muted'>{$user['email']}</small>
                        </div>
                        <button class='btn btn-primary w-100' onclick='showOTPModal({$user['id']}, \"$fullName\", \"{$user['username']}\")'>
                            <i class='fas fa-key me-2'></i>Generate OTP
                        </button>
                    </div>
                </div>
            </div>";
        }
        echo "</div>";
        
        echo "<div class='alert alert-info mt-3'>
        <strong>✨ New Features:</strong>
        <ul class='mb-0'>
            <li><strong>Modern Modal Design:</strong> Clean, professional popup instead of table updates</li>
            <li><strong>Loading States:</strong> Spinner animation while generating OTP</li>
            <li><strong>Copy to Clipboard:</strong> One-click copy functionality</li>
            <li><strong>Error Handling:</strong> Graceful error messages with retry options</li>
            <li><strong>Email Status:</strong> Clear indication of email delivery status</li>
            <li><strong>Primary Colors:</strong> Consistent branding throughout</li>
        </ul>
        </div>";
        
    } else {
        echo "<div class='alert alert-warning'>No active users available for testing</div>";
    }
} catch (Exception $e) {
    echo "<div class='alert alert-danger'>Database error: " . htmlspecialchars($e->getMessage()) . "</div>";
}

echo "</div>";

// Test 2: Before vs After Comparison
echo "<div class='test-section'>
<h2>📊 Before vs After Comparison</h2>
<div class='row'>
    <div class='col-md-6'>
        <div class='card border-danger'>
            <div class='card-header bg-danger text-white'>
                <h5 class='mb-0'>❌ Before (Broken)</h5>
            </div>
            <div class='card-body'>
                <h6>Issues:</h6>
                <ul>
                    <li><strong>No OTP Column:</strong> Table only had 6 columns, but JavaScript looked for 7th column</li>
                    <li><strong>JavaScript Error:</strong> <code>otpCell.innerHTML</code> failed because element didn't exist</li>
                    <li><strong>Poor UX:</strong> No loading states or error handling</li>
                    <li><strong>Inconsistent Styling:</strong> Basic colors, no primary branding</li>
                </ul>
                <h6>Code Problem:</h6>
                <pre><code>// This failed because td:nth-child(7) didn't exist
const otpCell = row.querySelector('td:nth-child(7)');
otpCell.innerHTML = '...'; // Error!</code></pre>
            </div>
        </div>
    </div>
    <div class='col-md-6'>
        <div class='card border-success'>
            <div class='card-header bg-success text-white'>
                <h5 class='mb-0'>✅ After (Fixed)</h5>
            </div>
            <div class='card-body'>
                <h6>Improvements:</h6>
                <ul>
                    <li><strong>Modern Modal:</strong> Professional popup with clean design</li>
                    <li><strong>Loading States:</strong> Spinner animation during generation</li>
                    <li><strong>Copy Feature:</strong> One-click clipboard copy</li>
                    <li><strong>Error Handling:</strong> Graceful error messages with retry</li>
                    <li><strong>Primary Colors:</strong> Consistent branding throughout</li>
                    <li><strong>Email Status:</strong> Clear delivery confirmation</li>
                </ul>
                <h6>New Approach:</h6>
                <pre><code>// Modern modal approach
function showOTPModal(userId, fullName, username) {
    // Show modal with loading state
    // Generate OTP via AJAX
    // Display in beautiful modal
}</code></pre>
            </div>
        </div>
    </div>
</div>
</div>";

// Test 3: Styling Improvements
echo "<div class='test-section'>
<h2>🎨 Primary Color Styling Improvements</h2>
<div class='row'>
    <div class='col-md-4'>
        <div class='card'>
            <div class='card-header'>
                <h6 class='mb-0'>Avatar Styling</h6>
            </div>
            <div class='card-body text-center'>
                <div class='avatar-demo mx-auto mb-2'>JS</div>
                <p class='small mb-0'>Primary gradient background</p>
                <p class='small text-muted'>Linear gradient: #4f46e5 → #7c3aed</p>
            </div>
        </div>
    </div>
    <div class='col-md-4'>
        <div class='card'>
            <div class='card-header'>
                <h6 class='mb-0'>Button Styling</h6>
            </div>
            <div class='card-body text-center'>
                <button class='btn btn-primary btn-sm mb-2'>Primary Button</button><br>
                <button class='btn btn-outline-primary btn-sm'>Outline Button</button>
                <p class='small text-muted mt-2'>Consistent primary colors</p>
            </div>
        </div>
    </div>
    <div class='col-md-4'>
        <div class='card'>
            <div class='card-header'>
                <h6 class='mb-0'>Modal Design</h6>
            </div>
            <div class='card-body text-center'>
                <div class='bg-light p-3 rounded mb-2' style='border: 2px dashed #4f46e5;'>
                    <div class='h5 text-primary font-monospace mb-1'>123456</div>
                    <small class='text-muted'>OTP Display</small>
                </div>
                <p class='small text-muted'>Modern card design</p>
            </div>
        </div>
    </div>
</div>
</div>";

// Test 4: Navigation Links
echo "<div class='test-section'>
<h2>🔗 Test Navigation</h2>
<div class='row'>
    <div class='col-md-4'>
        <div class='card'>
            <div class='card-body text-center'>
                <i class='fas fa-users fa-2x text-primary mb-3'></i>
                <h5>Active Users Page</h5>
                <p class='text-muted'>Test the actual fixed page</p>
                <a href='users.php' class='btn btn-primary'>Open Users Page</a>
            </div>
        </div>
    </div>
    <div class='col-md-4'>
        <div class='card'>
            <div class='card-body text-center'>
                <i class='fas fa-eye fa-2x text-primary mb-3'></i>
                <h5>View User (Working)</h5>
                <p class='text-muted'>Compare with working version</p>
                <a href='view-user.php?id=1' class='btn btn-success'>View User</a>
            </div>
        </div>
    </div>
    <div class='col-md-4'>
        <div class='card'>
            <div class='card-body text-center'>
                <i class='fas fa-cog fa-2x text-primary mb-3'></i>
                <h5>All Tests</h5>
                <p class='text-muted'>Comprehensive test suite</p>
                <a href='test_all_fixes.php' class='btn btn-outline-primary'>All Tests</a>
            </div>
        </div>
    </div>
</div>
</div>";

echo "</div>"; // Close container

// Include the modern OTP modal (copied from users.php)
echo "
<!-- Modern OTP Modal -->
<div id='otpModal' style='display: none; position: fixed; top: 0; left: 0; width: 100%; height: 100%; background: rgba(0,0,0,0.5); z-index: 1050; align-items: center; justify-content: center;'>
    <div class='modal-dialog modal-dialog-centered' style='max-width: 400px;'>
        <div class='modal-content' style='border: none; border-radius: 12px; box-shadow: 0 10px 40px rgba(0,0,0,0.2);'>
            <div class='modal-header' style='border-bottom: 1px solid #e9ecef; border-radius: 12px 12px 0 0; background: linear-gradient(135deg, #4f46e5 0%, #7c3aed 100%); color: white;'>
                <h5 class='modal-title'>
                    <i class='fas fa-key me-2'></i>Generate OTP
                </h5>
                <button type='button' class='btn-close btn-close-white' onclick='hideOTPModal()' aria-label='Close'></button>
            </div>
            <div class='modal-body p-0'>
                <div class='p-3 border-bottom bg-light'>
                    <div id='otpUserInfo'>
                        <!-- User info will be populated here -->
                    </div>
                </div>
                <div id='otpContent'>
                    <!-- OTP content will be populated here -->
                </div>
            </div>
        </div>
    </div>
</div>";

// Add the JavaScript functions (copied from users.php)
echo "<script>
// Modern OTP Modal Functions
function showOTPModal(userId, fullName, username) {
    const modal = document.getElementById('otpModal');
    const userInfo = document.getElementById('otpUserInfo');
    const otpContent = document.getElementById('otpContent');
    
    // Set user info
    userInfo.innerHTML = \`
        <div class=\"d-flex align-items-center\">
            <div class=\"avatar avatar-sm me-3\" style=\"background: linear-gradient(135deg, #4f46e5 0%, #7c3aed 100%); color: white; font-weight: 600;\">
                \${fullName.split(' ').map(n => n[0]).join('').toUpperCase()}
            </div>
            <div>
                <div class=\"fw-bold\">\${fullName}</div>
                <small class=\"text-muted\">@\${username}</small>
            </div>
        </div>
    \`;
    
    // Show loading state
    otpContent.innerHTML = \`
        <div class=\"text-center py-4\">
            <div class=\"spinner-border text-primary mb-3\" role=\"status\">
                <span class=\"visually-hidden\">Generating OTP...</span>
            </div>
            <p class=\"text-muted mb-0\">Generating secure OTP code...</p>
        </div>
    \`;
    
    // Show modal
    modal.style.display = 'flex';
    document.body.style.overflow = 'hidden';
    
    // Generate OTP
    fetch('ajax/generate-otp.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: \`user_id=\${userId}\`
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            const currentTime = new Date().toLocaleTimeString('en-US', { 
                hour12: false, 
                hour: '2-digit', 
                minute: '2-digit' 
            });
            
            otpContent.innerHTML = \`
                <div class=\"text-center py-4\">
                    <div class=\"mb-4\">
                        <div class=\"bg-light rounded-3 p-4 mb-3\" style=\"border: 2px dashed #4f46e5;\">
                            <div class=\"h2 text-primary font-monospace mb-2\">\${data.otp_code}</div>
                            <small class=\"text-muted\">
                                <i class=\"fas fa-clock me-1\"></i>
                                Generated at \${currentTime} • Expires in \${data.expires_in}
                            </small>
                        </div>
                        <div class=\"alert alert-\${data.email_sent ? 'success' : 'warning'} mb-0\">
                            <i class=\"fas fa-\${data.email_sent ? 'check-circle' : 'exclamation-triangle'} me-2\"></i>
                            \${data.email_sent ? 
                                \`OTP sent successfully to \${data.user_email}\` : 
                                'OTP generated but email delivery failed'
                            }
                        </div>
                    </div>
                    <div class=\"d-flex gap-2 justify-content-center\">
                        <button class=\"btn btn-outline-primary btn-sm\" onclick=\"copyOTPToClipboard('\${data.otp_code}')\">
                            <i class=\"fas fa-copy me-1\"></i>Copy Code
                        </button>
                        <button class=\"btn btn-outline-secondary btn-sm\" onclick=\"showOTPModal(\${userId}, '\${fullName}', '\${username}')\">
                            <i class=\"fas fa-redo me-1\"></i>Generate New
                        </button>
                    </div>
                </div>
            \`;
            
            // Show success notification
            showQuickNotification('success', \`OTP generated: \${data.otp_code}\`);
            
        } else {
            otpContent.innerHTML = \`
                <div class=\"text-center py-4\">
                    <div class=\"text-danger mb-3\">
                        <i class=\"fas fa-exclamation-triangle fa-2x\"></i>
                    </div>
                    <h5 class=\"text-danger mb-2\">Generation Failed</h5>
                    <p class=\"text-muted mb-3\">\${data.message}</p>
                    <button class=\"btn btn-primary btn-sm\" onclick=\"showOTPModal(\${userId}, '\${fullName}', '\${username}')\">
                        <i class=\"fas fa-redo me-1\"></i>Try Again
                    </button>
                </div>
            \`;
            
            showQuickNotification('error', data.message);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        otpContent.innerHTML = \`
            <div class=\"text-center py-4\">
                <div class=\"text-danger mb-3\">
                    <i class=\"fas fa-wifi fa-2x\"></i>
                </div>
                <h5 class=\"text-danger mb-2\">Connection Error</h5>
                <p class=\"text-muted mb-3\">Failed to connect to server. Please check your connection.</p>
                <button class=\"btn btn-primary btn-sm\" onclick=\"showOTPModal(\${userId}, '\${fullName}', '\${username}')\">
                    <i class=\"fas fa-redo me-1\"></i>Try Again
                </button>
            </div>
        \`;
        
        showQuickNotification('error', 'Connection error. Please try again.');
    });
}

function hideOTPModal() {
    const modal = document.getElementById('otpModal');
    modal.style.display = 'none';
    document.body.style.overflow = 'auto';
}

function copyOTPToClipboard(otpCode) {
    navigator.clipboard.writeText(otpCode).then(() => {
        showQuickNotification('success', 'OTP code copied to clipboard!');
    }).catch(() => {
        // Fallback for older browsers
        const textArea = document.createElement('textarea');
        textArea.value = otpCode;
        document.body.appendChild(textArea);
        textArea.select();
        document.execCommand('copy');
        document.body.removeChild(textArea);
        showQuickNotification('success', 'OTP code copied to clipboard!');
    });
}

function showQuickNotification(type, message) {
    // Simple notification function
    const notification = document.createElement('div');
    notification.className = \`alert alert-\${type === 'success' ? 'success' : 'danger'} position-fixed\`;
    notification.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
    notification.innerHTML = \`
        <i class=\"fas fa-\${type === 'success' ? 'check-circle' : 'exclamation-triangle'} me-2\"></i>
        \${message}
    \`;
    
    document.body.appendChild(notification);
    
    setTimeout(() => {
        notification.remove();
    }, 3000);
}

// Close modal on escape key
document.addEventListener('keydown', function(event) {
    if (event.key === 'Escape' && document.getElementById('otpModal').style.display === 'flex') {
        hideOTPModal();
    }
});
</script>";

echo "</body></html>";
?>
