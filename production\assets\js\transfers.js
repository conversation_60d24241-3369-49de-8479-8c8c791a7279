// Transfers Page JavaScript

document.addEventListener('DOMContentLoaded', function() {
    // Initialize transfers page functionality
    initializeTransfersPage();
    
    // Initialize form validation
    initializeFormValidation();
    
    // Initialize beneficiary selection
    initializeBeneficiarySelection();
    
    // Initialize amount formatting
    initializeAmountFormatting();
});

// Transfers page initialization
function initializeTransfersPage() {
    console.log('Transfers page initialized');
    
    // Add hover effects to recent transfers
    const transferItems = document.querySelectorAll('.transfer-item');
    transferItems.forEach(item => {
        item.addEventListener('click', function() {
            const beneficiaryName = this.querySelector('.transfer-name').textContent;
            const beneficiaryAccount = this.querySelector('.transfer-account').textContent;
            
            // Pre-fill form with selected beneficiary
            fillBeneficiaryDetails(beneficiaryName, beneficiaryAccount);
        });
    });
    
    // Initialize transfer form submission
    const transferForm = document.getElementById('transferForm');
    if (transferForm) {
        transferForm.addEventListener('submit', handleTransferSubmission);
    }
}

// Form validation
function initializeFormValidation() {
    const formControls = document.querySelectorAll('.form-control');
    
    formControls.forEach(control => {
        control.addEventListener('blur', function() {
            validateField(this);
        });
        
        control.addEventListener('input', function() {
            clearFieldError(this);
        });
    });
}

// Beneficiary selection
function initializeBeneficiarySelection() {
    const beneficiaryOptions = document.querySelectorAll('.beneficiary-option');
    
    beneficiaryOptions.forEach(option => {
        option.addEventListener('click', function() {
            // Remove selected class from all options
            beneficiaryOptions.forEach(opt => opt.classList.remove('selected'));
            
            // Add selected class to clicked option
            this.classList.add('selected');
            
            // Fill form with beneficiary details
            const name = this.querySelector('.beneficiary-name').textContent;
            const account = this.querySelector('.beneficiary-account').textContent;
            fillBeneficiaryDetails(name, account);
        });
    });
}

// Amount formatting
function initializeAmountFormatting() {
    const amountInput = document.getElementById('amount');
    if (amountInput) {
        amountInput.addEventListener('input', function() {
            formatAmountInput(this);
            updateTransferSummary();
        });
    }
}

// Fill beneficiary details
function fillBeneficiaryDetails(name, account) {
    const recipientNameField = document.getElementById('recipient_name');
    const recipientAccountField = document.getElementById('recipient_account');
    
    if (recipientNameField) recipientNameField.value = name;
    if (recipientAccountField) recipientAccountField.value = account;
    
    // Validate filled fields
    if (recipientNameField) validateField(recipientNameField);
    if (recipientAccountField) validateField(recipientAccountField);
}

// Format amount input
function formatAmountInput(input) {
    let value = input.value.replace(/[^\d.]/g, '');
    
    // Ensure only one decimal point
    const parts = value.split('.');
    if (parts.length > 2) {
        value = parts[0] + '.' + parts.slice(1).join('');
    }
    
    // Limit decimal places to 2
    if (parts[1] && parts[1].length > 2) {
        value = parts[0] + '.' + parts[1].substring(0, 2);
    }
    
    input.value = value;
}

// Validate field
function validateField(field) {
    const value = field.value.trim();
    const fieldName = field.name || field.id;
    let isValid = true;
    let errorMessage = '';
    
    // Required field validation
    if (field.hasAttribute('required') && !value) {
        isValid = false;
        errorMessage = 'This field is required';
    }
    
    // Specific field validations
    switch (fieldName) {
        case 'amount':
            const amount = parseFloat(value);
            if (value && (isNaN(amount) || amount <= 0)) {
                isValid = false;
                errorMessage = 'Please enter a valid amount';
            } else if (amount > 10000) {
                isValid = false;
                errorMessage = 'Amount cannot exceed $10,000';
            }
            break;
            
        case 'recipient_account':
            if (value && !/^\d{10,16}$/.test(value.replace(/\s/g, ''))) {
                isValid = false;
                errorMessage = 'Please enter a valid account number';
            }
            break;
            
        case 'recipient_name':
            if (value && value.length < 2) {
                isValid = false;
                errorMessage = 'Name must be at least 2 characters';
            }
            break;
    }
    
    // Update field appearance
    if (isValid) {
        field.classList.remove('is-invalid');
        field.classList.add('is-valid');
        clearFieldError(field);
    } else {
        field.classList.remove('is-valid');
        field.classList.add('is-invalid');
        showFieldError(field, errorMessage);
    }
    
    return isValid;
}

// Show field error
function showFieldError(field, message) {
    clearFieldError(field);
    
    const errorElement = document.createElement('div');
    errorElement.className = 'form-text text-danger';
    errorElement.textContent = message;
    errorElement.setAttribute('data-error-for', field.id || field.name);
    
    field.parentNode.appendChild(errorElement);
}

// Clear field error
function clearFieldError(field) {
    const existingError = field.parentNode.querySelector(`[data-error-for="${field.id || field.name}"]`);
    if (existingError) {
        existingError.remove();
    }
}

// Update transfer summary
function updateTransferSummary() {
    const amountInput = document.getElementById('amount');
    const summaryAmount = document.querySelector('.summary-amount');
    const summaryFee = document.querySelector('.summary-fee');
    const summaryTotal = document.querySelector('.summary-total');
    
    if (!amountInput || !summaryAmount) return;
    
    const amount = parseFloat(amountInput.value) || 0;
    const fee = amount > 1000 ? 5.00 : 0.00; // Example fee calculation
    const total = amount + fee;
    
    if (summaryAmount) summaryAmount.textContent = `$${amount.toFixed(2)}`;
    if (summaryFee) summaryFee.textContent = `$${fee.toFixed(2)}`;
    if (summaryTotal) summaryTotal.textContent = `$${total.toFixed(2)}`;
}

// Handle transfer submission
function handleTransferSubmission(event) {
    event.preventDefault();
    
    const form = event.target;
    const formData = new FormData(form);
    const submitButton = form.querySelector('.btn-transfer');
    
    // Validate all fields
    const fields = form.querySelectorAll('.form-control[required]');
    let isFormValid = true;
    
    fields.forEach(field => {
        if (!validateField(field)) {
            isFormValid = false;
        }
    });
    
    if (!isFormValid) {
        showAlert('Please correct the errors in the form', 'danger');
        return;
    }
    
    // Show loading state
    const originalText = submitButton.innerHTML;
    submitButton.innerHTML = '<span class="loading"></span> Processing...';
    submitButton.disabled = true;
    
    // Simulate API call
    setTimeout(() => {
        // In a real application, you would send the data to your server
        fetch('process_transfer.php', {
            method: 'POST',
            body: formData
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showAlert('Transfer completed successfully!', 'success');
                form.reset();
                updateTransferSummary();
                
                // Redirect to confirmation page
                setTimeout(() => {
                    window.location.href = 'transfer_confirmation.php?id=' + data.transfer_id;
                }, 2000);
            } else {
                showAlert('Transfer failed: ' + data.message, 'danger');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showAlert('An error occurred while processing the transfer', 'danger');
        })
        .finally(() => {
            submitButton.innerHTML = originalText;
            submitButton.disabled = false;
        });
    }, 2000);
}

// Utility functions
function showAlert(message, type = 'info') {
    const alertContainer = document.querySelector('.alert-container') || createAlertContainer();
    
    const alert = document.createElement('div');
    alert.className = `alert alert-${type}`;
    alert.textContent = message;
    
    alertContainer.appendChild(alert);
    
    // Auto remove after 5 seconds
    setTimeout(() => {
        alert.remove();
    }, 5000);
}

function createAlertContainer() {
    const container = document.createElement('div');
    container.className = 'alert-container';
    container.style.cssText = `
        position: fixed;
        top: 1rem;
        right: 1rem;
        z-index: 1002;
        max-width: 400px;
    `;
    
    document.body.appendChild(container);
    return container;
}

// Quick transfer functions
function quickTransfer(amount) {
    const amountInput = document.getElementById('amount');
    if (amountInput) {
        amountInput.value = amount.toString();
        formatAmountInput(amountInput);
        updateTransferSummary();
        validateField(amountInput);
    }
}

function clearForm() {
    const form = document.getElementById('transferForm');
    if (form) {
        form.reset();
        
        // Clear validation states
        const formControls = form.querySelectorAll('.form-control');
        formControls.forEach(control => {
            control.classList.remove('is-valid', 'is-invalid');
            clearFieldError(control);
        });
        
        // Clear beneficiary selection
        const beneficiaryOptions = document.querySelectorAll('.beneficiary-option');
        beneficiaryOptions.forEach(option => option.classList.remove('selected'));
        
        updateTransferSummary();
    }
}

// Export functions for global use
window.TransfersPage = {
    fillBeneficiaryDetails,
    validateField,
    updateTransferSummary,
    quickTransfer,
    clearForm,
    showAlert
};
