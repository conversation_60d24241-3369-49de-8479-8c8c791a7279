<?php
// Set page variables
$page_title = 'Dashboard';
$additional_css = ['dashboard-clean.css'];

// Include header template
require_once '../templates/user/header.php';

// Include database connection and check authentication
require_once '../config/config.php';
requireLogin();

// Get user data
try {
    $db = getDB();
    $user_id = $_SESSION['user_id'];

    // Get user account information
    $user_sql = "SELECT a.*,
                        COALESCE(a.balance, 0) as balance,
                        DATE_FORMAT(a.created_at, '%M %Y') as member_since,
                        DATEDIFF(NOW(), a.created_at) as days_member
                 FROM accounts a
                 WHERE a.id = ?";
    $user_result = $db->query($user_sql, [$user_id]);
    $user = $user_result->fetch_assoc();

    // Get current balance
    $current_balance = $user['balance'] ?? 0;

    // Get virtual cards
    $cards_result = $db->query("SELECT * FROM virtual_cards WHERE account_id = ? ORDER BY created_at DESC LIMIT 1", [$user_id]);
    $primary_card = $cards_result ? $cards_result->fetch_assoc() : null;

    // Get recent transactions
    $transactions_result = $db->query("SELECT * FROM account_transactions WHERE account_id = ? ORDER BY created_at DESC LIMIT 5", [$user_id]);
    $recent_transactions = [];
    if ($transactions_result) {
        while ($transaction = $transactions_result->fetch_assoc()) {
            $recent_transactions[] = $transaction;
        }
    }

    // Get monthly stats
    $current_month = date('Y-m');
    $monthly_stats_result = $db->query("SELECT 
                                          SUM(CASE WHEN transaction_type IN ('credit', 'deposit') THEN amount ELSE 0 END) as total_credits,
                                          SUM(CASE WHEN transaction_type IN ('debit', 'withdrawal') THEN amount ELSE 0 END) as total_debits,
                                          COUNT(*) as transaction_count
                                        FROM account_transactions 
                                        WHERE account_id = ? 
                                        AND DATE_FORMAT(created_at, '%Y-%m') = ?", [$user_id, $current_month]);
    $monthly_stats = $monthly_stats_result ? $monthly_stats_result->fetch_assoc() : [];

    $total_credits = $monthly_stats['total_credits'] ?? 0;
    $total_debits = $monthly_stats['total_debits'] ?? 0;
    $transaction_count = $monthly_stats['transaction_count'] ?? 0;

} catch (Exception $e) {
    error_log("Dashboard error: " . $e->getMessage());
    // Fallback data
    $user = ['first_name' => 'User', 'last_name' => '', 'email' => '', 'account_number' => '', 'account_type' => 'savings', 'status' => 'active', 'member_since' => date('F Y'), 'days_member' => 0];
    $current_balance = 0;
    $total_credits = $total_debits = $transaction_count = 0;
    $recent_transactions = [];
    $primary_card = null;
}

function formatCurrency($amount) {
    return '$' . number_format($amount, 2);
}
?>

<!-- Custom CSS for redesigned dashboard -->
<style>
.dashboard-redesign {
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
    min-height: 100vh;
    padding: 2rem 0;
}

.overview-card {
    background: linear-gradient(135deg, var(--primary-color, #206bc4) 0%, #1a5490 100%);
    color: white;
    border-radius: 20px;
    padding: 2.5rem;
    margin-bottom: 2rem;
    box-shadow: 0 10px 30px rgba(32, 107, 196, 0.3);
}

.balance-display {
    font-size: 3.5rem;
    font-weight: 800;
    margin-bottom: 1rem;
}

.info-cards {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
    margin-bottom: 2rem;
}

.info-card {
    background: white;
    border-radius: 16px;
    padding: 2rem;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    border-top: 4px solid var(--primary-color, #206bc4);
    transition: transform 0.3s ease;
}

.info-card:hover {
    transform: translateY(-4px);
}

.card-title {
    font-size: 1.25rem;
    font-weight: 700;
    color: #1f2937;
    margin-bottom: 1rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.card-icon {
    width: 40px;
    height: 40px;
    background: var(--primary-color, #206bc4);
    border-radius: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
}

.transaction-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem 0;
    border-bottom: 1px solid #f3f4f6;
}

.transaction-item:last-child {
    border-bottom: none;
}

.stat-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
}

.stat-item {
    text-align: center;
    padding: 1.5rem;
    background: #f8fafc;
    border-radius: 12px;
}

.stat-value {
    font-size: 2rem;
    font-weight: 800;
    margin-bottom: 0.5rem;
}

.stat-value.credit { color: #10b981; }
.stat-value.debit { color: #ef4444; }

@media (max-width: 768px) {
    .info-cards {
        grid-template-columns: 1fr;
    }
    .balance-display {
        font-size: 2.5rem;
    }
}
</style>

<div class="dashboard-redesign">
    <div class="container-fluid">
        <!-- Welcome Header -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="d-flex justify-content-between align-items-center bg-white p-4 rounded shadow-sm">
                    <div>
                        <h1 class="h2 mb-1">Welcome back, <?php echo htmlspecialchars($user['first_name']); ?>!</h1>
                        <p class="text-muted mb-0">Here's your comprehensive banking overview</p>
                    </div>
                    <div class="d-flex gap-2">
                        <a href="transfers/" class="btn btn-primary">
                            <i class="fas fa-paper-plane me-2"></i>Send Money
                        </a>
                        <a href="cards/" class="btn btn-success">
                            <i class="fas fa-plus me-2"></i>New Card
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <!-- Account Overview -->
        <div class="row">
            <div class="col-12">
                <div class="overview-card">
                    <div class="balance-display"><?php echo formatCurrency($current_balance); ?></div>
                    <div class="row">
                        <div class="col-md-6">
                            <h3><?php echo htmlspecialchars($user['first_name'] . ' ' . $user['last_name']); ?></h3>
                            <p class="mb-1">Account: <?php echo htmlspecialchars($user['account_number']); ?></p>
                            <span class="badge bg-light text-dark"><?php echo ucfirst($user['status']); ?></span>
                        </div>
                        <div class="col-md-6 text-md-end">
                            <h4><?php echo ucfirst($user['account_type']); ?> Account</h4>
                            <p>Member since <?php echo htmlspecialchars($user['member_since']); ?></p>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Information Cards -->
        <div class="info-cards">
            <!-- Balance Details -->
            <div class="info-card">
                <div class="card-title">
                    <div class="card-icon"><i class="fas fa-wallet"></i></div>
                    Account Balance
                </div>
                <div class="h3 text-primary mb-3"><?php echo formatCurrency($current_balance); ?></div>
                <div class="text-success">
                    <i class="fas fa-arrow-up"></i>
                    Available for transactions
                </div>
            </div>

            <!-- Recent Transactions -->
            <div class="info-card">
                <div class="card-title">
                    <div class="card-icon"><i class="fas fa-history"></i></div>
                    Recent Transactions
                </div>
                <?php if (empty($recent_transactions)): ?>
                    <p class="text-muted">No transactions found.</p>
                <?php else: ?>
                    <?php foreach (array_slice($recent_transactions, 0, 3) as $transaction): ?>
                        <div class="transaction-item">
                            <div>
                                <div class="fw-bold"><?php echo ucfirst($transaction['transaction_type']); ?></div>
                                <small class="text-muted"><?php echo htmlspecialchars($transaction['description']); ?></small>
                            </div>
                            <div class="fw-bold <?php echo $transaction['transaction_type'] === 'credit' ? 'text-success' : 'text-danger'; ?>">
                                <?php echo ($transaction['transaction_type'] === 'credit' ? '+' : '-') . formatCurrency($transaction['amount']); ?>
                            </div>
                        </div>
                    <?php endforeach; ?>
                <?php endif; ?>
                <a href="transactions/" class="btn btn-outline-primary btn-sm mt-3">View All</a>
            </div>

            <!-- Virtual Card -->
            <div class="info-card">
                <div class="card-title">
                    <div class="card-icon"><i class="fas fa-credit-card"></i></div>
                    Virtual Card
                </div>
                <?php if ($primary_card): ?>
                    <div class="card bg-primary text-white p-3 mb-3" style="border-radius: 12px;">
                        <div style="font-family: monospace; font-size: 1.1rem; letter-spacing: 2px;">
                            **** **** **** <?php echo substr($primary_card['card_number'], -4); ?>
                        </div>
                        <div class="d-flex justify-content-between mt-2">
                            <small><?php echo htmlspecialchars($primary_card['card_holder_name']); ?></small>
                            <small><?php echo date('m/y', strtotime($primary_card['expiry_date'])); ?></small>
                        </div>
                    </div>
                    <div class="text-success">
                        <i class="fas fa-check-circle"></i>
                        Balance: <?php echo formatCurrency($primary_card['card_balance']); ?>
                    </div>
                <?php else: ?>
                    <div class="text-center py-3">
                        <i class="fas fa-credit-card fa-3x text-muted mb-3"></i>
                        <p class="text-muted">No virtual cards found.</p>
                        <a href="cards/" class="btn btn-primary btn-sm">Apply for Card</a>
                    </div>
                <?php endif; ?>
            </div>

            <!-- Monthly Statistics -->
            <div class="info-card">
                <div class="card-title">
                    <div class="card-icon"><i class="fas fa-chart-bar"></i></div>
                    This Month's Activity
                </div>
                <div class="stat-grid">
                    <div class="stat-item">
                        <div class="stat-value credit"><?php echo formatCurrency($total_credits); ?></div>
                        <div class="text-muted">Credits</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-value debit"><?php echo formatCurrency($total_debits); ?></div>
                        <div class="text-muted">Debits</div>
                    </div>
                </div>
                <div class="mt-3 text-center">
                    <span class="badge bg-info"><?php echo $transaction_count; ?> transactions this month</span>
                </div>
            </div>

            <!-- Profile Summary -->
            <div class="info-card">
                <div class="card-title">
                    <div class="card-icon"><i class="fas fa-user"></i></div>
                    Profile Summary
                </div>
                <div class="d-flex align-items-center mb-3">
                    <div class="bg-primary text-white rounded-circle d-flex align-items-center justify-content-center me-3" style="width: 50px; height: 50px; font-weight: 700;">
                        <?php echo strtoupper(substr($user['first_name'], 0, 1) . substr($user['last_name'], 0, 1)); ?>
                    </div>
                    <div>
                        <div class="fw-bold"><?php echo htmlspecialchars($user['first_name'] . ' ' . $user['last_name']); ?></div>
                        <small class="text-muted"><?php echo htmlspecialchars($user['email']); ?></small>
                    </div>
                </div>
                <div class="row text-center">
                    <div class="col-6">
                        <div class="fw-bold text-primary"><?php echo $user['days_member']; ?></div>
                        <small class="text-muted">Days Member</small>
                    </div>
                    <div class="col-6">
                        <div class="fw-bold text-primary"><?php echo $primary_card ? 1 : 0; ?></div>
                        <small class="text-muted">Active Cards</small>
                    </div>
                </div>
                <a href="../profile/" class="btn btn-outline-primary btn-sm mt-3 w-100">Edit Profile</a>
            </div>
        </div>
    </div>
</div>

<?php require_once '../templates/user/footer.php'; ?>
