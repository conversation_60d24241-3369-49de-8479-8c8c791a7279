// Dashboard specific JavaScript

document.addEventListener('DOMContentLoaded', function() {
    // Initialize dashboard features
    initializeDashboardFeatures();
    
    // Initialize real-time updates
    initializeRealTimeUpdates();
    
    // Initialize dashboard animations
    initializeDashboardAnimations();
});

// Dashboard features initialization
function initializeDashboardFeatures() {
    // Add click handlers for quick actions
    const quickActionBtns = document.querySelectorAll('.quick-actions .btn');
    quickActionBtns.forEach(btn => {
        btn.addEventListener('click', function(e) {
            // Add loading state
            const originalText = this.innerHTML;
            this.innerHTML = '<span class="loading"></span> Loading...';
            this.style.pointerEvents = 'none';
            
            // Restore after navigation (fallback)
            setTimeout(() => {
                this.innerHTML = originalText;
                this.style.pointerEvents = 'auto';
            }, 2000);
        });
    });
    
    // Add hover effects to stat cards
    const statCards = document.querySelectorAll('.stat-card');
    statCards.forEach(card => {
        card.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-8px) scale(1.02)';
        });
        
        card.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(-4px) scale(1)';
        });
    });
    
    // Initialize balance refresh
    const balanceElement = document.querySelector('.balance-info h1');
    if (balanceElement) {
        balanceElement.addEventListener('click', refreshBalance);
    }
}

// Real-time updates
function initializeRealTimeUpdates() {
    // Refresh dashboard data every 30 seconds
    setInterval(refreshDashboardData, 30000);
    
    // Update timestamps
    setInterval(updateTimestamps, 60000);
}

// Dashboard animations
function initializeDashboardAnimations() {
    // Animate numbers on load
    animateNumbers();
    
    // Add stagger animation to cards
    const cards = document.querySelectorAll('.card');
    cards.forEach((card, index) => {
        card.style.animationDelay = `${index * 0.1}s`;
    });
}

// Refresh balance
function refreshBalance() {
    const balanceElement = document.querySelector('.balance-info h1');
    const originalText = balanceElement.textContent;
    
    balanceElement.innerHTML = '<span class="loading"></span>';
    
    // Simulate API call
    fetch('/online_banking/api/get_balance.php')
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                balanceElement.textContent = `USD ${parseFloat(data.balance).toLocaleString('en-US', {minimumFractionDigits: 2})}`;
                
                // Show success feedback
                showBalanceUpdateFeedback('Balance updated successfully', 'success');
            } else {
                balanceElement.textContent = originalText;
                showBalanceUpdateFeedback('Failed to update balance', 'error');
            }
        })
        .catch(error => {
            balanceElement.textContent = originalText;
            showBalanceUpdateFeedback('Connection error', 'error');
        });
}

// Show balance update feedback
function showBalanceUpdateFeedback(message, type) {
    const feedback = document.createElement('div');
    feedback.className = `alert alert-${type === 'success' ? 'success' : 'danger'}`;
    feedback.textContent = message;
    feedback.style.cssText = `
        position: fixed;
        top: 1rem;
        right: 1rem;
        z-index: 1000;
        min-width: 250px;
        animation: slideInRight 0.3s ease;
    `;
    
    document.body.appendChild(feedback);
    
    setTimeout(() => {
        feedback.style.animation = 'slideOutRight 0.3s ease';
        setTimeout(() => feedback.remove(), 300);
    }, 3000);
}

// Refresh dashboard data
function refreshDashboardData() {
    const cards = document.querySelectorAll('.card');
    
    fetch('/online_banking/api/dashboard_data.php')
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                updateDashboardStats(data.stats);
                updateRecentTransactions(data.transactions);
            }
        })
        .catch(error => {
            console.log('Dashboard refresh failed:', error);
        });
}

// Update dashboard statistics
function updateDashboardStats(stats) {
    const statCards = document.querySelectorAll('.stat-card');
    
    if (stats.money_in !== undefined) {
        const moneyInCard = statCards[0];
        const moneyInValue = moneyInCard.querySelector('h3');
        if (moneyInValue) {
            animateNumberChange(moneyInValue, `USD ${parseFloat(stats.money_in).toLocaleString('en-US', {minimumFractionDigits: 2})}`);
        }
    }
    
    if (stats.money_out !== undefined) {
        const moneyOutCard = statCards[1];
        const moneyOutValue = moneyOutCard.querySelector('h3');
        if (moneyOutValue) {
            animateNumberChange(moneyOutValue, `USD ${parseFloat(stats.money_out).toLocaleString('en-US', {minimumFractionDigits: 2})}`);
        }
    }
    
    if (stats.balance !== undefined) {
        const balanceCard = statCards[2];
        const balanceValue = balanceCard.querySelector('h3');
        if (balanceValue) {
            animateNumberChange(balanceValue, `USD ${parseFloat(stats.balance).toLocaleString('en-US', {minimumFractionDigits: 2})}`);
        }
    }
    
    if (stats.total_transactions !== undefined) {
        const transactionsCard = statCards[3];
        const transactionsValue = transactionsCard.querySelector('h3');
        if (transactionsValue) {
            animateNumberChange(transactionsValue, stats.total_transactions);
        }
    }
}

// Animate number changes
function animateNumberChange(element, newValue) {
    element.style.transform = 'scale(1.1)';
    element.style.color = 'var(--accent-color)';
    
    setTimeout(() => {
        element.textContent = newValue;
        element.style.transform = 'scale(1)';
        element.style.color = '';
    }, 150);
}

// Animate numbers on load
function animateNumbers() {
    const numberElements = document.querySelectorAll('.stat-card h3');
    
    numberElements.forEach((element, index) => {
        const finalValue = element.textContent;
        element.textContent = '0';
        
        setTimeout(() => {
            animateNumberChange(element, finalValue);
        }, index * 200);
    });
}

// Update timestamps
function updateTimestamps() {
    const timeElements = document.querySelectorAll('[data-timestamp]');
    
    timeElements.forEach(element => {
        const timestamp = element.getAttribute('data-timestamp');
        const date = new Date(timestamp);
        const now = new Date();
        const diff = now - date;
        
        let timeAgo;
        if (diff < 60000) {
            timeAgo = 'Just now';
        } else if (diff < 3600000) {
            timeAgo = `${Math.floor(diff / 60000)} minutes ago`;
        } else if (diff < 86400000) {
            timeAgo = `${Math.floor(diff / 3600000)} hours ago`;
        } else {
            timeAgo = date.toLocaleDateString();
        }
        
        element.textContent = timeAgo;
    });
}

// Add CSS animations
const style = document.createElement('style');
style.textContent = `
    @keyframes slideInRight {
        from {
            transform: translateX(100%);
            opacity: 0;
        }
        to {
            transform: translateX(0);
            opacity: 1;
        }
    }
    
    @keyframes slideOutRight {
        from {
            transform: translateX(0);
            opacity: 1;
        }
        to {
            transform: translateX(100%);
            opacity: 0;
        }
    }
    
    .stat-card {
        transition: transform 0.3s ease, box-shadow 0.3s ease;
    }
    
    .balance-info h1 {
        cursor: pointer;
        transition: all 0.2s ease;
    }
    
    .balance-info h1:hover {
        color: var(--accent-color);
        transform: scale(1.05);
    }
`;
document.head.appendChild(style);

// Export functions for external use
window.DashboardFeatures = {
    refreshBalance,
    refreshDashboardData,
    updateDashboardStats,
    animateNumberChange
};
