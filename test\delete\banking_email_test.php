<?php
/**
 * Banking System Email Test
 * Tests the actual email functionality used by the banking system
 */

// Include the banking system files
require_once 'config/config.php';
require_once 'config/database.php';
require_once 'config/email.php';

// Start session for banking system compatibility
session_start();

echo "<!DOCTYPE html>";
echo "<html><head><title>Banking Email Test</title>";
echo "<style>body{font-family:Arial,sans-serif;margin:20px;background:#f5f5f5;}</style>";
echo "</head><body>";

echo "<h1>🏦 Banking System Email Test</h1>";
echo "<p><strong>Testing actual banking system email functionality</strong></p>";
echo "<hr>";

$test_email = '<EMAIL>';
$test_name = 'Demo User';

// Test 1: Email validation
echo "<h2>Test 1: Email Validation</h2>";
if (function_exists('isValidEmail')) {
    $is_valid = isValidEmail($test_email);
    echo $is_valid ? "✅ Email validation: PASSED<br>" : "❌ Email validation: FAILED<br>";
} else {
    echo "❌ isValidEmail function not found<br>";
}

// Test 2: OTP Generation
echo "<h2>Test 2: OTP Generation</h2>";
if (function_exists('generateOTP')) {
    $test_otp = generateOTP();
    echo "✅ OTP generated: <strong>$test_otp</strong><br>";
} else {
    echo "❌ generateOTP function not found<br>";
    $test_otp = '123456'; // fallback
}

// Test 3: Send OTP Email
echo "<h2>Test 3: Banking System OTP Email</h2>";
if (function_exists('sendOTPEmail')) {
    try {
        $otp_result = sendOTPEmail($test_email, $test_otp, $test_name);
        if ($otp_result) {
            echo "✅ OTP email sent successfully<br>";
            echo "📧 Email sent to: $test_email<br>";
            echo "🔢 OTP code: $test_otp<br>";
        } else {
            echo "❌ OTP email failed to send<br>";
        }
    } catch (Exception $e) {
        echo "❌ OTP email error: " . $e->getMessage() . "<br>";
    }
} else {
    echo "❌ sendOTPEmail function not found<br>";
}

// Test 4: Send Welcome Email
echo "<h2>Test 4: Banking System Welcome Email</h2>";
if (function_exists('sendWelcomeEmail')) {
    $user_data = [
        'first_name' => 'Demo',
        'last_name' => 'User',
        'username' => 'demo_user',
        'email' => $test_email,
        'account_number' => '**********',
        'account_type' => 'savings',
        'currency' => 'USD',
        'balance' => 1000.00,
        'status' => 'active'
    ];
    
    try {
        $welcome_result = sendWelcomeEmail($test_email, $user_data);
        if ($welcome_result) {
            echo "✅ Welcome email sent successfully<br>";
            echo "📧 Email sent to: $test_email<br>";
        } else {
            echo "❌ Welcome email failed to send<br>";
        }
    } catch (Exception $e) {
        echo "❌ Welcome email error: " . $e->getMessage() . "<br>";
    }
} else {
    echo "❌ sendWelcomeEmail function not found<br>";
}

// Test 5: Direct sendEmail function
echo "<h2>Test 5: Direct sendEmail Function</h2>";
if (function_exists('sendEmail')) {
    $subject = "🏦 Banking System Test - " . date('H:i:s');
    $message = "
    <div style='font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px;'>
        <h2 style='color: #2c3e50;'>Banking System Email Test</h2>
        <div style='background: #e8f5e8; padding: 20px; border-radius: 8px; border-left: 4px solid #28a745;'>
            <h3 style='color: #155724; margin: 0 0 10px 0;'>✅ Test Successful!</h3>
            <p style='color: #155724; margin: 0;'>
                This email was sent directly using the banking system's sendEmail() function.
            </p>
        </div>
        <div style='margin: 20px 0;'>
            <h4>Test Details:</h4>
            <ul>
                <li><strong>Recipient:</strong> $test_email</li>
                <li><strong>Test Time:</strong> " . date('Y-m-d H:i:s') . "</li>
                <li><strong>Function:</strong> sendEmail()</li>
                <li><strong>OTP Code:</strong> $test_otp</li>
            </ul>
        </div>
        <p style='color: #6c757d; font-size: 14px; margin-top: 30px;'>
            This is an automated test from the Online Banking System.
        </p>
    </div>";
    
    try {
        $direct_result = sendEmail($test_email, $subject, $message, true);
        if ($direct_result) {
            echo "✅ Direct email sent successfully<br>";
            echo "📧 Email sent to: $test_email<br>";
            echo "📝 Subject: $subject<br>";
        } else {
            echo "❌ Direct email failed to send<br>";
        }
    } catch (Exception $e) {
        echo "❌ Direct email error: " . $e->getMessage() . "<br>";
    }
} else {
    echo "❌ sendEmail function not found<br>";
}

// Test 6: Check email logs
echo "<h2>Test 6: Email Logs</h2>";
$log_files = [
    'logs/email_simulation.log',
    'logs/email_welcome.log',
    'logs/simple_email_test.log'
];

foreach ($log_files as $log_file) {
    if (file_exists($log_file)) {
        $log_size = filesize($log_file);
        echo "📄 $log_file: EXISTS (" . number_format($log_size) . " bytes)<br>";
        
        if ($log_size > 0) {
            $log_content = file_get_contents($log_file);
            $log_lines = explode("\n", $log_content);
            $recent_lines = array_slice($log_lines, -3);
            echo "<pre style='background: #f8f9fa; padding: 10px; border-radius: 5px; font-size: 11px; margin: 5px 0;'>";
            echo "Recent entries:\n" . implode("\n", $recent_lines);
            echo "</pre>";
        }
    } else {
        echo "📄 $log_file: NOT FOUND<br>";
    }
}

// Test 7: Check email configuration
echo "<h2>Test 7: Email Configuration</h2>";
if (defined('SMTP_HOST')) {
    echo "📧 SMTP Host: " . SMTP_HOST . "<br>";
    echo "📧 SMTP Port: " . SMTP_PORT . "<br>";
    echo "📧 SMTP Username: " . SMTP_USERNAME . "<br>";
    echo "📧 From Email: " . FROM_EMAIL . "<br>";
    echo "📧 From Name: " . FROM_NAME . "<br>";
} else {
    echo "❌ Email configuration constants not defined<br>";
}

// Summary
echo "<hr>";
echo "<h2>📊 Test Summary</h2>";
echo "<div style='background: #e7f3ff; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
echo "<h3>🎯 What to Check:</h3>";
echo "<ol>";
echo "<li>Check email inbox at <strong><EMAIL></strong></li>";
echo "<li>Look for multiple test emails from this session</li>";
echo "<li>Check spam/junk folder if emails are not in inbox</li>";
echo "<li>Verify OTP code in emails: <strong>$test_otp</strong></li>";
echo "<li>Check that HTML formatting is working correctly</li>";
echo "</ol>";
echo "</div>";

echo "<div style='background: #fff3cd; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
echo "<h3>📝 Expected Emails:</h3>";
echo "<ul>";
echo "<li>🔢 <strong>OTP Email:</strong> Login verification code</li>";
echo "<li>👋 <strong>Welcome Email:</strong> Account creation notification</li>";
echo "<li>🧪 <strong>Test Email:</strong> Direct function test</li>";
echo "</ul>";
echo "</div>";

echo "<p><strong>Test completed at:</strong> " . date('Y-m-d H:i:s') . "</p>";
echo "</body></html>";
?>
