<?php
/**
 * Test Script for User Status History Fix
 * Tests the get_user_details.php fix for account_status_history table issue
 */

require_once '../config/config.php';
requireAdmin();

echo "<!DOCTYPE html>
<html>
<head>
    <title>User Status History Fix - Testing</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .success { background-color: #d4edda; border-color: #c3e6cb; color: #155724; }
        .error { background-color: #f8d7da; border-color: #f5c6cb; color: #721c24; }
        .info { background-color: #d1ecf1; border-color: #bee5eb; color: #0c5460; }
        .test-button { padding: 8px 16px; margin: 5px; border: none; border-radius: 4px; cursor: pointer; background-color: #007bff; color: white; }
        pre { background: #f8f9fa; padding: 10px; border-radius: 4px; overflow-x: auto; }
        table { border-collapse: collapse; width: 100%; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        th { background-color: #f8f9fa; }
    </style>
</head>
<body>";

echo "<h1>🔧 User Status History Fix Testing</h1>";

// Test 1: Check table existence
echo "<div class='test-section info'>
<h2>📁 Table Existence Check</h2>";

try {
    $db = getDB();
    
    // Check if user_status_history table exists
    $table_check = $db->query("SHOW TABLES LIKE 'user_status_history'");
    if ($table_check->num_rows > 0) {
        echo "<p>✅ <strong>user_status_history</strong> table exists</p>";
    } else {
        echo "<p>❌ <strong>user_status_history</strong> table missing</p>";
    }
    
    // Check if the old wrong table name exists
    $wrong_table_check = $db->query("SHOW TABLES LIKE 'account_status_history'");
    if ($wrong_table_check->num_rows > 0) {
        echo "<p>⚠️ <strong>account_status_history</strong> table exists (unexpected)</p>";
    } else {
        echo "<p>✅ <strong>account_status_history</strong> table doesn't exist (correct)</p>";
    }
    
} catch (Exception $e) {
    echo "<p class='error'>❌ Database error: " . htmlspecialchars($e->getMessage()) . "</p>";
}

echo "</div>";

// Test 2: Check table structure
echo "<div class='test-section info'>
<h2>🏗️ Table Structure Check</h2>";

try {
    $structure = $db->query("DESCRIBE user_status_history");
    if ($structure) {
        echo "<table>
        <tr><th>Field</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th><th>Extra</th></tr>";
        
        while ($row = $structure->fetch_assoc()) {
            echo "<tr>
                <td>{$row['Field']}</td>
                <td>{$row['Type']}</td>
                <td>{$row['Null']}</td>
                <td>{$row['Key']}</td>
                <td>{$row['Default']}</td>
                <td>{$row['Extra']}</td>
            </tr>";
        }
        echo "</table>";
        echo "<p>✅ Table structure retrieved successfully</p>";
    }
} catch (Exception $e) {
    echo "<p class='error'>❌ Error getting table structure: " . htmlspecialchars($e->getMessage()) . "</p>";
}

echo "</div>";

// Test 3: Get sample users for testing
echo "<div class='test-section info'>
<h2>👥 Available Test Users</h2>";

try {
    $users_query = "SELECT id, username, email, first_name, last_name, status 
                    FROM accounts 
                    WHERE is_admin = 0 
                    ORDER BY id 
                    LIMIT 10";
    $users_result = $db->query($users_query);
    
    if ($users_result->num_rows > 0) {
        echo "<table>
        <tr><th>ID</th><th>Username</th><th>Email</th><th>Name</th><th>Status</th><th>Actions</th></tr>";
        
        while ($user = $users_result->fetch_assoc()) {
            echo "<tr>
                <td>{$user['id']}</td>
                <td>{$user['username']}</td>
                <td>{$user['email']}</td>
                <td>{$user['first_name']} {$user['last_name']}</td>
                <td>{$user['status']}</td>
                <td>
                    <button class='test-button' onclick='testUserDetails({$user['id']})'>Test Details</button>
                </td>
            </tr>";
        }
        echo "</table>";
    } else {
        echo "<p class='error'>❌ No test users available</p>";
    }
} catch (Exception $e) {
    echo "<p class='error'>❌ Database error: " . htmlspecialchars($e->getMessage()) . "</p>";
}

echo "</div>";

// Test 4: Test the fixed query directly
echo "<div class='test-section'>
<h2>🔍 Direct Query Test</h2>";

try {
    // Test the fixed query with a sample user
    $sample_user_query = "SELECT id FROM accounts WHERE is_admin = 0 LIMIT 1";
    $sample_user_result = $db->query($sample_user_query);
    
    if ($sample_user_result && $sample_user_result->num_rows > 0) {
        $sample_user = $sample_user_result->fetch_assoc();
        $user_id = $sample_user['id'];
        
        echo "<p>Testing with user ID: <strong>$user_id</strong></p>";
        
        // Test the fixed query
        $status_history_query = "SELECT ush.*, 
                                a.first_name as changed_by_first_name, 
                                a.last_name as changed_by_last_name
                                FROM user_status_history ush
                                LEFT JOIN accounts a ON ush.changed_by = a.id
                                WHERE ush.account_id = ?
                                ORDER BY ush.changed_at DESC
                                LIMIT 5";
        
        $status_history_result = $db->query($status_history_query, [$user_id]);
        
        if ($status_history_result !== false) {
            echo "<p>✅ <strong>Fixed query executed successfully!</strong></p>";
            echo "<p>Status history records found: <strong>" . $status_history_result->num_rows . "</strong></p>";
            
            if ($status_history_result->num_rows > 0) {
                echo "<h4>Status History Records:</h4>";
                echo "<table>
                <tr><th>ID</th><th>Old Status</th><th>New Status</th><th>Reason</th><th>Changed By</th><th>Changed At</th></tr>";
                
                while ($row = $status_history_result->fetch_assoc()) {
                    $changed_by = $row['changed_by_first_name'] ? 
                        $row['changed_by_first_name'] . ' ' . $row['changed_by_last_name'] : 
                        'Unknown';
                    
                    echo "<tr>
                        <td>{$row['id']}</td>
                        <td>{$row['old_status']}</td>
                        <td>{$row['new_status']}</td>
                        <td>{$row['reason']}</td>
                        <td>$changed_by</td>
                        <td>{$row['changed_at']}</td>
                    </tr>";
                }
                echo "</table>";
            } else {
                echo "<p>ℹ️ No status history records found for this user (this is normal for new users)</p>";
            }
        } else {
            echo "<p class='error'>❌ Query execution failed</p>";
        }
    } else {
        echo "<p class='error'>❌ No sample user found for testing</p>";
    }
} catch (Exception $e) {
    echo "<p class='error'>❌ Query test error: " . htmlspecialchars($e->getMessage()) . "</p>";
}

echo "</div>";

// Test 5: AJAX endpoint test
echo "<div class='test-section'>
<h2>🌐 AJAX Endpoint Test</h2>
<p>This tests the actual get_user_details.php endpoint that was fixed</p>
<div id='ajax-test-results'></div>
</div>";

// JavaScript for testing
echo "<script>
// Test user details AJAX endpoint
function testUserDetails(userId) {
    const resultsDiv = document.getElementById('ajax-test-results');
    resultsDiv.innerHTML = '<p>🔄 Testing AJAX endpoint for user ID: ' + userId + '...</p>';
    
    fetch('ajax/get_user_details.php?user_id=' + userId)
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            resultsDiv.innerHTML = `
                <div class='success'>
                    <h4>✅ AJAX Endpoint Working!</h4>
                    <p><strong>User:</strong> \${data.user.first_name} \${data.user.last_name}</p>
                    <p><strong>Status:</strong> \${data.user.status}</p>
                    <p><strong>Status History Records:</strong> \${data.status_history ? data.status_history.length : 0}</p>
                    <p><strong>Virtual Cards:</strong> \${data.virtual_cards_count}</p>
                    <p><strong>Crypto Wallets:</strong> \${data.crypto_wallets_count}</p>
                    <p><strong>Recent Transactions:</strong> \${data.transactions ? data.transactions.length : 0}</p>
                </div>
            `;
        } else {
            resultsDiv.innerHTML = `
                <div class='error'>
                    <h4>❌ AJAX Endpoint Failed</h4>
                    <p><strong>Error:</strong> \${data.message}</p>
                </div>
            `;
        }
    })
    .catch(error => {
        resultsDiv.innerHTML = `
            <div class='error'>
                <h4>❌ Network Error</h4>
                <p><strong>Error:</strong> \${error.message}</p>
            </div>
        `;
    });
}

// Auto-test with first available user
document.addEventListener('DOMContentLoaded', function() {
    // Find first user ID from the table
    const firstUserButton = document.querySelector('.test-button');
    if (firstUserButton) {
        const userId = firstUserButton.getAttribute('onclick').match(/\\d+/)[0];
        if (userId) {
            setTimeout(() => {
                testUserDetails(userId);
            }, 1000);
        }
    }
});
</script>";

echo "</body></html>";
?>
