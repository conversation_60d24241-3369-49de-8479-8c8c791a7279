<?php
// Test database connection
require_once 'config/config.php';

echo "Testing database connection...\n";

try {
    $db = getDB();
    echo "Database connection successful!\n";
    
    // Test a simple query
    $result = $db->query("SELECT COUNT(*) as count FROM accounts");
    $row = $result->fetch_assoc();
    echo "Accounts table has " . $row['count'] . " records\n";
    
    // Test user query
    $user_query = "SELECT * FROM accounts WHERE id = ?";
    $user_result = $db->query($user_query, [1]);
    $user = $user_result->fetch_assoc();
    
    if ($user) {
        echo "User found: " . $user['first_name'] . " " . $user['last_name'] . "\n";
        echo "User balance: " . $user['balance'] . "\n";
    } else {
        echo "User not found\n";
    }
    
} catch (Exception $e) {
    echo "Database error: " . $e->getMessage() . "\n";
}

echo "Test completed.\n";
?>
