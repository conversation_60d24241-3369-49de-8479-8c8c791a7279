/**
 * Beneficiaries Page JavaScript
 * Handles beneficiaries management functionality
 */

// Initialize page when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    console.log('🏦 Beneficiaries page loaded');
    initializeBeneficiariesPage();
});

/**
 * Initialize beneficiaries page functionality
 */
function initializeBeneficiariesPage() {
    // Initialize Bootstrap components
    initializeBootstrapComponents();
    
    // Setup form validation
    setupFormValidation();
    
    // Setup account number validation
    setupAccountNumberValidation();
    
    console.log('✅ Beneficiaries page initialized');
}

/**
 * Initialize Bootstrap components
 */
function initializeBootstrapComponents() {
    // Check if Bootstrap is available
    if (typeof bootstrap !== 'undefined') {
        // Initialize tooltips
        var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
        var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
            return new bootstrap.Tooltip(tooltipTriggerEl);
        });

        // Initialize dropdowns
        var dropdownElementList = [].slice.call(document.querySelectorAll('.dropdown-toggle'));
        var dropdownList = dropdownElementList.map(function (dropdownToggleEl) {
            return new bootstrap.Dropdown(dropdownToggleEl);
        });
    }

    // Add modal close handlers for fallback
    document.addEventListener('click', function(e) {
        if (e.target.classList.contains('btn-close') || e.target.classList.contains('modal-backdrop')) {
            closeModal();
        }
    });
}

/**
 * Close modal (fallback function)
 */
function closeModal() {
    const modal = document.getElementById('addBeneficiaryModal');
    const backdrop = document.getElementById('modal-backdrop');

    if (modal) {
        modal.style.display = 'none';
        modal.classList.remove('show');
    }

    if (backdrop) {
        backdrop.remove();
    }

    document.body.classList.remove('modal-open');
}

/**
 * Show add beneficiary modal
 */
function showAddBeneficiaryModal() {
    console.log('🔧 showAddBeneficiaryModal called');
    console.log('Bootstrap available:', typeof bootstrap !== 'undefined');

    const modalElement = document.getElementById('addBeneficiaryModal');
    console.log('Modal element found:', !!modalElement);

    if (!modalElement) {
        alert('Modal element not found! Please check if the modal HTML is present.');
        return;
    }

    // Check if Bootstrap is available
    if (typeof bootstrap !== 'undefined') {
        console.log('Using Bootstrap modal');
        try {
            const modal = new bootstrap.Modal(modalElement);
            modal.show();
        } catch (error) {
            console.error('Bootstrap modal error:', error);
            // Fall back to manual modal
            showModalManually(modalElement);
        }
    } else {
        console.log('Bootstrap not available, using fallback');
        showModalManually(modalElement);
    }

    // Focus on first input
    setTimeout(() => {
        const firstInput = document.querySelector('#addBeneficiaryModal input[name="name"]');
        if (firstInput) {
            firstInput.focus();
        }
    }, 300);
}

/**
 * Show modal manually (fallback)
 */
function showModalManually(modalElement) {
    modalElement.style.display = 'block';
    modalElement.classList.add('show');
    document.body.classList.add('modal-open');

    // Create backdrop
    const existingBackdrop = document.getElementById('modal-backdrop');
    if (existingBackdrop) {
        existingBackdrop.remove();
    }

    const backdrop = document.createElement('div');
    backdrop.className = 'modal-backdrop fade show';
    backdrop.id = 'modal-backdrop';
    backdrop.onclick = closeModal;
    document.body.appendChild(backdrop);
}

/**
 * Setup form validation
 */
function setupFormValidation() {
    const form = document.getElementById('addBeneficiaryForm');
    if (!form) return;

    form.addEventListener('submit', function(e) {
        if (!validateBeneficiaryForm()) {
            e.preventDefault();
            e.stopPropagation();
        }
    });

    // Setup country-currency auto-selection
    setupCountryCurrencyMapping();
}

/**
 * Setup country to currency mapping
 */
function setupCountryCurrencyMapping() {
    const countrySelect = document.querySelector('select[name="country"]');
    const currencySelect = document.getElementById('currencySelect');

    if (!countrySelect || !currencySelect) return;

    // Country to currency mapping
    const countryCurrencyMap = {
        'USA': 'USD',
        'UK': 'GBP',
        'Canada': 'CAD',
        'Australia': 'AUD',
        'Germany': 'EUR',
        'France': 'EUR',
        'Italy': 'EUR',
        'Spain': 'EUR',
        'Netherlands': 'EUR',
        'Switzerland': 'CHF',
        'Belgium': 'EUR',
        'Austria': 'EUR',
        'Sweden': 'SEK',
        'Norway': 'NOK',
        'Denmark': 'DKK',
        'Finland': 'EUR',
        'Japan': 'JPY',
        'Singapore': 'SGD',
        'Hong Kong': 'HKD',
        'South Korea': 'KRW',
        'China': 'CNY',
        'India': 'INR',
        'UAE': 'AED',
        'Saudi Arabia': 'SAR',
        'Qatar': 'QAR',
        'Kuwait': 'KWD',
        'Bahrain': 'BHD',
        'South Africa': 'ZAR',
        'Nigeria': 'NGN',
        'Kenya': 'KES',
        'Egypt': 'EGP',
        'Morocco': 'MAD',
        'Brazil': 'BRL',
        'Mexico': 'MXN',
        'Argentina': 'ARS',
        'Chile': 'CLP',
        'Colombia': 'COP',
        'Peru': 'PEN',
        'New Zealand': 'NZD',
        'Ireland': 'EUR',
        'Portugal': 'EUR',
        'Greece': 'EUR',
        'Turkey': 'TRY',
        'Israel': 'ILS',
        'Russia': 'RUB',
        'Poland': 'PLN',
        'Czech Republic': 'CZK',
        'Hungary': 'HUF',
        'Romania': 'RON',
        'Bulgaria': 'BGN',
        'Croatia': 'HRK'
    };

    countrySelect.addEventListener('change', function() {
        const selectedCountry = this.value;
        const suggestedCurrency = countryCurrencyMap[selectedCountry];

        if (suggestedCurrency) {
            // Find and select the currency option
            const currencyOption = currencySelect.querySelector(`option[value="${suggestedCurrency}"]`);
            if (currencyOption) {
                currencySelect.value = suggestedCurrency;

                // Visual feedback
                currencySelect.style.borderColor = '#28a745';
                setTimeout(() => {
                    currencySelect.style.borderColor = '';
                }, 1000);
            }
        }
    });
}

/**
 * Validate beneficiary form
 */
function validateBeneficiaryForm() {
    const form = document.getElementById('addBeneficiaryForm');
    const name = form.querySelector('input[name="name"]').value.trim();
    const accountNumber = form.querySelector('input[name="account_number"]').value.trim();
    const bankName = form.querySelector('input[name="bank_name"]').value.trim();
    
    // Clear previous errors
    clearFormErrors();
    
    let isValid = true;
    
    // Validate name
    if (!name) {
        showFieldError('name', 'Beneficiary name is required');
        isValid = false;
    } else if (name.length < 2) {
        showFieldError('name', 'Name must be at least 2 characters');
        isValid = false;
    }
    
    // Validate account number
    if (!accountNumber) {
        showFieldError('account_number', 'Account number is required');
        isValid = false;
    } else if (!/^[0-9]{8,20}$/.test(accountNumber)) {
        showFieldError('account_number', 'Account number must be 8-20 digits');
        isValid = false;
    }
    
    // Validate bank name
    if (!bankName) {
        showFieldError('bank_name', 'Bank name is required');
        isValid = false;
    } else if (bankName.length < 2) {
        showFieldError('bank_name', 'Bank name must be at least 2 characters');
        isValid = false;
    }
    
    return isValid;
}

/**
 * Setup account number validation with internal user detection
 */
function setupAccountNumberValidation() {
    const accountInput = document.querySelector('input[name="account_number"]');
    if (!accountInput) return;
    
    let validationTimeout;
    
    accountInput.addEventListener('input', function() {
        clearTimeout(validationTimeout);
        const accountNumber = this.value.trim();
        
        // Clear previous validation messages
        clearAccountValidation();
        
        if (accountNumber.length >= 8) {
            validationTimeout = setTimeout(() => {
                validateAccountNumber(accountNumber);
            }, 500);
        }
    });
}

/**
 * Validate account number and check for internal users
 */
function validateAccountNumber(accountNumber) {
    // Show loading state
    showAccountValidationLoading();
    
    // Simulate API call to check account
    fetch('../../api/validate-account.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: `account_number=${encodeURIComponent(accountNumber)}`
    })
    .then(response => response.json())
    .then(data => {
        if (data.exists) {
            if (data.is_internal) {
                showAccountValidationSuccess(`✅ Internal user found: ${data.name}`, 'Inter-bank transfer available');
            } else {
                showAccountValidationInfo('Account format valid', 'External bank transfer');
            }
        } else {
            showAccountValidationInfo('Account format valid', 'External bank transfer');
        }
    })
    .catch(error => {
        console.error('Account validation error:', error);
        showAccountValidationInfo('Account format valid', 'Unable to verify account');
    });
}

/**
 * Show account validation loading state
 */
function showAccountValidationLoading() {
    const input = document.querySelector('input[name="account_number"]');
    const feedback = getOrCreateAccountFeedback();
    
    feedback.className = 'form-text text-info';
    feedback.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>Validating account...';
}

/**
 * Show account validation success
 */
function showAccountValidationSuccess(message, subtitle) {
    const feedback = getOrCreateAccountFeedback();
    feedback.className = 'form-text text-success';
    feedback.innerHTML = `<i class="fas fa-check-circle me-1"></i>${message}<br><small>${subtitle}</small>`;
}

/**
 * Show account validation info
 */
function showAccountValidationInfo(message, subtitle) {
    const feedback = getOrCreateAccountFeedback();
    feedback.className = 'form-text text-muted';
    feedback.innerHTML = `<i class="fas fa-info-circle me-1"></i>${message}<br><small>${subtitle}</small>`;
}

/**
 * Clear account validation
 */
function clearAccountValidation() {
    const feedback = document.getElementById('account-validation-feedback');
    if (feedback) {
        feedback.remove();
    }
}

/**
 * Get or create account feedback element
 */
function getOrCreateAccountFeedback() {
    let feedback = document.getElementById('account-validation-feedback');
    if (!feedback) {
        feedback = document.createElement('div');
        feedback.id = 'account-validation-feedback';
        
        const input = document.querySelector('input[name="account_number"]');
        input.parentNode.appendChild(feedback);
    }
    return feedback;
}

/**
 * Show field error
 */
function showFieldError(fieldName, message) {
    const field = document.querySelector(`input[name="${fieldName}"]`);
    if (!field) return;
    
    field.classList.add('is-invalid');
    
    // Remove existing error
    const existingError = field.parentNode.querySelector('.invalid-feedback');
    if (existingError) {
        existingError.remove();
    }
    
    // Add new error
    const errorDiv = document.createElement('div');
    errorDiv.className = 'invalid-feedback';
    errorDiv.textContent = message;
    field.parentNode.appendChild(errorDiv);
}

/**
 * Clear form errors
 */
function clearFormErrors() {
    const form = document.getElementById('addBeneficiaryForm');
    if (!form) return;
    
    // Remove invalid classes
    form.querySelectorAll('.is-invalid').forEach(field => {
        field.classList.remove('is-invalid');
    });
    
    // Remove error messages
    form.querySelectorAll('.invalid-feedback').forEach(error => {
        error.remove();
    });
}

/**
 * Toggle beneficiary favorite status
 */
function toggleFavorite(beneficiaryId, isFavorite) {
    document.getElementById('toggleFavoriteId').value = beneficiaryId;
    document.getElementById('toggleFavoriteValue').value = isFavorite;
    document.getElementById('toggleFavoriteForm').submit();
}

/**
 * Delete beneficiary with confirmation
 */
function deleteBeneficiary(beneficiaryId, beneficiaryName) {
    if (confirm(`Are you sure you want to delete "${beneficiaryName}" from your beneficiaries?\n\nThis action cannot be undone.`)) {
        document.getElementById('deleteBeneficiaryId').value = beneficiaryId;
        document.getElementById('deleteBeneficiaryForm').submit();
    }
}

/**
 * Show success message
 */
function showSuccessMessage(message) {
    const alertDiv = document.createElement('div');
    alertDiv.className = 'alert alert-success alert-dismissible fade show';
    alertDiv.innerHTML = `
        <i class="fas fa-check-circle me-2"></i>${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    
    const container = document.querySelector('.content-container');
    container.insertBefore(alertDiv, container.firstChild);
    
    // Auto dismiss after 5 seconds
    setTimeout(() => {
        if (alertDiv.parentNode) {
            alertDiv.remove();
        }
    }, 5000);
}

/**
 * Show error message
 */
function showErrorMessage(message) {
    const alertDiv = document.createElement('div');
    alertDiv.className = 'alert alert-danger alert-dismissible fade show';
    alertDiv.innerHTML = `
        <i class="fas fa-exclamation-circle me-2"></i>${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    
    const container = document.querySelector('.content-container');
    container.insertBefore(alertDiv, container.firstChild);
    
    // Auto dismiss after 8 seconds
    setTimeout(() => {
        if (alertDiv.parentNode) {
            alertDiv.remove();
        }
    }, 8000);
}
