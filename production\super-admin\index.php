<?php
/**
 * Super Admin Index - Redirect to Login
 * Automatically redirects to login.php when accessing super-admin directory
 */

// Start session if not already started
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// Check if already logged in as super admin
if (isset($_SESSION['super_admin_logged_in']) && $_SESSION['super_admin_logged_in'] === true) {
    // Check if 2FA is required and verified
    if (isset($_SESSION['super_admin_2fa_pending']) && $_SESSION['super_admin_2fa_pending'] === true) {
        // Redirect to 2FA verification
        header('Location: verify-2fa.php');
        exit;
    } elseif (isset($_SESSION['super_admin_2fa_verified']) && $_SESSION['super_admin_2fa_verified'] === true) {
        // Redirect to dashboard
        header('Location: dashboard.php');
        exit;
    } else {
        // Check if 2FA is enabled for this user
        require_once 'includes/2fa-functions.php';
        $username = $_SESSION['super_admin_username'] ?? '';
        
        if (!empty($username) && isSuperAdmin2FAEnabled($username)) {
            // 2FA is enabled but not verified, redirect to 2FA
            header('Location: verify-2fa.php');
            exit;
        } else {
            // No 2FA required, redirect to dashboard
            header('Location: dashboard.php');
            exit;
        }
    }
} else {
    // Not logged in, redirect to login page
    header('Location: login.php');
    exit;
}
?>
