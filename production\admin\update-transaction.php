<?php
require_once '../config/config.php';
requireAdmin();

header('Content-Type: application/json');

try {
    // Get JSON input
    $input = json_decode(file_get_contents('php://input'), true);
    
    if (!$input) {
        throw new Exception('Invalid JSON input');
    }
    
    $transaction_id = intval($input['transaction_id'] ?? 0);
    $reference_number = trim($input['reference_number'] ?? '');
    $transaction_type = trim($input['transaction_type'] ?? '');
    $amount = floatval($input['amount'] ?? 0);
    $currency = trim($input['currency'] ?? 'USD');
    $status = trim($input['status'] ?? '');
    $category = trim($input['category'] ?? '');
    $description = trim($input['description'] ?? '');
    $transaction_date = trim($input['transaction_date'] ?? '');
    
    // Validate inputs
    if ($transaction_id <= 0) {
        throw new Exception('Invalid transaction ID');
    }
    
    if (empty($reference_number)) {
        throw new Exception('Reference number is required');
    }
    
    $valid_types = ['credit', 'debit', 'transfer_in', 'transfer_out', 'deposit', 'withdrawal'];
    if (!in_array($transaction_type, $valid_types)) {
        throw new Exception('Invalid transaction type');
    }
    
    if ($amount <= 0) {
        throw new Exception('Amount must be greater than 0');
    }
    
    $valid_statuses = ['pending', 'processing', 'completed', 'cancelled', 'failed'];
    if (!in_array($status, $valid_statuses)) {
        throw new Exception('Invalid status');
    }
    
    if (empty($description)) {
        throw new Exception('Description is required');
    }
    
    // Validate date format
    if (!empty($transaction_date) && !DateTime::createFromFormat('Y-m-d', $transaction_date)) {
        throw new Exception('Invalid date format');
    }
    
    $db = getDB();
    
    // Check if transaction exists
    $check_query = "SELECT id, account_id, amount as current_amount, transaction_type as current_type, status as current_status 
                    FROM account_transactions WHERE id = ?";
    $check_result = $db->query($check_query, [$transaction_id]);
    
    if (!$check_result || $check_result->num_rows === 0) {
        throw new Exception('Transaction not found');
    }
    
    $current_transaction = $check_result->fetch_assoc();
    
    // Update transaction
    $update_query = "UPDATE account_transactions SET 
                     reference_number = ?, 
                     transaction_type = ?, 
                     amount = ?, 
                     currency = ?, 
                     status = ?, 
                     category = ?, 
                     description = ?,
                     transaction_date = COALESCE(?, transaction_date),
                     updated_at = NOW(),
                     processed_by = ?
                     WHERE id = ?";
    
    $params = [
        $reference_number,
        $transaction_type,
        $amount,
        $currency,
        $status,
        $category,
        $description,
        !empty($transaction_date) ? $transaction_date : null,
        $_SESSION['user_id'] ?? 1, // Default to admin user ID 1
        $transaction_id
    ];
    
    $result = $db->query($update_query, $params);
    
    if (!$result) {
        throw new Exception('Failed to update transaction');
    }
    
    // If amount or type changed, we might need to update account balance
    if ($current_transaction['current_amount'] != $amount || 
        $current_transaction['current_type'] != $transaction_type ||
        $current_transaction['current_status'] != $status) {
        
        // Recalculate account balance for this account
        $account_id = $current_transaction['account_id'];
        
        // Get all completed transactions for this account
        $balance_query = "SELECT 
                         SUM(CASE 
                             WHEN transaction_type IN ('credit', 'deposit', 'transfer_in') THEN amount 
                             ELSE -amount 
                         END) as balance
                         FROM account_transactions 
                         WHERE account_id = ? AND status = 'completed'";
        
        $balance_result = $db->query($balance_query, [$account_id]);
        $balance_data = $balance_result->fetch_assoc();
        $new_balance = $balance_data['balance'] ?? 0;
        
        // Update account balance
        $update_balance_query = "UPDATE accounts SET balance = ? WHERE id = ?";
        $db->query($update_balance_query, [$new_balance, $account_id]);
    }
    
    // Log the action
    error_log("Transaction updated: ID {$transaction_id}, Admin: " . ($_SESSION['user_id'] ?? 'Unknown'));
    
    echo json_encode([
        'success' => true,
        'message' => 'Transaction updated successfully',
        'transaction_id' => $transaction_id
    ]);
    
} catch (Exception $e) {
    http_response_code(400);
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ]);
}
?>
