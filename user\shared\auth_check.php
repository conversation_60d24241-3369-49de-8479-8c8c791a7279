<?php
/**
 * Enhanced User Session Authentication Check
 * Prevents session switching to admin/super-admin
 */

// Start session if not already started
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// Include config for session timeout constant
require_once __DIR__ . '/../../config/config.php';

/**
 * Strict user session validation
 */
function validateUserSession() {
    // Check basic session data
    if (!isset($_SESSION['user_id']) || !isset($_SESSION['username'])) {
        redirectToLogin('No session data');
        return false;
    }
    
    // Ensure this is NOT an admin session
    if (isset($_SESSION['is_admin']) && $_SESSION['is_admin'] === true) {
        redirectToLogin('Admin session detected in user area');
        return false;
    }
    
    if (isset($_SESSION['is_admin_session']) && $_SESSION['is_admin_session'] === true) {
        redirectToLogin('Admin session flag detected');
        return false;
    }
    
    // Ensure this is NOT a super admin session
    if (isset($_SESSION['super_admin_id'])) {
        redirectToLogin('Super admin session detected in user area');
        return false;
    }
    
    if (isset($_SESSION['super_admin_login_time'])) {
        redirectToLogin('Super admin login time detected in user area');
        return false;
    }
    
    // Check session timeout
    if (isset($_SESSION['last_activity'])) {
        if (time() - $_SESSION['last_activity'] > SESSION_TIMEOUT) {
            redirectToLogin('Session timeout');
            return false;
        }
    }
    
    // Validate user exists in database and is not admin
    try {
        $db = getDB();
        $user_check_sql = "SELECT id, username, is_admin, status FROM accounts WHERE id = ?";
        $user_result = $db->query($user_check_sql, [$_SESSION['user_id']]);
        
        if ($user_result->num_rows === 0) {
            redirectToLogin('User not found in database');
            return false;
        }
        
        $user = $user_result->fetch_assoc();
        
        // Ensure user is not admin
        if ($user['is_admin'] == 1) {
            redirectToLogin('Admin user detected in user area');
            return false;
        }
        
        // Ensure user account is active
        if ($user['status'] !== 'active') {
            redirectToLogin('User account is not active');
            return false;
        }
        
        // Ensure session username matches database
        if ($user['username'] !== $_SESSION['username']) {
            redirectToLogin('Session username mismatch');
            return false;
        }
        
    } catch (Exception $e) {
        error_log("User session validation error: " . $e->getMessage());
        redirectToLogin('Database validation failed');
        return false;
    }
    
    // Update last activity
    $_SESSION['last_activity'] = time();
    
    // Mark as validated user session
    $_SESSION['session_type'] = 'user';
    $_SESSION['validated_at'] = time();
    
    return true;
}

/**
 * Redirect to login with complete session cleanup
 */
function redirectToLogin($reason) {
    // Log the redirect reason
    error_log("User session redirect: {$reason} - User ID: " . ($_SESSION['user_id'] ?? 'unknown') . " - Session ID: " . session_id());
    
    // Store the reason for display
    $redirect_reason = $reason;
    
    // Clear session completely
    $_SESSION = [];
    
    // Delete session cookie
    if (ini_get('session.use_cookies')) {
        $params = session_get_cookie_params();
        setcookie(session_name(), '', time() - 42000,
            $params['path'], $params['domain'],
            $params['secure'], $params['httponly']
        );
    }
    
    // Destroy session
    session_destroy();
    
    // Determine redirect URL based on current location
    $current_dir = dirname($_SERVER['PHP_SELF']);
    $login_url = '../../auth/login.php';
    
    // Adjust path based on depth
    if (strpos($current_dir, '/user/') !== false) {
        $depth = substr_count($current_dir, '/') - substr_count('/user/', '/');
        $login_url = str_repeat('../', $depth) . 'auth/login.php';
    }
    
    // Add reason parameter
    $login_url .= '?reason=' . urlencode($redirect_reason);
    
    // Redirect to login
    header('Location: ' . $login_url);
    exit();
}

/**
 * Get current user data safely
 */
function getCurrentUser() {
    if (!validateUserSession()) {
        return null;
    }
    
    try {
        $db = getDB();
        $user_sql = "SELECT * FROM accounts WHERE id = ?";
        $user_result = $db->query($user_sql, [$_SESSION['user_id']]);
        
        if ($user_result->num_rows > 0) {
            return $user_result->fetch_assoc();
        }
    } catch (Exception $e) {
        error_log("Get current user error: " . $e->getMessage());
    }
    
    return null;
}

/**
 * Check if current session is valid user session
 */
function isValidUserSession() {
    return validateUserSession();
}

/**
 * Extend user session
 */
function extendUserSession() {
    if (validateUserSession()) {
        $_SESSION['last_activity'] = time();
        $_SESSION['extended_at'] = time();
        return true;
    }
    return false;
}

// Validate session immediately when this file is included
if (!validateUserSession()) {
    // Session validation failed, user will be redirected
    exit();
}

// Add session security headers
header('X-Frame-Options: DENY');
header('X-Content-Type-Options: nosniff');
header('X-XSS-Protection: 1; mode=block');
header('Referrer-Policy: strict-origin-when-cross-origin');

// Log successful session validation (for debugging)
if (defined('DEBUG_SESSION') && DEBUG_SESSION) {
    error_log("User session validated successfully - User ID: " . $_SESSION['user_id'] . " - Session ID: " . session_id());
}
?>
