<?php
/**
 * Transfer Receipt Page
 * Generates printable banking receipt for transfers
 */

// Enable error reporting for debugging
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Start session and check authentication
session_start();
if (!isset($_SESSION['user_id'])) {
    header('Location: ../../auth/login.php');
    exit();
}

// Include database connection
require_once '../../config/config.php';

// Get database connection
$db = getDB();
$user_id = $_SESSION['user_id'];

// Get transfer ID from URL
$transfer_id = intval($_GET['id'] ?? 0);

if (!$transfer_id) {
    die('Invalid transfer ID');
}

// Get transfer details
$transfer_query = "SELECT t.*, 
                          u.first_name as sender_first_name, u.last_name as sender_last_name,
                          u.account_number as sender_account, u.email as sender_email,
                          r.first_name as recipient_first_name, r.last_name as recipient_last_name,
                          r.account_number as recipient_account
                   FROM transfers t
                   LEFT JOIN accounts u ON t.user_id = u.id
                   LEFT JOIN accounts r ON t.recipient_id = r.id
                   WHERE t.id = ? AND t.user_id = ?";

$transfer_result = $db->query($transfer_query, [$transfer_id, $user_id]);

if ($transfer_result->num_rows === 0) {
    die('Transfer not found or access denied');
}

$transfer = $transfer_result->fetch_assoc();

// Format data for display
$sender_name = trim($transfer['sender_first_name'] . ' ' . $transfer['sender_last_name']);
$transfer_date = formatDate($transfer['created_at'], 'F j, Y g:i A');
$transfer_type_display = ucwords(str_replace('-', ' ', $transfer['transfer_type']));
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Transfer Receipt - <?php echo htmlspecialchars($transfer['reference_number']); ?></title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <!-- Receipt Styles -->
    <style>
        @media print {
            .no-print { display: none !important; }
            body { margin: 0; }
            .receipt-container { box-shadow: none !important; margin: 0 !important; }
        }
        
        body {
            background: #f8f9fa;
            font-family: 'Courier New', monospace;
        }
        
        .receipt-container {
            max-width: 600px;
            margin: 2rem auto;
            background: white;
            box-shadow: 0 0 20px rgba(0,0,0,0.1);
            border-radius: 8px;
            overflow: hidden;
        }
        
        .receipt-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 2rem;
            text-align: center;
        }
        
        .receipt-header h1 {
            margin: 0;
            font-size: 1.8rem;
            font-weight: bold;
        }
        
        .receipt-header .subtitle {
            margin-top: 0.5rem;
            opacity: 0.9;
        }
        
        .receipt-body {
            padding: 2rem;
        }
        
        .receipt-section {
            margin-bottom: 2rem;
            padding-bottom: 1rem;
            border-bottom: 1px dashed #dee2e6;
        }
        
        .receipt-section:last-child {
            border-bottom: none;
            margin-bottom: 0;
        }
        
        .receipt-row {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 0.75rem;
            font-size: 0.95rem;
        }
        
        .receipt-row:last-child {
            margin-bottom: 0;
        }
        
        .receipt-label {
            font-weight: 600;
            color: #495057;
            flex: 1;
        }
        
        .receipt-value {
            font-weight: 500;
            color: #212529;
            text-align: right;
            flex: 1;
        }
        
        .receipt-total {
            background: #f8f9fa;
            padding: 1rem;
            border-radius: 6px;
            margin-top: 1rem;
        }
        
        .receipt-total .receipt-row {
            font-size: 1.1rem;
            font-weight: bold;
            margin-bottom: 0;
        }
        
        .status-badge {
            display: inline-block;
            padding: 0.25rem 0.75rem;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }
        
        .status-completed {
            background: #d4edda;
            color: #155724;
        }
        
        .status-pending {
            background: #fff3cd;
            color: #856404;
        }
        
        .status-failed {
            background: #f8d7da;
            color: #721c24;
        }
        
        .reference-number {
            font-family: 'Courier New', monospace;
            font-weight: bold;
            font-size: 1.1rem;
            color: #667eea;
        }
        
        .account-number {
            font-family: 'Courier New', monospace;
            font-weight: 600;
        }
        
        .receipt-footer {
            background: #f8f9fa;
            padding: 1.5rem 2rem;
            text-align: center;
            font-size: 0.85rem;
            color: #6c757d;
            border-top: 1px solid #dee2e6;
        }
        
        .print-actions {
            text-align: center;
            margin: 2rem 0;
        }
        
        .btn-print {
            background: #667eea;
            border-color: #667eea;
            padding: 0.75rem 2rem;
            font-weight: 600;
        }
        
        .btn-print:hover {
            background: #5a6fd8;
            border-color: #5a6fd8;
        }
    </style>
</head>
<body>
    <div class="receipt-container">
        <!-- Receipt Header -->
        <div class="receipt-header">
            <h1><i class="fas fa-university me-2"></i>Online Banking</h1>
            <div class="subtitle">Transfer Receipt</div>
        </div>
        
        <!-- Receipt Body -->
        <div class="receipt-body">
            <!-- Transaction Details -->
            <div class="receipt-section">
                <h5 class="mb-3"><i class="fas fa-info-circle me-2"></i>Transaction Details</h5>
                
                <div class="receipt-row">
                    <span class="receipt-label">Reference Number:</span>
                    <span class="receipt-value reference-number"><?php echo htmlspecialchars($transfer['reference_number']); ?></span>
                </div>
                
                <div class="receipt-row">
                    <span class="receipt-label">Transfer Type:</span>
                    <span class="receipt-value"><?php echo htmlspecialchars($transfer_type_display); ?></span>
                </div>
                
                <div class="receipt-row">
                    <span class="receipt-label">Date & Time:</span>
                    <span class="receipt-value"><?php echo $transfer_date; ?></span>
                </div>
                
                <div class="receipt-row">
                    <span class="receipt-label">Status:</span>
                    <span class="receipt-value">
                        <span class="status-badge status-<?php echo $transfer['status']; ?>">
                            <?php echo ucfirst($transfer['status']); ?>
                        </span>
                    </span>
                </div>
            </div>
            
            <!-- Sender Information -->
            <div class="receipt-section">
                <h5 class="mb-3"><i class="fas fa-user me-2"></i>From</h5>
                
                <div class="receipt-row">
                    <span class="receipt-label">Account Holder:</span>
                    <span class="receipt-value"><?php echo htmlspecialchars($sender_name); ?></span>
                </div>
                
                <div class="receipt-row">
                    <span class="receipt-label">Account Number:</span>
                    <span class="receipt-value account-number"><?php echo htmlspecialchars($transfer['sender_account']); ?></span>
                </div>
                
                <div class="receipt-row">
                    <span class="receipt-label">Source:</span>
                    <span class="receipt-value"><?php echo ucwords(str_replace('_', ' ', $transfer['source_account_type'])); ?> Account</span>
                </div>
            </div>
            
            <!-- Recipient Information -->
            <div class="receipt-section">
                <h5 class="mb-3"><i class="fas fa-user-check me-2"></i>To</h5>
                
                <div class="receipt-row">
                    <span class="receipt-label">Beneficiary Name:</span>
                    <span class="receipt-value"><?php echo htmlspecialchars($transfer['beneficiary_name']); ?></span>
                </div>
                
                <div class="receipt-row">
                    <span class="receipt-label">Account Number:</span>
                    <span class="receipt-value account-number"><?php echo htmlspecialchars($transfer['beneficiary_account']); ?></span>
                </div>
                
                <?php if (!empty($transfer['beneficiary_bank'])): ?>
                <div class="receipt-row">
                    <span class="receipt-label">Bank Name:</span>
                    <span class="receipt-value"><?php echo htmlspecialchars($transfer['beneficiary_bank']); ?></span>
                </div>
                <?php endif; ?>
                
                <?php if (!empty($transfer['routing_code'])): ?>
                <div class="receipt-row">
                    <span class="receipt-label">Routing Code:</span>
                    <span class="receipt-value account-number"><?php echo htmlspecialchars($transfer['routing_code']); ?></span>
                </div>
                <?php endif; ?>
            </div>
            
            <!-- Amount Details -->
            <div class="receipt-section">
                <h5 class="mb-3"><i class="fas fa-dollar-sign me-2"></i>Amount Details</h5>
                
                <div class="receipt-row">
                    <span class="receipt-label">Transfer Amount:</span>
                    <span class="receipt-value"><?php echo formatCurrency($transfer['amount'], $transfer['currency']); ?></span>
                </div>
                
                <?php if ($transfer['transfer_fee'] > 0): ?>
                <div class="receipt-row">
                    <span class="receipt-label">Transfer Fee:</span>
                    <span class="receipt-value"><?php echo formatCurrency($transfer['transfer_fee'], $transfer['currency']); ?></span>
                </div>
                <?php endif; ?>
                
                <div class="receipt-total">
                    <div class="receipt-row">
                        <span class="receipt-label">Total Debited:</span>
                        <span class="receipt-value"><?php echo formatCurrency($transfer['total_amount'], $transfer['currency']); ?></span>
                    </div>
                </div>
            </div>
            
            <!-- Additional Information -->
            <?php if (!empty($transfer['narration'])): ?>
            <div class="receipt-section">
                <h5 class="mb-3"><i class="fas fa-comment me-2"></i>Purpose</h5>
                <div class="receipt-row">
                    <span class="receipt-value" style="flex: none; width: 100%;">
                        <?php echo htmlspecialchars($transfer['narration']); ?>
                    </span>
                </div>
            </div>
            <?php endif; ?>
        </div>
        
        <!-- Receipt Footer -->
        <div class="receipt-footer">
            <p class="mb-2">
                <strong>Important:</strong> This is a computer-generated receipt and does not require a signature.
            </p>
            <p class="mb-0">
                For any queries, please contact our customer support or visit your nearest branch.
            </p>
        </div>
    </div>
    
    <!-- Print Actions -->
    <div class="print-actions no-print">
        <button class="btn btn-primary btn-print me-2" onclick="window.print()">
            <i class="fas fa-print me-2"></i>Print Receipt
        </button>
        <button class="btn btn-outline-secondary" onclick="window.close()">
            <i class="fas fa-times me-2"></i>Close
        </button>
    </div>
    
    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        // Auto-focus for printing
        window.addEventListener('load', function() {
            // Auto-print if requested
            const urlParams = new URLSearchParams(window.location.search);
            if (urlParams.get('print') === '1') {
                setTimeout(() => window.print(), 500);
            }
        });
    </script>
</body>
</html>
