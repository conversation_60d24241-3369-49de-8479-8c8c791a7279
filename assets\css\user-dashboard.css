/* ===== USER DASHBOARD STYLES ===== */

/* CSS Variables - Now loaded dynamically from database via dynamic-css.php */
/* Fallback variables for when dynamic CSS fails to load */
:root {
    --primary-color: #206bc4;
    --primary-hover: #1a5490;
    --accent-color: #10b981;
    --accent-hover: #059669;
    --warning-color: #f59e0b;
    --danger-color: #ef4444;
    --text-primary: #1f2937;
    --text-secondary: #6b7280;
    --text-muted: #9ca3af;
    --border-color: #e5e7eb;
    --border-light: #f3f4f6;
    --background-light: #f9fafb;
    --background-white: #ffffff;
    --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
    --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
    --radius-sm: 0.375rem;
    --radius-md: 0.5rem;
    --radius-lg: 0.75rem;
    --radius-xl: 1rem;
}

/* Admin-style Avatar Components */
.admin-avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-weight: 600;
    font-size: 0.875rem;
    flex-shrink: 0;
}

.admin-avatar-sm {
    width: 32px;
    height: 32px;
    font-size: 0.75rem;
}

.admin-avatar-lg {
    width: 48px;
    height: 48px;
    font-size: 1rem;
}

/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    line-height: 1.6;
    color: var(--text-primary);
    background: #ffffff;
    min-height: 100vh;
}

/* Dashboard Wrapper */
.dashboard-wrapper {
    display: flex;
    min-height: 100vh;
}

/* Banking Sidebar Styles */
.banking-sidebar {
    width: 280px;
    background: linear-gradient(180deg, #ffffff 0%, #f8fafc 100%);
    border-right: 1px solid var(--border-color);
    padding: 0;
    position: fixed;
    height: 100vh;
    overflow-y: auto;
    overflow-x: hidden;
    box-shadow: 4px 0 20px rgba(0, 0, 0, 0.08);
    scrollbar-width: none; /* Firefox */
    -ms-overflow-style: none; /* IE and Edge */
    scroll-behavior: smooth; /* Smooth scrolling */
    z-index: 1000;
}

.banking-sidebar::-webkit-scrollbar {
    width: 0px;
    background: transparent;
    display: none; /* Completely hide scrollbar */
}

/* Sidebar Header */
.sidebar-header {
    padding: 1.5rem;
    border-bottom: 1px solid var(--border-light);
    background: white;
}

.bank-logo {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    margin-bottom: 1rem;
}

.logo-icon {
    width: 40px;
    height: 40px;
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-hover) 100%);
    border-radius: var(--radius-lg);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
}

.bank-name {
    font-size: 1.125rem;
    font-weight: 700;
    color: var(--text-primary);
}

.user-info {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 0.75rem;
    background: var(--background-light);
    border-radius: var(--radius-lg);
}

.user-avatar {
    width: 40px;
    height: 40px;
    background: linear-gradient(135deg, var(--accent-color) 0%, var(--accent-hover) 100%);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-weight: 600;
    font-size: 0.875rem;
}

.user-details {
    flex: 1;
}

.user-name {
    font-size: 0.875rem;
    font-weight: 600;
    color: var(--text-primary);
    line-height: 1.2;
}

.account-number {
    font-size: 0.75rem;
    color: var(--text-muted);
    font-family: 'Monaco', 'Menlo', monospace;
}

/* Sidebar Navigation */
.sidebar-nav {
    padding: 1rem 0;
}

.nav-section {
    margin-bottom: 1.5rem;
}

.nav-section-title {
    font-size: 0.75rem;
    font-weight: 600;
    color: var(--text-muted);
    text-transform: uppercase;
    letter-spacing: 0.05em;
    padding: 0 1.5rem;
    margin-bottom: 0.5rem;
}

.nav-list {
    list-style: none;
}

.nav-item {
    margin-bottom: 0.25rem;
}

.nav-link {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 0.75rem 1.5rem;
    color: var(--text-secondary);
    text-decoration: none;
    font-size: 0.875rem;
    font-weight: 500;
    transition: all 0.2s ease;
    border-left: 3px solid transparent;
}

.nav-link:hover {
    background: var(--background-light);
    color: var(--text-primary);
    border-left-color: var(--primary-color);
}

.nav-link.active {
    background: linear-gradient(90deg, rgba(37, 99, 235, 0.1) 0%, rgba(37, 99, 235, 0.05) 100%);
    color: var(--primary-color);
    border-left-color: var(--primary-color);
    font-weight: 600;
}

.nav-icon {
    width: 20px;
    height: 20px;
    flex-shrink: 0;
}

/* Sidebar Footer */
.sidebar-footer {
    margin-top: auto;
    padding: 1rem 1.5rem;
    border-top: 1px solid var(--border-light);
}

.logout-link {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 0.75rem;
    color: var(--danger-color);
    text-decoration: none;
    font-size: 0.875rem;
    font-weight: 500;
    border-radius: var(--radius-lg);
    transition: all 0.2s ease;
}

.logout-link:hover {
    background: rgba(239, 68, 68, 0.1);
    color: var(--danger-color);
}

.logout-icon {
    width: 20px;
    height: 20px;
}

/* Main Content Area */
.main-content {
    margin-left: 280px;
    padding: 2rem;
    background: #ffffff;
    min-height: 100vh;
    overflow-x: hidden;
    scroll-behavior: smooth;
    flex: 1;
}

/* Banking Container */
.banking-container {
    max-width: 1400px;
    margin: 0 auto;
    width: 100%;
}

.banking-dashboard {
    display: flex;
    flex-direction: column;
    gap: 2rem;
}

/* Page Header */
.page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;
    padding: 1.5rem 2rem;
    background: white;
    border-radius: 16px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    border: 1px solid var(--border-color);
}

.page-title h1 {
    font-size: 2rem;
    font-weight: 700;
    color: var(--text-primary);
    margin: 0 0 0.5rem 0;
}

.page-title p {
    font-size: 0.875rem;
    color: var(--text-secondary);
    margin: 0;
}

.page-actions {
    display: flex;
    align-items: center;
    gap: 1rem;
}

/* Responsive Design */
@media (max-width: 1024px) {
    .banking-sidebar {
        transform: translateX(-100%);
        transition: transform 0.3s ease;
    }
    
    .banking-sidebar.open {
        transform: translateX(0);
    }
    
    .main-content {
        margin-left: 0;
    }
}

@media (max-width: 768px) {
    .main-content {
        padding: 1rem;
    }
    
    .page-header {
        flex-direction: column;
        gap: 1rem;
        align-items: flex-start;
    }
    
    .page-actions {
        width: 100%;
        justify-content: flex-start;
    }
}
