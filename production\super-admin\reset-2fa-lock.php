<?php
/**
 * Reset 2FA lock for super admin
 */

session_start();

// Include required files
require_once 'includes/auth.php';

echo "<h1>Reset Super Admin 2FA Lock</h1>";

try {
    require_once '../config/database.php';
    $db = getDB();
    
    $username = 'superadmin';
    
    // Reset failed attempts and unlock
    $sql = "UPDATE super_admin_2fa_settings SET failed_attempts = 0, locked_until = NULL WHERE super_admin_username = ?";
    $result = $db->query($sql, [$username]);
    
    if ($result) {
        echo "✅ Successfully reset 2FA lock for super admin<br>";
        echo "- Failed attempts reset to 0<br>";
        echo "- Account unlocked<br>";
        
        // Log the action
        logSuperAdmin2FAAction($username, 'lock_reset', 'Super admin 2FA lock manually reset');
        
        echo "<br><a href='verify-2fa.php'>Try 2FA Verification Again</a><br>";
        echo "<a href='login.php'>Back to Login</a>";
    } else {
        echo "❌ Failed to reset 2FA lock<br>";
    }
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "<br>";
    echo "Stack trace:<br><pre>" . $e->getTraceAsString() . "</pre>";
}
?>
