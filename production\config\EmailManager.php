<?php
/**
 * Email Manager for Online Banking System
 * Handles both SMTP and local mail sending with comprehensive logging
 */

require_once __DIR__ . '/../vendor/autoload.php';

use <PERSON><PERSON><PERSON>ail<PERSON>\PHPMailer\PHPMailer;
use <PERSON><PERSON><PERSON>ailer\PHPMailer\Exception;

class EmailManager
{
    private $db;
    private $settings;
    private $logger;
    
    public function __construct($database = null)
    {
        $this->db = $database ?: getDB();
        $this->loadSettings();
        $this->logger = new EmailLogger($this->db);
    }
    
    /**
     * Load email settings from database
     */
    private function loadSettings()
    {
        try {
            $query = "SELECT * FROM email_settings WHERE is_active = 1 ORDER BY created_at DESC LIMIT 1";
            $result = $this->db->query($query);
            $this->settings = $result ? $result->fetch_assoc() : null;
            
            if (!$this->settings) {
                // Fallback to default settings
                $this->settings = [
                    'smtp_host' => 'smtp.hostinger.com',
                    'smtp_port' => 587,
                    'smtp_username' => '<EMAIL>',
                    'smtp_password' => base64_encode('Money2025@Demo#'),
                    'smtp_encryption' => 'tls',
                    'sender_name' => 'Online Banking System',
                    'sender_email' => '<EMAIL>',
                    'mail_method' => 'smtp'
                ];
            }
        } catch (Exception $e) {
            error_log("EmailManager: Failed to load settings - " . $e->getMessage());
            $this->settings = null;
        }
    }
    
    /**
     * Send email using configured method
     */
    public function sendEmail($to, $subject, $body, $type = 'notification', $user_id = null, $sent_by = null)
    {
        if (!$this->settings) {
            return ['success' => false, 'error' => 'Email settings not configured'];
        }
        
        try {
            $mail = new PHPMailer(true);
            
            // Configure based on method
            if ($this->settings['mail_method'] === 'smtp') {
                $mail->isSMTP();
                $mail->Host = $this->settings['smtp_host'];
                $mail->SMTPAuth = true;
                $mail->Username = $this->settings['smtp_username'];
                $mail->Password = base64_decode($this->settings['smtp_password']);
                $mail->SMTPSecure = $this->settings['smtp_encryption'];
                $mail->Port = $this->settings['smtp_port'];
            }
            
            // Set sender
            $mail->setFrom($this->settings['sender_email'], $this->settings['sender_name']);
            
            // Set recipient
            if (is_array($to)) {
                $mail->addAddress($to['email'], $to['name'] ?? '');
                $recipient_email = $to['email'];
                $recipient_name = $to['name'] ?? '';
            } else {
                $mail->addAddress($to);
                $recipient_email = $to;
                $recipient_name = '';
            }
            
            // Set content
            $mail->isHTML(true);
            $mail->Subject = $subject;
            $mail->Body = $body;
            
            // Send email
            $result = $mail->send();
            
            // Log success
            $this->logger->logEmail(
                $recipient_email,
                $recipient_name,
                $subject,
                $type,
                $this->settings['mail_method'],
                'sent',
                null,
                $sent_by,
                $user_id
            );
            
            return ['success' => true, 'error' => ''];
            
        } catch (Exception $e) {
            // Log failure
            $this->logger->logEmail(
                $recipient_email ?? $to,
                $recipient_name ?? '',
                $subject,
                $type,
                $this->settings['mail_method'] ?? 'unknown',
                'failed',
                $e->getMessage(),
                $sent_by,
                $user_id
            );
            
            return ['success' => false, 'error' => $e->getMessage()];
        }
    }
    
    /**
     * Send OTP email (compatible with existing system)
     */
    public function sendOTP($email, $otp, $user_name = '', $user_id = null)
    {
        $subject = 'Your Login Verification Code - Online Banking';
        $body = $this->generateOTPEmailTemplate($otp, $user_name);
        
        return $this->sendEmail($email, $subject, $body, 'otp', $user_id);
    }
    
    /**
     * Send welcome email
     */
    public function sendWelcomeEmail($email, $user_name, $user_id = null)
    {
        $subject = 'Welcome to Online Banking System';
        $body = $this->generateWelcomeEmailTemplate($user_name);
        
        return $this->sendEmail($email, $subject, $body, 'welcome', $user_id);
    }

    /**
     * Send pending approval email
     */
    public function sendPendingApprovalEmail($email, $user_data, $user_id = null)
    {
        $subject = 'Your Registration is Pending Approval';
        $body = $this->generatePendingApprovalEmailTemplate($user_data);
        
        return $this->sendEmail($email, $subject, $body, 'pending_approval', $user_id);
    }
    
    /**
     * Generate pending approval email template
     */
    private function generatePendingApprovalEmailTemplate($user_data)
    {
        $first_name = htmlspecialchars($user_data['first_name'] ?? '');
        $last_name = htmlspecialchars($user_data['last_name'] ?? '');
        $username = htmlspecialchars($user_data['username'] ?? '');
        $email = htmlspecialchars($user_data['email'] ?? '');
        
        return '
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px; background-color: #f8f9fa;">
            <div style="background-color: white; padding: 30px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1);">
                <div style="text-align: center; margin-bottom: 30px;">
                    <h1 style="color: #2c3e50; margin: 0;">🏦 Account Registration Received</h1>
                    <p style="color: #7f8c8d; margin: 10px 0 0 0;">Your account is pending approval</p>
                </div>
                
                <div style="margin-bottom: 30px;">
                    <p style="color: #2c3e50; margin: 0 0 20px 0; line-height: 1.6;">Dear ' . $first_name . ' ' . $last_name . ',</p>
                    <p style="color: #2c3e50; margin: 0 0 20px 0; line-height: 1.6;">
                        Thank you for registering with Online Banking System! We\'ve received your account details and they are currently being reviewed by our team.
                    </p>
                </div>
                
                <div style="background-color: #fff3cd; padding: 25px; border-radius: 8px; text-align: center; margin: 30px 0;">
                    <h2 style="color: #856404; margin: 0 0 10px 0;">Account Details</h2>
                    <table style="width: 100%; max-width: 300px; margin: 0 auto; border-collapse: collapse;">
                        <tr>
                            <td style="padding: 8px; text-align: left; color: #856404;">Name:</td>
                            <td style="padding: 8px; text-align: right; font-weight: bold;">' . $first_name . ' ' . $last_name . '</td>
                        </tr>
                        <tr>
                            <td style="padding: 8px; text-align: left; color: #856404;">Username:</td>
                            <td style="padding: 8px; text-align: right; font-weight: bold;">' . $username . '</td>
                        </tr>
                        <tr>
                            <td style="padding: 8px; text-align: left; color: #856404;">Email:</td>
                            <td style="padding: 8px; text-align: right; font-weight: bold;">' . $email . '</td>
                        </tr>
                    </table>
                </div>
                
                <div style="margin-bottom: 30px;">
                    <p style="color: #2c3e50; margin: 0 0 20px 0; line-height: 1.6;">
                        Our security team will review your registration and activate your account shortly. You\'ll receive another email once your account is approved and ready to use.
                    </p>
                    <p style="color: #2c3e50; margin: 0 0 20px 0; line-height: 1.6;">
                        This process usually takes 1-2 business days. Thank you for your patience.
                    </p>
                </div>
                
                <div style="text-align: center; margin-top: 30px; padding-top: 20px; border-top: 1px solid #dee2e6;">
                    <p style="color: #7f8c8d; margin: 0; font-size: 14px;">
                        This email was sent automatically by the Online Banking System.<br>
                        If you have any questions, please contact our customer support.
                    </p>
                </div>
            </div>
        </div>';
    }
    
    /**
     * Send status change notification
     */
    public function sendStatusChangeEmail($email, $user_name, $old_status, $new_status, $user_id = null, $sent_by = null)
    {
        $subject = 'Account Status Update - Online Banking';
        $body = $this->generateStatusChangeEmailTemplate($user_name, $old_status, $new_status);
        
        return $this->sendEmail($email, $subject, $body, 'status_change', $user_id, $sent_by);
    }
    
    /**
     * Generate OTP email template
     */
    private function generateOTPEmailTemplate($otp, $user_name = '')
    {
        $greeting = !empty($user_name) ? "Dear {$user_name}," : "Dear Valued Customer,";
        
        return '
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px; background-color: #f8f9fa;">
            <div style="background-color: white; padding: 30px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1);">
                <div style="text-align: center; margin-bottom: 30px;">
                    <h1 style="color: #2c3e50; margin: 0;">🏦 Online Banking System</h1>
                    <p style="color: #7f8c8d; margin: 10px 0 0 0;">Secure Login Verification</p>
                </div>
                
                <div style="margin-bottom: 30px;">
                    <p style="color: #2c3e50; margin: 0 0 20px 0; line-height: 1.6;">' . $greeting . '</p>
                    <p style="color: #2c3e50; margin: 0 0 20px 0; line-height: 1.6;">
                        You have requested to log in to your online banking account. Please use the verification code below to complete your login:
                    </p>
                </div>
                
                <div style="background-color: #e8f5e8; padding: 25px; border-radius: 8px; text-align: center; margin: 30px 0;">
                    <h2 style="color: #155724; margin: 0 0 10px 0;">Your Verification Code</h2>
                    <div style="font-size: 32px; font-weight: bold; color: #155724; letter-spacing: 8px; font-family: monospace;">
                        ' . $otp . '
                    </div>
                    <p style="color: #155724; margin: 15px 0 0 0; font-size: 14px;">
                        This code will expire in 10 minutes
                    </p>
                </div>
                
                <div style="background-color: #fff3cd; padding: 20px; border-radius: 8px; border-left: 4px solid #ffc107; margin: 30px 0;">
                    <h3 style="color: #856404; margin: 0 0 10px 0;">🔒 Security Notice</h3>
                    <ul style="color: #856404; margin: 0; padding-left: 20px; line-height: 1.6;">
                        <li>Never share this code with anyone</li>
                        <li>Our staff will never ask for this code</li>
                        <li>If you did not request this code, please contact us immediately</li>
                        <li>This code is only valid for 10 minutes</li>
                    </ul>
                </div>
                
                <div style="text-align: center; margin-top: 30px; padding-top: 20px; border-top: 1px solid #dee2e6;">
                    <p style="color: #7f8c8d; margin: 0; font-size: 14px;">
                        This email was sent automatically by the Online Banking System.<br>
                        If you have any questions, please contact our customer support.
                    </p>
                </div>
            </div>
        </div>';
    }
    
    /**
     * Generate welcome email template
     */
    private function generateWelcomeEmailTemplate($user_name)
    {
        return '
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px; background-color: #f8f9fa;">
            <div style="background-color: white; padding: 30px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1);">
                <div style="text-align: center; margin-bottom: 30px;">
                    <h1 style="color: #2c3e50; margin: 0;">🏦 Welcome to Online Banking</h1>
                    <p style="color: #7f8c8d; margin: 10px 0 0 0;">Your account has been created successfully</p>
                </div>
                
                <div style="background-color: #e8f5e8; padding: 20px; border-radius: 8px; border-left: 4px solid #28a745;">
                    <h2 style="color: #155724; margin: 0 0 15px 0;">Welcome, ' . htmlspecialchars($user_name) . '!</h2>
                    <p style="color: #155724; margin: 0; line-height: 1.6;">
                        Your online banking account has been successfully created. You can now access all our banking services securely.
                    </p>
                </div>
                
                <div style="margin: 30px 0;">
                    <h3 style="color: #2c3e50; margin: 0 0 15px 0;">What you can do:</h3>
                    <ul style="color: #2c3e50; line-height: 1.8; padding-left: 20px;">
                        <li>View your account balance and transaction history</li>
                        <li>Transfer funds securely</li>
                        <li>Manage your account settings</li>
                        <li>Access 24/7 customer support</li>
                    </ul>
                </div>
                
                <div style="text-align: center; margin: 30px 0;">
                    <a href="' . url('auth/login.php') . '" style="background-color: #007bff; color: white; padding: 12px 30px; text-decoration: none; border-radius: 5px; font-weight: bold;">
                        Login to Your Account
                    </a>
                </div>
                
                <div style="text-align: center; margin-top: 30px; padding-top: 20px; border-top: 1px solid #dee2e6;">
                    <p style="color: #7f8c8d; margin: 0; font-size: 14px;">
                        Thank you for choosing our Online Banking System.<br>
                        If you have any questions, please contact our customer support.
                    </p>
                </div>
            </div>
        </div>';
    }
    
    /**
     * Generate status change email template
     */
    private function generateStatusChangeEmailTemplate($user_name, $old_status, $new_status)
    {
        $status_colors = [
            'Active' => '#28a745',
            'Dormant/Inactive' => '#ffc107',
            'INACTIVE' => '#dc3545',
            'Disabled' => '#dc3545',
            'Suspend' => '#dc3545'
        ];
        
        $new_status_color = $status_colors[$new_status] ?? '#6c757d';
        
        return '
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px; background-color: #f8f9fa;">
            <div style="background-color: white; padding: 30px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1);">
                <div style="text-align: center; margin-bottom: 30px;">
                    <h1 style="color: #2c3e50; margin: 0;">🏦 Account Status Update</h1>
                    <p style="color: #7f8c8d; margin: 10px 0 0 0;">Important notification about your account</p>
                </div>
                
                <div style="margin-bottom: 30px;">
                    <p style="color: #2c3e50; margin: 0 0 20px 0; line-height: 1.6;">Dear ' . htmlspecialchars($user_name) . ',</p>
                    <p style="color: #2c3e50; margin: 0 0 20px 0; line-height: 1.6;">
                        We are writing to inform you that your account status has been updated.
                    </p>
                </div>
                
                <div style="background-color: #f8f9fa; padding: 25px; border-radius: 8px; margin: 30px 0;">
                    <h3 style="color: #2c3e50; margin: 0 0 20px 0; text-align: center;">Status Change Details</h3>
                    <table style="width: 100%; border-collapse: collapse;">
                        <tr>
                            <td style="padding: 10px 0; color: #7f8c8d; width: 40%;">Previous Status:</td>
                            <td style="padding: 10px 0; color: #2c3e50; font-weight: bold;">' . htmlspecialchars($old_status) . '</td>
                        </tr>
                        <tr>
                            <td style="padding: 10px 0; color: #7f8c8d;">New Status:</td>
                            <td style="padding: 10px 0;">
                                <span style="background-color: ' . $new_status_color . '; color: white; padding: 4px 12px; border-radius: 4px; font-weight: bold;">
                                    ' . htmlspecialchars($new_status) . '
                                </span>
                            </td>
                        </tr>
                        <tr>
                            <td style="padding: 10px 0; color: #7f8c8d;">Date:</td>
                            <td style="padding: 10px 0; color: #2c3e50;">' . date('M j, Y g:i A') . '</td>
                        </tr>
                    </table>
                </div>
                
                <div style="background-color: #d1ecf1; padding: 20px; border-radius: 8px; border-left: 4px solid #17a2b8; margin: 30px 0;">
                    <h3 style="color: #0c5460; margin: 0 0 10px 0;">ℹ️ What this means</h3>
                    <p style="color: #0c5460; margin: 0; line-height: 1.6;">
                        If you have any questions about this status change or need assistance with your account, 
                        please contact our customer support team immediately.
                    </p>
                </div>
                
                <div style="text-align: center; margin-top: 30px; padding-top: 20px; border-top: 1px solid #dee2e6;">
                    <p style="color: #7f8c8d; margin: 0; font-size: 14px;">
                        This notification was sent automatically by the Online Banking System.<br>
                        For support, please contact our customer service team.
                    </p>
                </div>
            </div>
        </div>';
    }
    
    /**
     * Get current email settings
     */
    public function getSettings()
    {
        return $this->settings;
    }
    
    /**
     * Test email configuration
     */
    public function testConfiguration($test_email, $method = null)
    {
        $test_method = $method ?: $this->settings['mail_method'];
        
        // Temporarily override method for testing
        $original_method = $this->settings['mail_method'];
        $this->settings['mail_method'] = $test_method;
        
        $subject = 'Email Configuration Test - ' . date('Y-m-d H:i:s');
        $body = $this->generateTestEmailTemplate($test_method);
        
        $result = $this->sendEmail($test_email, $subject, $body, 'test', null, $_SESSION['user_id'] ?? null);
        
        // Restore original method
        $this->settings['mail_method'] = $original_method;
        
        return $result;
    }
    
    /**
     * Generate test email template
     */
    private function generateTestEmailTemplate($method)
    {
        return '
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px; background-color: #f8f9fa;">
            <div style="background-color: white; padding: 30px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1);">
                <div style="text-align: center; margin-bottom: 30px;">
                    <h1 style="color: #2c3e50; margin: 0;">🏦 Email System Test</h1>
                    <p style="color: #7f8c8d; margin: 10px 0 0 0;">Configuration Verification</p>
                </div>
                
                <div style="background-color: #e8f5e8; padding: 20px; border-radius: 8px; border-left: 4px solid #28a745;">
                    <h2 style="color: #155724; margin: 0 0 15px 0;">✅ Email System Working!</h2>
                    <p style="color: #155724; margin: 0; line-height: 1.6;">
                        This test email confirms that your email configuration is working correctly.
                    </p>
                </div>
                
                <div style="margin: 30px 0;">
                    <h3 style="color: #2c3e50; margin: 0 0 15px 0;">Test Details:</h3>
                    <table style="width: 100%; border-collapse: collapse;">
                        <tr>
                            <td style="padding: 8px 0; color: #7f8c8d; width: 30%;">Method:</td>
                            <td style="padding: 8px 0; color: #2c3e50; font-weight: bold;">' . strtoupper($method) . '</td>
                        </tr>
                        <tr>
                            <td style="padding: 8px 0; color: #7f8c8d;">Sent At:</td>
                            <td style="padding: 8px 0; color: #2c3e50;">' . date('Y-m-d H:i:s') . '</td>
                        </tr>
                        <tr>
                            <td style="padding: 8px 0; color: #7f8c8d;">Server:</td>
                            <td style="padding: 8px 0; color: #2c3e50;">' . ($_SERVER['SERVER_NAME'] ?? 'localhost') . '</td>
                        </tr>
                    </table>
                </div>
                
                <div style="text-align: center; margin-top: 30px; padding-top: 20px; border-top: 1px solid #dee2e6;">
                    <p style="color: #7f8c8d; margin: 0; font-size: 14px;">
                        This email was sent automatically by the Online Banking System.<br>
                        If you received this email unexpectedly, please contact your system administrator.
                    </p>
                </div>
            </div>
        </div>';
    }
}

/**
 * Email Logger class for comprehensive email logging
 */
class EmailLogger
{
    private $db;
    
    public function __construct($database)
    {
        $this->db = $database;
    }
    
    /**
     * Log email sending attempt
     */
    public function logEmail($recipient_email, $recipient_name, $subject, $email_type, $method_used, $status, $error_message = null, $sent_by = null, $user_id = null)
    {
        try {
            $query = "INSERT INTO email_logs (
                recipient_email, recipient_name, subject, email_type, method_used, 
                status, error_message, sent_by, user_id
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)";
            
            $this->db->query($query, [
                $recipient_email,
                $recipient_name,
                $subject,
                $email_type,
                $method_used,
                $status,
                $error_message,
                $sent_by,
                $user_id
            ]);
            
        } catch (Exception $e) {
            error_log("EmailLogger: Failed to log email - " . $e->getMessage());
        }
    }
}
?>
