<?php
require_once '../../config/config.php';
requireAdmin();

header('Content-Type: application/json');

try {
    // Validate form data
    $user_id = intval($_POST['user_id'] ?? 0);
    $document_type = trim($_POST['document_type'] ?? '');
    $notes = trim($_POST['notes'] ?? '');
    
    if ($user_id <= 0) {
        throw new Exception('Invalid user ID');
    }
    
    $valid_types = ['passport', 'id_card', 'drivers_license', 'utility_bill', 'bank_statement', 'cheque', 'kyc_selfie', 'other'];
    if (!in_array($document_type, $valid_types)) {
        throw new Exception('Invalid document type');
    }
    
    // Check if file was uploaded
    if (!isset($_FILES['document_file']) || $_FILES['document_file']['error'] !== UPLOAD_ERR_OK) {
        throw new Exception('No file uploaded or upload error occurred');
    }
    
    $file = $_FILES['document_file'];
    
    // Validate file size (10MB max)
    $max_size = 10 * 1024 * 1024; // 10MB
    if ($file['size'] > $max_size) {
        throw new Exception('File size exceeds 10MB limit');
    }
    
    // Validate file type
    $file_extension = strtolower(pathinfo($file['name'], PATHINFO_EXTENSION));
    $allowed_extensions = ['pdf', 'jpg', 'jpeg', 'png'];
    
    if (!in_array($file_extension, $allowed_extensions)) {
        throw new Exception('Invalid file type. Only PDF, JPG, JPEG, and PNG files are allowed');
    }
    
    // Create upload directory if it doesn't exist
    $upload_dir = '../../uploads/documents/';
    if (!file_exists($upload_dir)) {
        mkdir($upload_dir, 0755, true);
    }
    
    // Generate unique filename
    $file_name = $document_type . '_' . $user_id . '_' . date('Ymd_His') . '_' . uniqid() . '.' . $file_extension;
    $file_path = $upload_dir . $file_name;
    $relative_path = '/uploads/documents/' . $file_name;
    
    // Move uploaded file
    if (!move_uploaded_file($file['tmp_name'], $file_path)) {
        throw new Exception('Failed to save uploaded file');
    }
    
    $db = getDB();
    
    // Check if user exists
    $user_check = "SELECT id, first_name, last_name FROM accounts WHERE id = ?";
    $user_result = $db->query($user_check, [$user_id]);
    
    if (!$user_result || $user_result->num_rows === 0) {
        // Clean up uploaded file
        unlink($file_path);
        throw new Exception('User not found');
    }
    
    $user = $user_result->fetch_assoc();
    
    // Insert document record
    $insert_query = "INSERT INTO user_documents 
                     (user_id, document_type, document_name, file_path, file_size, file_type, 
                      verification_status, uploaded_by, notes) 
                     VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)";
    
    $document_id = $db->insert($insert_query, [
        $user_id,
        $document_type,
        $file['name'],
        $relative_path,
        $file['size'],
        $file['type'],
        'pending',
        $_SESSION['user_id'],
        $notes
    ]);
    
    if (!$document_id) {
        // Clean up uploaded file
        unlink($file_path);
        throw new Exception('Failed to save document record');
    }
    
    // Add to verification history
    $history_query = "INSERT INTO document_verification_history 
                      (document_id, action, performed_by, reason, notes) 
                      VALUES (?, ?, ?, ?, ?)";
    
    $db->query($history_query, [
        $document_id,
        'uploaded',
        $_SESSION['user_id'],
        'Document uploaded by admin',
        $notes
    ]);
    
    // Log the action
    error_log("Document uploaded: Document ID {$document_id}, User: {$user_id}, Type: {$document_type}, Admin: {$_SESSION['user_id']}");
    
    echo json_encode([
        'success' => true,
        'message' => 'Document uploaded successfully',
        'document_id' => $document_id,
        'user_name' => $user['first_name'] . ' ' . $user['last_name']
    ]);
    
} catch (Exception $e) {
    error_log("Document upload error: " . $e->getMessage());
    
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ]);
}
?>
