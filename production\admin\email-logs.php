<?php
require_once '../config/config.php';
requireAdmin();

$page_title = 'Email Logs';

// Pagination settings
$records_per_page = 25;
$current_page = intval($_GET['page'] ?? 1);
$offset = ($current_page - 1) * $records_per_page;

// Filter parameters
$filter_status = $_GET['status'] ?? '';
$filter_type = $_GET['type'] ?? '';
$filter_date_from = $_GET['date_from'] ?? '';
$filter_date_to = $_GET['date_to'] ?? '';
$filter_email = $_GET['email'] ?? '';

// Build WHERE clause for filters
$where_conditions = [];
$params = [];

if (!empty($filter_status)) {
    $where_conditions[] = "el.status = ?";
    $params[] = $filter_status;
}

if (!empty($filter_type)) {
    $where_conditions[] = "el.email_type = ?";
    $params[] = $filter_type;
}

if (!empty($filter_email)) {
    $where_conditions[] = "el.recipient_email LIKE ?";
    $params[] = "%$filter_email%";
}

if (!empty($filter_date_from)) {
    $where_conditions[] = "DATE(el.sent_at) >= ?";
    $params[] = $filter_date_from;
}

if (!empty($filter_date_to)) {
    $where_conditions[] = "DATE(el.sent_at) <= ?";
    $params[] = $filter_date_to;
}

$where_clause = !empty($where_conditions) ? "WHERE " . implode(" AND ", $where_conditions) : "";

try {
    $db = getDB();
    
    // Get total count for pagination
    $count_query = "SELECT COUNT(*) as total FROM email_logs el $where_clause";
    if (!empty($params)) {
        $count_result = $db->query($count_query, $params);
    } else {
        $count_result = $db->query($count_query);
    }
    $total_records = $count_result->fetch_assoc()['total'];
    $total_pages = ceil($total_records / $records_per_page);
    
    // Get email logs with pagination
    $logs_query = "SELECT el.*, 
                   a.first_name as sent_by_first_name, a.last_name as sent_by_last_name,
                   u.first_name as user_first_name, u.last_name as user_last_name, u.username
                   FROM email_logs el 
                   LEFT JOIN accounts a ON el.sent_by = a.id 
                   LEFT JOIN accounts u ON el.user_id = u.id 
                   $where_clause
                   ORDER BY el.sent_at DESC 
                   LIMIT $records_per_page OFFSET $offset";
    
    if (!empty($params)) {
        $logs_result = $db->query($logs_query, $params);
    } else {
        $logs_result = $db->query($logs_query);
    }
    
    $email_logs = [];
    if ($logs_result) {
        while ($row = $logs_result->fetch_assoc()) {
            $email_logs[] = $row;
        }
    }
    
    // Get summary statistics
    $stats_query = "SELECT 
                    COUNT(*) as total_emails,
                    COUNT(CASE WHEN status = 'sent' THEN 1 END) as sent_emails,
                    COUNT(CASE WHEN status = 'failed' THEN 1 END) as failed_emails,
                    COUNT(CASE WHEN email_type = 'otp' THEN 1 END) as otp_emails,
                    COUNT(CASE WHEN email_type = 'test' THEN 1 END) as test_emails
                    FROM email_logs";
    $stats_result = $db->query($stats_query);
    $stats = $stats_result ? $stats_result->fetch_assoc() : ['total_emails' => 0, 'sent_emails' => 0, 'failed_emails' => 0, 'otp_emails' => 0, 'test_emails' => 0];
    
} catch (Exception $e) {
    $error = "Failed to load email logs: " . $e->getMessage();
    $email_logs = [];
    $total_records = 0;
    $total_pages = 0;
    $stats = ['total_emails' => 0, 'sent_emails' => 0, 'failed_emails' => 0, 'otp_emails' => 0, 'test_emails' => 0];
}

include 'includes/admin-header.php';
?>

<!-- Breadcrumb -->
<nav aria-label="breadcrumb" style="margin-bottom: 1rem;">
    <ol class="breadcrumb">
        <li class="breadcrumb-item"><a href="index.php">Admin</a></li>
        <li class="breadcrumb-item active" aria-current="page">Email Logs</li>
    </ol>
</nav>

<!-- Error Message -->
<?php if (isset($error)): ?>
<div class="alert alert-danger" role="alert">
    <div class="d-flex">
        <div><i class="fas fa-exclamation-circle me-2"></i></div>
        <div>
            <h4 class="alert-title">Error!</h4>
            <div class="text-muted"><?php echo htmlspecialchars($error); ?></div>
        </div>
    </div>
</div>
<?php endif; ?>

<!-- Statistics Cards -->
<div class="row row-cards mb-4">
    <div class="col-sm-6 col-lg-3">
        <div class="card card-sm">
            <div class="card-body">
                <div class="row align-items-center">
                    <div class="col-auto">
                        <span class="bg-primary text-white avatar">
                            <i class="fas fa-envelope"></i>
                        </span>
                    </div>
                    <div class="col">
                        <div class="font-weight-medium"><?php echo number_format($stats['total_emails']); ?></div>
                        <div class="text-muted">Total Emails</div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-sm-6 col-lg-3">
        <div class="card card-sm">
            <div class="card-body">
                <div class="row align-items-center">
                    <div class="col-auto">
                        <span class="bg-success text-white avatar">
                            <i class="fas fa-check-circle"></i>
                        </span>
                    </div>
                    <div class="col">
                        <div class="font-weight-medium"><?php echo number_format($stats['sent_emails']); ?></div>
                        <div class="text-muted">Sent Successfully</div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-sm-6 col-lg-3">
        <div class="card card-sm">
            <div class="card-body">
                <div class="row align-items-center">
                    <div class="col-auto">
                        <span class="bg-danger text-white avatar">
                            <i class="fas fa-times-circle"></i>
                        </span>
                    </div>
                    <div class="col">
                        <div class="font-weight-medium"><?php echo number_format($stats['failed_emails']); ?></div>
                        <div class="text-muted">Failed</div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-sm-6 col-lg-3">
        <div class="card card-sm">
            <div class="card-body">
                <div class="row align-items-center">
                    <div class="col-auto">
                        <span class="bg-info text-white avatar">
                            <i class="fas fa-shield-alt"></i>
                        </span>
                    </div>
                    <div class="col">
                        <div class="font-weight-medium"><?php echo number_format($stats['otp_emails']); ?></div>
                        <div class="text-muted">OTP Emails</div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Filters Card -->
<div class="row row-cards mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h3 class="card-title">
                    <i class="fas fa-filter me-2"></i>
                    Email Log Filters
                </h3>
                <div class="card-actions">
                    <a href="email-logs.php" class="btn btn-outline-secondary btn-sm">
                        <i class="fas fa-times me-2"></i>
                        Clear Filters
                    </a>
                    <a href="email-settings.php" class="btn btn-primary btn-sm">
                        <i class="fas fa-cog me-2"></i>
                        Email Settings
                    </a>
                </div>
            </div>
            <div class="card-body">
                <form method="GET" action="" class="row g-2">
                    <div class="col-md-2">
                        <label class="form-label">Status</label>
                        <select name="status" class="form-select form-select-sm">
                            <option value="">All Status</option>
                            <option value="sent" <?php echo $filter_status === 'sent' ? 'selected' : ''; ?>>Sent</option>
                            <option value="failed" <?php echo $filter_status === 'failed' ? 'selected' : ''; ?>>Failed</option>
                            <option value="pending" <?php echo $filter_status === 'pending' ? 'selected' : ''; ?>>Pending</option>
                        </select>
                    </div>
                    
                    <div class="col-md-2">
                        <label class="form-label">Type</label>
                        <select name="type" class="form-select form-select-sm">
                            <option value="">All Types</option>
                            <option value="otp" <?php echo $filter_type === 'otp' ? 'selected' : ''; ?>>OTP</option>
                            <option value="test" <?php echo $filter_type === 'test' ? 'selected' : ''; ?>>Test</option>
                            <option value="notification" <?php echo $filter_type === 'notification' ? 'selected' : ''; ?>>Notification</option>
                            <option value="alert" <?php echo $filter_type === 'alert' ? 'selected' : ''; ?>>Alert</option>
                            <option value="welcome" <?php echo $filter_type === 'welcome' ? 'selected' : ''; ?>>Welcome</option>
                            <option value="status_change" <?php echo $filter_type === 'status_change' ? 'selected' : ''; ?>>Status Change</option>
                        </select>
                    </div>
                    
                    <div class="col-md-2">
                        <label class="form-label">Email</label>
                        <input type="text" name="email" class="form-control form-control-sm" placeholder="Search email..." value="<?php echo htmlspecialchars($filter_email); ?>">
                    </div>
                    
                    <div class="col-md-2">
                        <label class="form-label">Date From</label>
                        <input type="date" name="date_from" class="form-control form-control-sm" value="<?php echo htmlspecialchars($filter_date_from); ?>">
                    </div>
                    
                    <div class="col-md-2">
                        <label class="form-label">Date To</label>
                        <input type="date" name="date_to" class="form-control form-control-sm" value="<?php echo htmlspecialchars($filter_date_to); ?>">
                    </div>
                    
                    <div class="col-md-2">
                        <label class="form-label">&nbsp;</label>
                        <div class="btn-group d-block" role="group">
                            <button type="submit" class="btn btn-primary btn-sm">
                                <i class="fas fa-search me-1"></i>
                                Filter
                            </button>
                            <a href="email-logs.php" class="btn btn-outline-secondary btn-sm">
                                <i class="fas fa-times me-1"></i>
                                Clear
                            </a>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- Email Logs Table -->
<div class="row row-cards">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h3 class="card-title">
                    <i class="fas fa-history me-2"></i>
                    Email Logs
                    <span class="badge bg-secondary ms-2"><?php echo number_format($total_records); ?> total</span>
                </h3>
                <div class="card-actions">
                    <span class="text-muted">
                        Showing <?php echo number_format(($current_page - 1) * $records_per_page + 1); ?> -
                        <?php echo number_format(min($current_page * $records_per_page, $total_records)); ?>
                        of <?php echo number_format($total_records); ?> emails
                    </span>
                </div>
            </div>
            <div class="card-body p-0">
                <?php if (!empty($email_logs)): ?>
                <div class="table-responsive">
                    <table class="table table-vcenter card-table table-sm">
                        <thead>
                            <tr>
                                <th class="w-1">#</th>
                                <th>Recipient</th>
                                <th>Subject</th>
                                <th>Type</th>
                                <th>Method</th>
                                <th>Status</th>
                                <th>Sent By</th>
                                <th>Date</th>
                                <th class="w-1">Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php
                            $row_number = ($current_page - 1) * $records_per_page + 1;
                            foreach ($email_logs as $log):
                            ?>
                            <tr>
                                <!-- Row Number -->
                                <td>
                                    <span class="text-muted"><?php echo $row_number++; ?></span>
                                </td>

                                <!-- Recipient -->
                                <td>
                                    <div class="d-flex flex-column">
                                        <div class="fw-bold" style="font-size: 0.85rem;"><?php echo htmlspecialchars($log['recipient_email']); ?></div>
                                        <?php if (!empty($log['recipient_name'])): ?>
                                        <small class="text-muted"><?php echo htmlspecialchars($log['recipient_name']); ?></small>
                                        <?php endif; ?>
                                        <?php if (!empty($log['user_first_name'])): ?>
                                        <small class="text-info">@<?php echo htmlspecialchars($log['username']); ?></small>
                                        <?php endif; ?>
                                    </div>
                                </td>

                                <!-- Subject -->
                                <td>
                                    <div class="text-truncate" style="max-width: 200px;" title="<?php echo htmlspecialchars($log['subject']); ?>">
                                        <?php echo htmlspecialchars($log['subject']); ?>
                                    </div>
                                </td>

                                <!-- Type -->
                                <td>
                                    <?php
                                    $type_colors = [
                                        'otp' => 'warning',
                                        'test' => 'info',
                                        'notification' => 'primary',
                                        'alert' => 'danger',
                                        'welcome' => 'success',
                                        'status_change' => 'secondary'
                                    ];
                                    $type_color = $type_colors[$log['email_type']] ?? 'secondary';
                                    ?>
                                    <span class="badge bg-<?php echo $type_color; ?> badge-sm">
                                        <?php echo ucfirst($log['email_type']); ?>
                                    </span>
                                </td>

                                <!-- Method -->
                                <td>
                                    <span class="badge bg-<?php echo $log['method_used'] === 'smtp' ? 'success' : 'info'; ?> badge-sm">
                                        <?php echo strtoupper($log['method_used']); ?>
                                    </span>
                                </td>

                                <!-- Status -->
                                <td>
                                    <?php
                                    $status_colors = [
                                        'sent' => 'success',
                                        'failed' => 'danger',
                                        'pending' => 'warning'
                                    ];
                                    $status_color = $status_colors[$log['status']] ?? 'secondary';
                                    ?>
                                    <span class="badge bg-<?php echo $status_color; ?> badge-sm">
                                        <?php echo ucfirst($log['status']); ?>
                                    </span>
                                </td>

                                <!-- Sent By -->
                                <td>
                                    <?php if (!empty($log['sent_by_first_name'])): ?>
                                    <div class="d-flex align-items-center">
                                        <div class="avatar avatar-xs me-2">
                                            <?php echo strtoupper(substr($log['sent_by_first_name'], 0, 1) . substr($log['sent_by_last_name'], 0, 1)); ?>
                                        </div>
                                        <div>
                                            <div style="font-size: 0.8rem;"><?php echo htmlspecialchars($log['sent_by_first_name'] . ' ' . $log['sent_by_last_name']); ?></div>
                                        </div>
                                    </div>
                                    <?php else: ?>
                                    <span class="text-muted">System</span>
                                    <?php endif; ?>
                                </td>

                                <!-- Date -->
                                <td>
                                    <div class="d-flex flex-column">
                                        <div class="text-muted" style="font-size: 0.85rem;"><?php echo date('M j, Y', strtotime($log['sent_at'])); ?></div>
                                        <small class="text-muted"><?php echo date('g:i A', strtotime($log['sent_at'])); ?></small>
                                    </div>
                                </td>

                                <!-- Actions -->
                                <td>
                                    <button type="button" class="btn btn-sm btn-outline-primary" onclick="viewEmailDetails('<?php echo $log['id']; ?>')" title="View Details">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                </td>
                            </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>

                <!-- Pagination -->
                <?php if ($total_pages > 1): ?>
                <div class="card-footer d-flex align-items-center">
                    <p class="m-0 text-muted">
                        Showing <span><?php echo number_format(($current_page - 1) * $records_per_page + 1); ?></span>
                        to <span><?php echo number_format(min($current_page * $records_per_page, $total_records)); ?></span>
                        of <span><?php echo number_format($total_records); ?></span> entries
                    </p>
                    <ul class="pagination m-0 ms-auto">
                        <?php if ($current_page > 1): ?>
                        <li class="page-item">
                            <a class="page-link" href="?page=<?php echo $current_page - 1; ?><?php echo !empty($_GET) ? '&' . http_build_query(array_diff_key($_GET, ['page' => ''])) : ''; ?>">
                                <i class="fas fa-chevron-left"></i> prev
                            </a>
                        </li>
                        <?php endif; ?>

                        <?php
                        $start_page = max(1, $current_page - 2);
                        $end_page = min($total_pages, $current_page + 2);

                        for ($i = $start_page; $i <= $end_page; $i++):
                        ?>
                        <li class="page-item <?php echo $i == $current_page ? 'active' : ''; ?>">
                            <a class="page-link" href="?page=<?php echo $i; ?><?php echo !empty($_GET) ? '&' . http_build_query(array_diff_key($_GET, ['page' => ''])) : ''; ?>">
                                <?php echo $i; ?>
                            </a>
                        </li>
                        <?php endfor; ?>

                        <?php if ($current_page < $total_pages): ?>
                        <li class="page-item">
                            <a class="page-link" href="?page=<?php echo $current_page + 1; ?><?php echo !empty($_GET) ? '&' . http_build_query(array_diff_key($_GET, ['page' => ''])) : ''; ?>">
                                next <i class="fas fa-chevron-right"></i>
                            </a>
                        </li>
                        <?php endif; ?>
                    </ul>
                </div>
                <?php endif; ?>
                <?php else: ?>
                <div class="empty">
                    <div class="empty-img">
                        <i class="fas fa-history" style="font-size: 3rem; color: #6c757d;"></i>
                    </div>
                    <p class="empty-title">No email logs found</p>
                    <p class="empty-subtitle text-muted">
                        <?php if (!empty(array_filter([$filter_status, $filter_type, $filter_email, $filter_date_from, $filter_date_to]))): ?>
                        Try adjusting your filters to see more results.
                        <?php else: ?>
                        No emails have been sent yet.
                        <?php endif; ?>
                    </p>
                    <div class="empty-action">
                        <a href="email-settings.php" class="btn btn-primary">
                            <i class="fas fa-cog me-2"></i>
                            Configure Email Settings
                        </a>
                    </div>
                </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<script>
function viewEmailDetails(emailId) {
    // Simple alert for now - can be enhanced later
    alert('Email Details for ID: ' + emailId + '\n\nThis feature will show detailed email information in a future update.');
}
</script>

<?php include 'includes/admin-footer.php'; ?>
