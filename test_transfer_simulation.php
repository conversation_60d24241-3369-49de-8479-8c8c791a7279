<?php
/**
 * Transfer System Simulation Test
 * Tests both inter-bank and local bank transfers
 */

// Enable error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Include required files
require_once 'config/config.php';

// Start session
session_start();

// Test configuration
$test_results = [];

/**
 * Log test result
 */
function logTest($test_name, $success, $message = '', $details = '') {
    global $test_results;
    
    $test_results[] = [
        'name' => $test_name,
        'success' => $success,
        'message' => $message,
        'details' => $details,
        'timestamp' => date('Y-m-d H:i:s')
    ];
    
    $status = $success ? '✅ PASS' : '❌ FAIL';
    echo "<div style='margin: 10px 0; padding: 10px; border-left: 4px solid " . ($success ? '#28a745' : '#dc3545') . "; background: " . ($success ? '#d4edda' : '#f8d7da') . ";'>";
    echo "<strong>$status: $test_name</strong><br>";
    if ($message) echo "Message: $message<br>";
    if ($details) echo "Details: <code>$details</code>";
    echo "</div>";
}

/**
 * Check if OTP table has correct structure
 */
function checkOTPTableStructure() {
    try {
        $db = getDB();
        $result = $db->query("DESCRIBE user_otps");
        $columns = [];

        if ($result) {
            while ($row = $result->fetch_assoc()) {
                $columns[] = $row['Field'];
            }
        }

        $required_columns = ['id', 'user_id', 'otp_code', 'expires_at', 'source', 'used', 'created_at'];
        $missing = array_diff($required_columns, $columns);

        if (empty($missing)) {
            logTest('OTP Table Structure', true, 'All required columns present', implode(', ', $columns));
            return true;
        } else {
            logTest('OTP Table Structure', false, 'Missing columns', implode(', ', $missing));
            return false;
        }

    } catch (Exception $e) {
        logTest('OTP Table Structure', false, 'Error checking OTP table', $e->getMessage());
        return false;
    }
}

/**
 * Setup test users and data
 */
function setupTestData() {
    try {
        $db = getDB();
        
        // Get test users
        $users_query = "SELECT id, account_number, first_name, last_name, email, balance, is_admin 
                       FROM accounts WHERE status = 'active' AND is_admin = 0 LIMIT 3";
        $users_result = $db->query($users_query);
        
        $users = [];
        while ($row = $users_result->fetch_assoc()) {
            $users[] = $row;
        }
        
        if (count($users) < 2) {
            logTest('Setup Test Data', false, 'Need at least 2 active users for testing');
            return null;
        }
        
        // Ensure users have sufficient balance
        foreach ($users as $user) {
            if ($user['balance'] < 100) {
                $update_balance = "UPDATE accounts SET balance = 1000.00 WHERE id = ?";
                $db->query($update_balance, [$user['id']]);
            }
        }
        
        logTest('Setup Test Data', true, "Found " . count($users) . " test users", 
               "Users: " . implode(', ', array_column($users, 'first_name')));
        
        return $users;
        
    } catch (Exception $e) {
        logTest('Setup Test Data', false, 'Error setting up test data', $e->getMessage());
        return null;
    }
}

/**
 * Test inter-bank transfer
 */
function testInterBankTransfer($users) {
    if (count($users) < 2) {
        logTest('Inter-Bank Transfer', false, 'Need at least 2 users for inter-bank transfer');
        return;
    }
    
    $sender = $users[0];
    $recipient = $users[1];
    
    // Set session for sender
    $_SESSION['user_id'] = $sender['id'];
    
    // Prepare transfer data
    $transfer_data = [
        'transfer_type' => 'inter-bank',
        'source_account' => 'main',
        'beneficiary_account' => $recipient['account_number'],
        'beneficiary_name' => $recipient['first_name'] . ' ' . $recipient['last_name'],
        'amount' => 25.00,
        'currency' => 'USD',
        'narration' => 'Test inter-bank transfer'
    ];
    
    try {
        // Mock the request
        $json_data = json_encode($transfer_data);
        
        // Create a test file that simulates the fixed process-transfer.php
        $test_content = file_get_contents('user/transfers/process-transfer-fixed.php');

        // Replace php://input with our test data
        $test_content = str_replace("file_get_contents('php://input')", "'" . addslashes($json_data) . "'", $test_content);

        // Remove function redeclarations to avoid conflicts
        $test_content = str_replace('function logTransferDebug($message) {', '// function logTransferDebug($message) {', $test_content);
        $test_content = str_replace('    error_log("TRANSFER_DEBUG: " . $message);', '    // error_log("TRANSFER_DEBUG: " . $message);', $test_content);
        $test_content = str_replace('}', '// }', $test_content, 1); // Replace first closing brace

        // Write to temporary file
        $temp_file = 'temp_transfer_test.php';
        file_put_contents($temp_file, $test_content);
        
        // Capture output
        ob_start();
        include $temp_file;
        $output = ob_get_clean();
        
        // Clean up
        unlink($temp_file);
        
        // Parse response
        $response = json_decode($output, true);
        
        if ($response && isset($response['success']) && $response['success']) {
            logTest('Inter-Bank Transfer', true, 'Transfer completed successfully', 
                   "Reference: " . ($response['reference_number'] ?? 'N/A') . ", Amount: $" . ($response['amount'] ?? 'N/A'));
        } else {
            $error_msg = $response['message'] ?? 'Unknown error';
            logTest('Inter-Bank Transfer', false, 'Transfer failed', $error_msg);
        }
        
    } catch (Exception $e) {
        logTest('Inter-Bank Transfer', false, 'Exception during transfer', $e->getMessage());
    }
}

/**
 * Test local bank transfer with OTP
 */
function testLocalBankTransfer($users) {
    if (empty($users)) {
        logTest('Local Bank Transfer', false, 'No users available for testing');
        return;
    }
    
    $sender = $users[0];
    
    // Set session for sender
    $_SESSION['user_id'] = $sender['id'];
    
    try {
        $db = getDB();
        
        // Generate test OTP
        $otp_code = sprintf('%06d', mt_rand(100000, 999999));
        
        // Insert OTP
        $insert_otp_sql = "INSERT INTO user_otps (user_id, otp_code, expires_at, source, used, created_at) 
                          VALUES (?, ?, DATE_ADD(NOW(), INTERVAL 10 MINUTE), 'transfer', 0, NOW())";
        $db->query($insert_otp_sql, [$sender['id'], $otp_code]);
        
        // Prepare transfer data
        $transfer_data = [
            'transfer_type' => 'local-bank',
            'source_account' => 'main',
            'beneficiary_account' => '**********',
            'beneficiary_name' => 'External Bank User',
            'beneficiary_bank' => 'External Bank',
            'routing_code' => '*********',
            'account_type' => 'savings',
            'amount' => 50.00,
            'currency' => 'USD',
            'narration' => 'Test local bank transfer',
            'otp_code' => $otp_code
        ];
        
        // Mock the request
        $json_data = json_encode($transfer_data);
        
        // Create a test file that simulates the fixed process-transfer.php
        $test_content = file_get_contents('user/transfers/process-transfer-fixed.php');

        // Replace php://input with our test data
        $test_content = str_replace("file_get_contents('php://input')", "'" . addslashes($json_data) . "'", $test_content);

        // Remove function redeclarations to avoid conflicts
        $test_content = str_replace('function logTransferDebug($message) {', '// function logTransferDebug($message) {', $test_content);
        $test_content = str_replace('    error_log("TRANSFER_DEBUG: " . $message);', '    // error_log("TRANSFER_DEBUG: " . $message);', $test_content);

        // Write to temporary file
        $temp_file = 'temp_transfer_test_local.php';
        file_put_contents($temp_file, $test_content);
        
        // Capture output
        ob_start();
        include $temp_file;
        $output = ob_get_clean();
        
        // Clean up
        unlink($temp_file);
        
        // Parse response
        $response = json_decode($output, true);
        
        if ($response && isset($response['success']) && $response['success']) {
            logTest('Local Bank Transfer', true, 'Transfer completed successfully', 
                   "Reference: " . ($response['reference_number'] ?? 'N/A') . ", Amount: $" . ($response['amount'] ?? 'N/A') . ", Fee: $" . ($response['fee'] ?? 'N/A'));
        } else {
            $error_msg = $response['message'] ?? 'Unknown error';
            logTest('Local Bank Transfer', false, 'Transfer failed', $error_msg);
        }
        
    } catch (Exception $e) {
        logTest('Local Bank Transfer', false, 'Exception during transfer', $e->getMessage());
    }
}

// Start testing
echo "<!DOCTYPE html><html><head><title>Transfer Simulation Test</title>";
echo "<style>body{font-family:Arial,sans-serif;margin:20px;} code{background:#f4f4f4;padding:2px 4px;border-radius:3px;}</style>";
echo "</head><body>";
echo "<h1>🚀 Transfer System Simulation Test</h1>";
echo "<p><strong>Test Started:</strong> " . date('Y-m-d H:i:s') . "</p>";

// Run tests
echo "<h2>📋 Simulation Results</h2>";

$users = setupTestData();
if ($users) {
    testInterBankTransfer($users);
    testLocalBankTransfer($users);
}

// Summary
echo "<h2>📊 Test Summary</h2>";
$total_tests = count($test_results);
$passed_tests = array_filter($test_results, function($test) { return $test['success']; });
$passed_count = count($passed_tests);
$failed_count = $total_tests - $passed_count;

$overall_success = ($failed_count === 0);

echo "<div style='padding: 15px; background: " . ($overall_success ? '#d4edda' : '#f8d7da') . "; border: 1px solid " . ($overall_success ? '#c3e6cb' : '#f5c6cb') . "; border-radius: 5px;'>";
echo "<strong>Overall Result: " . ($overall_success ? '✅ ALL TESTS PASSED' : '❌ SOME TESTS FAILED') . "</strong><br>";
echo "Total Tests: $total_tests | Passed: $passed_count | Failed: $failed_count";
echo "</div>";

echo "<h3>🔧 Next Steps:</h3>";
if ($overall_success) {
    echo "<p>✅ All simulation tests passed! The corrected transfer system is ready for implementation.</p>";
    echo "<p><strong>To implement:</strong> Replace the current <code>process-transfer.php</code> with <code>process-transfer-fixed.php</code></p>";
} else {
    echo "<p>❌ Some tests failed. Review the errors and fix before implementing.</p>";
}

echo "</body></html>";
?>
