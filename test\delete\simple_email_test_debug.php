<?php
/**
 * Simple Email Test with Debug Information
 * Basic test to check what's working
 */

// Enable all error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);
ini_set('log_errors', 1);

echo "<h1>🏦 Simple Email Test Debug</h1>";
echo "<p>Testing basic functionality step by step...</p>";

// Test 1: Basic PHP
echo "<h2>Test 1: Basic PHP</h2>";
echo "✅ PHP is working<br>";
echo "PHP Version: " . phpversion() . "<br>";
echo "Current time: " . date('Y-m-d H:i:s') . "<br>";

// Test 2: File includes
echo "<h2>Test 2: File Includes</h2>";
try {
    if (file_exists('config/config.php')) {
        echo "✅ config/config.php exists<br>";
        require_once 'config/config.php';
        echo "✅ config/config.php loaded<br>";
    } else {
        echo "❌ config/config.php not found<br>";
    }
    
    if (file_exists('config/email.php')) {
        echo "✅ config/email.php exists<br>";
        require_once 'config/email.php';
        echo "✅ config/email.php loaded<br>";
    } else {
        echo "❌ config/email.php not found<br>";
    }
} catch (Exception $e) {
    echo "❌ Include error: " . $e->getMessage() . "<br>";
}

// Test 3: Constants
echo "<h2>Test 3: Email Constants</h2>";
if (defined('SMTP_HOST')) {
    echo "✅ SMTP_HOST: " . SMTP_HOST . "<br>";
} else {
    echo "❌ SMTP_HOST not defined<br>";
}

if (defined('SMTP_PORT')) {
    echo "✅ SMTP_PORT: " . SMTP_PORT . "<br>";
} else {
    echo "❌ SMTP_PORT not defined<br>";
}

if (defined('SMTP_USERNAME')) {
    echo "✅ SMTP_USERNAME: " . SMTP_USERNAME . "<br>";
} else {
    echo "❌ SMTP_USERNAME not defined<br>";
}

if (defined('FROM_EMAIL')) {
    echo "✅ FROM_EMAIL: " . FROM_EMAIL . "<br>";
} else {
    echo "❌ FROM_EMAIL not defined<br>";
}

// Test 4: PHPMailer
echo "<h2>Test 4: PHPMailer</h2>";
if (file_exists('vendor/phpmailer/phpmailer/src/PHPMailer.php')) {
    echo "✅ PHPMailer files exist<br>";
    
    try {
        require_once 'vendor/phpmailer/phpmailer/src/Exception.php';
        require_once 'vendor/phpmailer/phpmailer/src/SMTP.php';
        require_once 'vendor/phpmailer/phpmailer/src/PHPMailer.php';
        echo "✅ PHPMailer loaded manually<br>";
        
        $mail = new PHPMailer\PHPMailer\PHPMailer(true);
        echo "✅ PHPMailer instance created<br>";
    } catch (Exception $e) {
        echo "❌ PHPMailer error: " . $e->getMessage() . "<br>";
    }
} else {
    echo "❌ PHPMailer files not found<br>";
}

// Test 5: Simple Email Test
echo "<h2>Test 5: Simple Email Test</h2>";
if (isset($mail) && defined('SMTP_HOST')) {
    try {
        $test_email = '<EMAIL>';
        
        // Configure SMTP
        $mail->isSMTP();
        $mail->Host = SMTP_HOST;
        $mail->SMTPAuth = true;
        $mail->Username = SMTP_USERNAME;
        $mail->Password = SMTP_PASSWORD;
        $mail->SMTPSecure = PHPMailer\PHPMailer\PHPMailer::ENCRYPTION_SMTPS; // SSL for port 465
        $mail->Port = SMTP_PORT;
        $mail->SMTPDebug = 0;
        
        // Set email content
        $mail->setFrom(FROM_EMAIL, 'Online Banking System');
        $mail->addAddress($test_email, 'Demo Developer');
        $mail->isHTML(true);
        $mail->Subject = '🏦 Simple Email Test - ' . date('H:i:s');
        $mail->Body = '
        <div style="font-family: Arial, sans-serif; padding: 20px; background: #f5f5f5;">
            <div style="max-width: 600px; margin: 0 auto; background: white; padding: 30px; border-radius: 10px;">
                <h1 style="color: #28a745;">🎉 Email Test Successful!</h1>
                <p>This simple email test is working perfectly!</p>
                <div style="background: #e7f3ff; padding: 15px; border-radius: 5px; margin: 20px 0;">
                    <h3>Test Details:</h3>
                    <ul>
                        <li><strong>SMTP Host:</strong> ' . SMTP_HOST . '</li>
                        <li><strong>SMTP Port:</strong> ' . SMTP_PORT . '</li>
                        <li><strong>Test Time:</strong> ' . date('Y-m-d H:i:s') . '</li>
                        <li><strong>Recipient:</strong> ' . $test_email . '</li>
                    </ul>
                </div>
                <p style="color: #28a745; font-weight: bold;">✅ Banking system email functionality is working!</p>
            </div>
        </div>';
        
        echo "📤 Attempting to send email to $test_email...<br>";
        
        $result = $mail->send();
        
        if ($result) {
            echo "<div style='background: #d4edda; padding: 15px; border-radius: 5px; margin: 10px 0; color: #155724;'>";
            echo "<h3>🎉 EMAIL SENT SUCCESSFULLY!</h3>";
            echo "<p>✅ Email sent to: <strong>$test_email</strong></p>";
            echo "<p>✅ SMTP Host: " . SMTP_HOST . "</p>";
            echo "<p>✅ SMTP Port: " . SMTP_PORT . " (SSL)</p>";
            echo "<p>✅ Time: " . date('Y-m-d H:i:s') . "</p>";
            echo "<p><strong>Check your <NAME_EMAIL>!</strong></p>";
            echo "</div>";
        } else {
            echo "❌ Email sending failed<br>";
        }
        
    } catch (Exception $e) {
        echo "❌ Email error: " . $e->getMessage() . "<br>";
        echo "Error details: " . $e->getTraceAsString() . "<br>";
    }
} else {
    echo "❌ Cannot test email - missing requirements<br>";
}

// Test 6: Functions
echo "<h2>Test 6: Banking Functions</h2>";
if (function_exists('sendEmailSMTP')) {
    echo "✅ sendEmailSMTP function exists<br>";
} else {
    echo "❌ sendEmailSMTP function not found<br>";
}

if (function_exists('sendWelcomeEmail')) {
    echo "✅ sendWelcomeEmail function exists<br>";
} else {
    echo "❌ sendWelcomeEmail function not found<br>";
}

if (function_exists('generateOTP')) {
    echo "✅ generateOTP function exists<br>";
    $test_otp = generateOTP();
    echo "✅ Generated OTP: <strong>$test_otp</strong><br>";
} else {
    echo "❌ generateOTP function not found<br>";
}

// Test 7: Database
echo "<h2>Test 7: Database Connection</h2>";
try {
    if (file_exists('config/database.php')) {
        require_once 'config/database.php';
        echo "✅ database.php loaded<br>";
        
        if (function_exists('getDB')) {
            $db = getDB();
            echo "✅ Database connection successful<br>";
        } else {
            echo "❌ getDB function not found<br>";
        }
    } else {
        echo "❌ database.php not found<br>";
    }
} catch (Exception $e) {
    echo "❌ Database error: " . $e->getMessage() . "<br>";
}

echo "<hr>";
echo "<h2>📊 Summary</h2>";
echo "<p><strong>Test completed at:</strong> " . date('Y-m-d H:i:s') . "</p>";
echo "<p>If you see this message, PHP is working and the basic test completed.</p>";
echo "<p><strong>Check your <NAME_EMAIL> for the test message!</strong></p>";

// Show any PHP errors
if (function_exists('error_get_last')) {
    $last_error = error_get_last();
    if ($last_error) {
        echo "<h3>⚠️ Last PHP Error:</h3>";
        echo "<pre>" . print_r($last_error, true) . "</pre>";
    }
}
?>
