<?php
/**
 * Debug Session Data
 */

// Start session
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

header('Content-Type: application/json');

echo json_encode([
    'session_id' => session_id(),
    'session_data' => $_SESSION,
    'current_time' => time(),
    'user_id' => $_SESSION['user_id'] ?? 'not set',
    'wire_transfer_otp' => $_SESSION['wire_transfer_otp'] ?? 'not set',
    'wire_transfer_otp_expires' => $_SESSION['wire_transfer_otp_expires'] ?? 'not set',
    'wire_transfer_data' => $_SESSION['wire_transfer_data'] ?? 'not set'
]);

exit();
