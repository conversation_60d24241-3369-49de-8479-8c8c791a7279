<?php
// Test OTP generation directly
session_start();

// Set user session for testing
$_SESSION['user_id'] = 1;

// Include required files
require_once 'config/config.php';
require_once 'config/email.php';

// Set JSON response header
header('Content-Type: application/json');

try {
    $db = getDB();
    $user_id = $_SESSION['user_id'];
    
    // Get user information
    $user_query = "SELECT * FROM accounts WHERE id = ?";
    $user_result = $db->query($user_query, [$user_id]);
    $user = $user_result->fetch_assoc();
    
    if (!$user) {
        throw new Exception('User not found');
    }
    
    // Test data
    $input = [
        'amount' => 100,
        'purpose_of_payment' => 'family_support',
        'beneficiary_account_name' => '<PERSON>'
    ];
    
    // Generate 6-digit OTP
    $otp = sprintf('%06d', mt_rand(0, 999999));
    
    // Store OTP in session with expiration
    $_SESSION['wire_transfer_otp'] = $otp;
    $_SESSION['wire_transfer_otp_expires'] = time() + 300; // 5 minutes
    $_SESSION['wire_transfer_data'] = $input; // Store transfer data for processing
    
    // Prepare email content
    $subject = 'Wire Transfer OTP Verification - ' . APP_NAME;
    $amount = formatCurrency($input['amount'] ?? 0, $user['currency']);
    
    $message = "
    <div style='font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;'>
        <div style='background: linear-gradient(135deg, #007bff, #6610f2); padding: 30px; text-align: center; color: white;'>
            <h1 style='margin: 0; font-size: 24px;'>Wire Transfer Verification</h1>
        </div>
        
        <div style='padding: 30px; background-color: #f8f9fa;'>
            <h2 style='color: #333; margin-bottom: 20px;'>OTP Verification Required</h2>
            
            <p>Dear {$user['first_name']} {$user['last_name']},</p>
            
            <p>You have initiated a wire transfer with the following details:</p>
            
            <div style='background: white; padding: 20px; border-radius: 8px; margin: 20px 0; border-left: 4px solid #007bff;'>
                <table style='width: 100%; border-collapse: collapse;'>
                    <tr>
                        <td style='padding: 8px 0; font-weight: bold; color: #555;'>Amount:</td>
                        <td style='padding: 8px 0; color: #333;'>{$amount}</td>
                    </tr>
                    <tr>
                        <td style='padding: 8px 0; font-weight: bold; color: #555;'>Transfer Type:</td>
                        <td style='padding: 8px 0; color: #333;'>Wire Transfer (International)</td>
                    </tr>
                    <tr>
                        <td style='padding: 8px 0; font-weight: bold; color: #555;'>Purpose:</td>
                        <td style='padding: 8px 0; color: #333;'>" . ucwords(str_replace('_', ' ', $input['purpose_of_payment'] ?? 'Not specified')) . "</td>
                    </tr>
                </table>
            </div>
            
            <div style='text-align: center; margin: 30px 0;'>
                <div style='background: #007bff; color: white; padding: 20px; border-radius: 8px; display: inline-block;'>
                    <h3 style='margin: 0 0 10px 0;'>Your OTP Code</h3>
                    <div style='font-size: 32px; font-weight: bold; letter-spacing: 8px; font-family: monospace;'>{$otp}</div>
                </div>
            </div>
            
            <div style='background: #fff3cd; border: 1px solid #ffeaa7; padding: 15px; border-radius: 8px; margin: 20px 0;'>
                <p style='margin: 0; color: #856404;'>
                    <strong>Security Notice:</strong> This OTP is valid for 5 minutes only. 
                    Do not share this code with anyone. Our staff will never ask for your OTP.
                </p>
            </div>
            
            <p>If you did not initiate this transfer, please contact our support team immediately.</p>
            
            <div style='text-align: center; margin-top: 30px;'>
                <p style='color: #666; font-size: 14px;'>
                    This is an automated message from " . APP_NAME . " Banking System<br>
                    Generated on " . date('Y-m-d H:i:s') . "
                </p>
            </div>
        </div>
    </div>
    ";
    
    // Send email using the existing email function
    if (sendEmailSMTP($user['email'], $subject, $message)) {
        echo json_encode([
            'success' => true,
            'message' => 'OTP sent successfully to your registered email address',
            'otp' => $otp // For testing only
        ]);
    } else {
        throw new Exception('Failed to send OTP email');
    }
    
} catch (Exception $e) {
    error_log("Generate wire OTP error: " . $e->getMessage());
    echo json_encode([
        'success' => false,
        'error' => 'Failed to generate OTP. Please try again.',
        'debug' => $e->getMessage()
    ]);
}
?>
