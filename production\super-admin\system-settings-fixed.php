<?php
/**
 * Super Admin System Settings - Fixed Version
 * Complete system configuration with logo/favicon upload including SVG support
 */

session_start();

// Include required files
require_once 'includes/auth.php';

// Require super admin authentication
requireSuperAdminAuth();

$page_title = 'System Settings';
$page_subtitle = 'Configure system-wide settings and contact information';

$success = '';
$errors = [];

// Function to get all settings
function getAllSettings() {
    try {
        require_once '../config/database.php';
        $db = getDB();

        $settings = [];
        $result = $db->query("SELECT setting_key, setting_value FROM super_admin_settings");
        
        if ($result) {
            while ($row = $result->fetch_assoc()) {
                $settings[$row['setting_key']] = $row['setting_value'];
            }
        }
        
        // Add defaults for missing settings
        $defaults = [
            'site_name' => 'SecureBank Online Banking',
            'site_url' => 'http://localhost/online_banking',
            'support_email' => '<EMAIL>',
            'support_phone' => '1-800-BANKING',
            'security_email' => '<EMAIL>',
            'security_phone' => '1-800-SECURITY',
            'admin_email' => '<EMAIL>',
            'noreply_email' => '<EMAIL>',
            'company_address' => '123 Banking Street, Financial District, NY 10001',
            'company_phone' => '1-800-MAIN-BANK',
            'email_footer_text' => 'Your trusted financial partner',
            'privacy_policy_url' => '',
            'terms_of_service_url' => '',
            'help_center_url' => '',
            'max_login_attempts' => '5',
            'session_timeout' => '30',
            'otp_expiry_minutes' => '10',
            'maintenance_mode' => '0',
            'maintenance_message' => 'System is under maintenance. Please try again later.',
            'site_logo' => '',
            'site_favicon' => ''
        ];
        
        foreach ($defaults as $key => $default_value) {
            if (!isset($settings[$key])) {
                $settings[$key] = $default_value;
            }
        }
        
        return $settings;
    } catch (Exception $e) {
        error_log("Error getting settings: " . $e->getMessage());
        return [];
    }
}

// Function to update setting
function updateSetting($key, $value, $reason = '') {
    try {
        require_once '../config/database.php';
        $db = getDB();

        $sql = "INSERT INTO super_admin_settings (setting_key, setting_value, setting_description)
                VALUES (?, ?, ?)
                ON DUPLICATE KEY UPDATE
                setting_value = VALUES(setting_value),
                updated_at = CURRENT_TIMESTAMP";

        $result = $db->query($sql, [$key, $value, $reason]);
        
        // Simple logging (optional)
        if ($result) {
            try {
                logSuperAdminAction('setting_update', "Updated setting: $key");
            } catch (Exception $e) {
                // Ignore logging errors
            }
        }
        
        return $result;
    } catch (Exception $e) {
        error_log("Error updating setting: " . $e->getMessage());
        return false;
    }
}

// Handle form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    try {
        // Handle file uploads first
        $upload_dir = '../assets/uploads/';
        if (!is_dir($upload_dir)) {
            mkdir($upload_dir, 0755, true);
        }

        // Handle logo upload (including SVG support)
        if (isset($_FILES['site_logo']) && $_FILES['site_logo']['error'] === UPLOAD_ERR_OK) {
            $logo_info = pathinfo($_FILES['site_logo']['name']);
            $logo_extension = strtolower($logo_info['extension']);

            if (in_array($logo_extension, ['jpg', 'jpeg', 'png', 'gif', 'svg'])) {
                $logo_filename = 'logo.' . $logo_extension;
                $logo_path = $upload_dir . $logo_filename;

                if (move_uploaded_file($_FILES['site_logo']['tmp_name'], $logo_path)) {
                    updateSetting('site_logo', 'assets/uploads/' . $logo_filename, 'Logo uploaded via super admin panel');
                    $success = "Logo uploaded successfully!";
                }
            } else {
                $errors[] = "Invalid logo file format. Please use JPG, PNG, GIF, or SVG.";
            }
        }

        // Handle favicon upload (including SVG support)
        if (isset($_FILES['site_favicon']) && $_FILES['site_favicon']['error'] === UPLOAD_ERR_OK) {
            $favicon_info = pathinfo($_FILES['site_favicon']['name']);
            $favicon_extension = strtolower($favicon_info['extension']);

            if (in_array($favicon_extension, ['ico', 'png', 'jpg', 'jpeg', 'svg'])) {
                $favicon_filename = 'favicon.' . $favicon_extension;
                $favicon_path = $upload_dir . $favicon_filename;

                if (move_uploaded_file($_FILES['site_favicon']['tmp_name'], $favicon_path)) {
                    updateSetting('site_favicon', 'assets/uploads/' . $favicon_filename, 'Favicon uploaded via super admin panel');
                    $success = "Favicon uploaded successfully!";
                }
            } else {
                $errors[] = "Invalid favicon file format. Please use ICO, PNG, JPG, JPEG, or SVG.";
            }
        }

        // Handle text settings
        $settings_to_update = [
            'site_name', 'site_url', 'support_email', 'support_phone',
            'security_email', 'security_phone', 'noreply_email', 'admin_email',
            'company_address', 'company_phone', 'email_footer_text',
            'privacy_policy_url', 'terms_of_service_url', 'help_center_url',
            'max_login_attempts', 'session_timeout', 'otp_expiry_minutes',
            'maintenance_message'
        ];
        
        // Handle maintenance mode checkbox
        $maintenance_mode = isset($_POST['maintenance_mode']) ? '1' : '0';
        
        $updated_count = 0;
        foreach ($settings_to_update as $key) {
            if (isset($_POST[$key])) {
                $value = $_POST[$key];
                
                // Basic validation
                if ($key === 'site_name' && empty(trim($value))) {
                    $errors[] = "Site name cannot be empty";
                    continue;
                }
                
                if (in_array($key, ['support_email', 'security_email', 'admin_email', 'noreply_email'])) {
                    if (!filter_var($value, FILTER_VALIDATE_EMAIL)) {
                        $errors[] = "Invalid email format for $key";
                        continue;
                    }
                }
                
                if (updateSetting($key, $value, $_POST['change_reason'] ?? 'Settings update via super admin panel')) {
                    $updated_count++;
                }
            }
        }
        
        // Update maintenance mode
        if (updateSetting('maintenance_mode', $maintenance_mode, $_POST['change_reason'] ?? 'Settings update via super admin panel')) {
            $updated_count++;
        }
        
        if (empty($errors) && $updated_count > 0) {
            $success = "Successfully updated $updated_count settings.";
        }
        
    } catch (Exception $e) {
        $errors[] = "Error updating settings: " . $e->getMessage();
    }
}

// Get current settings
$current_settings = getAllSettings();

// Include header
include 'includes/header.php';
?>

<!-- Success/Error Messages -->
<?php if ($success): ?>
    <div class="alert alert-success alert-dismissible fade show">
        <i class="fas fa-check-circle"></i> <?php echo htmlspecialchars($success); ?>
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
<?php endif; ?>

<?php if (!empty($errors)): ?>
    <div class="alert alert-danger alert-dismissible fade show">
        <i class="fas fa-exclamation-circle"></i> 
        <strong>Errors:</strong>
        <ul class="mb-0 mt-2">
            <?php foreach ($errors as $error): ?>
                <li><?php echo htmlspecialchars($error); ?></li>
            <?php endforeach; ?>
        </ul>
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
<?php endif; ?>

<form method="POST" class="needs-validation" novalidate enctype="multipart/form-data">
    <!-- Site Configuration -->
    <div class="card border-0 shadow-sm mb-4">
        <div class="card-header bg-primary text-white">
            <h5 class="card-title mb-0">
                <i class="fas fa-globe"></i> Site Configuration
            </h5>
        </div>
        <div class="card-body">
            <div class="row">
                <div class="col-md-6">
                    <div class="mb-3">
                        <label for="site_name" class="form-label">Site Name</label>
                        <input type="text" class="form-control" id="site_name" name="site_name" 
                               value="<?php echo htmlspecialchars($current_settings['site_name'] ?? ''); ?>" required>
                        <div class="form-text">Name displayed throughout the system and in emails</div>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="mb-3">
                        <label for="site_url" class="form-label">Site URL</label>
                        <input type="url" class="form-control" id="site_url" name="site_url" 
                               value="<?php echo htmlspecialchars($current_settings['site_url'] ?? ''); ?>" required>
                        <div class="form-text">Base URL of your banking platform</div>
                    </div>
                </div>
            </div>
            
            <div class="mb-3">
                <label for="email_footer_text" class="form-label">Email Footer Text</label>
                <input type="text" class="form-control" id="email_footer_text" name="email_footer_text"
                       value="<?php echo htmlspecialchars($current_settings['email_footer_text'] ?? ''); ?>">
                <div class="form-text">Text displayed in email footers</div>
            </div>
        </div>
    </div>

    <!-- Branding & Assets -->
    <div class="card border-0 shadow-sm mb-4">
        <div class="card-header bg-dark text-white">
            <h5 class="card-title mb-0">
                <i class="fas fa-image"></i> Branding & Assets
            </h5>
        </div>
        <div class="card-body">
            <div class="row">
                <div class="col-md-6">
                    <div class="mb-3">
                        <label for="site_logo" class="form-label">Site Logo</label>
                        <input type="file" class="form-control" id="site_logo" name="site_logo" accept=".jpg,.jpeg,.png,.gif,.svg">
                        <div class="form-text">Upload JPG, PNG, GIF, or SVG. Recommended size: 200x60px</div>

                        <?php if (!empty($current_settings['site_logo'])): ?>
                            <div class="mt-2">
                                <small class="text-muted">Current logo:</small><br>
                                <?php if (pathinfo($current_settings['site_logo'], PATHINFO_EXTENSION) === 'svg'): ?>
                                    <object data="../<?php echo htmlspecialchars($current_settings['site_logo']); ?>"
                                            type="image/svg+xml" style="max-height: 50px; max-width: 200px;" class="border rounded">
                                        <img src="../<?php echo htmlspecialchars($current_settings['site_logo']); ?>"
                                             alt="Current Logo" style="max-height: 50px; max-width: 200px;" class="border rounded">
                                    </object>
                                <?php else: ?>
                                    <img src="../<?php echo htmlspecialchars($current_settings['site_logo']); ?>"
                                         alt="Current Logo" style="max-height: 50px; max-width: 200px;" class="border rounded">
                                <?php endif; ?>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="mb-3">
                        <label for="site_favicon" class="form-label">Site Favicon</label>
                        <input type="file" class="form-control" id="site_favicon" name="site_favicon" accept=".ico,.png,.jpg,.jpeg,.svg">
                        <div class="form-text">Upload ICO, PNG, JPG, JPEG, or SVG. Recommended size: 32x32px or 16x16px</div>

                        <?php if (!empty($current_settings['site_favicon'])): ?>
                            <div class="mt-2">
                                <small class="text-muted">Current favicon:</small><br>
                                <?php if (pathinfo($current_settings['site_favicon'], PATHINFO_EXTENSION) === 'svg'): ?>
                                    <object data="../<?php echo htmlspecialchars($current_settings['site_favicon']); ?>"
                                            type="image/svg+xml" style="max-height: 32px; max-width: 32px;" class="border rounded">
                                        <img src="../<?php echo htmlspecialchars($current_settings['site_favicon']); ?>"
                                             alt="Current Favicon" style="max-height: 32px; max-width: 32px;" class="border rounded">
                                    </object>
                                <?php else: ?>
                                    <img src="../<?php echo htmlspecialchars($current_settings['site_favicon']); ?>"
                                         alt="Current Favicon" style="max-height: 32px; max-width: 32px;" class="border rounded">
                                <?php endif; ?>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>

            <div class="alert alert-info">
                <i class="fas fa-info-circle"></i>
                <strong>Note:</strong> After uploading new branding assets, they will be automatically applied throughout the system.
                Clear your browser cache if you don't see changes immediately. SVG files are supported for modern browsers.
            </div>
        </div>
    </div>

    <!-- Contact Information -->
    <div class="card border-0 shadow-sm mb-4">
        <div class="card-header bg-success text-white">
            <h5 class="card-title mb-0">
                <i class="fas fa-address-book"></i> Contact Information
            </h5>
        </div>
        <div class="card-body">
            <div class="row">
                <div class="col-md-6">
                    <div class="mb-3">
                        <label for="support_email" class="form-label">Support Email</label>
                        <input type="email" class="form-control" id="support_email" name="support_email"
                               value="<?php echo htmlspecialchars($current_settings['support_email'] ?? ''); ?>" required>
                    </div>

                    <div class="mb-3">
                        <label for="security_email" class="form-label">Security Email</label>
                        <input type="email" class="form-control" id="security_email" name="security_email"
                               value="<?php echo htmlspecialchars($current_settings['security_email'] ?? ''); ?>" required>
                    </div>

                    <div class="mb-3">
                        <label for="admin_email" class="form-label">Admin Email</label>
                        <input type="email" class="form-control" id="admin_email" name="admin_email"
                               value="<?php echo htmlspecialchars($current_settings['admin_email'] ?? ''); ?>" required>
                    </div>

                    <div class="mb-3">
                        <label for="noreply_email" class="form-label">No-Reply Email</label>
                        <input type="email" class="form-control" id="noreply_email" name="noreply_email"
                               value="<?php echo htmlspecialchars($current_settings['noreply_email'] ?? ''); ?>" required>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="mb-3">
                        <label for="support_phone" class="form-label">Support Phone</label>
                        <input type="text" class="form-control" id="support_phone" name="support_phone"
                               value="<?php echo htmlspecialchars($current_settings['support_phone'] ?? ''); ?>" required>
                    </div>

                    <div class="mb-3">
                        <label for="security_phone" class="form-label">Security Phone</label>
                        <input type="text" class="form-control" id="security_phone" name="security_phone"
                               value="<?php echo htmlspecialchars($current_settings['security_phone'] ?? ''); ?>" required>
                    </div>

                    <div class="mb-3">
                        <label for="company_phone" class="form-label">Company Phone</label>
                        <input type="text" class="form-control" id="company_phone" name="company_phone"
                               value="<?php echo htmlspecialchars($current_settings['company_phone'] ?? ''); ?>">
                    </div>
                </div>
            </div>

            <div class="mb-3">
                <label for="company_address" class="form-label">Company Address</label>
                <textarea class="form-control" id="company_address" name="company_address" rows="2"><?php echo htmlspecialchars($current_settings['company_address'] ?? ''); ?></textarea>
            </div>
        </div>
    </div>

    <!-- System Settings -->
    <div class="card border-0 shadow-sm mb-4">
        <div class="card-header bg-warning text-dark">
            <h5 class="card-title mb-0">
                <i class="fas fa-cogs"></i> System Settings
            </h5>
        </div>
        <div class="card-body">
            <div class="row">
                <div class="col-md-4">
                    <div class="mb-3">
                        <label for="max_login_attempts" class="form-label">Max Login Attempts</label>
                        <input type="number" class="form-control" id="max_login_attempts" name="max_login_attempts"
                               value="<?php echo htmlspecialchars($current_settings['max_login_attempts'] ?? '5'); ?>" min="1" max="10">
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="mb-3">
                        <label for="session_timeout" class="form-label">Session Timeout (minutes)</label>
                        <input type="number" class="form-control" id="session_timeout" name="session_timeout"
                               value="<?php echo htmlspecialchars($current_settings['session_timeout'] ?? '30'); ?>" min="5" max="120">
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="mb-3">
                        <label for="otp_expiry_minutes" class="form-label">OTP Expiry (minutes)</label>
                        <input type="number" class="form-control" id="otp_expiry_minutes" name="otp_expiry_minutes"
                               value="<?php echo htmlspecialchars($current_settings['otp_expiry_minutes'] ?? '10'); ?>" min="1" max="30">
                    </div>
                </div>
            </div>

            <div class="mb-3">
                <div class="form-check">
                    <input type="checkbox" class="form-check-input" id="maintenance_mode" name="maintenance_mode" value="1"
                           <?php echo ($current_settings['maintenance_mode'] ?? false) ? 'checked' : ''; ?>>
                    <label class="form-check-label" for="maintenance_mode">
                        <strong>Maintenance Mode</strong> - Temporarily disable user access
                    </label>
                </div>
            </div>

            <div class="mb-3">
                <label for="maintenance_message" class="form-label">Maintenance Message</label>
                <textarea class="form-control" id="maintenance_message" name="maintenance_message" rows="2"><?php echo htmlspecialchars($current_settings['maintenance_message'] ?? ''); ?></textarea>
            </div>
        </div>
    </div>

    <!-- URLs Configuration -->
    <div class="card border-0 shadow-sm mb-4">
        <div class="card-header bg-info text-white">
            <h5 class="card-title mb-0">
                <i class="fas fa-link"></i> URLs Configuration
            </h5>
        </div>
        <div class="card-body">
            <div class="row">
                <div class="col-md-4">
                    <div class="mb-3">
                        <label for="privacy_policy_url" class="form-label">Privacy Policy URL</label>
                        <input type="url" class="form-control" id="privacy_policy_url" name="privacy_policy_url"
                               value="<?php echo htmlspecialchars($current_settings['privacy_policy_url'] ?? ''); ?>">
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="mb-3">
                        <label for="terms_of_service_url" class="form-label">Terms of Service URL</label>
                        <input type="url" class="form-control" id="terms_of_service_url" name="terms_of_service_url"
                               value="<?php echo htmlspecialchars($current_settings['terms_of_service_url'] ?? ''); ?>">
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="mb-3">
                        <label for="help_center_url" class="form-label">Help Center URL</label>
                        <input type="url" class="form-control" id="help_center_url" name="help_center_url"
                               value="<?php echo htmlspecialchars($current_settings['help_center_url'] ?? ''); ?>">
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Change Reason -->
    <div class="card border-0 shadow-sm mb-4">
        <div class="card-header bg-secondary text-white">
            <h5 class="card-title mb-0">
                <i class="fas fa-edit"></i> Change Information
            </h5>
        </div>
        <div class="card-body">
            <div class="mb-3">
                <label for="change_reason" class="form-label">Reason for Changes (Optional)</label>
                <input type="text" class="form-control" id="change_reason" name="change_reason"
                       placeholder="e.g., Updated contact information, Security enhancement, etc.">
                <div class="form-text">This will be logged for audit purposes</div>
            </div>
        </div>
    </div>

    <!-- Action Buttons -->
    <div class="d-flex justify-content-between">
        <a href="dashboard.php" class="btn btn-outline-secondary">
            <i class="fas fa-arrow-left"></i> Back to Dashboard
        </a>

        <div>
            <button type="submit" class="btn btn-primary btn-lg">
                <i class="fas fa-save"></i> Update Settings
            </button>
            <a href="smtp-config.php" class="btn btn-outline-warning btn-lg ms-2">
                <i class="fas fa-server"></i> SMTP Config
            </a>
        </div>
    </div>
</form>

<script>
// Form validation
(function() {
    'use strict';
    window.addEventListener('load', function() {
        var forms = document.getElementsByClassName('needs-validation');
        var validation = Array.prototype.filter.call(forms, function(form) {
            form.addEventListener('submit', function(event) {
                if (form.checkValidity() === false) {
                    event.preventDefault();
                    event.stopPropagation();
                }
                form.classList.add('was-validated');
            }, false);
        });
    }, false);
})();

// Maintenance mode warning
document.getElementById('maintenance_mode').addEventListener('change', function() {
    if (this.checked) {
        if (!confirm('Are you sure you want to enable maintenance mode? This will prevent users from accessing the system.')) {
            this.checked = false;
        }
    }
});
</script>

<?php
// Include footer
include 'includes/footer.php';
?>
