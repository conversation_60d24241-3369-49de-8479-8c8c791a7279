<?php
session_start();
require_once '../config/config.php';

// Check if user is in OTP verification process
if (!isset($_SESSION['admin_otp_user_id'])) {
    redirect('admin/login.php');
}

// Check if 2FA is still required (in case settings changed during login process)
$security_settings = getSecuritySettings();
$require_2fa_for_admin = $security_settings['require_2fa_for_admin'] ?? 1;

if (!$require_2fa_for_admin) {
    // 2FA has been disabled, complete login directly
    $user_id = $_SESSION['admin_otp_user_id'];

    try {
        $db = getDB();
        $sql = "SELECT id, username, first_name, last_name, email, is_admin, status
                FROM accounts WHERE id = ? AND is_admin = 1 AND status = 'active'";

        $result = $db->query($sql, [$user_id]);

        if ($result && $result->num_rows === 1) {
            $user = $result->fetch_assoc();

            // Clear any existing user session first
            clearUserSession();

            // Set admin session variables with admin session flag
            $_SESSION['user_id'] = $user['id'];
            $_SESSION['username'] = $user['username'];
            $_SESSION['first_name'] = $user['first_name'];
            $_SESSION['last_name'] = $user['last_name'];
            $_SESSION['account_number'] = '**********'; // Admin account number
            $_SESSION['is_admin'] = true;
            $_SESSION['is_admin_session'] = true; // Flag to distinguish admin sessions
            $_SESSION['last_activity'] = time();

            // Clear OTP session variables
            unset($_SESSION['admin_otp_user_id']);
            unset($_SESSION['admin_otp_email']);
            unset($_SESSION['admin_otp_name']);
            unset($_SESSION['admin_email_failed']);

            // Update last login time
            $db->query("UPDATE accounts SET last_login = NOW() WHERE id = ?", [$user['id']]);

            // Log activity
            logActivity($user['id'], 'Admin login completed (2FA disabled during verification)', 'accounts', $user['id']);

            // Redirect to admin dashboard
            redirect('admin/');
        }
    } catch (Exception $e) {
        error_log("Admin OTP bypass error: " . $e->getMessage());
        // Continue with normal OTP verification if there's an error
    }
}

$page_title = 'Admin OTP Verification';
$errors = [];
$success = '';
$user_id = $_SESSION['admin_otp_user_id'];
$user_email = $_SESSION['admin_otp_email'] ?? '';
$user_name = $_SESSION['admin_otp_name'] ?? '';
$email_failed = isset($_SESSION['admin_email_failed']) && $_SESSION['admin_email_failed'];

// Get site settings for logo, name and favicon
function getSiteSettings() {
    try {
        require_once '../config/database.php';
        $db = getDB();

        $settings = [];
        $result = $db->query("SELECT setting_key, setting_value FROM super_admin_settings WHERE setting_key IN ('site_name', 'site_logo', 'site_favicon')");

        if ($result) {
            while ($row = $result->fetch_assoc()) {
                $settings[$row['setting_key']] = $row['setting_value'];
            }
        }

        return [
            'site_name' => $settings['site_name'] ?? 'SecureBank Online Banking',
            'site_logo' => $settings['site_logo'] ?? '',
            'site_favicon' => $settings['site_favicon'] ?? ''
        ];
    } catch (Exception $e) {
        error_log("Error getting site settings: " . $e->getMessage());
        return [
            'site_name' => 'SecureBank Online Banking',
            'site_logo' => '',
            'site_favicon' => ''
        ];
    }
}

$site_settings = getSiteSettings();

// Helper function to mask email address
function maskEmail($email) {
    if (empty($email)) {
        return '';
    }

    $parts = explode('@', $email);
    if (count($parts) !== 2) {
        return $email;
    }

    $username = $parts[0];
    $domain = $parts[1];

    if (strlen($username) <= 2) {
        return $username . '@' . $domain;
    }

    $masked_username = substr($username, 0, 2) . str_repeat('*', strlen($username) - 2);
    return $masked_username . '@' . $domain;
}

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $otp = sanitizeInput($_POST['otp'] ?? '');
    
    if (empty($otp)) {
        $errors[] = 'Please enter the verification code.';
    }
    
    if (strlen($otp) !== 6 || !ctype_digit($otp)) {
        $errors[] = 'Please enter a valid 6-digit verification code.';
    }
    
    if (empty($errors)) {
        try {
            if (verifyOTP($user_id, $otp)) {
                // OTP verified successfully - complete admin login
                $db = getDB();
                $sql = "SELECT id, username, first_name, last_name, email, is_admin, status
                        FROM accounts WHERE id = ? AND is_admin = 1 AND status = 'active'";
                
                $result = $db->query($sql, [$user_id]);
                
                if ($result && $result->num_rows === 1) {
                    $user = $result->fetch_assoc();
                    
                    // Clear any existing user session first
                    clearUserSession();

                    // Set admin session variables with admin session flag
                    $_SESSION['user_id'] = $user['id'];
                    $_SESSION['username'] = $user['username'];
                    $_SESSION['first_name'] = $user['first_name'];
                    $_SESSION['last_name'] = $user['last_name'];
                    $_SESSION['account_number'] = '**********'; // Admin account number
                    $_SESSION['is_admin'] = true;
                    $_SESSION['is_admin_session'] = true; // Flag to distinguish admin sessions
                    $_SESSION['last_activity'] = time();
                    
                    // Clear OTP session variables
                    unset($_SESSION['admin_otp_user_id']);
                    unset($_SESSION['admin_otp_email']);
                    unset($_SESSION['admin_otp_name']);
                    unset($_SESSION['admin_email_failed']);
                    
                    // Update last login time
                    $db->query("UPDATE accounts SET last_login = NOW() WHERE id = ?", [$user['id']]);
                    
                    // Log activity
                    logActivity($user['id'], 'Admin completed OTP verification and logged in');

                    // Redirect to admin dashboard
                    redirect('admin/');
                } else {
                    $errors[] = 'Admin account not found or inactive.';
                }
            } else {
                $errors[] = 'Invalid or expired verification code. Please try again.';
            }
            
        } catch (Exception $e) {
            error_log("Admin OTP verification error: " . $e->getMessage());
            $errors[] = 'Verification failed. Please try again.';
        }
    }
}

// Handle resend OTP request
if (isset($_GET['resend']) && $_GET['resend'] === '1') {
    try {
        $db = getDB();
        $sql = "SELECT email, first_name, last_name FROM accounts WHERE id = ? AND is_admin = 1";
        $result = $db->query($sql, [$user_id]);

        if ($result && $result->num_rows === 1) {
            $user = $result->fetch_assoc();
            $otp = generateOTP();

            if (storeOTP($user_id, $otp, 10, 'admin')) {
                $emailSent = sendOTPEmail($user['email'], $otp, $user['first_name'] . ' ' . $user['last_name']);

                if ($emailSent) {
                    $success = 'A new verification code has been sent to your email.';
                    unset($_SESSION['admin_email_failed']); // Clear email failed flag
                } else {
                    $_SESSION['admin_email_failed'] = true;
                    $success = 'A new verification code has been generated.';
                }
            } else {
                $errors[] = 'Failed to generate verification code. Please try again.';
            }
        }
    } catch (Exception $e) {
        error_log("Admin OTP resend error: " . $e->getMessage());
        $errors[] = 'Failed to resend verification code. Please try again.';
    }
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8"/>
    <meta name="viewport" content="width=device-width, initial-scale=1, viewport-fit=cover"/>
    <meta http-equiv="X-UA-Compatible" content="ie=edge"/>
    <title><?php echo htmlspecialchars($page_title); ?> - <?php echo APP_NAME; ?></title>

    <!-- Favicon -->
    <?php if (!empty($site_settings['site_favicon']) && file_exists('../' . $site_settings['site_favicon'])): ?>
        <link rel="icon" type="image/x-icon" href="../<?php echo htmlspecialchars($site_settings['site_favicon']); ?>">
    <?php else: ?>
        <link rel="icon" type="image/x-icon" href="../assets/img/favicon.ico">
    <?php endif; ?>

    <!-- CSS files -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f8f9fa;
            min-height: 100vh;
            margin: 0;
            padding: 0;
        }

        .container-fluid {
            min-height: 100vh;
            padding: 0;
        }

        .row {
            min-height: 100vh;
            margin: 0;
        }

        .left-panel {
            background: white;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: flex-start;
            padding: 60px 80px;
            position: relative;
        }

        .back-link {
            position: absolute;
            top: 20px;
            left: 20px;
            color: #6b7280;
            text-decoration: none;
            font-size: 14px;
            font-weight: 500;
            transition: color 0.2s ease;
        }

        .back-link:hover {
            color: #dc2626;
        }

        .logo {
            margin-bottom: 40px;
            text-align: left;
            width: 100%;
        }

        .logo img {
            max-width: 200px;
            height: auto;
            max-height: 80px;
            object-fit: contain;
            background: transparent;
            mix-blend-mode: multiply;
        }

        .logo-fallback {
            font-size: 24px;
            font-weight: 700;
            color: #dc2626;
            margin: 0;
        }

        .welcome-text {
            text-align: left;
            margin-bottom: 40px;
            width: 100%;
        }

        .welcome-text h1 {
            font-size: 28px;
            font-weight: 600;
            color: #1a1a1a;
            margin-bottom: 10px;
        }

        .welcome-text p {
            color: #6b7280;
            font-size: 16px;
            margin: 0;
        }

        .login-form {
            width: 100%;
            max-width: 400px;
            text-align: left;
        }

        .form-group {
            margin-bottom: 20px;
            width: 100%;
        }

        .form-label {
            font-weight: 500;
            color: #374151;
            margin-bottom: 8px;
            display: block;
            font-size: 14px;
        }

        .form-control {
            border: 1px solid #d1d5db;
            border-radius: 8px;
            padding: 12px 16px;
            font-size: 16px;
            transition: all 0.2s ease;
            background: white;
            width: 100%;
            box-sizing: border-box;
        }

        .form-control:focus {
            border-color: #dc2626;
            box-shadow: 0 0 0 3px rgba(220, 38, 38, 0.1);
            outline: none;
        }

        .btn-login {
            background: #dc2626;
            border: none;
            color: white;
            padding: 12px 24px;
            border-radius: 8px;
            font-weight: 500;
            font-size: 16px;
            width: 100%;
            transition: all 0.2s ease;
            cursor: pointer;
            box-sizing: border-box;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
        }

        .btn-login:hover {
            background: #b91c1c;
            transform: translateY(-1px);
        }

        .alert {
            border-radius: 8px;
            border: 1px solid #e5e7eb;
            padding: 12px 16px;
            margin-bottom: 20px;
        }

        .alert-danger {
            background: #fef2f2;
            color: #dc2626;
            border-color: #fecaca;
        }

        .alert-success {
            background: #f0fdf4;
            color: #16a34a;
            border-color: #bbf7d0;
        }

        .alert-warning {
            background: #fffbeb;
            color: #d97706;
            border-color: #fed7aa;
        }

        .security-notice {
            background: #f9fafb;
            border-radius: 8px;
            padding: 16px;
            margin-top: 20px;
            text-align: left;
            border: 1px solid #e5e7eb;
        }

        .security-notice h6 {
            color: #374151;
            font-weight: 600;
            margin-bottom: 8px;
            font-size: 14px;
        }

        .security-notice p {
            color: #6b7280;
            font-size: 13px;
            margin: 0;
            line-height: 1.5;
        }

        .btn-resend {
            background: transparent;
            border: 1px solid #d1d5db;
            color: #6b7280;
            padding: 10px 20px;
            border-radius: 8px;
            font-weight: 500;
            font-size: 14px;
            width: 100%;
            transition: all 0.2s ease;
            cursor: pointer;
            margin-top: 15px;
            text-decoration: none;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
        }

        .btn-resend:hover {
            border-color: #dc2626;
            color: #dc2626;
        }

        .right-panel {
            background: linear-gradient(135deg, #dc2626 0%, #b91c1c 50%, #991b1b 100%);
            position: relative;
            overflow: hidden;
            display: flex;
            flex-direction: column;
        }

        .right-panel::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-image: url('../demo-images/superadmin_background.jpg');
            background-size: cover;
            background-position: center;
            background-repeat: no-repeat;
            opacity: 0.15;
            z-index: 1;
        }

        .right-panel::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><circle cx="20" cy="20" r="2" fill="rgba(255,255,255,0.1)"/><circle cx="80" cy="30" r="1.5" fill="rgba(255,255,255,0.1)"/><circle cx="30" cy="80" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="70" cy="70" r="1" fill="rgba(255,255,255,0.1)"/></svg>');
            opacity: 0.2;
            z-index: 2;
        }

        .right-panel-content {
            display: flex;
            flex-direction: column;
            height: 100%;
            z-index: 3;
            position: relative;
            padding: 40px;
        }

        .feature-illustration {
            flex: 1;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 40px;
        }

        .bottom-text {
            flex: 0 0 auto;
            text-align: center;
            color: white;
        }

        .bottom-text h3 {
            font-size: 20px;
            font-weight: 600;
            margin-bottom: 10px;
            opacity: 0.95;
        }

        .bottom-text p {
            font-size: 14px;
            opacity: 0.8;
            margin: 0;
            line-height: 1.5;
        }

        @media (max-width: 768px) {
            .left-panel {
                padding: 40px 30px;
            }

            .right-panel {
                display: none;
            }

            .welcome-text h1 {
                font-size: 24px;
            }

            .logo img {
                max-width: 150px;
                max-height: 60px;
            }
        }

        @media (max-width: 576px) {
            .left-panel {
                padding: 30px 20px;
            }

            .welcome-text h1 {
                font-size: 22px;
            }
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- Left Panel - OTP Verification Form -->
            <div class="col-md-5 left-panel">
                <a href="login.php" class="back-link">
                    <i class="fas fa-arrow-left"></i> Back to Login
                </a>

                <!-- Logo -->
                <div class="logo">
                    <?php if (!empty($site_settings['site_logo']) && file_exists('../' . $site_settings['site_logo'])): ?>
                        <img src="../<?php echo htmlspecialchars($site_settings['site_logo']); ?>"
                             alt="<?php echo htmlspecialchars($site_settings['site_name']); ?>"
                             onerror="this.style.display='none'; this.nextElementSibling.style.display='block';">
                        <h2 class="logo-fallback" style="display: none;"><?php echo htmlspecialchars($site_settings['site_name']); ?></h2>
                    <?php else: ?>
                        <h2 class="logo-fallback"><?php echo htmlspecialchars($site_settings['site_name']); ?></h2>
                    <?php endif; ?>
                </div>

                <!-- Welcome Text -->
                <div class="welcome-text">
                    <h1>Admin Verification Required</h1>
                    <p>Please enter the 6-digit code sent to your email</p>
                </div>

                <!-- OTP Verification Form -->
                <div class="login-form">
                    <?php if (!empty($errors)): ?>
                        <div class="alert alert-danger">
                            <i class="fas fa-exclamation-triangle"></i>
                            <strong>Verification Failed:</strong>
                            <ul style="margin: 8px 0 0 0; padding-left: 20px;">
                                <?php foreach ($errors as $error): ?>
                                <li><?php echo htmlspecialchars($error); ?></li>
                                <?php endforeach; ?>
                            </ul>
                        </div>
                    <?php endif; ?>

                    <?php if (!empty($success)): ?>
                        <div class="alert alert-success">
                            <i class="fas fa-check-circle"></i>
                            <?php echo htmlspecialchars($success); ?>
                        </div>
                    <?php endif; ?>

                    <?php if ($email_failed): ?>
                        <div class="alert alert-warning">
                            <i class="fas fa-exclamation-triangle"></i>
                            <strong>Email Delivery Failed:</strong> The verification code was generated but could not be sent to your email. Please contact support or check with an administrator for the code.
                        </div>
                    <?php endif; ?>

                    <form method="POST" action="">
                        <div class="form-group">
                            <label for="otp" class="form-label">Verification Code</label>
                            <input type="text"
                                   class="form-control"
                                   id="otp"
                                   name="otp"
                                   placeholder="Enter 6-digit code"
                                   maxlength="6"
                                   pattern="[0-9]{6}"
                                   required
                                   autofocus
                                   autocomplete="one-time-code"
                                   style="text-align: center; letter-spacing: 2px; font-weight: 600;">
                        </div>

                        <button type="submit" class="btn-login">
                            <i class="fas fa-shield-alt"></i> Verify & Access Admin Panel
                        </button>
                    </form>

                    <a href="?resend=1" class="btn-resend">
                        <i class="fas fa-redo"></i> Resend Verification Code
                    </a>

                    <div class="security-notice">
                        <h6><i class="fas fa-info-circle"></i> Email Information</h6>
                        <p>
                            A 6-digit verification code has been sent to: <strong><?php echo htmlspecialchars(maskEmail($user_email)); ?></strong><br>
                            The code will expire in 10 minutes for security purposes.
                        </p>
                    </div>
                </div>
            </div>

            <!-- Right Panel - Background Image, SVG, and Text -->
            <div class="col-md-7 right-panel">
                <div class="right-panel-content">
                    <!-- Banking Admin Illustration -->
                    <div class="feature-illustration">
                        <svg width="400" height="200" viewBox="0 0 400 200" xmlns="http://www.w3.org/2000/svg">
                            <defs>
                                <linearGradient id="adminGradient" x1="0%" y1="0%" x2="100%" y2="100%">
                                    <stop offset="0%" style="stop-color:#ffffff;stop-opacity:0.4" />
                                    <stop offset="100%" style="stop-color:#ffffff;stop-opacity:0.1" />
                                </linearGradient>
                                <linearGradient id="dashboardGradient" x1="0%" y1="0%" x2="100%" y2="100%">
                                    <stop offset="0%" style="stop-color:#ffffff;stop-opacity:0.5" />
                                    <stop offset="100%" style="stop-color:#ffffff;stop-opacity:0.2" />
                                </linearGradient>
                            </defs>

                            <!-- Admin Dashboard -->
                            <rect x="50" y="40" width="300" height="120" rx="12" fill="url(#adminGradient)" stroke="rgba(255,255,255,0.4)" stroke-width="2"/>

                            <!-- Dashboard Header -->
                            <rect x="70" y="60" width="260" height="12" rx="6" fill="rgba(255,255,255,0.6)"/>

                            <!-- Admin Cards -->
                            <rect x="70" y="85" width="80" height="60" rx="8" fill="url(#dashboardGradient)"/>
                            <rect x="160" y="85" width="80" height="60" rx="8" fill="url(#dashboardGradient)"/>
                            <rect x="250" y="85" width="80" height="60" rx="8" fill="url(#dashboardGradient)"/>

                            <!-- Card Icons -->
                            <circle cx="110" cy="105" r="8" fill="rgba(255,255,255,0.7)"/>
                            <circle cx="200" cy="105" r="8" fill="rgba(255,255,255,0.7)"/>
                            <circle cx="290" cy="105" r="8" fill="rgba(255,255,255,0.7)"/>

                            <!-- Card Text Lines -->
                            <rect x="80" y="125" width="60" height="3" rx="1" fill="rgba(255,255,255,0.5)"/>
                            <rect x="170" y="125" width="60" height="3" rx="1" fill="rgba(255,255,255,0.5)"/>
                            <rect x="260" y="125" width="60" height="3" rx="1" fill="rgba(255,255,255,0.5)"/>

                            <rect x="80" y="135" width="40" height="3" rx="1" fill="rgba(255,255,255,0.4)"/>
                            <rect x="170" y="135" width="40" height="3" rx="1" fill="rgba(255,255,255,0.4)"/>
                            <rect x="260" y="135" width="40" height="3" rx="1" fill="rgba(255,255,255,0.4)"/>

                            <!-- Admin Shield -->
                            <path d="M200 10 L220 5 L240 10 L240 30 Q240 40 220 45 Q200 40 200 30 Z" fill="rgba(255,255,255,0.6)" stroke="rgba(255,255,255,0.8)" stroke-width="2"/>
                            <path d="M210 20 L218 25 L230 15" stroke="rgba(255,255,255,0.9)" stroke-width="2" fill="none"/>

                            <!-- Floating Admin Elements -->
                            <circle cx="30" cy="30" r="3" fill="rgba(255,255,255,0.5)"/>
                            <circle cx="370" cy="25" r="2" fill="rgba(255,255,255,0.4)"/>
                            <circle cx="20" cy="100" r="2" fill="rgba(255,255,255,0.3)"/>
                            <circle cx="380" cy="120" r="3" fill="rgba(255,255,255,0.4)"/>
                            <circle cx="15" cy="170" r="2" fill="rgba(255,255,255,0.3)"/>
                            <circle cx="385" cy="180" r="2" fill="rgba(255,255,255,0.4)"/>

                            <!-- Connection Lines -->
                            <path d="M33 30 Q50 25 70 30" stroke="rgba(255,255,255,0.3)" stroke-width="2" fill="none"/>
                            <path d="M370 28 Q350 35 330 30" stroke="rgba(255,255,255,0.3)" stroke-width="2" fill="none"/>
                            <path d="M22 100 Q35 95 50 100" stroke="rgba(255,255,255,0.2)" stroke-width="1" fill="none"/>
                        </svg>
                    </div>

                    <!-- Bottom Text -->
                    <div class="bottom-text">
                        <h3>Administrative Control Center</h3>
                        <p>Manage users, monitor transactions, configure system settings, and oversee all banking operations from this secure administrative interface.</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Auto-focus on OTP input and format input
        document.addEventListener('DOMContentLoaded', function() {
            const otpInput = document.getElementById('otp');

            // Format input to only allow numbers
            otpInput.addEventListener('input', function(e) {
                this.value = this.value.replace(/[^0-9]/g, '');
                if (this.value.length === 6) {
                    // Auto-submit when 6 digits are entered
                    this.form.submit();
                }
            });

            // Auto-focus
            otpInput.focus();
        });
    </script>
</body>
</html>
