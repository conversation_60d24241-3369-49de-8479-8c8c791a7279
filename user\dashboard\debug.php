<?php
/**
 * Debug Dashboard - Find the issue
 */

// Enable error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<!DOCTYPE html><html><head><title>Debug Dashboard</title></head><body>";
echo "<h1>Dashboard Debug</h1>";

try {
    echo "<p>✅ PHP is working</p>";
    
    // Test session
    if (session_status() === PHP_SESSION_NONE) {
        session_start();
    }
    echo "<p>✅ Session started</p>";
    
    // Check if logged in
    if (isset($_SESSION['user_id'])) {
        echo "<p>✅ User logged in: " . $_SESSION['user_id'] . "</p>";
    } else {
        echo "<p>❌ User not logged in</p>";
        echo "<p>Redirecting to login...</p>";
        echo "<script>window.location.href = '../../auth/login.php';</script>";
        exit();
    }
    
    // Test config include
    echo "<p>Testing config include...</p>";
    if (file_exists('../../config/config.php')) {
        echo "<p>✅ Config file exists</p>";
        require_once '../../config/config.php';
        echo "<p>✅ Config loaded</p>";
    } else {
        echo "<p>❌ Config file not found at ../../config/config.php</p>";
    }
    
    // Test database
    echo "<p>Testing database...</p>";
    $db = getDB();
    echo "<p>✅ Database connected</p>";
    
    // Test user data
    $user_id = $_SESSION['user_id'];
    echo "<p>User ID: $user_id</p>";
    
    $user_result = $db->query("SELECT * FROM accounts WHERE id = ?", [$user_id]);
    if ($user_result) {
        $user = $user_result->fetch_assoc();
        echo "<p>✅ User data loaded: " . htmlspecialchars($user['first_name'] . ' ' . $user['last_name']) . "</p>";
        echo "<p>Balance: $" . number_format($user['balance'], 2) . "</p>";
        echo "<p>Account: " . htmlspecialchars($user['account_number']) . "</p>";
    } else {
        echo "<p>❌ Failed to load user data</p>";
    }
    
    // Test header include
    echo "<p>Testing header include...</p>";
    if (file_exists('../shared/header.php')) {
        echo "<p>✅ Header file exists</p>";
    } else {
        echo "<p>❌ Header file not found at ../shared/header.php</p>";
    }
    
    // Test sidebar include
    echo "<p>Testing sidebar include...</p>";
    if (file_exists('../shared/sidebar.php')) {
        echo "<p>✅ Sidebar file exists</p>";
    } else {
        echo "<p>❌ Sidebar file not found at ../shared/sidebar.php</p>";
    }
    
    echo "<h2>✅ All tests passed!</h2>";
    echo "<p><a href='index.php'>Try Main Dashboard</a></p>";
    
} catch (Exception $e) {
    echo "<h2>❌ Error Found!</h2>";
    echo "<p><strong>Error:</strong> " . htmlspecialchars($e->getMessage()) . "</p>";
    echo "<p><strong>File:</strong> " . $e->getFile() . "</p>";
    echo "<p><strong>Line:</strong> " . $e->getLine() . "</p>";
    echo "<pre>" . htmlspecialchars($e->getTraceAsString()) . "</pre>";
}

echo "</body></html>";
?>
