<?php
/**
 * Audit Logger Class
 * Tracks all admin actions and sensitive operations for compliance and security
 */

require_once 'ErrorHandler.php';

class AuditLogger {
    
    private static $instance = null;
    private $auditFile;
    private $dbConnection;
    
    // Action types for categorization
    const ACTION_LOGIN = 'LOGIN';
    const ACTION_LOGOUT = 'LOGOUT';
    const ACTION_CREATE = 'CREATE';
    const ACTION_UPDATE = 'UPDATE';
    const ACTION_DELETE = 'DELETE';
    const ACTION_VIEW = 'VIEW';
    const ACTION_EXPORT = 'EXPORT';
    const ACTION_IMPORT = 'IMPORT';
    const ACTION_ADMIN = 'ADMIN';
    const ACTION_FINANCIAL = 'FINANCIAL';
    const ACTION_SECURITY = 'SECURITY';
    
    private function __construct() {
        $this->auditFile = __DIR__ . '/../logs/audit.log';
        $this->ensureLogDirectory();
        $this->initializeDatabase();
    }
    
    /**
     * Get singleton instance
     */
    public static function getInstance() {
        if (self::$instance === null) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    /**
     * Ensure log directory exists
     */
    private function ensureLogDirectory() {
        $logDir = dirname($this->auditFile);
        if (!is_dir($logDir)) {
            mkdir($logDir, 0755, true);
        }
    }
    
    /**
     * Initialize database connection for audit storage
     */
    private function initializeDatabase() {
        try {
            require_once 'database.php';
            $this->dbConnection = getDB();
            $this->createAuditTable();
        } catch (Exception $e) {
            ErrorHandler::logError('Failed to initialize audit database', [
                'error' => $e->getMessage()
            ], 'ERROR');
        }
    }
    
    /**
     * Create audit table if it doesn't exist
     */
    private function createAuditTable() {
        $sql = "CREATE TABLE IF NOT EXISTS audit_logs (
            id INT AUTO_INCREMENT PRIMARY KEY,
            timestamp DATETIME NOT NULL,
            user_id INT,
            user_type VARCHAR(20),
            action_type VARCHAR(50) NOT NULL,
            action_description TEXT NOT NULL,
            resource_type VARCHAR(100),
            resource_id VARCHAR(100),
            ip_address VARCHAR(45),
            user_agent TEXT,
            session_id VARCHAR(128),
            request_uri TEXT,
            request_method VARCHAR(10),
            old_values JSON,
            new_values JSON,
            additional_data JSON,
            severity VARCHAR(20) DEFAULT 'INFO',
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            INDEX idx_timestamp (timestamp),
            INDEX idx_user_id (user_id),
            INDEX idx_action_type (action_type),
            INDEX idx_resource (resource_type, resource_id)
        )";
        
        try {
            $this->dbConnection->query($sql);
        } catch (Exception $e) {
            ErrorHandler::logError('Failed to create audit table', [
                'error' => $e->getMessage()
            ], 'ERROR');
        }
    }
    
    /**
     * Log audit event
     * 
     * @param string $actionType Type of action (use class constants)
     * @param string $description Human-readable description
     * @param array $options Additional options
     */
    public static function log($actionType, $description, $options = []) {
        $instance = self::getInstance();
        
        $auditData = [
            'timestamp' => date('Y-m-d H:i:s'),
            'user_id' => $_SESSION['user_id'] ?? null,
            'user_type' => $_SESSION['user_type'] ?? 'anonymous',
            'action_type' => $actionType,
            'action_description' => $description,
            'resource_type' => $options['resource_type'] ?? null,
            'resource_id' => $options['resource_id'] ?? null,
            'ip_address' => $_SERVER['REMOTE_ADDR'] ?? 'unknown',
            'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? 'unknown',
            'session_id' => session_id() ?: 'no-session',
            'request_uri' => $_SERVER['REQUEST_URI'] ?? 'unknown',
            'request_method' => $_SERVER['REQUEST_METHOD'] ?? 'unknown',
            'old_values' => isset($options['old_values']) ? json_encode($options['old_values']) : null,
            'new_values' => isset($options['new_values']) ? json_encode($options['new_values']) : null,
            'additional_data' => isset($options['additional_data']) ? json_encode($options['additional_data']) : null,
            'severity' => $options['severity'] ?? 'INFO'
        ];
        
        // Log to file
        $instance->logToFile($auditData);
        
        // Log to database
        $instance->logToDatabase($auditData);
    }
    
    /**
     * Log to file
     */
    private function logToFile($auditData) {
        $logEntry = json_encode($auditData, JSON_UNESCAPED_SLASHES);
        file_put_contents($this->auditFile, $logEntry . PHP_EOL, FILE_APPEND | LOCK_EX);
    }
    
    /**
     * Log to database
     */
    private function logToDatabase($auditData) {
        if (!$this->dbConnection) {
            return;
        }

        try {
            $sql = "INSERT INTO audit_logs (
                timestamp, user_id, user_type, action_type, action_description,
                resource_type, resource_id, ip_address, user_agent, session_id,
                request_uri, request_method, old_values, new_values, additional_data, severity
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";

            // Use the same database pattern as the rest of the codebase
            $params = [
                $auditData['timestamp'],
                $auditData['user_id'],
                $auditData['user_type'],
                $auditData['action_type'],
                $auditData['action_description'],
                $auditData['resource_type'],
                $auditData['resource_id'],
                $auditData['ip_address'],
                substr($auditData['user_agent'], 0, 500), // Limit length
                $auditData['session_id'],
                $auditData['request_uri'],
                $auditData['request_method'],
                $auditData['old_values'],
                $auditData['new_values'],
                $auditData['additional_data'],
                $auditData['severity']
            ];

            $this->dbConnection->query($sql, $params);
        } catch (Exception $e) {
            ErrorHandler::logError('Failed to log audit to database', [
                'error' => $e->getMessage(),
                'audit_data' => $auditData
            ], 'ERROR');
        }
    }
    
    /**
     * Log user login
     */
    public static function logLogin($userId, $userType, $success = true) {
        $description = $success ? 'User logged in successfully' : 'Failed login attempt';
        $severity = $success ? 'INFO' : 'WARNING';
        
        self::log(self::ACTION_LOGIN, $description, [
            'resource_type' => 'user',
            'resource_id' => $userId,
            'severity' => $severity,
            'additional_data' => [
                'success' => $success,
                'user_type' => $userType
            ]
        ]);
    }
    
    /**
     * Log user logout
     */
    public static function logLogout($userId, $userType, $reason = 'User logout') {
        self::log(self::ACTION_LOGOUT, "User logged out: {$reason}", [
            'resource_type' => 'user',
            'resource_id' => $userId,
            'additional_data' => [
                'reason' => $reason,
                'user_type' => $userType
            ]
        ]);
    }
    
    /**
     * Log financial transaction
     */
    public static function logFinancialAction($action, $description, $userId, $amount, $currency, $additionalData = []) {
        self::log(self::ACTION_FINANCIAL, $description, [
            'resource_type' => 'transaction',
            'resource_id' => $additionalData['transaction_id'] ?? null,
            'severity' => 'INFO',
            'additional_data' => array_merge([
                'action' => $action,
                'user_id' => $userId,
                'amount' => $amount,
                'currency' => $currency
            ], $additionalData)
        ]);
    }
    
    /**
     * Log admin action
     */
    public static function logAdminAction($action, $description, $resourceType = null, $resourceId = null, $oldValues = null, $newValues = null) {
        self::log(self::ACTION_ADMIN, $description, [
            'resource_type' => $resourceType,
            'resource_id' => $resourceId,
            'old_values' => $oldValues,
            'new_values' => $newValues,
            'severity' => 'INFO',
            'additional_data' => [
                'admin_action' => $action
            ]
        ]);
    }
    
    /**
     * Log security event
     */
    public static function logSecurityEvent($event, $description, $severity = 'WARNING', $additionalData = []) {
        self::log(self::ACTION_SECURITY, $description, [
            'severity' => $severity,
            'additional_data' => array_merge([
                'security_event' => $event
            ], $additionalData)
        ]);
    }
    
    /**
     * Get audit logs with filters
     */
    public static function getLogs($filters = [], $limit = 100, $offset = 0) {
        $instance = self::getInstance();

        if (!$instance->dbConnection) {
            return [];
        }

        try {
            $sql = "SELECT * FROM audit_logs WHERE 1=1";
            $params = [];

            // Apply filters
            if (isset($filters['user_id'])) {
                $sql .= " AND user_id = ?";
                $params[] = $filters['user_id'];
            }

            if (isset($filters['action_type'])) {
                $sql .= " AND action_type = ?";
                $params[] = $filters['action_type'];
            }

            if (isset($filters['date_from'])) {
                $sql .= " AND timestamp >= ?";
                $params[] = $filters['date_from'];
            }

            if (isset($filters['date_to'])) {
                $sql .= " AND timestamp <= ?";
                $params[] = $filters['date_to'];
            }

            if (isset($filters['severity'])) {
                $sql .= " AND severity = ?";
                $params[] = $filters['severity'];
            }

            $sql .= " ORDER BY timestamp DESC LIMIT ? OFFSET ?";
            $params[] = $limit;
            $params[] = $offset;

            // Use the same database pattern as the rest of the codebase
            $result = $instance->dbConnection->query($sql, $params);
            $logs = [];

            if ($result) {
                while ($row = $result->fetch_assoc()) {
                    $logs[] = $row;
                }
            }

            return $logs;
        } catch (Exception $e) {
            ErrorHandler::logError('Failed to retrieve audit logs', [
                'error' => $e->getMessage(),
                'filters' => $filters
            ], 'ERROR');
            return [];
        }
    }
    
    /**
     * Get audit statistics
     */
    public static function getStatistics($dateFrom = null, $dateTo = null) {
        $instance = self::getInstance();

        if (!$instance->dbConnection) {
            return [];
        }

        try {
            $sql = "SELECT
                action_type,
                COUNT(*) as count,
                COUNT(DISTINCT user_id) as unique_users
                FROM audit_logs
                WHERE 1=1";

            $params = [];

            if ($dateFrom) {
                $sql .= " AND timestamp >= ?";
                $params[] = $dateFrom;
            }

            if ($dateTo) {
                $sql .= " AND timestamp <= ?";
                $params[] = $dateTo;
            }

            $sql .= " GROUP BY action_type ORDER BY count DESC";

            // Use the same database pattern as the rest of the codebase
            $result = $instance->dbConnection->query($sql, $params);
            $statistics = [];

            if ($result) {
                while ($row = $result->fetch_assoc()) {
                    $statistics[] = $row;
                }
            }

            return $statistics;
        } catch (Exception $e) {
            ErrorHandler::logError('Failed to get audit statistics', [
                'error' => $e->getMessage()
            ], 'ERROR');
            return [];
        }
    }
    
    /**
     * Clean up old audit logs (keep last 90 days)
     */
    public static function cleanup($daysToKeep = 90) {
        $instance = self::getInstance();

        if (!$instance->dbConnection) {
            return;
        }

        try {
            $cutoffDate = date('Y-m-d H:i:s', strtotime("-{$daysToKeep} days"));

            $sql = "DELETE FROM audit_logs WHERE timestamp < ?";

            // Use the same database pattern as the rest of the codebase
            $result = $instance->dbConnection->query($sql, [$cutoffDate]);

            ErrorHandler::logError('Audit logs cleanup completed', [
                'cutoff_date' => $cutoffDate,
                'operation_result' => $result ? 'success' : 'failed'
            ], 'INFO');

        } catch (Exception $e) {
            ErrorHandler::logError('Failed to cleanup audit logs', [
                'error' => $e->getMessage()
            ], 'ERROR');
        }
    }
}
?>
