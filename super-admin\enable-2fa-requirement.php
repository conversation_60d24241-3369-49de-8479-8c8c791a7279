<?php
/**
 * Enable 2FA Requirement for Super Admin
 * This script adds the setting to require 2FA for super admin login
 */

session_start();
require_once 'includes/auth.php';
requireSuperAdminAuth();

$message = '';
$error = '';

// Handle form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    try {
        require_once '../config/database.php';
        $db = getDB();
        
        $action = $_POST['action'] ?? '';
        
        if ($action === 'enable_2fa_requirement') {
            // Add or update the require_2fa setting
            $sql = "INSERT INTO super_admin_settings (setting_key, setting_value, setting_description, setting_type) 
                    VALUES ('require_2fa', '1', 'Require 2FA authentication for super admin login', 'boolean') 
                    ON DUPLICATE KEY UPDATE 
                    setting_value = '1', 
                    setting_description = 'Require 2FA authentication for super admin login'";
            
            if ($db->query($sql)) {
                $message = "✅ 2FA requirement has been enabled! Super admin login will now require authentication.";
                
                // Log the action
                require_once 'includes/2fa-functions.php';
                logSuperAdmin2FAAction($_SESSION['super_admin_username'] ?? 'superadmin', '2fa_requirement_enabled', 'Super admin 2FA requirement enabled');
            } else {
                $error = "Failed to enable 2FA requirement.";
            }
        } elseif ($action === 'disable_2fa_requirement') {
            // Disable the require_2fa setting
            $sql = "INSERT INTO super_admin_settings (setting_key, setting_value, setting_description, setting_type) 
                    VALUES ('require_2fa', '0', 'Require 2FA authentication for super admin login', 'boolean') 
                    ON DUPLICATE KEY UPDATE 
                    setting_value = '0'";
            
            if ($db->query($sql)) {
                $message = "⚠️ 2FA requirement has been disabled. Super admin login will not require authentication.";
                
                // Log the action
                require_once 'includes/2fa-functions.php';
                logSuperAdmin2FAAction($_SESSION['super_admin_username'] ?? 'superadmin', '2fa_requirement_disabled', 'Super admin 2FA requirement disabled');
            } else {
                $error = "Failed to disable 2FA requirement.";
            }
        }
    } catch (Exception $e) {
        $error = "Error: " . $e->getMessage();
    }
}

// Check current status
$current_status = false;
try {
    require_once '../config/database.php';
    require_once 'includes/2fa-functions.php';
    $current_status = isSuperAdmin2FARequired();
} catch (Exception $e) {
    $error = "Error checking current status: " . $e->getMessage();
}

// Set page title
$page_title = '2FA Requirement Settings';
require_once 'includes/header.php';
?>

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="page-header">
                <h1 class="page-title">
                    <i class="fas fa-shield-alt"></i>
                    2FA Requirement Settings
                </h1>
                <p class="page-subtitle">Enable or disable 2FA requirement for super admin login</p>
            </div>
        </div>
    </div>

    <?php if ($error): ?>
    <div class="alert alert-danger alert-dismissible fade show" role="alert">
        <i class="fas fa-exclamation-triangle"></i>
        <strong>Error:</strong> <?php echo htmlspecialchars($error); ?>
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
    <?php endif; ?>

    <?php if ($message): ?>
    <div class="alert alert-success alert-dismissible fade show" role="alert">
        <i class="fas fa-check-circle"></i>
        <?php echo htmlspecialchars($message); ?>
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
    <?php endif; ?>

    <div class="row">
        <div class="col-lg-8">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-primary text-white">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-cog"></i> 2FA Requirement Status
                    </h5>
                </div>
                <div class="card-body">
                    <div class="status-display p-4 mb-4 rounded" style="background: <?php echo $current_status ? '#d4edda' : '#f8d7da'; ?>; border: 1px solid <?php echo $current_status ? '#c3e6cb' : '#f5c6cb'; ?>;">
                        <div class="d-flex align-items-center">
                            <div class="status-icon me-3">
                                <i class="fas fa-<?php echo $current_status ? 'check-circle' : 'times-circle'; ?> fa-2x" style="color: <?php echo $current_status ? '#155724' : '#721c24'; ?>;"></i>
                            </div>
                            <div>
                                <h5 class="mb-1" style="color: <?php echo $current_status ? '#155724' : '#721c24'; ?>;">
                                    2FA Requirement: <?php echo $current_status ? 'ENABLED' : 'DISABLED'; ?>
                                </h5>
                                <p class="mb-0" style="color: <?php echo $current_status ? '#155724' : '#721c24'; ?>;">
                                    <?php if ($current_status): ?>
                                        Super admin login requires authentication (6-digit code or Google Authenticator)
                                    <?php else: ?>
                                        Super admin can login without 2FA authentication
                                    <?php endif; ?>
                                </p>
                            </div>
                        </div>
                    </div>

                    <div class="row g-3">
                        <?php if (!$current_status): ?>
                        <div class="col-md-6">
                            <form method="POST" action="">
                                <input type="hidden" name="action" value="enable_2fa_requirement">
                                <button type="submit" class="btn btn-success w-100" 
                                        onclick="return confirm('Are you sure you want to enable 2FA requirement? This will require authentication for all future super admin logins.')">
                                    <i class="fas fa-shield-alt"></i>
                                    Enable 2FA Requirement
                                </button>
                            </form>
                        </div>
                        <?php else: ?>
                        <div class="col-md-6">
                            <form method="POST" action="">
                                <input type="hidden" name="action" value="disable_2fa_requirement">
                                <button type="submit" class="btn btn-warning w-100" 
                                        onclick="return confirm('Are you sure you want to disable 2FA requirement? This will reduce security by allowing login without authentication.')">
                                    <i class="fas fa-exclamation-triangle"></i>
                                    Disable 2FA Requirement
                                </button>
                            </form>
                        </div>
                        <?php endif; ?>
                        
                        <div class="col-md-6">
                            <a href="2fa-setup.php" class="btn btn-primary w-100">
                                <i class="fas fa-cog"></i>
                                Configure Authentication Methods
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-lg-4">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-info text-white">
                    <h6 class="card-title mb-0">
                        <i class="fas fa-info-circle"></i> Important Information
                    </h6>
                </div>
                <div class="card-body">
                    <h6>Before Enabling 2FA Requirement:</h6>
                    <ul class="small">
                        <li>Make sure you have configured at least one authentication method</li>
                        <li>Test your authentication method works correctly</li>
                        <li>Keep backup access in case of issues</li>
                    </ul>
                    
                    <h6 class="mt-3">Security Benefits:</h6>
                    <ul class="small">
                        <li>Prevents unauthorized access even with password</li>
                        <li>Adds extra layer of protection</li>
                        <li>Meets security compliance requirements</li>
                    </ul>
                    
                    <div class="alert alert-warning mt-3">
                        <small>
                            <strong>Warning:</strong> Make sure you can authenticate before enabling the requirement!
                        </small>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<?php require_once 'includes/footer.php'; ?>
