<?php
require_once 'config/config.php';

echo "<h2>Password Check for testuser</h2>";

try {
    $db = getDB();
    
    // Get testuser details
    $user_query = "SELECT id, username, password FROM accounts WHERE username = 'testuser'";
    $user_result = $db->query($user_query);
    
    if ($user = $user_result->fetch_assoc()) {
        echo "User ID: " . $user['id'] . "<br>";
        echo "Username: " . $user['username'] . "<br>";
        echo "Password Hash: " . $user['password'] . "<br><br>";
        
        // Test common passwords
        $test_passwords = ['password', 'test123', 'testuser', '123456', 'admin', 'test'];
        
        echo "<h3>Testing Common Passwords:</h3>";
        foreach ($test_passwords as $test_pass) {
            if (password_verify($test_pass, $user['password'])) {
                echo "<strong style='color: green;'>✓ Password '$test_pass' works!</strong><br>";
            } else {
                echo "✗ Password '$test_pass' does not work<br>";
            }
        }
    } else {
        echo "User 'testuser' not found.";
    }
    
} catch (Exception $e) {
    echo "Error: " . $e->getMessage();
}
?>
