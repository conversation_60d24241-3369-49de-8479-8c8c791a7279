/**
 * Admin Settings JavaScript
 * Handles settings-specific functionality
 */

document.addEventListener('DOMContentLoaded', function() {
    initializeSettings();
    initializeThemePreview();
    initializeColorSchemePreview();
    initializeFormValidation();
    initializeSettingsPreview();
});

/**
 * Initialize Settings
 */
function initializeSettings() {
    // Add animation to settings cards
    const settingsCards = document.querySelectorAll('.settings-card');
    settingsCards.forEach((card, index) => {
        card.style.animationDelay = `${index * 0.1}s`;
        card.classList.add('fade-in-up');
    });

    // Initialize switch animations
    const switches = document.querySelectorAll('.form-check.form-switch .form-check-input');
    switches.forEach(switchEl => {
        switchEl.addEventListener('change', function() {
            this.parentElement.classList.add('switch-animated');
            setTimeout(() => {
                this.parentElement.classList.remove('switch-animated');
            }, 300);
        });
    });

    // Auto-save settings on change
    initializeAutoSave();
}

/**
 * Initialize Theme Preview
 */
function initializeThemePreview() {
    const themeInputs = document.querySelectorAll('input[name="theme"]');
    
    themeInputs.forEach(input => {
        input.addEventListener('change', function() {
            previewTheme(this.value);
            updateThemePreview(this.value);
        });
    });
    
    // Initialize with current theme
    const currentTheme = document.querySelector('input[name="theme"]:checked');
    if (currentTheme) {
        previewTheme(currentTheme.value);
    }
}

/**
 * Initialize Color Scheme Preview
 */
function initializeColorSchemePreview() {
    const colorSchemeSelect = document.querySelector('select[name="color_scheme"]');
    
    if (colorSchemeSelect) {
        // Create color preview
        createColorPreview(colorSchemeSelect);
        
        colorSchemeSelect.addEventListener('change', function() {
            previewColorScheme(this.value);
            updateColorPreview(this.value);
        });
        
        // Initialize with current color scheme
        previewColorScheme(colorSchemeSelect.value);
    }
}

/**
 * Initialize Form Validation
 */
function initializeFormValidation() {
    const forms = document.querySelectorAll('form');
    
    forms.forEach(form => {
        form.addEventListener('submit', function(e) {
            if (!validateSettingsForm(this)) {
                e.preventDefault();
                return false;
            }
            
            // Show loading state
            const submitBtn = this.querySelector('button[type="submit"]');
            if (submitBtn) {
                setButtonLoading(submitBtn, true);
            }
        });
        
        // Real-time validation
        const inputs = form.querySelectorAll('input, select, textarea');
        inputs.forEach(input => {
            input.addEventListener('blur', function() {
                validateField(this);
            });
            
            input.addEventListener('input', function() {
                clearFieldError(this);
            });
        });
    });
}

/**
 * Initialize Settings Preview
 */
function initializeSettingsPreview() {
    // Create live preview area
    createSettingsPreview();
    
    // Update preview when settings change
    const allInputs = document.querySelectorAll('input, select');
    allInputs.forEach(input => {
        input.addEventListener('change', updateSettingsPreview);
    });
}

/**
 * Preview Theme
 */
function previewTheme(theme) {
    document.documentElement.setAttribute('data-theme-preview', theme);
    
    // Update body class for immediate visual feedback
    document.body.classList.remove('theme-light', 'theme-dark');
    document.body.classList.add(`theme-${theme}`);
    
    // Show preview notification
    showPreviewNotification(`Theme changed to ${theme}`, 'info');
}

/**
 * Preview Color Scheme
 */
function previewColorScheme(scheme) {
    document.documentElement.setAttribute('data-color-scheme-preview', scheme);
    
    // Update CSS custom properties for immediate preview
    const root = document.documentElement;
    const colors = getColorSchemeColors(scheme);
    
    Object.keys(colors).forEach(property => {
        root.style.setProperty(property, colors[property]);
    });
    
    showPreviewNotification(`Color scheme changed to ${scheme}`, 'info');
}

/**
 * Get Color Scheme Colors
 */
function getColorSchemeColors(scheme) {
    const colorSchemes = {
        blue: {
            '--primary-color': '#206bc4',
            '--primary-dark': '#1a5490',
            '--primary-light': '#4a8bc2'
        },
        green: {
            '--primary-color': '#10b981',
            '--primary-dark': '#059669',
            '--primary-light': '#dcfce7'
        },
        purple: {
            '--primary-color': '#8b5cf6',
            '--primary-dark': '#7c3aed',
            '--primary-light': '#ede9fe'
        },
        red: {
            '--primary-color': '#ef4444',
            '--primary-dark': '#dc2626',
            '--primary-light': '#fee2e2'
        },
        orange: {
            '--primary-color': '#f59e0b',
            '--primary-dark': '#d97706',
            '--primary-light': '#fef3c7'
        }
    };
    
    return colorSchemes[scheme] || colorSchemes.blue;
}

/**
 * Create Color Preview
 */
function createColorPreview(selectElement) {
    const preview = document.createElement('div');
    preview.className = 'color-scheme-preview';
    preview.innerHTML = `
        <div class="color-dot blue" data-scheme="blue" title="Blue"></div>
        <div class="color-dot green" data-scheme="green" title="Green"></div>
        <div class="color-dot purple" data-scheme="purple" title="Purple"></div>
        <div class="color-dot red" data-scheme="red" title="Red"></div>
        <div class="color-dot orange" data-scheme="orange" title="Orange"></div>
    `;
    
    selectElement.parentElement.appendChild(preview);
    
    // Add click handlers to color dots
    const colorDots = preview.querySelectorAll('.color-dot');
    colorDots.forEach(dot => {
        dot.addEventListener('click', function() {
            const scheme = this.getAttribute('data-scheme');
            selectElement.value = scheme;
            selectElement.dispatchEvent(new Event('change'));
            updateColorPreview(scheme);
        });
    });
    
    // Initialize with current selection
    updateColorPreview(selectElement.value);
}

/**
 * Update Color Preview
 */
function updateColorPreview(selectedScheme) {
    const colorDots = document.querySelectorAll('.color-dot');
    colorDots.forEach(dot => {
        dot.classList.remove('selected');
        if (dot.getAttribute('data-scheme') === selectedScheme) {
            dot.classList.add('selected');
            dot.style.transform = 'scale(1.2)';
            dot.style.boxShadow = '0 0 0 3px rgba(59, 130, 246, 0.3)';
        } else {
            dot.style.transform = '';
            dot.style.boxShadow = '';
        }
    });
}

/**
 * Update Theme Preview
 */
function updateThemePreview(theme) {
    const themeLabels = document.querySelectorAll('.form-selectgroup-label');
    themeLabels.forEach(label => {
        const input = label.previousElementSibling;
        if (input && input.value === theme) {
            label.style.transform = 'scale(1.02)';
            setTimeout(() => {
                label.style.transform = '';
            }, 200);
        }
    });
}

/**
 * Create Settings Preview
 */
function createSettingsPreview() {
    const previewContainer = document.createElement('div');
    previewContainer.className = 'settings-preview';
    previewContainer.innerHTML = `
        <h4>Live Preview</h4>
        <p>Changes will be applied immediately for preview. Click "Update" to save permanently.</p>
        <div id="preview-content"></div>
    `;
    
    // Add to the first settings card
    const firstCard = document.querySelector('.settings-card');
    if (firstCard) {
        firstCard.appendChild(previewContainer);
    }
}

/**
 * Update Settings Preview
 */
function updateSettingsPreview() {
    const previewContent = document.getElementById('preview-content');
    if (!previewContent) return;
    
    const theme = document.querySelector('input[name="theme"]:checked')?.value || 'light';
    const colorScheme = document.querySelector('select[name="color_scheme"]')?.value || 'blue';
    const sidebarCollapsed = document.querySelector('input[name="sidebar_collapsed"]')?.checked || false;
    const headerFixed = document.querySelector('input[name="header_fixed"]')?.checked || false;
    const animationsEnabled = document.querySelector('input[name="animations_enabled"]')?.checked || false;
    
    previewContent.innerHTML = `
        <div class="preview-item">
            <strong>Theme:</strong> ${theme.charAt(0).toUpperCase() + theme.slice(1)}
        </div>
        <div class="preview-item">
            <strong>Color Scheme:</strong> ${colorScheme.charAt(0).toUpperCase() + colorScheme.slice(1)}
        </div>
        <div class="preview-item">
            <strong>Sidebar:</strong> ${sidebarCollapsed ? 'Collapsed' : 'Expanded'}
        </div>
        <div class="preview-item">
            <strong>Header:</strong> ${headerFixed ? 'Fixed' : 'Static'}
        </div>
        <div class="preview-item">
            <strong>Animations:</strong> ${animationsEnabled ? 'Enabled' : 'Disabled'}
        </div>
    `;
}

/**
 * Initialize Auto Save
 */
function initializeAutoSave() {
    let autoSaveTimeout;
    const autoSaveInputs = document.querySelectorAll('input[type="checkbox"], select');
    
    autoSaveInputs.forEach(input => {
        input.addEventListener('change', function() {
            clearTimeout(autoSaveTimeout);
            autoSaveTimeout = setTimeout(() => {
                autoSaveSettings();
            }, 2000);
        });
    });
}

/**
 * Auto Save Settings
 */
function autoSaveSettings() {
    const formData = new FormData();
    formData.append('action', 'appearance');
    
    // Collect appearance settings
    const theme = document.querySelector('input[name="theme"]:checked')?.value;
    const colorScheme = document.querySelector('select[name="color_scheme"]')?.value;
    const sidebarCollapsed = document.querySelector('input[name="sidebar_collapsed"]')?.checked;
    const headerFixed = document.querySelector('input[name="header_fixed"]')?.checked;
    const animationsEnabled = document.querySelector('input[name="animations_enabled"]')?.checked;
    
    if (theme) formData.append('theme', theme);
    if (colorScheme) formData.append('color_scheme', colorScheme);
    if (sidebarCollapsed) formData.append('sidebar_collapsed', '1');
    if (headerFixed) formData.append('header_fixed', '1');
    if (animationsEnabled) formData.append('animations_enabled', '1');
    
    fetch('index.php', {
        method: 'POST',
        body: formData
    })
    .then(response => response.text())
    .then(() => {
        showPreviewNotification('Settings auto-saved', 'success');
    })
    .catch(error => {
        console.error('Auto-save failed:', error);
        showPreviewNotification('Auto-save failed', 'error');
    });
}

/**
 * Validate Settings Form
 */
function validateSettingsForm(form) {
    let isValid = true;
    const requiredFields = form.querySelectorAll('[required]');
    
    requiredFields.forEach(field => {
        if (!field.value.trim()) {
            showFieldError(field, 'This field is required');
            isValid = false;
        }
    });
    
    // Validate numeric fields
    const numericFields = form.querySelectorAll('input[type="number"]');
    numericFields.forEach(field => {
        if (field.value && (isNaN(field.value) || parseFloat(field.value) < 0)) {
            showFieldError(field, 'Please enter a valid positive number');
            isValid = false;
        }
    });
    
    return isValid;
}

/**
 * Validate Field
 */
function validateField(field) {
    clearFieldError(field);
    
    if (field.hasAttribute('required') && !field.value.trim()) {
        showFieldError(field, 'This field is required');
        return false;
    }
    
    if (field.type === 'number' && field.value && (isNaN(field.value) || parseFloat(field.value) < 0)) {
        showFieldError(field, 'Please enter a valid positive number');
        return false;
    }
    
    return true;
}

/**
 * Show Field Error
 */
function showFieldError(field, message) {
    clearFieldError(field);
    
    field.classList.add('is-invalid');
    
    const errorDiv = document.createElement('div');
    errorDiv.className = 'invalid-feedback';
    errorDiv.textContent = message;
    
    field.parentElement.appendChild(errorDiv);
}

/**
 * Clear Field Error
 */
function clearFieldError(field) {
    field.classList.remove('is-invalid');
    const errorDiv = field.parentElement.querySelector('.invalid-feedback');
    if (errorDiv) {
        errorDiv.remove();
    }
}

/**
 * Show Preview Notification
 */
function showPreviewNotification(message, type = 'info') {
    const notification = document.createElement('div');
    notification.className = `alert alert-${type} preview-notification`;
    notification.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        z-index: 1050;
        min-width: 300px;
        animation: slideInRight 0.3s ease-out;
    `;
    notification.innerHTML = `
        <div class="d-flex align-items-center">
            <div class="alert-icon">
                ${type === 'success' ? '✓' : type === 'error' ? '✗' : 'ℹ'}
            </div>
            <div>${message}</div>
        </div>
    `;
    
    document.body.appendChild(notification);
    
    setTimeout(() => {
        notification.style.animation = 'slideOutRight 0.3s ease-out';
        setTimeout(() => {
            if (notification.parentNode) {
                notification.remove();
            }
        }, 300);
    }, 3000);
}

/**
 * Set Button Loading
 */
function setButtonLoading(button, loading) {
    if (loading) {
        button.disabled = true;
        button.innerHTML = '<span class="spinner-border spinner-border-sm me-2" role="status" aria-hidden="true"></span>Updating...';
    } else {
        button.disabled = false;
        button.innerHTML = button.getAttribute('data-original-text') || 'Update';
    }
}

// Add CSS animations
const style = document.createElement('style');
style.textContent = `
    @keyframes slideInRight {
        from {
            transform: translateX(100%);
            opacity: 0;
        }
        to {
            transform: translateX(0);
            opacity: 1;
        }
    }
    
    @keyframes slideOutRight {
        from {
            transform: translateX(0);
            opacity: 1;
        }
        to {
            transform: translateX(100%);
            opacity: 0;
        }
    }
    
    @keyframes fade-in-up {
        from {
            opacity: 0;
            transform: translateY(30px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }
    
    .fade-in-up {
        animation: fade-in-up 0.6s ease-out forwards;
        opacity: 0;
    }
    
    .switch-animated {
        animation: switchPulse 0.3s ease-out;
    }
    
    @keyframes switchPulse {
        0%, 100% {
            transform: scale(1);
        }
        50% {
            transform: scale(1.05);
        }
    }
    
    .preview-item {
        padding: 0.5rem;
        margin: 0.25rem 0;
        background: rgba(59, 130, 246, 0.1);
        border-radius: 6px;
        font-size: 0.875rem;
    }
`;
document.head.appendChild(style);

// Export functions for global access
window.Settings = {
    previewTheme,
    previewColorScheme,
    updateSettingsPreview,
    autoSaveSettings,
    validateSettingsForm
};
