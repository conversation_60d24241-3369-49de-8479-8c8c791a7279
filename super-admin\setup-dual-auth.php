<?php
/**
 * Super Admin Dual Authentication Setup
 * Upgrades the existing 2FA system to support both 6-digit security codes and Google Authenticator
 */

session_start();

// Include required files
require_once 'includes/auth.php';

// Require super admin authentication
requireSuperAdminAuth();

$error = '';
$success = '';
$status = [];

// Check current database status
function checkDualAuthStatus() {
    try {
        require_once '../config/database.php';
        $db = getDB();
        
        $status = [
            'table_exists' => false,
            'columns_exist' => false,
            'settings_exist' => false,
            'needs_upgrade' => false
        ];
        
        // Check if table exists
        $result = $db->query("SHOW TABLES LIKE 'super_admin_2fa_settings'");
        $status['table_exists'] = $result && $result->num_rows > 0;
        
        if ($status['table_exists']) {
            // Check if new columns exist
            $result = $db->query("SHOW COLUMNS FROM super_admin_2fa_settings LIKE 'security_code_hash'");
            $status['columns_exist'] = $result && $result->num_rows > 0;
            
            // Check if settings exist
            $result = $db->query("SELECT COUNT(*) as count FROM super_admin_settings WHERE setting_key = 'dual_auth_enabled'");
            if ($result && $row = $result->fetch_assoc()) {
                $status['settings_exist'] = $row['count'] > 0;
            }
            
            $status['needs_upgrade'] = !$status['columns_exist'] || !$status['settings_exist'];
        }
        
        return $status;
    } catch (Exception $e) {
        error_log("Error checking dual auth status: " . $e->getMessage());
        return ['error' => $e->getMessage()];
    }
}

// Handle form submissions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    try {
        $action = $_POST['action'] ?? '';
        
        if ($action === 'upgrade_database') {
            require_once '../config/database.php';
            $db = getDB();
            
            // Read and execute the upgrade SQL file
            $sql_file = '../database/upgrade_super_admin_dual_auth.sql';
            if (file_exists($sql_file)) {
                $sql = file_get_contents($sql_file);
                
                // Split SQL statements
                $statements = array_filter(array_map('trim', explode(';', $sql)));
                
                $executed = 0;
                $errors = [];
                
                foreach ($statements as $statement) {
                    if (!empty($statement) && !preg_match('/^\s*--/', $statement)) {
                        try {
                            $db->query($statement);
                            $executed++;
                        } catch (Exception $e) {
                            // Ignore "already exists" errors and duplicate key errors
                            if (strpos($e->getMessage(), 'already exists') === false && 
                                strpos($e->getMessage(), 'Duplicate') === false &&
                                strpos($e->getMessage(), 'duplicate') === false) {
                                $errors[] = $e->getMessage();
                            }
                        }
                    }
                }
                
                if (empty($errors)) {
                    $success = "Dual authentication system upgraded successfully! ($executed statements executed)";
                    
                    // Log the upgrade
                    logSuperAdminAction('dual_auth_upgrade', 'Dual authentication system upgraded');
                } else {
                    $error = "Some errors occurred during upgrade: " . implode(', ', $errors);
                }
            } else {
                $error = "Upgrade SQL file not found: $sql_file";
            }
        }
    } catch (Exception $e) {
        $error = "Error during upgrade: " . $e->getMessage();
    }
}

// Get current status
$status = checkDualAuthStatus();

// Set page title
$page_title = 'Dual Authentication Setup';

// Include header
require_once 'includes/header.php';
?>

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="page-header">
                <h1 class="page-title">
                    <i class="fas fa-shield-alt"></i>
                    Dual Authentication Setup
                </h1>
                <p class="page-subtitle">Upgrade to support both 6-digit security codes and Google Authenticator</p>
            </div>
        </div>
    </div>

    <?php if ($error): ?>
    <div class="alert alert-danger alert-dismissible fade show" role="alert">
        <i class="fas fa-exclamation-triangle"></i>
        <strong>Error:</strong> <?php echo htmlspecialchars($error); ?>
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
    <?php endif; ?>

    <?php if ($success): ?>
    <div class="alert alert-success alert-dismissible fade show" role="alert">
        <i class="fas fa-check-circle"></i>
        <strong>Success:</strong> <?php echo htmlspecialchars($success); ?>
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
    <?php endif; ?>

    <div class="row">
        <div class="col-lg-8">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-primary text-white">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-database"></i> Database Status
                    </h5>
                </div>
                <div class="card-body">
                    <?php if (isset($status['error'])): ?>
                        <div class="alert alert-danger">
                            <i class="fas fa-exclamation-triangle"></i>
                            Error checking database status: <?php echo htmlspecialchars($status['error']); ?>
                        </div>
                    <?php else: ?>
                        <div class="row g-3">
                            <div class="col-md-6">
                                <div class="status-item">
                                    <div class="status-icon <?php echo $status['table_exists'] ? 'text-success' : 'text-danger'; ?>">
                                        <i class="fas fa-<?php echo $status['table_exists'] ? 'check-circle' : 'times-circle'; ?>"></i>
                                    </div>
                                    <div class="status-content">
                                        <h6>2FA Table</h6>
                                        <p class="text-muted mb-0">
                                            <?php echo $status['table_exists'] ? 'Table exists' : 'Table missing'; ?>
                                        </p>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="col-md-6">
                                <div class="status-item">
                                    <div class="status-icon <?php echo $status['columns_exist'] ? 'text-success' : 'text-warning'; ?>">
                                        <i class="fas fa-<?php echo $status['columns_exist'] ? 'check-circle' : 'exclamation-triangle'; ?>"></i>
                                    </div>
                                    <div class="status-content">
                                        <h6>Dual Auth Columns</h6>
                                        <p class="text-muted mb-0">
                                            <?php echo $status['columns_exist'] ? 'Columns exist' : 'Needs upgrade'; ?>
                                        </p>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="col-md-6">
                                <div class="status-item">
                                    <div class="status-icon <?php echo $status['settings_exist'] ? 'text-success' : 'text-warning'; ?>">
                                        <i class="fas fa-<?php echo $status['settings_exist'] ? 'check-circle' : 'exclamation-triangle'; ?>"></i>
                                    </div>
                                    <div class="status-content">
                                        <h6>System Settings</h6>
                                        <p class="text-muted mb-0">
                                            <?php echo $status['settings_exist'] ? 'Settings configured' : 'Needs configuration'; ?>
                                        </p>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="col-md-6">
                                <div class="status-item">
                                    <div class="status-icon <?php echo !$status['needs_upgrade'] ? 'text-success' : 'text-info'; ?>">
                                        <i class="fas fa-<?php echo !$status['needs_upgrade'] ? 'check-circle' : 'info-circle'; ?>"></i>
                                    </div>
                                    <div class="status-content">
                                        <h6>System Status</h6>
                                        <p class="text-muted mb-0">
                                            <?php echo !$status['needs_upgrade'] ? 'Ready to use' : 'Upgrade required'; ?>
                                        </p>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <?php if ($status['needs_upgrade']): ?>
                        <div class="mt-4">
                            <div class="alert alert-info">
                                <i class="fas fa-info-circle"></i>
                                <strong>Upgrade Required:</strong> Your system needs to be upgraded to support dual authentication.
                            </div>
                            
                            <form method="POST" action="" onsubmit="return confirm('Are you sure you want to upgrade the database? This action cannot be undone.')">
                                <input type="hidden" name="action" value="upgrade_database">
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-rocket"></i> Upgrade Database
                                </button>
                            </form>
                        </div>
                        <?php else: ?>
                        <div class="mt-4">
                            <div class="alert alert-success">
                                <i class="fas fa-check-circle"></i>
                                <strong>System Ready:</strong> Your dual authentication system is ready to use!
                            </div>
                            
                            <a href="2fa-setup.php" class="btn btn-success">
                                <i class="fas fa-cog"></i> Configure Authentication
                            </a>
                        </div>
                        <?php endif; ?>
                    <?php endif; ?>
                </div>
            </div>
        </div>
        
        <div class="col-lg-4">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-info text-white">
                    <h6 class="card-title mb-0">
                        <i class="fas fa-info-circle"></i> About Dual Authentication
                    </h6>
                </div>
                <div class="card-body">
                    <h6>Features:</h6>
                    <ul class="list-unstyled">
                        <li><i class="fas fa-check text-success me-2"></i> 6-digit security codes</li>
                        <li><i class="fas fa-check text-success me-2"></i> Google Authenticator support</li>
                        <li><i class="fas fa-check text-success me-2"></i> Choose your preferred method</li>
                        <li><i class="fas fa-check text-success me-2"></i> Enhanced security</li>
                        <li><i class="fas fa-check text-success me-2"></i> Backup authentication</li>
                    </ul>
                    
                    <h6 class="mt-3">Security Benefits:</h6>
                    <p class="small text-muted">
                        The dual authentication system provides multiple layers of security while giving you flexibility in how you authenticate.
                    </p>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.status-item {
    display: flex;
    align-items: center;
    padding: 1rem;
    background: #f8f9fa;
    border-radius: 8px;
    border: 1px solid #e9ecef;
}

.status-icon {
    font-size: 1.5rem;
    margin-right: 1rem;
}

.status-content h6 {
    margin-bottom: 0.25rem;
    font-weight: 600;
}
</style>

<?php require_once 'includes/footer.php'; ?>
