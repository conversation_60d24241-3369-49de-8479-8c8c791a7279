<?php
require_once '../config/config.php';
requireAdmin();

header('Content-Type: application/json');

try {
    // Get JSON input
    $input = json_decode(file_get_contents('php://input'), true);
    
    if (!$input) {
        throw new Exception('Invalid JSON input');
    }
    
    $transaction_id = intval($input['transaction_id'] ?? 0);
    $status = trim($input['status'] ?? '');
    
    // Validate inputs
    if ($transaction_id <= 0) {
        throw new Exception('Invalid transaction ID');
    }
    
    $valid_statuses = ['pending', 'processing', 'completed', 'cancelled', 'failed'];
    if (!in_array($status, $valid_statuses)) {
        throw new Exception('Invalid status');
    }
    
    $db = getDB();
    
    // Check if transaction exists
    $check_query = "SELECT id, account_id, amount, transaction_type, status as current_status 
                    FROM account_transactions WHERE id = ?";
    $check_result = $db->query($check_query, [$transaction_id]);
    
    if (!$check_result || $check_result->num_rows === 0) {
        throw new Exception('Transaction not found');
    }
    
    $transaction = $check_result->fetch_assoc();
    
    // Update transaction status
    $update_query = "UPDATE account_transactions SET 
                     status = ?, 
                     updated_at = NOW(),
                     processed_by = ?
                     WHERE id = ?";
    
    $result = $db->query($update_query, [
        $status,
        $_SESSION['user_id'] ?? 1, // Default to admin user ID 1
        $transaction_id
    ]);
    
    if (!$result) {
        throw new Exception('Failed to update transaction status');
    }
    
    // If status changed to/from completed, update account balance
    if ($transaction['current_status'] != $status && 
        ($status == 'completed' || $transaction['current_status'] == 'completed')) {
        
        $account_id = $transaction['account_id'];
        
        // Recalculate account balance
        $balance_query = "SELECT 
                         SUM(CASE 
                             WHEN transaction_type IN ('credit', 'deposit', 'transfer_in') THEN amount 
                             ELSE -amount 
                         END) as balance
                         FROM account_transactions 
                         WHERE account_id = ? AND status = 'completed'";
        
        $balance_result = $db->query($balance_query, [$account_id]);
        $balance_data = $balance_result->fetch_assoc();
        $new_balance = $balance_data['balance'] ?? 0;
        
        // Update account balance
        $update_balance_query = "UPDATE accounts SET balance = ? WHERE id = ?";
        $db->query($update_balance_query, [$new_balance, $account_id]);
    }
    
    // Log the action
    error_log("Transaction status updated: ID {$transaction_id}, Status: {$status}, Admin: " . ($_SESSION['user_id'] ?? 'Unknown'));
    
    echo json_encode([
        'success' => true,
        'message' => 'Transaction status updated successfully',
        'transaction_id' => $transaction_id,
        'new_status' => $status
    ]);
    
} catch (Exception $e) {
    http_response_code(400);
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ]);
}
?>
