<?php
// Debug script to check file paths
echo "<h1>Path Debug Information</h1>";

echo "<h2>Current Directory Structure</h2>";
echo "<strong>Current script location:</strong> " . __FILE__ . "<br>";
echo "<strong>Install directory:</strong> " . __DIR__ . "<br>";
echo "<strong>Parent directory (should be root):</strong> " . dirname(__DIR__) . "<br>";

$production_path = dirname(__DIR__);
echo "<strong>Production path being checked:</strong> " . $production_path . "<br>";

echo "<h2>Contents of Root Directory</h2>";
if (is_dir($production_path)) {
    $items = scandir($production_path);
    echo "<ul>";
    foreach ($items as $item) {
        if ($item !== '.' && $item !== '..') {
            $full_path = $production_path . '/' . $item;
            $type = is_dir($full_path) ? 'DIR' : 'FILE';
            echo "<li><strong>$type:</strong> $item</li>";
        }
    }
    echo "</ul>";
} else {
    echo "<p style='color: red;'>Directory not found or not accessible!</p>";
}

echo "<h2>Required Files Check</h2>";
$required_files = ['index.php', 'login.php', 'register.php', 'logout.php'];
foreach ($required_files as $file) {
    $path = $production_path . '/' . $file;
    $exists = file_exists($path);
    $color = $exists ? 'green' : 'red';
    $status = $exists ? 'EXISTS' : 'MISSING';
    echo "<p style='color: $color;'>$file: $status (checked: $path)</p>";
}

echo "<h2>Required Directories Check</h2>";
$required_dirs = ['admin', 'super-admin', 'dashboard', 'auth', 'config', 'database', 'assets', 'uploads', 'logs', 'vendor', 'includes'];
foreach ($required_dirs as $dir) {
    $path = $production_path . '/' . $dir;
    $exists = is_dir($path);
    $color = $exists ? 'green' : 'red';
    $status = $exists ? 'EXISTS' : 'MISSING';
    echo "<p style='color: $color;'>$dir/: $status (checked: $path)</p>";
}

echo "<h2>Config Directory Check</h2>";
$config_path = $production_path . '/config';
if (is_dir($config_path)) {
    echo "<p style='color: green;'>Config directory exists</p>";
    $config_files = scandir($config_path);
    echo "<ul>";
    foreach ($config_files as $file) {
        if ($file !== '.' && $file !== '..') {
            echo "<li>$file</li>";
        }
    }
    echo "</ul>";
} else {
    echo "<p style='color: red;'>Config directory missing</p>";
}
?>
