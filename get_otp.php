<?php
require_once 'config/config.php';

try {
    $db = getDB();

    // Get the latest OTP for testuser2025
    $user_query = "SELECT id FROM accounts WHERE username = 'testuser2025'";
    $user_result = $db->query($user_query);

    if ($user_result->num_rows > 0) {
        $user = $user_result->fetch_assoc();

        $otp_query = "SELECT otp_code, created_at FROM user_otps WHERE user_id = ? ORDER BY created_at DESC LIMIT 1";
        $otp_result = $db->query($otp_query, [$user['id']]);

        if ($otp_result->num_rows > 0) {
            $otp = $otp_result->fetch_assoc();
            echo "<h1 style='color: green; font-size: 48px; text-align: center;'>" . $otp['otp_code'] . "</h1>";
            echo "<p style='text-align: center;'>Created: " . $otp['created_at'] . "</p>";
        } else {
            echo "No OTP found";
        }
    } else {
        echo "User not found";
    }

} catch (Exception $e) {
    echo "Error: " . $e->getMessage();
}
?>
