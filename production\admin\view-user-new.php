<?php
/**
 * User Details Page - Refactored Version
 * Displays comprehensive user information using modular components
 */

require_once '../config/config.php';
requireAdmin();

// Get user ID from URL
$user_id = intval($_GET['id'] ?? 0);

if ($user_id <= 0) {
    header('Location: users.php');
    exit();
}

$page_title = 'User Details';

// Include the data loading logic
require_once 'includes/user-data-loader.php';

// Include admin header
include 'includes/admin-header.php';
?>

<!-- Include page-specific CSS -->
<link rel="stylesheet" href="../assets/admin/css/view-user.css">

<?php
// Include header component
include '../includes/components/user-header.php';

// Include overview cards component
include '../includes/components/user-overview-cards.php';
?>

<div class="row row-cards">
    <?php
    // Include personal and account information components
    include '../includes/components/user-personal-info.php';
    include '../includes/components/user-account-info.php';
    ?>
</div>

<?php
// Include security section component
include '../includes/components/user-security-section.php';

// Include financial information component
include '../includes/components/user-financial-section.php';

// Include virtual cards and crypto section
include '../includes/components/user-cards-crypto-section.php';

// Include document management section
include '../includes/components/user-documents-section.php';

// Include modals
include '../includes/components/user-modals.php';
?>

<!-- Include page-specific JavaScript -->
<script src="../assets/admin/js/view-user.js"></script>
<script src="js/document-management.js"></script>

<?php include 'includes/admin-footer.php'; ?>
