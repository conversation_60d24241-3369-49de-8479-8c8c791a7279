#!/bin/bash

# MySQL Connection Script for Online Banking Database
# This script provides quick access to MySQL command line

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
BOLD='\033[1m'
NC='\033[0m' # No Color

# Database connection parameters
DB_HOST="localhost"
DB_USER="root"
DB_PASS="root"
DB_NAME="online_banking"

# Function to print colored output
print_header() {
    echo -e "${CYAN}${BOLD}"
    echo "========================================"
    echo "   MySQL Database Connection Tool"
    echo "========================================"
    echo -e "${NC}"
}

print_success() {
    echo -e "${GREEN}✓ $1${NC}"
}

print_error() {
    echo -e "${RED}✗ $1${NC}"
}

print_info() {
    echo -e "${BLUE}ℹ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠ $1${NC}"
}

# Check if MySQL client is available
check_mysql() {
    if ! command -v mysql &> /dev/null; then
        print_error "MySQL client is not found in PATH"
        echo
        echo "Please make sure MySQL is installed and added to PATH"
        echo
        echo "For macOS with Homebrew:"
        echo "  brew install mysql"
        echo
        echo "For Ubuntu/Debian:"
        echo "  sudo apt-get install mysql-client"
        echo
        echo "For CentOS/RHEL:"
        echo "  sudo yum install mysql"
        echo
        exit 1
    fi
}

# Main menu function
show_menu() {
    clear
    print_header
    print_info "Host: $DB_HOST"
    print_info "Database: $DB_NAME"
    print_info "User: $DB_USER"
    echo
    echo "Choose connection option:"
    echo
    echo "1. Connect to Online Banking Database"
    echo "2. Connect to MySQL (no database selected)"
    echo "3. Show Database Information"
    echo "4. Create Database Backup"
    echo "5. Import SQL File"
    echo "6. Run Quick Queries"
    echo "7. Database Health Check"
    echo "8. Exit"
    echo
}

# Connect to specific database
connect_database() {
    echo
    print_info "Connecting to $DB_NAME database..."
    print_warning "Type 'exit' or 'quit' to return to this menu"
    echo
    mysql -h "$DB_HOST" -u "$DB_USER" -p"$DB_PASS" "$DB_NAME"
    echo
}

# Connect to MySQL server
connect_mysql() {
    echo
    print_info "Connecting to MySQL server..."
    print_warning "Type 'exit' or 'quit' to return to this menu"
    echo
    mysql -h "$DB_HOST" -u "$DB_USER" -p"$DB_PASS"
    echo
}

# Show database information
show_db_info() {
    echo
    print_info "Database Information:"
    echo "====================="
    echo "Host: $DB_HOST"
    echo "Database: $DB_NAME"
    echo "User: $DB_USER"
    echo
    print_info "Checking database status..."
    
    if mysql -h "$DB_HOST" -u "$DB_USER" -p"$DB_PASS" -e "SELECT 'Database connection successful!' as Status; USE $DB_NAME; SHOW TABLES;" 2>/dev/null; then
        print_success "Database connection and query successful!"
    else
        print_error "Database connection failed!"
    fi
    echo
}

# Create database backup
create_backup() {
    echo
    print_info "Creating database backup..."
    
    # Create backup filename with timestamp
    backup_file="backup_${DB_NAME}_$(date +%Y%m%d_%H%M%S).sql"
    
    print_info "Backup file: $backup_file"
    echo
    
    if mysqldump -h "$DB_HOST" -u "$DB_USER" -p"$DB_PASS" --single-transaction --routines --triggers "$DB_NAME" > "$backup_file" 2>/dev/null; then
        print_success "Backup created successfully!"
        print_info "File: $backup_file"
        print_info "Size: $(du -h "$backup_file" | cut -f1)"
    else
        print_error "Backup failed!"
    fi
    echo
}

# Import SQL file
import_sql() {
    echo
    read -p "Enter SQL file path to import: " sql_file
    
    if [[ ! -f "$sql_file" ]]; then
        print_error "File not found: $sql_file"
        return
    fi
    
    echo
    print_info "Importing $sql_file into $DB_NAME..."
    
    if mysql -h "$DB_HOST" -u "$DB_USER" -p"$DB_PASS" "$DB_NAME" < "$sql_file" 2>/dev/null; then
        print_success "Import completed successfully!"
    else
        print_error "Import failed!"
    fi
    echo
}

# Quick queries menu
quick_queries() {
    while true; do
        echo
        print_info "Quick Database Queries:"
        echo "======================="
        echo
        echo "1. Show all tables"
        echo "2. Count users"
        echo "3. Show recent transactions"
        echo "4. Show database size"
        echo "5. Show user status summary"
        echo "6. Show table sizes"
        echo "7. Back to main menu"
        echo
        read -p "Enter your choice (1-7): " query_choice
        
        case $query_choice in
            1)
                print_info "Showing all tables..."
                mysql -h "$DB_HOST" -u "$DB_USER" -p"$DB_PASS" "$DB_NAME" -e "SHOW TABLES;" 2>/dev/null
                ;;
            2)
                print_info "Counting users..."
                mysql -h "$DB_HOST" -u "$DB_USER" -p"$DB_PASS" "$DB_NAME" -e "SELECT COUNT(*) as 'Total Users' FROM accounts;" 2>/dev/null
                ;;
            3)
                print_info "Showing recent transactions..."
                mysql -h "$DB_HOST" -u "$DB_USER" -p"$DB_PASS" "$DB_NAME" -e "SELECT id, sender_id, amount, status, created_at FROM transfers ORDER BY created_at DESC LIMIT 10;" 2>/dev/null
                ;;
            4)
                print_info "Calculating database size..."
                mysql -h "$DB_HOST" -u "$DB_USER" -p"$DB_PASS" "$DB_NAME" -e "SELECT ROUND(SUM(data_length + index_length) / 1024 / 1024, 2) AS 'Database Size (MB)' FROM information_schema.TABLES WHERE table_schema = '$DB_NAME';" 2>/dev/null
                ;;
            5)
                print_info "Showing user status summary..."
                mysql -h "$DB_HOST" -u "$DB_USER" -p"$DB_PASS" "$DB_NAME" -e "SELECT status, COUNT(*) as count FROM accounts GROUP BY status;" 2>/dev/null
                ;;
            6)
                print_info "Showing table sizes..."
                mysql -h "$DB_HOST" -u "$DB_USER" -p"$DB_PASS" "$DB_NAME" -e "SELECT table_name AS 'Table', ROUND(((data_length + index_length) / 1024 / 1024), 2) AS 'Size (MB)' FROM information_schema.TABLES WHERE table_schema = '$DB_NAME' ORDER BY (data_length + index_length) DESC;" 2>/dev/null
                ;;
            7)
                return
                ;;
            *)
                print_error "Invalid choice! Please enter a number between 1-7."
                ;;
        esac
        
        echo
        read -p "Press Enter to continue..."
    done
}

# Database health check
health_check() {
    echo
    print_info "Running database health check..."
    echo "================================"
    echo
    
    # Check connection
    print_info "1. Testing database connection..."
    if mysql -h "$DB_HOST" -u "$DB_USER" -p"$DB_PASS" -e "SELECT 1;" &>/dev/null; then
        print_success "Database connection: OK"
    else
        print_error "Database connection: FAILED"
        return
    fi
    
    # Check if database exists
    print_info "2. Checking if database exists..."
    if mysql -h "$DB_HOST" -u "$DB_USER" -p"$DB_PASS" -e "USE $DB_NAME;" &>/dev/null; then
        print_success "Database exists: OK"
    else
        print_error "Database does not exist: FAILED"
        return
    fi
    
    # Check critical tables
    print_info "3. Checking critical tables..."
    critical_tables=("accounts" "transfers" "transactions" "system_settings")
    
    for table in "${critical_tables[@]}"; do
        if mysql -h "$DB_HOST" -u "$DB_USER" -p"$DB_PASS" "$DB_NAME" -e "SELECT 1 FROM $table LIMIT 1;" &>/dev/null; then
            print_success "Table $table: OK"
        else
            print_error "Table $table: MISSING or INACCESSIBLE"
        fi
    done
    
    # Check database size
    print_info "4. Database size check..."
    size=$(mysql -h "$DB_HOST" -u "$DB_USER" -p"$DB_PASS" "$DB_NAME" -e "SELECT ROUND(SUM(data_length + index_length) / 1024 / 1024, 2) FROM information_schema.TABLES WHERE table_schema = '$DB_NAME';" 2>/dev/null | tail -n 1)
    if [[ -n "$size" ]]; then
        print_success "Database size: ${size} MB"
    else
        print_error "Could not determine database size"
    fi
    
    # Check user count
    print_info "5. User count check..."
    user_count=$(mysql -h "$DB_HOST" -u "$DB_USER" -p"$DB_PASS" "$DB_NAME" -e "SELECT COUNT(*) FROM accounts;" 2>/dev/null | tail -n 1)
    if [[ -n "$user_count" ]]; then
        print_success "Total users: $user_count"
    else
        print_error "Could not count users"
    fi
    
    echo
    print_success "Health check completed!"
    echo
}

# Main script execution
main() {
    # Check if MySQL is available
    check_mysql
    
    while true; do
        show_menu
        read -p "Enter your choice (1-8): " choice
        
        case $choice in
            1)
                connect_database
                ;;
            2)
                connect_mysql
                ;;
            3)
                show_db_info
                ;;
            4)
                create_backup
                ;;
            5)
                import_sql
                ;;
            6)
                quick_queries
                ;;
            7)
                health_check
                ;;
            8)
                echo
                print_success "Thank you for using the MySQL Connection Tool!"
                echo
                exit 0
                ;;
            *)
                print_error "Invalid choice! Please enter a number between 1-8."
                ;;
        esac
        
        if [[ $choice != 6 ]]; then
            echo
            read -p "Press Enter to continue..."
        fi
    done
}

# Run the main function
main
