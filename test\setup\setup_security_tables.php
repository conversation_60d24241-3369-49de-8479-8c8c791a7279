<?php
/**
 * Setup script for secure deletion tables
 */

require_once '../config/database.php';

try {
    $db = getDB();
    $sql = file_get_contents('../database/secure_deletion_tables.sql');
    
    // Split by semicolon but be careful with the split
    $statements = preg_split('/;\s*$/m', $sql);
    
    foreach ($statements as $stmt) {
        $stmt = trim($stmt);
        if (!empty($stmt)) {
            try {
                $db->getConnection()->query($stmt);
                echo "✓ Executed: " . substr(str_replace(["\n", "\r"], ' ', $stmt), 0, 70) . "...\n";
            } catch (Exception $e) {
                echo "✗ Error: " . $e->getMessage() . "\n";
                echo "Statement: " . substr($stmt, 0, 100) . "...\n";
            }
        }
    }
    
    echo "\n✓ Database security tables setup complete.\n";
    
} catch (Exception $e) {
    echo "✗ Fatal error: " . $e->getMessage() . "\n";
}
?>
