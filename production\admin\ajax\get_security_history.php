<?php
require_once '../../config/config.php';
requireAdmin();

header('Content-Type: application/json');

try {
    if (!isset($_GET['user_id']) || empty($_GET['user_id'])) {
        throw new Exception("User ID is required");
    }
    
    $user_id = intval($_GET['user_id']);
    $db = getDB();
    
    // Get user information
    $user_query = "SELECT id, first_name, last_name, username, email FROM accounts WHERE id = ? AND is_admin = 0";
    $user_result = $db->query($user_query, [$user_id]);
    $user = $user_result ? $user_result->fetch_assoc() : null;
    
    if (!$user) {
        throw new Exception("User not found");
    }
    
    // Get security history
    $history_query = "SELECT ush.*, 
                     a.first_name as changed_by_first_name, 
                     a.last_name as changed_by_last_name
                     FROM user_security_history ush
                     LEFT JOIN accounts a ON ush.changed_by = a.id
                     WHERE ush.user_id = ?
                     ORDER BY ush.changed_at DESC
                     LIMIT 50";
    
    $history_result = $db->query($history_query, [$user_id]);
    $history = [];
    
    if ($history_result) {
        while ($row = $history_result->fetch_assoc()) {
            $history[] = $row;
        }
    }
    
    echo json_encode([
        'success' => true,
        'user' => $user,
        'history' => $history
    ]);
    
} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'error' => $e->getMessage()
    ]);
}
?>
