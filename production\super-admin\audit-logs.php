<?php
/**
 * Super Admin Audit Logs
 * View system audit trail and security logs
 */

$page_title = 'Audit Logs';
$page_subtitle = 'System audit trail and security monitoring';

// Include header
include 'includes/header.php';

// Get filters
$page = isset($_GET['page']) ? (int)$_GET['page'] : 1;
$limit = isset($_GET['limit']) ? (int)$_GET['limit'] : 20; // Allow limit to be set via URL
$limit = in_array($limit, [10, 20, 50, 100]) ? $limit : 20; // Validate limit
$offset = ($page - 1) * $limit;
$action_filter = isset($_GET['action']) ? trim($_GET['action']) : '';
$date_from = isset($_GET['date_from']) ? $_GET['date_from'] : '';
$date_to = isset($_GET['date_to']) ? $_GET['date_to'] : '';

try {
    require_once '../config/database.php';
    $db = getDB();
    
    // Build filter query
    $where_conditions = [];
    $params = [];
    
    if (!empty($action_filter)) {
        $where_conditions[] = "action LIKE ?";
        $params[] = "%$action_filter%";
    }
    
    if (!empty($date_from)) {
        $where_conditions[] = "DATE(created_at) >= ?";
        $params[] = $date_from;
    }
    
    if (!empty($date_to)) {
        $where_conditions[] = "DATE(created_at) <= ?";
        $params[] = $date_to;
    }
    
    $where_clause = '';
    if (!empty($where_conditions)) {
        $where_clause = 'WHERE ' . implode(' AND ', $where_conditions);
    }
    
    // Get total count
    $count_sql = "SELECT COUNT(*) as total FROM super_admin_audit_log $where_clause";
    $count_result = $db->query($count_sql, $params);
    $total_logs = $count_result ? $count_result->fetch_assoc()['total'] : 0;
    $total_pages = ceil($total_logs / $limit);
    
    // Get logs
    $sql = "SELECT * FROM super_admin_audit_log $where_clause 
            ORDER BY created_at DESC 
            LIMIT ? OFFSET ?";
    
    $params[] = $limit;
    $params[] = $offset;
    
    $logs_result = $db->query($sql, $params);
    $logs = [];
    
    if ($logs_result) {
        while ($row = $logs_result->fetch_assoc()) {
            $row['details'] = json_decode($row['details'], true);
            $logs[] = $row;
        }
    }
    
    // Get action statistics
    $stats_sql = "SELECT action, COUNT(*) as count FROM super_admin_audit_log
                  WHERE created_at >= DATE_SUB(NOW(), INTERVAL 24 HOUR)
                  GROUP BY action ORDER BY count DESC LIMIT 10";
    $stats_result = $db->query($stats_sql);
    $action_stats = [];

    if ($stats_result) {
        while ($row = $stats_result->fetch_assoc()) {
            $action_stats[] = $row;
        }
    }

    // Get accurate statistics from database
    $today_count_result = $db->query("SELECT COUNT(*) as count FROM super_admin_audit_log WHERE DATE(created_at) = CURDATE()");
    $today_count = $today_count_result ? $today_count_result->fetch_assoc()['count'] : 0;

    $login_count_result = $db->query("SELECT COUNT(*) as count FROM super_admin_audit_log WHERE action LIKE '%login%'");
    $login_count = $login_count_result ? $login_count_result->fetch_assoc()['count'] : 0;

    $security_count_result = $db->query("SELECT COUNT(*) as count FROM super_admin_audit_log WHERE action IN ('login_failed', 'session_timeout', 'unauthorized_access')");
    $security_count = $security_count_result ? $security_count_result->fetch_assoc()['count'] : 0;
    
} catch (Exception $e) {
    error_log("Failed to load audit logs: " . $e->getMessage());
    $logs = [];
    $total_logs = 0;
    $total_pages = 0;
    $action_stats = [];
    $today_count = 0;
    $login_count = 0;
    $security_count = 0;
}

// Log page access
logSuperAdminAction('audit_logs_access', 'Super admin accessed audit logs');

// Helper function to build pagination URLs
function buildPaginationUrl($page_num, $action_filter, $date_from, $date_to, $limit) {
    $params = [];
    $params['page'] = $page_num;
    if ($action_filter) $params['action'] = $action_filter;
    if ($date_from) $params['date_from'] = $date_from;
    if ($date_to) $params['date_to'] = $date_to;
    if ($limit != 20) $params['limit'] = $limit; // Only add if not default

    return '?' . http_build_query($params);
}
?>

<!-- Statistics Cards -->
<div class="row mb-4">
    <div class="col-md-3">
        <div class="card border-0 shadow-sm">
            <div class="card-body text-center">
                <h3 class="text-primary"><?php echo number_format($total_logs); ?></h3>
                <p class="text-muted mb-0">Total Entries</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card border-0 shadow-sm">
            <div class="card-body text-center">
                <h3 class="text-success"><?php echo number_format($today_count); ?></h3>
                <p class="text-muted mb-0">Today</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card border-0 shadow-sm">
            <div class="card-body text-center">
                <h3 class="text-info"><?php echo number_format($login_count); ?></h3>
                <p class="text-muted mb-0">Login Events</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card border-0 shadow-sm">
            <div class="card-body text-center">
                <h3 class="text-warning"><?php echo number_format($security_count); ?></h3>
                <p class="text-muted mb-0">Security Events</p>
            </div>
        </div>
    </div>
</div>

<!-- Filters -->
<div class="card border-0 shadow-sm mb-4">
    <div class="card-header bg-white border-0 py-3">
        <h5 class="card-title mb-0">
            <i class="fas fa-filter text-primary"></i> Filter Logs
        </h5>
    </div>
    <div class="card-body">
        <form method="GET" class="row g-3">
            <div class="col-md-3">
                <label for="action" class="form-label">Action</label>
                <input type="text" class="form-control" id="action" name="action" 
                       value="<?php echo htmlspecialchars($action_filter); ?>" 
                       placeholder="e.g., login, logout, settings">
            </div>
            <div class="col-md-3">
                <label for="date_from" class="form-label">From Date</label>
                <input type="date" class="form-control" id="date_from" name="date_from" 
                       value="<?php echo htmlspecialchars($date_from); ?>">
            </div>
            <div class="col-md-3">
                <label for="date_to" class="form-label">To Date</label>
                <input type="date" class="form-control" id="date_to" name="date_to" 
                       value="<?php echo htmlspecialchars($date_to); ?>">
            </div>
            <div class="col-md-3">
                <label class="form-label">&nbsp;</label>
                <div class="d-grid gap-2">
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-search"></i> Filter
                    </button>
                    <a href="audit-logs.php" class="btn btn-outline-secondary">
                        <i class="fas fa-times"></i> Clear
                    </a>
                </div>
            </div>
        </form>
    </div>
</div>

<div class="row">
    <!-- Audit Logs -->
    <div class="col-lg-8">
        <div class="card border-0 shadow-sm">
            <div class="card-header bg-white border-0 py-3 d-flex justify-content-between align-items-center">
                <h5 class="card-title mb-0">
                    <i class="fas fa-clipboard-list text-info"></i> Audit Trail
                </h5>
                <div class="d-flex align-items-center gap-3">
                    <?php if ($total_pages > 1): ?>
                        <small class="text-muted">
                            Page <?php echo $page; ?> of <?php echo $total_pages; ?>
                            (<?php echo number_format(($page - 1) * $limit + 1); ?>-<?php echo number_format(min($page * $limit, $total_logs)); ?> of <?php echo number_format($total_logs); ?>)
                        </small>
                    <?php endif; ?>
                    <span class="badge bg-primary"><?php echo number_format($total_logs); ?> entries</span>
                </div>
            </div>
            <div class="card-body p-0">
                <?php if (empty($logs)): ?>
                    <div class="text-center py-5">
                        <i class="fas fa-clipboard-list fa-3x text-muted mb-3"></i>
                        <p class="text-muted">No audit logs found.</p>
                    </div>
                <?php else: ?>
                    <div class="list-group list-group-flush">
                        <?php foreach ($logs as $log): ?>
                            <div class="list-group-item border-0 py-3">
                                <div class="d-flex align-items-start">
                                    <div class="flex-shrink-0 me-3">
                                        <?php
                                        $icon_class = 'fas fa-info-circle text-info';
                                        $bg_class = 'bg-light';
                                        
                                        switch ($log['action']) {
                                            case 'login':
                                                $icon_class = 'fas fa-sign-in-alt text-success';
                                                $bg_class = 'bg-success bg-opacity-10';
                                                break;
                                            case 'logout':
                                                $icon_class = 'fas fa-sign-out-alt text-warning';
                                                $bg_class = 'bg-warning bg-opacity-10';
                                                break;
                                            case 'login_failed':
                                                $icon_class = 'fas fa-exclamation-triangle text-danger';
                                                $bg_class = 'bg-danger bg-opacity-10';
                                                break;
                                            case 'settings_update':
                                            case 'smtp_config_update':
                                                $icon_class = 'fas fa-cogs text-primary';
                                                $bg_class = 'bg-primary bg-opacity-10';
                                                break;
                                            case 'user_suspended':
                                            case 'user_activated':
                                            case 'user_deleted':
                                                $icon_class = 'fas fa-users text-warning';
                                                $bg_class = 'bg-warning bg-opacity-10';
                                                break;
                                            case 'email_template_test':
                                                $icon_class = 'fas fa-envelope text-info';
                                                $bg_class = 'bg-info bg-opacity-10';
                                                break;
                                        }
                                        ?>
                                        <div class="rounded-circle p-2 <?php echo $bg_class; ?>">
                                            <i class="<?php echo $icon_class; ?>"></i>
                                        </div>
                                    </div>
                                    <div class="flex-grow-1">
                                        <div class="d-flex justify-content-between align-items-start mb-1">
                                            <h6 class="mb-0"><?php echo htmlspecialchars($log['description']); ?></h6>
                                            <small class="text-muted">
                                                <?php echo date('M j, Y g:i A', strtotime($log['created_at'])); ?>
                                            </small>
                                        </div>
                                        
                                        <div class="d-flex align-items-center gap-3 mb-2">
                                            <small class="text-muted">
                                                <i class="fas fa-user"></i> <?php echo htmlspecialchars($log['username']); ?>
                                            </small>
                                            <small class="text-muted">
                                                <i class="fas fa-map-marker-alt"></i> <?php echo htmlspecialchars($log['ip_address']); ?>
                                            </small>
                                            <small class="text-muted">
                                                <i class="fas fa-tag"></i> <?php echo htmlspecialchars($log['action']); ?>
                                            </small>
                                        </div>
                                        
                                        <?php if (!empty($log['details'])): ?>
                                            <div class="mt-2">
                                                <button class="btn btn-sm btn-outline-secondary" type="button" 
                                                        data-bs-toggle="collapse" data-bs-target="#details-<?php echo $log['id']; ?>">
                                                    <i class="fas fa-eye"></i> View Details
                                                </button>
                                                <div class="collapse mt-2" id="details-<?php echo $log['id']; ?>">
                                                    <div class="card card-body bg-light">
                                                        <pre class="mb-0 small"><?php echo htmlspecialchars(json_encode($log['details'], JSON_PRETTY_PRINT)); ?></pre>
                                                    </div>
                                                </div>
                                            </div>
                                        <?php endif; ?>
                                    </div>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>
                <?php endif; ?>
            </div>
        </div>
        
        <!-- Enhanced Pagination -->
        <?php if ($total_pages > 1): ?>
            <nav aria-label="Audit logs pagination" class="mt-4">
                <div class="d-flex justify-content-between align-items-center mb-3">
                    <div class="text-muted small">
                        Showing <?php echo number_format(($page - 1) * $limit + 1); ?>-<?php echo number_format(min($page * $limit, $total_logs)); ?>
                        of <?php echo number_format($total_logs); ?> entries
                    </div>
                    <div class="d-flex align-items-center gap-2">
                        <span class="text-muted small">Items per page:</span>
                        <select class="form-select form-select-sm" style="width: auto;" onchange="changeItemsPerPage(this.value)">
                            <option value="10" <?php echo $limit == 10 ? 'selected' : ''; ?>>10</option>
                            <option value="20" <?php echo $limit == 20 ? 'selected' : ''; ?>>20</option>
                            <option value="50" <?php echo $limit == 50 ? 'selected' : ''; ?>>50</option>
                            <option value="100" <?php echo $limit == 100 ? 'selected' : ''; ?>>100</option>
                        </select>
                    </div>
                </div>

                <ul class="pagination justify-content-center">
                    <!-- First Page -->
                    <?php if ($page > 3): ?>
                        <li class="page-item">
                            <a class="page-link" href="<?php echo buildPaginationUrl(1, $action_filter, $date_from, $date_to, $limit); ?>">
                                <i class="fas fa-angle-double-left"></i> First
                            </a>
                        </li>
                    <?php endif; ?>

                    <!-- Previous Page -->
                    <?php if ($page > 1): ?>
                        <li class="page-item">
                            <a class="page-link" href="<?php echo buildPaginationUrl($page - 1, $action_filter, $date_from, $date_to, $limit); ?>">
                                <i class="fas fa-chevron-left"></i>
                            </a>
                        </li>
                    <?php endif; ?>

                    <!-- Page Numbers -->
                    <?php
                    $start_page = max(1, $page - 3);
                    $end_page = min($total_pages, $page + 3);

                    for ($i = $start_page; $i <= $end_page; $i++):
                    ?>
                        <li class="page-item <?php echo $i === $page ? 'active' : ''; ?>">
                            <a class="page-link" href="<?php echo buildPaginationUrl($i, $action_filter, $date_from, $date_to, $limit); ?>">
                                <?php echo $i; ?>
                            </a>
                        </li>
                    <?php endfor; ?>

                    <!-- Next Page -->
                    <?php if ($page < $total_pages): ?>
                        <li class="page-item">
                            <a class="page-link" href="<?php echo buildPaginationUrl($page + 1, $action_filter, $date_from, $date_to, $limit); ?>">
                                <i class="fas fa-chevron-right"></i>
                            </a>
                        </li>
                    <?php endif; ?>

                    <!-- Last Page -->
                    <?php if ($page < $total_pages - 2): ?>
                        <li class="page-item">
                            <a class="page-link" href="<?php echo buildPaginationUrl($total_pages, $action_filter, $date_from, $date_to, $limit); ?>">
                                Last <i class="fas fa-angle-double-right"></i>
                            </a>
                        </li>
                    <?php endif; ?>
                </ul>
            </nav>
        <?php endif; ?>
    </div>
    
    <!-- Action Statistics -->
    <div class="col-lg-4">
        <div class="card border-0 shadow-sm">
            <div class="card-header bg-white border-0 py-3">
                <h5 class="card-title mb-0">
                    <i class="fas fa-chart-bar text-success"></i> Top Actions (24h)
                </h5>
            </div>
            <div class="card-body">
                <?php if (empty($action_stats)): ?>
                    <div class="text-center py-3">
                        <i class="fas fa-chart-bar fa-2x text-muted mb-2"></i>
                        <p class="text-muted small">No activity in the last 24 hours.</p>
                    </div>
                <?php else: ?>
                    <?php foreach ($action_stats as $stat): ?>
                        <div class="d-flex justify-content-between align-items-center mb-3">
                            <div>
                                <span class="fw-medium"><?php echo ucfirst(str_replace('_', ' ', $stat['action'])); ?></span>
                            </div>
                            <div>
                                <span class="badge bg-primary"><?php echo number_format($stat['count']); ?></span>
                            </div>
                        </div>
                    <?php endforeach; ?>
                <?php endif; ?>
            </div>
        </div>
        
        <!-- Export Options -->
        <div class="card border-0 shadow-sm mt-4">
            <div class="card-header bg-white border-0 py-3">
                <h5 class="card-title mb-0">
                    <i class="fas fa-download text-info"></i> Export Options
                </h5>
            </div>
            <div class="card-body">
                <div class="d-grid gap-2">
                    <button class="btn btn-outline-primary" onclick="exportLogs('csv')">
                        <i class="fas fa-file-csv"></i> Export as CSV
                    </button>
                    <button class="btn btn-outline-success" onclick="exportLogs('json')">
                        <i class="fas fa-file-code"></i> Export as JSON
                    </button>
                    <button class="btn btn-outline-info" onclick="exportLogs('pdf')">
                        <i class="fas fa-file-pdf"></i> Export as PDF
                    </button>
                </div>
                
                <hr>
                
                <div class="alert alert-info">
                    <i class="fas fa-info-circle"></i>
                    <strong>Note:</strong><br>
                    <small>Exports include current filter settings and are limited to 1000 records for performance.</small>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function exportLogs(format) {
    const params = new URLSearchParams(window.location.search);
    params.set('export', format);
    params.set('limit', '1000');

    window.location.href = 'export-audit-logs.php?' + params.toString();
}

function changeItemsPerPage(newLimit) {
    const params = new URLSearchParams(window.location.search);
    params.set('limit', newLimit);
    params.set('page', '1'); // Reset to first page when changing limit

    window.location.href = '?' + params.toString();
}
</script>

<?php
// Include footer
include 'includes/footer.php';
?>
