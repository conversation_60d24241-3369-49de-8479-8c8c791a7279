<?php
/**
 * Manual Database Fix for Dual Authentication
 * This script adds the missing columns to support 6-digit security codes
 */

session_start();
require_once 'includes/auth.php';
requireSuperAdminAuth();

echo "<h2>Dual Authentication Database Fix</h2>";
echo "<p>This script will add the missing columns to support 6-digit security codes.</p>";

try {
    require_once '../config/database.php';
    $db = getDB();
    
    echo "<p>✓ Database connection successful</p>";
    
    // Add the missing columns one by one
    $columns_to_add = [
        "security_code_hash VARCHAR(255) DEFAULT NULL COMMENT 'Hashed 6-digit security code'",
        "security_code_enabled TINYINT(1) DEFAULT 0 COMMENT 'Whether 6-digit security code is enabled'",
        "auth_method ENUM('none', 'security_code', 'google_2fa', 'both') DEFAULT 'none' COMMENT 'Active authentication method'",
        "security_code_created_at TIMESTAMP NULL DEFAULT NULL COMMENT 'When security code was created'",
        "security_code_last_used TIMESTAMP NULL DEFAULT NULL COMMENT 'When security code was last used'"
    ];
    
    echo "<h3>Adding Missing Columns:</h3>";
    
    foreach ($columns_to_add as $column_def) {
        $column_name = explode(' ', $column_def)[0];
        
        try {
            // Check if column exists first
            $check_sql = "SHOW COLUMNS FROM super_admin_2fa_settings LIKE '$column_name'";
            $result = $db->query($check_sql);
            
            if ($result && $result->num_rows > 0) {
                echo "<p>⚠ Column '$column_name' already exists - skipping</p>";
            } else {
                // Add the column
                $sql = "ALTER TABLE super_admin_2fa_settings ADD COLUMN $column_def";
                $db->query($sql);
                echo "<p>✓ Added column: $column_name</p>";
            }
        } catch (Exception $e) {
            echo "<p>✗ Error adding column '$column_name': " . htmlspecialchars($e->getMessage()) . "</p>";
        }
    }
    
    // Add indexes
    echo "<h3>Adding Indexes:</h3>";
    
    try {
        $db->query("ALTER TABLE super_admin_2fa_settings ADD INDEX idx_auth_method (auth_method)");
        echo "<p>✓ Added index: idx_auth_method</p>";
    } catch (Exception $e) {
        echo "<p>⚠ Index idx_auth_method: " . htmlspecialchars($e->getMessage()) . "</p>";
    }
    
    try {
        $db->query("ALTER TABLE super_admin_2fa_settings ADD INDEX idx_security_code_enabled (security_code_enabled)");
        echo "<p>✓ Added index: idx_security_code_enabled</p>";
    } catch (Exception $e) {
        echo "<p>⚠ Index idx_security_code_enabled: " . htmlspecialchars($e->getMessage()) . "</p>";
    }
    
    // Update existing records
    echo "<h3>Updating Existing Records:</h3>";
    
    try {
        $sql = "UPDATE super_admin_2fa_settings SET auth_method = CASE 
                    WHEN google_2fa_enabled = 1 AND google_2fa_secret IS NOT NULL THEN 'google_2fa' 
                    ELSE 'none' 
                END 
                WHERE auth_method IS NULL OR auth_method = ''";
        $result = $db->query($sql);
        echo "<p>✓ Updated existing records with proper auth_method</p>";
    } catch (Exception $e) {
        echo "<p>✗ Error updating records: " . htmlspecialchars($e->getMessage()) . "</p>";
    }
    
    // Add system settings
    echo "<h3>Adding System Settings:</h3>";
    
    $settings = [
        ['dual_auth_enabled', '1', 'Enable dual authentication system (6-digit code + Google 2FA)', 'boolean'],
        ['security_code_length', '6', 'Length of security code (default: 6 digits)', 'number'],
        ['security_code_expiry_days', '90', 'Days before security code expires and needs to be changed', 'number'],
        ['allow_both_auth_methods', '1', 'Allow super admins to use both authentication methods', 'boolean'],
        ['default_auth_method', 'security_code', 'Default authentication method for new super admins', 'text']
    ];
    
    foreach ($settings as $setting) {
        try {
            $sql = "INSERT INTO super_admin_settings (setting_key, setting_value, setting_description, setting_type) 
                    VALUES (?, ?, ?, ?) 
                    ON DUPLICATE KEY UPDATE 
                    setting_value = VALUES(setting_value), 
                    setting_description = VALUES(setting_description)";
            $db->query($sql, $setting);
            echo "<p>✓ Added/Updated setting: " . htmlspecialchars($setting[0]) . "</p>";
        } catch (Exception $e) {
            echo "<p>✗ Error adding setting '" . htmlspecialchars($setting[0]) . "': " . htmlspecialchars($e->getMessage()) . "</p>";
        }
    }
    
    // Test the function
    echo "<h3>Testing Security Code Function:</h3>";
    
    try {
        require_once 'includes/2fa-functions.php';
        
        $username = $_SESSION['super_admin_username'] ?? 'superadmin';
        $test_code = '123456';
        
        if (setSuperAdminSecurityCode($username, $test_code)) {
            echo "<p>✓ setSuperAdminSecurityCode function works correctly!</p>";
            
            // Clean up test
            $db->query("UPDATE super_admin_2fa_settings SET security_code_hash = NULL, security_code_enabled = 0 WHERE super_admin_username = ?", [$username]);
            echo "<p>✓ Test data cleaned up</p>";
        } else {
            echo "<p>✗ setSuperAdminSecurityCode function failed</p>";
        }
    } catch (Exception $e) {
        echo "<p>✗ Error testing function: " . htmlspecialchars($e->getMessage()) . "</p>";
    }
    
    echo "<h3>✅ Database Fix Complete!</h3>";
    echo "<p>You can now go to <a href='2fa-setup.php'>2FA Setup</a> to configure your 6-digit security code.</p>";
    
} catch (Exception $e) {
    echo "<p>✗ Database connection failed: " . htmlspecialchars($e->getMessage()) . "</p>";
}
?>
