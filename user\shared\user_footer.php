<?php
/**
 * User Dashboard Footer Component
 * Professional footer for user dashboard
 */

// Include config if not already included
if (!function_exists('getBankName')) {
    require_once __DIR__ . '/../../config/config.php';
}

// Get current year
$current_year = date('Y');

// Get site name from super admin settings or fallback
$site_name = getBankName();

// Get base URL
$protocol = isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? 'https' : 'http';
$host = $_SERVER['HTTP_HOST'];
$base_url = $protocol . '://' . $host . '/online_banking';
?>

<!-- User Dashboard Footer -->
<footer class="user-dashboard-footer">
    <div class="footer-content">
        <div class="footer-left">
            <div class="footer-brand">
                <strong><?php echo htmlspecialchars($site_name); ?></strong>
            </div>
            <div class="footer-copyright">
                &copy; <?php echo $current_year; ?> <?php echo htmlspecialchars($site_name); ?>. All rights reserved.
            </div>
        </div>
        
        <div class="footer-right">
            <div class="footer-links">
                <a href="#" class="footer-link">Privacy Policy</a>
                <a href="#" class="footer-link">Terms of Service</a>
                <a href="#" class="footer-link">Support</a>
                <a href="#" class="footer-link">Contact</a>
            </div>
            <div class="footer-security">
                <i class="fas fa-shield-alt"></i>
                <span>Secured Banking</span>
            </div>
        </div>
    </div>
</footer>

<style>
    /* Include dynamic CSS variables */
    <?php
    if (function_exists('getInlineDynamicCSS')) {
        echo getInlineDynamicCSS();
    }
    ?>

.user-dashboard-footer {
    background: var(--background-white);
    border-top: 1px solid var(--border-color);
    padding: 2rem;
    margin-top: auto;
    box-shadow: 0 -2px 10px rgba(0,0,0,0.05);
    width: 100%;
}

.footer-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 100%;
    margin: 0;
}

.footer-left {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.footer-brand {
    font-size: 1.125rem;
    font-weight: 600;
    color: var(--text-primary);
}

.footer-copyright {
    font-size: 0.875rem;
    color: var(--text-secondary);
}

.footer-right {
    display: flex;
    flex-direction: column;
    align-items: flex-end;
    gap: 0.75rem;
}

.footer-links {
    display: flex;
    gap: 1.5rem;
}

.footer-link {
    font-size: 0.875rem;
    color: var(--text-secondary);
    text-decoration: none;
    transition: color 0.2s ease;
}

.footer-link:hover {
    color: var(--primary-color);
    text-decoration: none;
}

.footer-security {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.875rem;
    color: var(--success-color);
    font-weight: 500;
}

.footer-security i {
    font-size: 1rem;
}

/* Mobile responsiveness */
@media (max-width: 768px) {
    .user-dashboard-footer {
        padding: 1.5rem 1rem;
    }

    .footer-content {
        flex-direction: column;
        gap: 1.5rem;
        text-align: center;
    }

    .footer-right {
        align-items: center;
    }

    .footer-links {
        flex-wrap: wrap;
        justify-content: center;
        gap: 1rem;
    }
}
</style>

<!-- Bootstrap JavaScript -->
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
