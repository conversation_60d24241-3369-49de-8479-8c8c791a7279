<?php
/**
 * Database Diagnostic and Fix Script for Dual Authentication
 * This script will check and fix the database structure for the dual authentication system
 */

session_start();
require_once 'includes/auth.php';
requireSuperAdminAuth();

$results = [];
$errors = [];
$fixes_applied = [];

try {
    require_once '../config/database.php';
    $db = getDB();
    $results[] = "✓ Database connection successful";
    
    // Check if super_admin_2fa_settings table exists
    $table_check = $db->query("SHOW TABLES LIKE 'super_admin_2fa_settings'");
    if ($table_check && $table_check->num_rows > 0) {
        $results[] = "✓ super_admin_2fa_settings table exists";
        
        // Check current columns
        $columns_result = $db->query("SHOW COLUMNS FROM super_admin_2fa_settings");
        $existing_columns = [];
        
        if ($columns_result) {
            while ($row = $columns_result->fetch_assoc()) {
                $existing_columns[] = $row['Field'];
            }
            $results[] = "✓ Current columns: " . implode(', ', $existing_columns);
        }
        
        // Check for new columns needed for dual auth
        $required_columns = [
            'security_code_hash' => 'VARCHAR(255) DEFAULT NULL COMMENT "Hashed 6-digit security code"',
            'security_code_enabled' => 'TINYINT(1) DEFAULT 0 COMMENT "Whether 6-digit security code is enabled"',
            'auth_method' => 'ENUM("none", "security_code", "google_2fa", "both") DEFAULT "none" COMMENT "Active authentication method"',
            'security_code_created_at' => 'TIMESTAMP NULL DEFAULT NULL COMMENT "When security code was created"',
            'security_code_last_used' => 'TIMESTAMP NULL DEFAULT NULL COMMENT "When security code was last used"'
        ];
        
        $missing_columns = [];
        foreach ($required_columns as $column => $definition) {
            if (!in_array($column, $existing_columns)) {
                $missing_columns[$column] = $definition;
            }
        }
        
        if (!empty($missing_columns)) {
            $results[] = "⚠ Missing columns for dual auth: " . implode(', ', array_keys($missing_columns));
            
            // Apply fixes if requested
            if (isset($_POST['apply_fixes'])) {
                foreach ($missing_columns as $column => $definition) {
                    try {
                        $sql = "ALTER TABLE super_admin_2fa_settings ADD COLUMN $column $definition";
                        $db->query($sql);
                        $fixes_applied[] = "✓ Added column: $column";
                    } catch (Exception $e) {
                        $errors[] = "✗ Failed to add column $column: " . $e->getMessage();
                    }
                }
                
                // Add indexes
                try {
                    $db->query("ALTER TABLE super_admin_2fa_settings ADD INDEX IF NOT EXISTS idx_auth_method (auth_method)");
                    $fixes_applied[] = "✓ Added index: idx_auth_method";
                } catch (Exception $e) {
                    // Index might already exist
                }
                
                try {
                    $db->query("ALTER TABLE super_admin_2fa_settings ADD INDEX IF NOT EXISTS idx_security_code_enabled (security_code_enabled)");
                    $fixes_applied[] = "✓ Added index: idx_security_code_enabled";
                } catch (Exception $e) {
                    // Index might already exist
                }
                
                // Update existing records
                try {
                    $db->query("UPDATE super_admin_2fa_settings SET auth_method = CASE WHEN google_2fa_enabled = 1 AND google_2fa_secret IS NOT NULL THEN 'google_2fa' ELSE 'none' END WHERE auth_method = 'none' OR auth_method IS NULL");
                    $fixes_applied[] = "✓ Updated existing records with proper auth_method";
                } catch (Exception $e) {
                    $errors[] = "✗ Failed to update existing records: " . $e->getMessage();
                }
            }
        } else {
            $results[] = "✓ All required columns exist for dual authentication";
        }
        
        // Check super_admin_settings table for dual auth settings
        $settings_check = $db->query("SELECT COUNT(*) as count FROM super_admin_settings WHERE setting_key = 'dual_auth_enabled'");
        if ($settings_check && $row = $settings_check->fetch_assoc()) {
            if ($row['count'] > 0) {
                $results[] = "✓ Dual auth settings exist in super_admin_settings";
            } else {
                $results[] = "⚠ Missing dual auth settings in super_admin_settings";
                
                if (isset($_POST['apply_fixes'])) {
                    // Add missing settings
                    $settings = [
                        ['dual_auth_enabled', '1', 'Enable dual authentication system (6-digit code + Google 2FA)', 'boolean'],
                        ['security_code_length', '6', 'Length of security code (default: 6 digits)', 'number'],
                        ['security_code_expiry_days', '90', 'Days before security code expires and needs to be changed', 'number'],
                        ['allow_both_auth_methods', '1', 'Allow super admins to use both authentication methods', 'boolean'],
                        ['default_auth_method', 'security_code', 'Default authentication method for new super admins', 'text']
                    ];
                    
                    foreach ($settings as $setting) {
                        try {
                            $sql = "INSERT INTO super_admin_settings (setting_key, setting_value, setting_description, setting_type) VALUES (?, ?, ?, ?) ON DUPLICATE KEY UPDATE setting_value = VALUES(setting_value), setting_description = VALUES(setting_description)";
                            $db->query($sql, $setting);
                            $fixes_applied[] = "✓ Added setting: " . $setting[0];
                        } catch (Exception $e) {
                            $errors[] = "✗ Failed to add setting " . $setting[0] . ": " . $e->getMessage();
                        }
                    }
                }
            }
        }
        
        // Test the setSuperAdminSecurityCode function
        if (empty($missing_columns) && isset($_POST['test_function'])) {
            $test_code = '123456';
            $username = $_SESSION['super_admin_username'] ?? 'superadmin';
            
            try {
                // Include the 2FA functions
                require_once 'includes/2fa-functions.php';
                
                if (setSuperAdminSecurityCode($username, $test_code)) {
                    $results[] = "✓ setSuperAdminSecurityCode function works correctly";
                    
                    // Clean up test
                    $db->query("UPDATE super_admin_2fa_settings SET security_code_hash = NULL, security_code_enabled = 0 WHERE super_admin_username = ?", [$username]);
                } else {
                    $errors[] = "✗ setSuperAdminSecurityCode function failed";
                }
            } catch (Exception $e) {
                $errors[] = "✗ Error testing setSuperAdminSecurityCode: " . $e->getMessage();
            }
        }
        
    } else {
        $errors[] = "✗ super_admin_2fa_settings table does not exist";
        
        if (isset($_POST['apply_fixes'])) {
            // Create the table
            try {
                $sql = file_get_contents('../database/create_super_admin_2fa_table.sql');
                $statements = array_filter(array_map('trim', explode(';', $sql)));
                
                foreach ($statements as $statement) {
                    if (!empty($statement) && !preg_match('/^\s*--/', $statement)) {
                        $db->query($statement);
                    }
                }
                $fixes_applied[] = "✓ Created super_admin_2fa_settings table";
            } catch (Exception $e) {
                $errors[] = "✗ Failed to create table: " . $e->getMessage();
            }
        }
    }
    
} catch (Exception $e) {
    $errors[] = "✗ Database connection failed: " . $e->getMessage();
}

// Set page title
$page_title = 'Database Diagnostic';
require_once 'includes/header.php';
?>

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="page-header">
                <h1 class="page-title">
                    <i class="fas fa-database"></i>
                    Database Diagnostic & Fix
                </h1>
                <p class="page-subtitle">Check and fix database structure for dual authentication</p>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-lg-8">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-primary text-white">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-search"></i> Diagnostic Results
                    </h5>
                </div>
                <div class="card-body">
                    <?php if (!empty($results)): ?>
                    <h6>Status Check:</h6>
                    <ul class="list-unstyled">
                        <?php foreach ($results as $result): ?>
                        <li class="mb-2">
                            <?php if (strpos($result, '✓') === 0): ?>
                                <span class="text-success"><?php echo htmlspecialchars($result); ?></span>
                            <?php elseif (strpos($result, '⚠') === 0): ?>
                                <span class="text-warning"><?php echo htmlspecialchars($result); ?></span>
                            <?php else: ?>
                                <span class="text-info"><?php echo htmlspecialchars($result); ?></span>
                            <?php endif; ?>
                        </li>
                        <?php endforeach; ?>
                    </ul>
                    <?php endif; ?>

                    <?php if (!empty($fixes_applied)): ?>
                    <h6 class="mt-4">Fixes Applied:</h6>
                    <ul class="list-unstyled">
                        <?php foreach ($fixes_applied as $fix): ?>
                        <li class="mb-2">
                            <span class="text-success"><?php echo htmlspecialchars($fix); ?></span>
                        </li>
                        <?php endforeach; ?>
                    </ul>
                    <?php endif; ?>

                    <?php if (!empty($errors)): ?>
                    <h6 class="mt-4">Errors:</h6>
                    <ul class="list-unstyled">
                        <?php foreach ($errors as $error): ?>
                        <li class="mb-2">
                            <span class="text-danger"><?php echo htmlspecialchars($error); ?></span>
                        </li>
                        <?php endforeach; ?>
                    </ul>
                    <?php endif; ?>

                    <div class="mt-4">
                        <form method="POST" action="" class="d-inline">
                            <button type="submit" name="apply_fixes" value="1" class="btn btn-warning me-2"
                                    onclick="return confirm('Are you sure you want to apply database fixes? This will modify your database structure.')">
                                <i class="fas fa-wrench"></i> Apply Fixes
                            </button>
                        </form>
                        
                        <form method="POST" action="" class="d-inline">
                            <button type="submit" name="test_function" value="1" class="btn btn-info me-2">
                                <i class="fas fa-test-tube"></i> Test Function
                            </button>
                        </form>
                        
                        <a href="2fa-setup.php" class="btn btn-success">
                            <i class="fas fa-cog"></i> Go to 2FA Setup
                        </a>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-lg-4">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-info text-white">
                    <h6 class="card-title mb-0">
                        <i class="fas fa-info-circle"></i> What This Does
                    </h6>
                </div>
                <div class="card-body">
                    <p class="small">This diagnostic tool:</p>
                    <ul class="small">
                        <li>Checks if the database table exists</li>
                        <li>Verifies all required columns are present</li>
                        <li>Tests the security code function</li>
                        <li>Applies fixes automatically if needed</li>
                    </ul>
                    
                    <div class="alert alert-warning mt-3">
                        <small>
                            <strong>Note:</strong> Always backup your database before applying fixes.
                        </small>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<?php require_once 'includes/footer.php'; ?>
