<?php
/**
 * Admin Add User Test - Simulates admin user creation form
 * Tests the exact same process as admin/add-user.php
 */

// Start session for admin simulation
session_start();

// Simulate admin session (for testing purposes)
$_SESSION['user_id'] = 1;
$_SESSION['username'] = 'admin';
$_SESSION['is_admin'] = true;

// Include required files
require_once 'config/config.php';
require_once 'config/database.php';
require_once 'config/email.php';
require_once 'includes/functions.php';

// Enable error reporting
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

echo "<!DOCTYPE html>";
echo "<html><head><title>Admin Add User Test</title>";
echo "<style>
body { font-family: Arial, sans-serif; margin: 20px; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); min-height: 100vh; }
.container { max-width: 800px; margin: 0 auto; background: white; padding: 30px; border-radius: 15px; box-shadow: 0 8px 32px rgba(0,0,0,0.3); }
.header { background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%); color: white; padding: 30px; text-align: center; border-radius: 10px; margin-bottom: 20px; }
.success { background: linear-gradient(135deg, #28a745 0%, #20c997 100%); color: white; padding: 20px; border-radius: 10px; margin: 15px 0; }
.error { background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%); color: white; padding: 20px; border-radius: 10px; margin: 15px 0; }
.info { background: #e7f3ff; border: 1px solid #b3d9ff; color: #0c5460; padding: 20px; border-radius: 10px; margin: 15px 0; }
.form-group { margin: 15px 0; }
.form-group label { display: block; font-weight: bold; margin-bottom: 5px; }
.form-group input, .form-group select { width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 5px; }
.btn { background: #007bff; color: white; padding: 12px 24px; border: none; border-radius: 5px; cursor: pointer; }
.btn:hover { background: #0056b3; }
</style>";
echo "</head><body>";

echo "<div class='container'>";
echo "<div class='header'>";
echo "<h1>🏦 Admin Add User Test</h1>";
echo "<p>Testing admin user creation functionality with email</p>";
echo "</div>";

$errors = [];
$success = '';

// Simulate form submission
if (!isset($_POST['submit'])) {
    // Show form
    echo "<h2>👤 Create New User Account</h2>";
    echo "<div class='info'>This form simulates the admin add user functionality. Click 'Create User' to test.</div>";
    
    echo "<form method='POST'>";
    echo "<div class='form-group'>";
    echo "<label>Username:</label>";
    echo "<input type='text' name='username' value='demothedev' required>";
    echo "</div>";
    
    echo "<div class='form-group'>";
    echo "<label>Email:</label>";
    echo "<input type='email' name='email' value='<EMAIL>' required>";
    echo "</div>";
    
    echo "<div class='form-group'>";
    echo "<label>First Name:</label>";
    echo "<input type='text' name='first_name' value='Demo' required>";
    echo "</div>";
    
    echo "<div class='form-group'>";
    echo "<label>Last Name:</label>";
    echo "<input type='text' name='last_name' value='Developer' required>";
    echo "</div>";
    
    echo "<div class='form-group'>";
    echo "<label>Phone:</label>";
    echo "<input type='text' name='phone' value='******-0123'>";
    echo "</div>";
    
    echo "<div class='form-group'>";
    echo "<label>Address:</label>";
    echo "<input type='text' name='address' value='123 Demo Street, Test City, TC 12345'>";
    echo "</div>";
    
    echo "<div class='form-group'>";
    echo "<label>Date of Birth:</label>";
    echo "<input type='date' name='date_of_birth' value='1990-01-01'>";
    echo "</div>";
    
    echo "<div class='form-group'>";
    echo "<label>Occupation:</label>";
    echo "<input type='text' name='occupation' value='Software Developer'>";
    echo "</div>";
    
    echo "<div class='form-group'>";
    echo "<label>Account Type:</label>";
    echo "<select name='account_type'>";
    echo "<option value='savings' selected>Savings</option>";
    echo "<option value='checking'>Checking</option>";
    echo "<option value='business'>Business</option>";
    echo "</select>";
    echo "</div>";
    
    echo "<div class='form-group'>";
    echo "<label>Initial Balance:</label>";
    echo "<input type='number' name='initial_balance' value='5000.00' step='0.01' min='0'>";
    echo "</div>";
    
    echo "<div class='form-group'>";
    echo "<label>Password:</label>";
    echo "<input type='password' name='password' value='DemoPass123!' required>";
    echo "</div>";
    
    echo "<div class='form-group'>";
    echo "<label>Confirm Password:</label>";
    echo "<input type='password' name='confirm_password' value='DemoPass123!' required>";
    echo "</div>";
    
    echo "<div class='form-group'>";
    echo "<label>Currency:</label>";
    echo "<select name='currency'>";
    echo "<option value='USD' selected>USD</option>";
    echo "<option value='EUR'>EUR</option>";
    echo "<option value='GBP'>GBP</option>";
    echo "</select>";
    echo "</div>";
    
    echo "<div class='form-group'>";
    echo "<label>Status:</label>";
    echo "<select name='status'>";
    echo "<option value='active' selected>Active</option>";
    echo "<option value='suspended'>Suspended</option>";
    echo "</select>";
    echo "</div>";
    
    echo "<div class='form-group'>";
    echo "<label>KYC Status:</label>";
    echo "<select name='kyc_status'>";
    echo "<option value='pending' selected>Pending</option>";
    echo "<option value='verified'>Verified</option>";
    echo "<option value='rejected'>Rejected</option>";
    echo "</select>";
    echo "</div>";
    
    echo "<button type='submit' name='submit' class='btn'>🚀 Create User & Send Welcome Email</button>";
    echo "</form>";
    
} else {
    // Process form submission (same logic as admin/add-user.php)
    echo "<h2>🔄 Processing User Creation...</h2>";
    
    // Sanitize and validate input (same as admin/add-user.php)
    $username = sanitizeInput($_POST['username'] ?? '');
    $email = sanitizeInput($_POST['email'] ?? '');
    $first_name = sanitizeInput($_POST['first_name'] ?? '');
    $last_name = sanitizeInput($_POST['last_name'] ?? '');
    $phone = sanitizeInput($_POST['phone'] ?? '');
    $address = sanitizeInput($_POST['address'] ?? '');
    $date_of_birth = sanitizeInput($_POST['date_of_birth'] ?? '');
    $occupation = sanitizeInput($_POST['occupation'] ?? '');
    $marital_status = sanitizeInput($_POST['marital_status'] ?? 'single');
    $gender = sanitizeInput($_POST['gender'] ?? 'male');
    $currency = sanitizeInput($_POST['currency'] ?? 'USD');
    $account_type = sanitizeInput($_POST['account_type'] ?? 'savings');
    $initial_balance = floatval($_POST['initial_balance'] ?? 0);
    $password = $_POST['password'] ?? '';
    $confirm_password = $_POST['confirm_password'] ?? '';
    $kyc_status = sanitizeInput($_POST['kyc_status'] ?? 'pending');
    $status = sanitizeInput($_POST['status'] ?? 'active');
    
    // Validation (same as admin/add-user.php)
    if (empty($username)) {
        $errors[] = 'Username is required.';
    } elseif (strlen($username) < 3) {
        $errors[] = 'Username must be at least 3 characters long.';
    }
    
    if (empty($email)) {
        $errors[] = 'Email is required.';
    } elseif (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
        $errors[] = 'Please enter a valid email address.';
    }
    
    if (empty($first_name)) {
        $errors[] = 'First name is required.';
    }
    
    if (empty($last_name)) {
        $errors[] = 'Last name is required.';
    }
    
    if (empty($password)) {
        $errors[] = 'Password is required.';
    } elseif (strlen($password) < 8) {
        $errors[] = 'Password must be at least 8 characters long.';
    }
    
    if ($password !== $confirm_password) {
        $errors[] = 'Passwords do not match.';
    }
    
    // Check for existing username and email
    if (empty($errors)) {
        try {
            $db = getDB();
            
            $existing_user = $db->query("SELECT id FROM accounts WHERE username = ? OR email = ?", [$username, $email]);
            if ($existing_user && $existing_user->num_rows > 0) {
                $errors[] = 'Username or email already exists.';
            }
        } catch (Exception $e) {
            $errors[] = 'Database error during validation.';
        }
    }
    
    // Display validation results
    if (!empty($errors)) {
        echo "<div class='error'>";
        echo "<h3>❌ Validation Errors:</h3>";
        echo "<ul>";
        foreach ($errors as $error) {
            echo "<li>" . htmlspecialchars($error) . "</li>";
        }
        echo "</ul>";
        echo "</div>";
    }
    
    // Create user if no errors (same logic as admin/add-user.php)
    if (empty($errors)) {
        try {
            $db = getDB();
            $db->beginTransaction();
            
            // Generate account number
            $account_number = generateAccountNumber();
            echo "<div class='info'>📋 Generated Account Number: <strong>$account_number</strong></div>";
            
            // Hash password
            $hashed_password = hashPassword($password);
            echo "<div class='info'>🔐 Password hashed successfully</div>";
            
            // Insert user (same SQL as admin/add-user.php)
            $sql = "INSERT INTO accounts (
                        account_number, username, password, email, first_name, last_name,
                        phone, address, date_of_birth, occupation, marital_status, gender,
                        currency, account_type, balance, status, kyc_status, is_admin
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, 0)";

            $params = [
                $account_number, $username, $hashed_password, $email, $first_name, $last_name,
                $phone, $address, $date_of_birth ?: null, $occupation, $marital_status, $gender,
                $currency, $account_type, $initial_balance, $status, $kyc_status
            ];
            
            $user_id = $db->insert($sql, $params);
            echo "<div class='success'>✅ User created successfully with ID: $user_id</div>";

            $db->commit();

            // Prepare user data for welcome email (same as admin/add-user.php)
            $user_data = [
                'first_name' => $first_name,
                'last_name' => $last_name,
                'username' => $username,
                'email' => $email,
                'account_number' => $account_number,
                'account_type' => $account_type,
                'currency' => $currency,
                'balance' => $initial_balance,
                'status' => $status
            ];

            echo "<h3>📧 Sending Welcome Email...</h3>";
            echo "<div class='info'>📤 Attempting to send welcome email to: <strong>$email</strong></div>";
            
            // Send welcome email (same as admin/add-user.php)
            $emailSent = sendWelcomeEmail($email, $user_data);

            if ($emailSent) {
                echo "<div class='success'>";
                echo "<h3>🎉 SUCCESS! User Created and Welcome Email Sent!</h3>";
                echo "<p><strong>Account Details:</strong></p>";
                echo "<ul>";
                echo "<li><strong>Username:</strong> $username</li>";
                echo "<li><strong>Email:</strong> $email</li>";
                echo "<li><strong>Account Number:</strong> $account_number</li>";
                echo "<li><strong>Password:</strong> $password</li>";
                echo "<li><strong>Initial Balance:</strong> $" . number_format($initial_balance, 2) . "</li>";
                echo "<li><strong>Status:</strong> $status</li>";
                echo "</ul>";
                echo "<p>✅ Welcome email sent successfully to <strong>$email</strong></p>";
                echo "</div>";
            } else {
                echo "<div class='error'>";
                echo "<h3>⚠️ User Created but Welcome Email Failed</h3>";
                echo "<p>Account Number: <strong>$account_number</strong></p>";
                echo "<p>Note: Welcome email could not be sent. Check email configuration.</p>";
                echo "</div>";
            }
            
        } catch (Exception $e) {
            if (isset($db)) {
                $db->rollback();
            }
            echo "<div class='error'>❌ Failed to create user: " . $e->getMessage() . "</div>";
        }
    }
    
    echo "<div class='info'>";
    echo "<h3>🔍 What to Check:</h3>";
    echo "<ol>";
    echo "<li>Check email inbox at <strong><EMAIL></strong></li>";
    echo "<li>Look for welcome email with account details</li>";
    echo "<li>Check spam/junk folder if not in inbox</li>";
    echo "<li>Try logging in with the created credentials</li>";
    echo "</ol>";
    echo "</div>";
    
    echo "<p><a href='admin_add_user_test.php' class='btn'>🔄 Test Again</a></p>";
}

echo "<p><strong>Test completed at:</strong> " . date('Y-m-d H:i:s') . "</p>";
echo "</div>";
echo "</body></html>";
?>
