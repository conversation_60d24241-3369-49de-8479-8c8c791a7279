<?php
require_once '../config/config.php';
requireLogin();

$page_title = 'Help & Support';
$site_name = getBankName();

// Handle ticket submission
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action']) && $_POST['action'] === 'create_ticket') {
    try {
        $db = getDB();
        $user_id = $_SESSION['user_id'];

        $subject = trim($_POST['subject'] ?? '');
        $message = trim($_POST['message'] ?? '');
        $priority = $_POST['priority'] ?? 'medium';

        // Validate inputs
        if (empty($subject) || empty($message)) {
            throw new Exception('Subject and message are required');
        }

        if (!in_array($priority, ['low', 'medium', 'high', 'urgent'])) {
            $priority = 'medium';
        }

        // Generate ticket number
        $ticket_number = 'TKT-' . date('Ymd') . '-' . str_pad(rand(1, 9999), 4, '0', STR_PAD_LEFT);

        // Insert ticket
        $insert_sql = "INSERT INTO tickets (ticket_number, user_id, subject, message, status, priority, created_at)
                       VALUES (?, ?, ?, ?, 'open', ?, NOW())";

        $result = $db->query($insert_sql, [$ticket_number, $user_id, $subject, $message, $priority]);

        if ($result) {
            $success_message = "Support ticket created successfully! Ticket number: $ticket_number";
        } else {
            throw new Exception('Failed to create support ticket');
        }

    } catch (Exception $e) {
        $error_message = $e->getMessage();
    }
}

// Get user's support tickets
try {
    $db = getDB();
    $user_id = $_SESSION['user_id'];

    // Get user's tickets
    $tickets_sql = "SELECT * FROM tickets WHERE user_id = ? ORDER BY created_at DESC LIMIT 10";
    $tickets_result = $db->query($tickets_sql, [$user_id]);
    $user_tickets = [];
    while ($ticket = $tickets_result->fetch_assoc()) {
        $user_tickets[] = $ticket;
    }

    // Get ticket statistics
    $stats_sql = "SELECT
                    COUNT(*) as total_tickets,
                    COUNT(CASE WHEN status = 'open' THEN 1 END) as open_tickets,
                    COUNT(CASE WHEN status = 'in_progress' THEN 1 END) as in_progress_tickets,
                    COUNT(CASE WHEN status = 'resolved' THEN 1 END) as resolved_tickets
                  FROM tickets WHERE user_id = ?";

    $stats_result = $db->query($stats_sql, [$user_id]);
    $ticket_stats = $stats_result->fetch_assoc();

} catch (Exception $e) {
    error_log("Help page error: " . $e->getMessage());
    $user_tickets = [];
    $ticket_stats = ['total_tickets' => 0, 'open_tickets' => 0, 'in_progress_tickets' => 0, 'resolved_tickets' => 0];
}

// Include header
require_once '../includes/dashboard/header.php';

// Include sidebar
require_once '../includes/dashboard/sidebar.php';
?>

<!-- Main Content -->
<div class="main-content">
    <!-- Top Bar -->
    <div class="top-bar">
        <h1>Help & Support</h1>
        <div class="top-bar-actions">
            <button class="btn-outline" onclick="showCreateTicketModal()">
                <svg width="16" height="16" fill="currentColor" viewBox="0 0 16 16">
                    <path d="M8 4a.5.5 0 01.5.5v3h3a.5.5 0 010 1h-3v3a.5.5 0 01-1 0v-3h-3a.5.5 0 010-1h3v-3A.5.5 0 018 4z"/>
                </svg>
                New Ticket
            </button>
            <button class="btn-primary" onclick="window.location.reload()">
                <svg width="16" height="16" fill="currentColor" viewBox="0 0 16 16">
                    <path fill-rule="evenodd" d="M8 3a5 5 0 104.546 2.914.5.5 0 00-.908-.417A4 4 0 118 4v1z"/>
                    <path d="M8 4.466V.534a.25.25 0 01.41-.192l2.36 1.966c.12.1.12.284 0 .384L8.41 4.658A.25.25 0 018 4.466z"/>
                </svg>
                Refresh
            </button>
            <div style="width: 40px; height: 40px; border-radius: 50%; background: #6366f1; display: flex; align-items: center; justify-content: center; color: white; font-weight: 600;">
                <?php echo strtoupper(substr($_SESSION['first_name'] ?? 'U', 0, 1)); ?>
            </div>
        </div>
    </div>

    <!-- Success/Error Messages -->
    <?php if (isset($success_message)): ?>
        <div class="alert alert-success" style="margin-bottom: 2rem; padding: 1rem; background: #d1fae5; border: 1px solid #10b981; border-radius: 8px; color: #065f46;">
            <strong>Success!</strong> <?php echo htmlspecialchars($success_message); ?>
        </div>
    <?php endif; ?>

    <?php if (isset($error_message)): ?>
        <div class="alert alert-danger" style="margin-bottom: 2rem; padding: 1rem; background: #fee2e2; border: 1px solid #ef4444; border-radius: 8px; color: #991b1b;">
            <strong>Error!</strong> <?php echo htmlspecialchars($error_message); ?>
        </div>
    <?php endif; ?>

    <!-- Support Statistics -->
    <div class="stats-grid" style="margin-bottom: 2rem;">
        <div class="stat-card">
            <h3 class="stat-title">Total Tickets</h3>
            <p class="stat-value"><?php echo number_format($ticket_stats['total_tickets']); ?></p>
            <p class="stat-change">All time</p>
        </div>
        <div class="stat-card">
            <h3 class="stat-title">Open Tickets</h3>
            <p class="stat-value"><?php echo number_format($ticket_stats['open_tickets']); ?></p>
            <p class="stat-change">Awaiting response</p>
        </div>
        <div class="stat-card">
            <h3 class="stat-title">In Progress</h3>
            <p class="stat-value"><?php echo number_format($ticket_stats['in_progress_tickets']); ?></p>
            <p class="stat-change">Being worked on</p>
        </div>
        <div class="stat-card">
            <h3 class="stat-title">Resolved</h3>
            <p class="stat-value"><?php echo number_format($ticket_stats['resolved_tickets']); ?></p>
            <p class="stat-change">Completed</p>
        </div>
    </div>

    <!-- Main Dashboard Grid -->
    <div class="dashboard-grid">
        <div class="main-section">
            <!-- Support Tickets -->
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">Your Support Tickets</h3>
                    <button class="btn-outline" onclick="showCreateTicketModal()">Create New Ticket</button>
                </div>
                <div class="card-body">
                    <?php if (!empty($user_tickets)): ?>
                        <?php foreach ($user_tickets as $ticket): ?>
                            <div class="transaction-item">
                                <div class="transaction-icon" style="background: <?php
                                    echo $ticket['status'] === 'resolved' ? '#10b981' :
                                        ($ticket['status'] === 'in_progress' ? '#f59e0b' : '#6366f1');
                                ?>;">
                                    <?php if ($ticket['status'] === 'resolved'): ?>
                                        <svg width="16" height="16" fill="currentColor" viewBox="0 0 16 16">
                                            <path d="M10.97 4.97a.75.75 0 0 1 1.07 1.05l-3.99 4.99a.75.75 0 0 1-1.08.02L4.324 8.384a.75.75 0 1 1 1.06-1.06l2.094 2.093 3.473-4.425a.267.267 0 0 1 .02-.022z"/>
                                        </svg>
                                    <?php elseif ($ticket['status'] === 'in_progress'): ?>
                                        <svg width="16" height="16" fill="currentColor" viewBox="0 0 16 16">
                                            <path d="M8 3a5 5 0 104.546 2.914.5.5 0 00-.908-.417A4 4 0 118 4v1z"/>
                                            <path d="M8 4.466V.534a.25.25 0 01.41-.192l2.36 1.966c.12.1.12.284 0 .384L8.41 4.658A.25.25 0 018 4.466z"/>
                                        </svg>
                                    <?php else: ?>
                                        <svg width="16" height="16" fill="currentColor" viewBox="0 0 16 16">
                                            <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-8-3a1 1 0 00-.867.5 1 1 0 11-1.731-1A3 3 0 0113 8a3.001 3.001 0 01-2 2.83V11a1 1 0 11-2 0v-1a1 1 0 011-1 1 1 0 100-2zm0 8a1 1 0 100-2 1 1 0 000 2z"/>
                                        </svg>
                                    <?php endif; ?>
                                </div>
                                <div class="transaction-content">
                                    <div class="transaction-title">
                                        <?php echo htmlspecialchars($ticket['subject']); ?>
                                    </div>
                                    <div class="transaction-subtitle">
                                        Ticket #<?php echo htmlspecialchars($ticket['ticket_number']); ?> •
                                        Priority: <?php echo ucfirst($ticket['priority']); ?>
                                    </div>
                                </div>
                                <div style="text-align: right;">
                                    <span class="<?php
                                        echo $ticket['status'] === 'resolved' ? 'status-completed' :
                                            ($ticket['status'] === 'in_progress' ? 'status-pending' : 'status-pending');
                                    ?>">
                                        <?php echo ucfirst(str_replace('_', ' ', $ticket['status'])); ?>
                                    </span>
                                    <div class="transaction-date">
                                        <?php echo date('M j, Y', strtotime($ticket['created_at'])); ?>
                                    </div>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    <?php else: ?>
                        <div style="text-align: center; padding: 3rem 1rem;">
                            <div style="font-size: 3rem; color: #e5e7eb; margin-bottom: 1rem;">🎫</div>
                            <h3 style="color: #6b7280; margin-bottom: 0.5rem;">No Support Tickets</h3>
                            <p style="color: #9ca3af; margin-bottom: 2rem;">Need help? Create your first support ticket and we'll assist you.</p>
                            <button class="btn-primary" onclick="showCreateTicketModal()">Create Support Ticket</button>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>

        <!-- Sidebar Section -->
        <div class="sidebar-section">
            <!-- Quick Help -->
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">Quick Help</h3>
                </div>
                <div style="padding: 1.5rem;">
                    <div style="display: flex; flex-direction: column; gap: 0.75rem;">
                        <button class="btn-primary" onclick="showCreateTicketModal()" style="width: 100%;">
                            <svg width="16" height="16" fill="currentColor" viewBox="0 0 16 16" style="margin-right: 0.5rem;">
                                <path d="M8 4a.5.5 0 01.5.5v3h3a.5.5 0 010 1h-3v3a.5.5 0 01-1 0v-3h-3a.5.5 0 010-1h3v-3A.5.5 0 018 4z"/>
                            </svg>
                            Create Support Ticket
                        </button>
                        <button class="btn-outline" onclick="showFAQ()" style="width: 100%;">
                            <svg width="16" height="16" fill="currentColor" viewBox="0 0 16 16" style="margin-right: 0.5rem;">
                                <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-8-3a1 1 0 00-.867.5 1 1 0 11-1.731-1A3 3 0 0113 8a3.001 3.001 0 01-2 2.83V11a1 1 0 11-2 0v-1a1 1 0 011-1 1 1 0 100-2zm0 8a1 1 0 100-2 1 1 0 000 2z"/>
                            </svg>
                            View FAQ
                        </button>
                        <button class="btn-outline" onclick="contactSupport()" style="width: 100%;">
                            <svg width="16" height="16" fill="currentColor" viewBox="0 0 16 16" style="margin-right: 0.5rem;">
                                <path d="M8 1a2.5 2.5 0 012.5 2.5V4h-5v-.5A2.5 2.5 0 018 1zm3.5 3v-.5a3.5 3.5 0 11-7 0V4H1v10a2 2 0 002 2h10a2 2 0 002-2V4h-3.5z"/>
                            </svg>
                            Live Chat
                        </button>
                    </div>
                </div>
            </div>

            <!-- Common Issues -->
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">Common Issues</h3>
                </div>
                <div style="padding: 1rem;">
                    <div style="display: flex; flex-direction: column; gap: 0.75rem;">
                        <div style="padding: 0.75rem; border: 1px solid #e5e7eb; border-radius: 8px; cursor: pointer;" onclick="showSolution('password')">
                            <div style="font-weight: 500; margin-bottom: 0.25rem;">Password Reset</div>
                            <div style="font-size: 0.875rem; color: #6b7280;">How to reset your password</div>
                        </div>
                        <div style="padding: 0.75rem; border: 1px solid #e5e7eb; border-radius: 8px; cursor: pointer;" onclick="showSolution('transfer')">
                            <div style="font-weight: 500; margin-bottom: 0.25rem;">Transfer Issues</div>
                            <div style="font-size: 0.875rem; color: #6b7280;">Problems with money transfers</div>
                        </div>
                        <div style="padding: 0.75rem; border: 1px solid #e5e7eb; border-radius: 8px; cursor: pointer;" onclick="showSolution('card')">
                            <div style="font-weight: 500; margin-bottom: 0.25rem;">Virtual Card Problems</div>
                            <div style="font-size: 0.875rem; color: #6b7280;">Issues with virtual cards</div>
                        </div>
                        <div style="padding: 0.75rem; border: 1px solid #e5e7eb; border-radius: 8px; cursor: pointer;" onclick="showSolution('account')">
                            <div style="font-weight: 500; margin-bottom: 0.25rem;">Account Access</div>
                            <div style="font-size: 0.875rem; color: #6b7280;">Can't access your account</div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Contact Information -->
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">Contact Us</h3>
                </div>
                <div style="padding: 1.5rem;">
                    <div style="display: flex; flex-direction: column; gap: 1rem;">
                        <div style="display: flex; align-items: center; gap: 0.75rem;">
                            <div style="width: 32px; height: 32px; border-radius: 50%; background: #6366f1; display: flex; align-items: center; justify-content: center; color: white;">
                                <svg width="16" height="16" fill="currentColor" viewBox="0 0 16 16">
                                    <path d="M3.654 1.328a.678.678 0 0 0-1.015-.063L1.605 2.3c-.483.484-.661 1.169-.45 1.77a17.568 17.568 0 0 0 4.168 6.608 17.569 17.569 0 0 0 6.608 4.168c.601.211 1.286.033 1.77-.45l1.034-1.034a.678.678 0 0 0-.063-1.015l-2.307-1.794a.678.678 0 0 0-.58-.122L9.98 10.97a6.716 6.716 0 0 1-3.284-.878 6.716 6.716 0 0 1-.878-3.284l.54-1.805a.678.678 0 0 0-.122-.58L3.654 1.328z"/>
                                </svg>
                            </div>
                            <div>
                                <div style="font-weight: 500;">Phone Support</div>
                                <div style="font-size: 0.875rem; color: #6b7280;">1-800-BANK-HELP</div>
                                <div style="font-size: 0.75rem; color: #9ca3af;">24/7 Available</div>
                            </div>
                        </div>
                        <div style="display: flex; align-items: center; gap: 0.75rem;">
                            <div style="width: 32px; height: 32px; border-radius: 50%; background: #10b981; display: flex; align-items: center; justify-content: center; color: white;">
                                <svg width="16" height="16" fill="currentColor" viewBox="0 0 16 16">
                                    <path d="M0 4a2 2 0 012-2h12a2 2 0 012 2v8a2 2 0 01-2 2H2a2 2 0 01-2-2V4zm2-1a1 1 0 00-1 1v.217l7 4.2 7-4.2V4a1 1 0 00-1-1H2zm13 2.383l-4.758 2.855L15 11.114v-5.73zm-.034 6.878L9.271 8.82 8 9.583 6.728 8.82l-5.694 3.44A1 1 0 002 13h12a1 1 0 00.966-.739zM1 11.114l4.758-2.876L1 5.383v5.73z"/>
                                </svg>
                            </div>
                            <div>
                                <div style="font-weight: 500;">Email Support</div>
                                <div style="font-size: 0.875rem; color: #6b7280;"><EMAIL></div>
                                <div style="font-size: 0.75rem; color: #9ca3af;">Response within 24h</div>
                            </div>
                        </div>
                        <div style="display: flex; align-items: center; gap: 0.75rem;">
                            <div style="width: 32px; height: 32px; border-radius: 50%; background: #f59e0b; display: flex; align-items: center; justify-content: center; color: white;">
                                <svg width="16" height="16" fill="currentColor" viewBox="0 0 16 16">
                                    <path fill-rule="evenodd" d="M18 5v8a2 2 0 01-2 2h-5l-5 4v-4H4a2 2 0 01-2-2V5a2 2 0 012-2h12a2 2 0 012 2zM7 8H5v2h2V8zm2 0h2v2H9V8zm6 0h-2v2h2V8z"/>
                                </svg>
                            </div>
                            <div>
                                <div style="font-weight: 500;">Live Chat</div>
                                <div style="font-size: 0.875rem; color: #6b7280;">Available now</div>
                                <div style="font-size: 0.75rem; color: #9ca3af;">Instant response</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Create Ticket Modal -->
<div class="modal fade" id="createTicketModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Create Support Ticket</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form method="POST">
                <input type="hidden" name="action" value="create_ticket">
                <div class="modal-body">
                    <div class="mb-3">
                        <label class="form-label">Subject *</label>
                        <input type="text" class="form-control" name="subject" required placeholder="Brief description of your issue">
                    </div>
                    <div class="mb-3">
                        <label class="form-label">Priority</label>
                        <select class="form-control" name="priority">
                            <option value="low">Low - General inquiry</option>
                            <option value="medium" selected>Medium - Standard issue</option>
                            <option value="high">High - Important issue</option>
                            <option value="urgent">Urgent - Critical issue</option>
                        </select>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">Message *</label>
                        <textarea class="form-control" name="message" rows="6" required placeholder="Please describe your issue in detail..."></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary">Create Ticket</button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
function showCreateTicketModal() {
    const modal = new bootstrap.Modal(document.getElementById('createTicketModal'));
    modal.show();
}

function showFAQ() {
    alert('FAQ section coming soon!');
}

function contactSupport() {
    alert('Live chat functionality coming soon!');
}

function showSolution(type) {
    let message = '';
    switch(type) {
        case 'password':
            message = 'To reset your password:\n1. Go to Settings page\n2. Click "Change Password"\n3. Enter your current password\n4. Enter your new password\n5. Confirm the new password';
            break;
        case 'transfer':
            message = 'For transfer issues:\n1. Check your account balance\n2. Verify recipient details\n3. Ensure you have sufficient funds\n4. Contact support if the issue persists';
            break;
        case 'card':
            message = 'For virtual card problems:\n1. Check if the card is active\n2. Verify spending limits\n3. Check card balance\n4. Try freezing and unfreezing the card';
            break;
        case 'account':
            message = 'If you cannot access your account:\n1. Try resetting your password\n2. Clear your browser cache\n3. Check if your account is suspended\n4. Contact support for assistance';
            break;
    }
    alert(message);
}
</script>

<?php
// Include footer
require_once '../includes/dashboard/footer.php';
?>
