[2025-07-21 16:12:29] === ADMIN ERROR CHECK STARTED ===
[2025-07-21 16:12:29] Step 1: Testing basic PHP functionality
[2025-07-21 16:12:29] Step 2: Testing file paths
[2025-07-21 16:12:29] Config path: C:\MAMP\htdocs\online_banking\admin/../config/config.php
[2025-07-21 16:12:29] Debug path: C:\MAMP\htdocs\online_banking\admin/../config/debug-system.php
[2025-07-21 16:12:29] Step 2: File paths verified
[2025-07-21 16:12:29] Step 3: Including debug system
[2025-07-21 16:12:29] Step 3: Debug system loaded successfully
[2025-07-21 16:12:29] Step 4: Including config
[2025-07-21 16:12:29] Step 4: Config loaded successfully
[2025-07-21 16:12:29] Step 5: Testing database connection
[2025-07-21 16:12:29] Step 5: Database connected successfully
[2025-07-21 16:12:29] Step 6: Testing admin authentication function
[2025-07-21 16:12:29] Step 6: requireAdmin function exists
[2025-07-21 16:12:29] Step 7: Testing session
[2025-07-21 16:12:29] Step 7: Session handling working
[2025-07-21 16:12:29] Step 8: Testing admin header include
[2025-07-21 16:12:29] Step 8: Admin header file readable (30345 bytes)
[2025-07-21 16:12:29] Step 9: Testing dynamic CSS
[2025-07-21 16:12:58] === ADMIN ERROR CHECK STARTED ===
[2025-07-21 16:12:58] Step 1: Testing basic PHP functionality
[2025-07-21 16:12:58] Step 2: Testing file paths
[2025-07-21 16:12:58] Config path: C:\MAMP\htdocs\online_banking\admin/../config/config.php
[2025-07-21 16:12:58] Debug path: C:\MAMP\htdocs\online_banking\admin/../config/debug-system.php
[2025-07-21 16:12:58] Step 2: File paths verified
[2025-07-21 16:12:58] Step 3: Including debug system
[2025-07-21 16:12:58] Step 3: Debug system loaded successfully
[2025-07-21 16:12:58] Step 4: Including config
[2025-07-21 16:12:58] Step 4: Config loaded successfully
[2025-07-21 16:12:58] Step 5: Testing database connection
[2025-07-21 16:12:58] Step 5: Database connected successfully
[2025-07-21 16:12:58] Step 6: Testing admin authentication function
[2025-07-21 16:12:58] Step 6: requireAdmin function exists
[2025-07-21 16:12:58] Step 7: Testing session
[2025-07-21 16:12:58] Step 7: Session handling working
[2025-07-21 16:12:58] Step 8: Testing admin header include
[2025-07-21 16:12:58] Step 8: Admin header file readable (30345 bytes)
[2025-07-21 16:12:58] Step 9: Testing dynamic CSS
[2025-07-21 16:12:58] Step 9: Dynamic CSS generated (1414 chars)
[2025-07-21 16:12:58] === ALL BASIC TESTS PASSED ===
[2025-07-21 16:12:58] Step 10: Simulating admin index execution
[2025-07-21 16:12:58] Step 10: Database queries working - Found 14 users
[2025-07-21 16:12:58] === FULL SIMULATION SUCCESSFUL ===
