<?php
require_once '../config/config.php';
requireAdmin();

header('Content-Type: application/json');

try {
    $transaction_id = intval($_GET['id'] ?? 0);
    $type = $_GET['type'] ?? 'account';
    
    if ($transaction_id <= 0) {
        throw new Exception('Invalid transaction ID');
    }
    
    $db = getDB();
    
    if ($type === 'account') {
        // Get account transaction details
        $query = "SELECT at.*,
                  a.first_name, a.last_name, a.username, a.account_number,
                  admin.first_name as admin_first_name, admin.last_name as admin_last_name
                  FROM account_transactions at
                  LEFT JOIN accounts a ON at.account_id = a.id
                  LEFT JOIN accounts admin ON at.processed_by = admin.id
                  WHERE at.id = ?";
        
        $result = $db->query($query, [$transaction_id]);
        $transaction = $result->fetch_assoc();
        
        if (!$transaction) {
            throw new Exception('Transaction not found');
        }
        
        // Add currency if not set
        if (empty($transaction['currency'])) {
            $transaction['currency'] = 'USD';
        }
        
        echo json_encode($transaction);
        
    } elseif ($type === 'card') {
        // Get virtual card transaction details
        $query = "SELECT vct.*, 
                  vc.card_number, vc.card_holder_name,
                  a.first_name, a.last_name, a.username, a.account_number,
                  admin.first_name as admin_first_name, admin.last_name as admin_last_name
                  FROM virtual_card_transactions vct 
                  LEFT JOIN virtual_cards vc ON vct.card_id = vc.card_id 
                  LEFT JOIN accounts a ON vct.account_id = a.id 
                  LEFT JOIN accounts admin ON vct.processed_by = admin.id 
                  WHERE vct.id = ?";
        
        $result = $db->query($query, [$transaction_id]);
        $transaction = $result->fetch_assoc();
        
        if (!$transaction) {
            throw new Exception('Card transaction not found');
        }
        
        // Add currency if not set
        if (empty($transaction['currency'])) {
            $transaction['currency'] = 'USD';
        }
        
        echo json_encode($transaction);
        
    } else {
        throw new Exception('Invalid transaction type');
    }
    
} catch (Exception $e) {
    http_response_code(400);
    echo json_encode(['error' => $e->getMessage()]);
}
?>
