-- Create user_documents table for document management
CREATE TABLE IF NOT EXISTS user_documents (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    document_type VARCHAR(50) NOT NULL,
    document_name VARCHAR(255) NOT NULL,
    file_path VARCHAR(500) NOT NULL,
    file_size INT NOT NULL,
    file_type VARCHAR(100) NOT NULL,
    verification_status ENUM('pending', 'approved', 'rejected') DEFAULT 'pending',
    notes TEXT,
    uploaded_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES accounts(id) ON DELETE CASCADE,
    INDEX idx_user_id (user_id),
    INDEX idx_verification_status (verification_status)
);

-- Insert some sample documents for testing (adjust user IDs as needed)
INSERT INTO user_documents (user_id, document_type, document_name, file_path, file_size, file_type, verification_status, notes) VALUES
(1, 'id_card', 'Driver License.pdf', 'uploads/documents/user_1/drivers_license.pdf', 2048576, 'application/pdf', 'pending', 'Uploaded driver license for verification'),
(1, 'proof_of_address', 'Utility Bill.jpg', 'uploads/documents/user_1/utility_bill.jpg', 1024000, 'image/jpeg', 'approved', 'Utility bill showing current address'),
(2, 'passport', 'Passport Copy.pdf', 'uploads/documents/user_2/passport.pdf', 3072000, 'application/pdf', 'pending', 'Passport copy for international verification'),
(2, 'bank_statement', 'Bank Statement.pdf', 'uploads/documents/user_2/bank_statement.pdf', 1536000, 'application/pdf', 'rejected', 'Bank statement is too old');
