<?php
// Install PHPMailer using Composer
$output = [];
$status = null;
$command = __DIR__ . '/composer.phar require phpmailer/phpmailer';
exec($command, $output, $status);

echo "<h1>PHPMailer Installation Result</h1>";
echo "<pre>";
echo "Command: $command\n";
echo "Status: $status\n";
echo "Output:\n" . implode("\n", $output);
echo "</pre>";

if ($status === 0) {
    echo "<p style='color:green;'>PHPMailer installed successfully!</p>";
} else {
    echo "<p style='color:red;'>Failed to install PHPMailer.</p>";
}
?>
