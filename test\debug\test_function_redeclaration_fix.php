<?php
/**
 * Test script to verify function redeclaration fix
 */

// Enable error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);
ini_set('log_errors', 1);
ini_set('error_log', __DIR__ . '/../../logs/error.log');

echo "<h1>Function Redeclaration Fix Test</h1>";

try {
    echo "<h2>1. Testing Config Include</h2>";
    require_once '../../config/config.php';
    echo "✅ Config included successfully<br>";
    
    echo "<h2>2. Testing Super Admin Settings Include</h2>";
    require_once '../../config/super_admin_settings.php';
    echo "✅ Super admin settings included successfully<br>";
    
    echo "<h2>3. Testing 2FA Functions Include</h2>";
    require_once '../../super-admin/includes/2fa-functions.php';
    echo "✅ 2FA functions included successfully<br>";
    
    echo "<h2>4. Testing getSuperAdminSetting Function</h2>";
    $test_setting = getSuperAdminSetting('test_key', 'default_value');
    echo "✅ getSuperAdminSetting function works: " . $test_setting . "<br>";
    
    echo "<h2>5. Testing Super Admin 2FA Functions</h2>";
    $settings = getSuperAdmin2FASettings('superadmin');
    echo "✅ getSuperAdmin2FASettings function works<br>";
    
    $is_enabled = isSuperAdmin2FAEnabled('superadmin');
    echo "✅ isSuperAdmin2FAEnabled function works: " . ($is_enabled ? 'enabled' : 'disabled') . "<br>";
    
    $is_required = isSuperAdmin2FARequired();
    echo "✅ isSuperAdmin2FARequired function works: " . ($is_required ? 'required' : 'not required') . "<br>";
    
    echo "<h2>✅ All tests passed! Function redeclaration issue is fixed.</h2>";
    
} catch (Exception $e) {
    echo "<h2>❌ Error occurred:</h2>";
    echo "<p><strong>Message:</strong> " . $e->getMessage() . "</p>";
    echo "<p><strong>File:</strong> " . $e->getFile() . "</p>";
    echo "<p><strong>Line:</strong> " . $e->getLine() . "</p>";
    echo "<p><strong>Trace:</strong></p>";
    echo "<pre>" . $e->getTraceAsString() . "</pre>";
} catch (Error $e) {
    echo "<h2>❌ Fatal error occurred:</h2>";
    echo "<p><strong>Message:</strong> " . $e->getMessage() . "</p>";
    echo "<p><strong>File:</strong> " . $e->getFile() . "</p>";
    echo "<p><strong>Line:</strong> " . $e->getLine() . "</p>";
    echo "<p><strong>Trace:</strong></p>";
    echo "<pre>" . $e->getTraceAsString() . "</pre>";
}

echo "<hr>";
echo "<p><strong>Log file location:</strong> " . __DIR__ . '/../../logs/error.log</p>';
echo "<p><strong>Check the error log for detailed information.</strong></p>";
?>
