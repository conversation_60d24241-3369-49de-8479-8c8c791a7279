<?php
/**
 * Print Bank Statement
 * Generates a print-friendly bank statement for outgoing transfers
 */

// Start session and check authentication
session_start();

if (!isset($_SESSION['user_id'])) {
    echo "<!DOCTYPE html><html><head><title>Session Error</title></head><body>";
    echo "<h1>Session Expired</h1>";
    echo "<p>Please <a href='../../auth/login.php'>login again</a></p>";
    echo "</body></html>";
    exit();
}

// Include required files
require_once '../../config/config.php';

// Get database connection
try {
    $db = getDB();
    $user_id = $_SESSION['user_id'];

    // Get user information
    $user_query = "SELECT * FROM accounts WHERE id = ?";
    $user_result = $db->query($user_query, [$user_id]);
    $user = $user_result->fetch_assoc();
} catch (Exception $e) {
    echo "<!DOCTYPE html><html><head><title>Database Error</title></head><body>";
    echo "<h1>Database Error</h1>";
    echo "<p>" . htmlspecialchars($e->getMessage()) . "</p>";
    echo "</body></html>";
    exit();
}

if (!$user) {
    echo "<!DOCTYPE html><html><head><title>User Error</title></head><body>";
    echo "<h1>User Not Found</h1>";
    echo "<p>User ID: " . htmlspecialchars($user_id) . "</p>";
    echo "</body></html>";
    exit();
}

// Get filter parameters from GET request
$date_from = $_GET['date_from'] ?? '';
$date_to = $_GET['date_to'] ?? '';
$transfer_type = $_GET['transfer_type'] ?? '';
$min_amount = $_GET['min_amount'] ?? '';
$max_amount = $_GET['max_amount'] ?? '';

// Build query with filters
$where_clause = "sender_id = ?";
$params = [$user_id];

if (!empty($date_from)) {
    $where_clause .= " AND DATE(created_at) >= ?";
    $params[] = $date_from;
}

if (!empty($date_to)) {
    $where_clause .= " AND DATE(created_at) <= ?";
    $params[] = $date_to;
}

if (!empty($transfer_type) && $transfer_type !== 'all') {
    $where_clause .= " AND transfer_type = ?";
    $params[] = $transfer_type;
}

if (!empty($min_amount)) {
    $where_clause .= " AND amount >= ?";
    $params[] = $min_amount;
}

if (!empty($max_amount)) {
    $where_clause .= " AND amount <= ?";
    $params[] = $max_amount;
}

// Get transfers data
$transfers_query = "SELECT * FROM transfers WHERE $where_clause ORDER BY created_at DESC";
try {
    $transfers_result = $db->query($transfers_query, $params);
    $transfers = [];
    while ($row = $transfers_result->fetch_assoc()) {
        $transfers[] = $row;
    }
} catch (Exception $e) {
    echo "Query Error: " . $e->getMessage();
    echo "<br>Query: " . $transfers_query;
    echo "<br>Params: " . print_r($params, true);
    exit();
}

// Calculate totals
$total_amount = 0;
$total_fees = 0;
foreach ($transfers as $transfer) {
    $total_amount += $transfer['amount'];
    $total_fees += $transfer['fee'];
}

// Set headers for PDF display
header('Content-Type: text/html; charset=UTF-8');

// Generate statement period
$period_start = !empty($date_from) ? date('M j, Y', strtotime($date_from)) : 'Beginning';
$period_end = !empty($date_to) ? date('M j, Y', strtotime($date_to)) : 'Present';
$statement_period = ($period_start === 'Beginning' && $period_end === 'Present') ? 'All Time' : "$period_start - $period_end";

?>
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>Bank Statement - <?php echo htmlspecialchars($user['first_name'] . ' ' . $user['last_name']); ?></title>
    <style>
        @page {
            size: A4;
            margin: 0.75in;
        }

        @media print {
            body { margin: 0; }
            .no-print { display: none !important; }
        }

        body {
            font-family: 'Arial', sans-serif;
            font-size: 11px;
            line-height: 1.4;
            color: #000;
            margin: 0;
            padding: 20px;
            background: white;
        }

        .print-controls {
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 1000;
            background: white;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 5px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .print-btn {
            background: #007bff;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            margin-right: 5px;
        }

        .print-btn:hover {
            background: #0056b3;
        }
        
        .statement-header {
            text-align: center;
            border-bottom: 3px solid #000;
            padding-bottom: 20px;
            margin-bottom: 30px;
        }
        
        .bank-name {
            font-size: 28px;
            font-weight: bold;
            margin-bottom: 5px;
            color: #2c3e50;
        }
        
        .statement-title {
            font-size: 18px;
            font-weight: bold;
            letter-spacing: 2px;
            color: #34495e;
            margin-bottom: 10px;
        }
        
        .statement-period {
            font-size: 14px;
            color: #7f8c8d;
            font-weight: 500;
        }
        
        .account-info {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 30px;
            margin-bottom: 30px;
            padding: 20px;
            background: #f8f9fa;
            border: 1px solid #dee2e6;
        }
        
        .info-section h3 {
            font-size: 14px;
            font-weight: bold;
            margin-bottom: 10px;
            color: #2c3e50;
            border-bottom: 1px solid #bdc3c7;
            padding-bottom: 5px;
        }
        
        .info-row {
            display: flex;
            justify-content: space-between;
            margin-bottom: 5px;
        }
        
        .info-label {
            font-weight: 500;
            color: #555;
        }
        
        .info-value {
            font-weight: 600;
            color: #2c3e50;
        }
        
        .summary-section {
            margin-bottom: 30px;
            padding: 20px;
            background: #e8f4f8;
            border: 1px solid #3498db;
            border-radius: 5px;
        }
        
        .summary-title {
            font-size: 16px;
            font-weight: bold;
            margin-bottom: 15px;
            color: #2c3e50;
            text-align: center;
        }
        
        .summary-grid {
            display: grid;
            grid-template-columns: 1fr 1fr 1fr;
            gap: 20px;
            text-align: center;
        }
        
        .summary-item {
            padding: 10px;
            background: white;
            border-radius: 3px;
            border: 1px solid #bdc3c7;
        }
        
        .summary-label {
            font-size: 10px;
            color: #7f8c8d;
            text-transform: uppercase;
            letter-spacing: 1px;
            margin-bottom: 5px;
        }
        
        .summary-value {
            font-size: 16px;
            font-weight: bold;
            color: #2c3e50;
        }
        
        .transactions-section {
            margin-bottom: 30px;
        }
        
        .section-title {
            font-size: 16px;
            font-weight: bold;
            margin-bottom: 15px;
            color: #2c3e50;
            border-bottom: 2px solid #3498db;
            padding-bottom: 5px;
        }
        
        .transactions-table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
            font-size: 10px;
        }
        
        .transactions-table th {
            background: #34495e;
            color: white;
            padding: 8px 5px;
            text-align: left;
            font-weight: bold;
            font-size: 9px;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }
        
        .transactions-table td {
            padding: 6px 5px;
            border-bottom: 1px solid #ecf0f1;
            vertical-align: top;
        }
        
        .transactions-table tr:nth-child(even) {
            background: #f8f9fa;
        }
        
        .amount-negative {
            color: #e74c3c;
            font-weight: bold;
        }
        
        .amount-positive {
            color: #27ae60;
            font-weight: bold;
        }
        
        .status-completed {
            color: #27ae60;
            font-weight: bold;
            font-size: 9px;
        }
        
        .status-pending {
            color: #f39c12;
            font-weight: bold;
            font-size: 9px;
        }
        
        .status-failed {
            color: #e74c3c;
            font-weight: bold;
            font-size: 9px;
        }
        
        .footer {
            position: fixed;
            bottom: 0.5in;
            left: 0.75in;
            right: 0.75in;
            text-align: center;
            font-size: 9px;
            color: #7f8c8d;
            border-top: 1px solid #bdc3c7;
            padding-top: 10px;
        }
        
        .page-break {
            page-break-before: always;
        }
        
        @media print {
            body { -webkit-print-color-adjust: exact; }
        }
    </style>
</head>
<body>
    <!-- Print Controls -->
    <div class="print-controls no-print">
        <button class="print-btn" onclick="window.print()">🖨️ Print Statement</button>
        <button class="print-btn" onclick="window.close()">✕ Close</button>
    </div>

    <div class="statement-header">
        <div class="bank-name">PremierBank Pro</div>
        <div class="statement-title">OUTGOING TRANSFER STATEMENT</div>
        <div class="statement-period"><?php echo htmlspecialchars($statement_period); ?></div>
    </div>

    <div class="account-info">
        <div class="info-section">
            <h3>Account Holder Information</h3>
            <div class="info-row">
                <span class="info-label">Name:</span>
                <span class="info-value"><?php echo htmlspecialchars($user['first_name'] . ' ' . $user['last_name']); ?></span>
            </div>
            <div class="info-row">
                <span class="info-label">Account Number:</span>
                <span class="info-value">****<?php echo substr($user['account_number'], -4); ?></span>
            </div>
            <div class="info-row">
                <span class="info-label">Account Type:</span>
                <span class="info-value"><?php echo ucfirst($user['account_type']); ?> Account</span>
            </div>
            <div class="info-row">
                <span class="info-label">Email:</span>
                <span class="info-value"><?php echo htmlspecialchars($user['email']); ?></span>
            </div>
        </div>
        <div class="info-section">
            <h3>Statement Information</h3>
            <div class="info-row">
                <span class="info-label">Statement Period:</span>
                <span class="info-value"><?php echo htmlspecialchars($statement_period); ?></span>
            </div>
            <div class="info-row">
                <span class="info-label">Generated On:</span>
                <span class="info-value"><?php echo date('M j, Y g:i A'); ?></span>
            </div>
            <div class="info-row">
                <span class="info-label">Total Transactions:</span>
                <span class="info-value"><?php echo count($transfers); ?></span>
            </div>
            <div class="info-row">
                <span class="info-label">Current Balance:</span>
                <span class="info-value">$<?php echo number_format($user['balance'], 2); ?></span>
            </div>
        </div>
    </div>

    <div class="summary-section">
        <div class="summary-title">Statement Summary</div>
        <div class="summary-grid">
            <div class="summary-item">
                <div class="summary-label">Total Transferred</div>
                <div class="summary-value amount-negative">-$<?php echo number_format($total_amount, 2); ?></div>
            </div>
            <div class="summary-item">
                <div class="summary-label">Total Fees</div>
                <div class="summary-value">$<?php echo number_format($total_fees, 2); ?></div>
            </div>
            <div class="summary-item">
                <div class="summary-label">Total Debited</div>
                <div class="summary-value amount-negative">-$<?php echo number_format($total_amount + $total_fees, 2); ?></div>
            </div>
        </div>
    </div>

    <div class="transactions-section">
        <div class="section-title">Transaction Details</div>
        
        <?php if (empty($transfers)): ?>
            <p style="text-align: center; color: #7f8c8d; font-style: italic; padding: 20px;">
                No outgoing transfers found for the selected period.
            </p>
        <?php else: ?>
            <table class="transactions-table">
                <thead>
                    <tr>
                        <th>Date</th>
                        <th>Reference</th>
                        <th>Recipient</th>
                        <th>Type</th>
                        <th>Description</th>
                        <th>Amount</th>
                        <th>Fee</th>
                        <th>Status</th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($transfers as $transfer): ?>
                    <tr>
                        <td><?php echo date('M j, Y', strtotime($transfer['created_at'])); ?></td>
                        <td style="font-family: monospace; font-size: 9px;"><?php echo htmlspecialchars($transfer['transaction_id']); ?></td>
                        <td>
                            <?php echo htmlspecialchars($transfer['recipient_name']); ?><br>
                            <small style="color: #7f8c8d;">****<?php echo substr($transfer['recipient_account'], -4); ?></small>
                        </td>
                        <td><?php echo ucfirst($transfer['transfer_type']); ?></td>
                        <td style="max-width: 120px; word-wrap: break-word;">
                            <?php echo htmlspecialchars(substr($transfer['description'], 0, 50)); ?>
                            <?php if (strlen($transfer['description']) > 50) echo '...'; ?>
                        </td>
                        <td class="amount-negative">-$<?php echo number_format($transfer['amount'], 2); ?></td>
                        <td><?php echo $transfer['fee'] > 0 ? '$' . number_format($transfer['fee'], 2) : 'Free'; ?></td>
                        <td class="status-<?php echo $transfer['status']; ?>">
                            <?php echo strtoupper($transfer['status']); ?>
                        </td>
                    </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
        <?php endif; ?>
    </div>

    <div class="footer">
        <p><strong>PremierBank Pro</strong> - Confidential Bank Statement</p>
        <p>This statement is generated electronically and contains confidential information. Please retain for your records.</p>
        <p>Generated on: <?php echo date('M j, Y g:i A'); ?> | Page 1 of 1</p>
    </div>

    <script>
        // Print functionality
        function printStatement() {
            window.print();
        }

        // Optional: Auto-print after a short delay (uncomment if desired)
        // setTimeout(() => window.print(), 1000);
    </script>
</body>
</html>
