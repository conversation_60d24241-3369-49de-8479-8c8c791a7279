<?php
require_once '../config/config.php';
requireAdmin();

$user_id = intval($_GET['id'] ?? 0);
$response = ['success' => false, 'message' => '', 'otp' => '', 'expires' => ''];

if ($user_id <= 0) {
    $response['message'] = 'Invalid user ID.';
    if (isset($_GET['ajax'])) {
        header('Content-Type: application/json');
        echo json_encode($response);
        exit();
    } else {
        setFlashMessage('error', 'Invalid user ID.');
        redirect('users.php');
    }
}

try {
    $db = getDB();
    
    // Get user information
    $sql = "SELECT id, username, first_name, last_name, email, status FROM accounts WHERE id = ? AND is_admin = 0";
    $result = $db->query($sql, [$user_id]);
    
    if ($result->num_rows === 0) {
        $response['message'] = 'User not found.';
        if (isset($_GET['ajax'])) {
            header('Content-Type: application/json');
            echo json_encode($response);
            exit();
        } else {
            setFlashMessage('error', 'User not found.');
            redirect('users.php');
        }
    }
    
    $user = $result->fetch_assoc();
    
    if ($user['status'] !== 'active') {
        $response['message'] = 'Cannot generate OTP for inactive user.';
        if (isset($_GET['ajax'])) {
            header('Content-Type: application/json');
            echo json_encode($response);
            exit();
        } else {
            setFlashMessage('error', 'Cannot generate OTP for inactive user.');
            redirect('view-user.php?id=' . $user_id);
        }
    }
    
    // Generate new OTP
    $otp = generateOTP();
    $user_name = $user['first_name'] . ' ' . $user['last_name'];
    
    // Store OTP in database with 'admin' source
    if (storeOTP($user_id, $otp, 10, 'admin')) {
        // Try to send email (optional for admin-generated OTP)
        $emailSent = sendOTPEmail($user['email'], $otp, $user_name);
        
        // Log admin activity
        logActivity($_SESSION['user_id'], 'Admin generated OTP for user', 'accounts', $user_id, null, [
            'otp_generated' => true,
            'email_sent' => $emailSent,
            'target_user' => $user['username']
        ]);
        
        $expires_at = date('Y-m-d H:i:s', time() + (10 * 60)); // 10 minutes from now
        
        $response['success'] = true;
        $response['otp'] = $otp;
        $response['expires'] = formatDate($expires_at, 'M d, Y H:i');
        $response['message'] = $emailSent ? 
            'OTP generated and sent to user email successfully.' : 
            'OTP generated successfully. Email delivery failed, but you can provide the code to the user.';
        
        if (isset($_GET['ajax'])) {
            header('Content-Type: application/json');
            echo json_encode($response);
            exit();
        } else {
            setFlashMessage('success', $response['message'] . ' OTP: ' . $otp);
            redirect('view-user.php?id=' . $user_id);
        }
    } else {
        $response['message'] = 'Failed to generate OTP. Please try again.';
        if (isset($_GET['ajax'])) {
            header('Content-Type: application/json');
            echo json_encode($response);
            exit();
        } else {
            setFlashMessage('error', 'Failed to generate OTP. Please try again.');
            redirect('view-user.php?id=' . $user_id);
        }
    }
    
} catch (Exception $e) {
    error_log("Generate OTP error: " . $e->getMessage());
    $response['message'] = 'An error occurred while generating OTP.';
    
    if (isset($_GET['ajax'])) {
        header('Content-Type: application/json');
        echo json_encode($response);
        exit();
    } else {
        setFlashMessage('error', 'An error occurred while generating OTP.');
        redirect('view-user.php?id=' . $user_id);
    }
}
?>
