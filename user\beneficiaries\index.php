<?php
/**
 * User Beneficiaries Management Page
 * Comprehensive beneficiaries management with modern UI
 */

// Enable error reporting for debugging
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Start session and check authentication
session_start();
if (!isset($_SESSION['user_id'])) {
    header('Location: ../../auth/login.php');
    exit();
}

// Include database connection and dynamic CSS
require_once '../../config/config.php';
require_once '../../config/dynamic-css.php';

// Get user data from database
$db = getDB();
$user_id = $_SESSION['user_id'];

// Get user account information
$user_query = "SELECT * FROM accounts WHERE id = ?";
$user_result = $db->query($user_query, [$user_id]);
$user = $user_result->fetch_assoc();

if (!$user) {
    header('Location: ../../auth/login.php');
    exit();
}

// Handle form submissions
$success_message = '';
$error_message = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    try {
        if (isset($_POST['action'])) {
            switch ($_POST['action']) {
                case 'add_beneficiary':
                    $name = sanitizeInput($_POST['name']);
                    $account_number = sanitizeInput($_POST['account_number']);
                    $bank_name = sanitizeInput($_POST['bank_name']);
                    $bank_code = sanitizeInput($_POST['bank_code'] ?? '');
                    $country = sanitizeInput($_POST['country'] ?? 'USA');
                    $currency = sanitizeInput($_POST['currency'] ?? 'USD');
                    $is_favorite = isset($_POST['is_favorite']) ? 1 : 0;
                    
                    // Validation
                    if (empty($name) || empty($account_number) || empty($bank_name)) {
                        throw new Exception('Please fill in all required fields.');
                    }
                    
                    // Check if beneficiary already exists (same account number for this user)
                    $check_sql = "SELECT id FROM beneficiaries WHERE user_id = ? AND account_number = ?";
                    $check_result = $db->query($check_sql, [$user_id, $account_number]);

                    if ($check_result->num_rows > 0) {
                        throw new Exception('A beneficiary with this account number already exists in your list.');
                    }
                    
                    // Check if account number belongs to an internal user (for inter-bank transfers)
                    $internal_check_sql = "SELECT id, first_name, last_name FROM accounts WHERE account_number = ? AND is_admin = 0";
                    $internal_result = $db->query($internal_check_sql, [$account_number]);
                    $is_internal = $internal_result->num_rows > 0;
                    
                    // Insert new beneficiary
                    $insert_sql = "INSERT INTO beneficiaries (user_id, name, account_number, bank_name, bank_code, country, currency, is_favorite, created_at) 
                                   VALUES (?, ?, ?, ?, ?, ?, ?, ?, NOW())";
                    $db->query($insert_sql, [$user_id, $name, $account_number, $bank_name, $bank_code, $country, $currency, $is_favorite]);
                    
                    $success_message = 'Beneficiary added successfully!' . ($is_internal ? ' (Internal user detected - Inter-bank transfers available)' : '');
                    break;
                    
                case 'delete_beneficiary':
                    $beneficiary_id = (int)$_POST['beneficiary_id'];
                    
                    // Delete beneficiary
                    $delete_sql = "DELETE FROM beneficiaries WHERE id = ? AND user_id = ?";
                    $db->query($delete_sql, [$beneficiary_id, $user_id]);
                    
                    $success_message = 'Beneficiary removed successfully!';
                    break;
                    
                case 'toggle_favorite':
                    $beneficiary_id = (int)$_POST['beneficiary_id'];
                    $is_favorite = (int)$_POST['is_favorite'];
                    
                    // Update favorite status
                    $update_sql = "UPDATE beneficiaries SET is_favorite = ? WHERE id = ? AND user_id = ?";
                    $db->query($update_sql, [$is_favorite, $beneficiary_id, $user_id]);
                    
                    $success_message = $is_favorite ? 'Added to favorites!' : 'Removed from favorites!';
                    break;
            }
        }
    } catch (Exception $e) {
        $error_message = $e->getMessage();
    }
}

// Get user's beneficiaries and statistics
try {
    // Get all beneficiaries with internal user detection
    $beneficiaries_sql = "SELECT b.*, 
                                 a.id as internal_user_id,
                                 a.first_name as internal_first_name,
                                 a.last_name as internal_last_name
                          FROM beneficiaries b
                          LEFT JOIN accounts a ON b.account_number = a.account_number AND a.is_admin = 0
                          WHERE b.user_id = ? 
                          ORDER BY b.is_favorite DESC, b.name ASC";
    $beneficiaries_result = $db->query($beneficiaries_sql, [$user_id]);
    $beneficiaries = [];
    while ($row = $beneficiaries_result->fetch_assoc()) {
        $beneficiaries[] = $row;
    }
    
    // Get statistics
    $stats_sql = "SELECT 
                    COUNT(*) as total_beneficiaries,
                    COUNT(CASE WHEN is_favorite = 1 THEN 1 END) as favorite_count,
                    COUNT(DISTINCT country) as countries_count
                  FROM beneficiaries WHERE user_id = ?";
    $stats_result = $db->query($stats_sql, [$user_id]);
    $stats = $stats_result->fetch_assoc();
    
} catch (Exception $e) {
    error_log("Beneficiaries page error: " . $e->getMessage());
    $beneficiaries = [];
    $stats = [
        'total_beneficiaries' => 0,
        'favorite_count' => 0,
        'countries_count' => 0
    ];
}

// Set page title and subtitle
$page_title = 'Beneficiaries';
$page_subtitle = 'Manage your saved transfer recipients';

// Include header
require_once '../shared/header.php';
?>

<!-- Include Beneficiaries CSS -->
<link rel="stylesheet" href="beneficiaries.css">

<!-- Dynamic CSS Variables Only -->
<style>
    <?php echo getInlineDynamicCSS(); ?>
</style>

<!-- Sidebar -->
<?php require_once '../shared/sidebar.php'; ?>

<!-- Main Content Wrapper -->
<div class="main-content-wrapper">
    <!-- User Header Component -->
    <?php require_once '../shared/user_header.php'; ?>

    <div class="main-content" style="min-height: calc(100vh - 200px); display: flex; flex-direction: column;">

        <!-- Content Container -->
        <div class="content-container" style="flex: 1;">

        <!-- Mini Hero Section -->
        <div class="beneficiaries-hero mb-4">
            <div class="hero-content">
                <div class="hero-main">
                    <div class="hero-title">Beneficiaries</div>
                    <div class="hero-subtitle">Manage your saved transfer recipients for quick and easy transfers</div>
                    <div class="hero-stats">
                        Total Beneficiaries: <?php echo number_format($stats['total_beneficiaries']); ?> | 
                        Favorites: <?php echo number_format($stats['favorite_count']); ?>
                    </div>
                </div>
                <div class="hero-actions">
                    <button class="btn btn-primary" onclick="showAddBeneficiaryModal()">
                        <i class="fas fa-plus me-2"></i>Add Beneficiary
                    </button>
                </div>
            </div>
        </div>

        <!-- Success/Error Messages -->
        <?php if (!empty($success_message)): ?>
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            <i class="fas fa-check-circle me-2"></i><?php echo htmlspecialchars($success_message); ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
        <?php endif; ?>

        <?php if (!empty($error_message)): ?>
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <i class="fas fa-exclamation-circle me-2"></i><?php echo htmlspecialchars($error_message); ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
        <?php endif; ?>

        <!-- Stats Cards Section -->
        <div class="balance-overview-new mb-4">
            <!-- Total Beneficiaries Card -->
            <div class="balance-card-new">
                <div class="balance-icon">
                    <i class="fas fa-users"></i>
                </div>
                <div class="balance-info">
                    <div class="balance-label">Total</div>
                    <div class="balance-amount available">
                        <?php echo number_format($stats['total_beneficiaries']); ?>
                    </div>
                    <div class="balance-subtitle">Beneficiaries</div>
                </div>
            </div>

            <!-- Favorites Card -->
            <div class="balance-card-new">
                <div class="balance-icon" style="background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);">
                    <i class="fas fa-star"></i>
                </div>
                <div class="balance-info">
                    <div class="balance-label">Favorites</div>
                    <div class="balance-amount" style="color: #f59e0b;">
                        <?php echo number_format($stats['favorite_count']); ?>
                    </div>
                    <div class="balance-subtitle">Quick Access</div>
                </div>
            </div>

            <!-- Countries Card -->
            <div class="balance-card-new">
                <div class="balance-icon" style="background: linear-gradient(135deg, #10b981 0%, #059669 100%);">
                    <i class="fas fa-globe"></i>
                </div>
                <div class="balance-info">
                    <div class="balance-label">Countries</div>
                    <div class="balance-amount" style="color: #10b981;">
                        <?php echo number_format($stats['countries_count']); ?>
                    </div>
                    <div class="balance-subtitle">Global Reach</div>
                </div>
            </div>

            <!-- Quick Transfer Card -->
            <div class="balance-card-new">
                <div class="balance-icon" style="background: linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%);">
                    <i class="fas fa-paper-plane"></i>
                </div>
                <div class="balance-info">
                    <div class="balance-label">Quick Transfer</div>
                    <div class="balance-amount" style="color: #8b5cf6;">
                        <a href="../transfers/" style="color: inherit; text-decoration: none;">Go</a>
                    </div>
                    <div class="balance-subtitle">Send Money</div>
                </div>
            </div>
        </div>

        <!-- Beneficiaries List Section -->
        <div class="beneficiaries-section">
            <div class="beneficiaries-header">
                <h3><i class="fas fa-address-book me-2"></i>Your Beneficiaries</h3>
                <div class="beneficiaries-summary">
                    Showing <?php echo number_format(count($beneficiaries)); ?> beneficiaries
                </div>
            </div>

            <?php if (!empty($beneficiaries)): ?>
            <div class="beneficiaries-grid">
                <?php foreach ($beneficiaries as $beneficiary): ?>
                <div class="beneficiary-card">
                    <div class="beneficiary-header">
                        <div class="beneficiary-info">
                            <div class="beneficiary-name">
                                <?php echo htmlspecialchars($beneficiary['name']); ?>
                                <?php if ($beneficiary['is_favorite']): ?>
                                <i class="fas fa-star favorite-star" title="Favorite"></i>
                                <?php endif; ?>
                                <?php if ($beneficiary['internal_user_id']): ?>
                                <span class="internal-badge" title="Internal User - Inter-bank transfers available">
                                    <i class="fas fa-university"></i> Internal
                                </span>
                                <?php endif; ?>
                            </div>
                            <div class="beneficiary-account">
                                <span class="account-number"><?php echo htmlspecialchars($beneficiary['account_number']); ?></span>
                            </div>
                            <div class="beneficiary-bank">
                                <?php echo htmlspecialchars($beneficiary['bank_name']); ?>
                                <?php if (!empty($beneficiary['bank_code'])): ?>
                                <span class="bank-code">(<?php echo htmlspecialchars($beneficiary['bank_code']); ?>)</span>
                                <?php endif; ?>
                            </div>
                        </div>
                        <div class="beneficiary-actions">
                            <div class="dropdown">
                                <button class="btn btn-sm btn-outline-secondary dropdown-toggle" type="button" data-bs-toggle="dropdown">
                                    <i class="fas fa-ellipsis-v"></i>
                                </button>
                                <ul class="dropdown-menu">
                                    <li>
                                        <a class="dropdown-item" href="../transfers/?beneficiary=<?php echo $beneficiary['id']; ?>">
                                            <i class="fas fa-paper-plane me-2"></i>Send Money
                                        </a>
                                    </li>
                                    <li>
                                        <button class="dropdown-item" onclick="toggleFavorite(<?php echo $beneficiary['id']; ?>, <?php echo $beneficiary['is_favorite'] ? 0 : 1; ?>)">
                                            <i class="fas fa-star me-2"></i>
                                            <?php echo $beneficiary['is_favorite'] ? 'Remove from Favorites' : 'Add to Favorites'; ?>
                                        </button>
                                    </li>
                                    <li><hr class="dropdown-divider"></li>
                                    <li>
                                        <button class="dropdown-item text-danger" onclick="deleteBeneficiary(<?php echo $beneficiary['id']; ?>, '<?php echo htmlspecialchars($beneficiary['name'], ENT_QUOTES); ?>')">
                                            <i class="fas fa-trash me-2"></i>Delete
                                        </button>
                                    </li>
                                </ul>
                            </div>
                        </div>
                    </div>
                    <div class="beneficiary-details">
                        <div class="detail-item">
                            <span class="detail-label">Country:</span>
                            <span class="detail-value"><?php echo htmlspecialchars($beneficiary['country']); ?></span>
                        </div>
                        <div class="detail-item">
                            <span class="detail-label">Currency:</span>
                            <span class="detail-value"><?php echo htmlspecialchars($beneficiary['currency']); ?></span>
                        </div>
                        <div class="detail-item">
                            <span class="detail-label">Added:</span>
                            <span class="detail-value"><?php echo formatDate($beneficiary['created_at'], 'M j, Y'); ?></span>
                        </div>
                    </div>
                </div>
                <?php endforeach; ?>
            </div>

            <?php else: ?>
            <!-- Empty State -->
            <div class="empty-state">
                <i class="fas fa-users fa-3x text-muted mb-3"></i>
                <h5 class="text-muted">No beneficiaries found</h5>
                <p class="text-muted">
                    Add your first beneficiary to start making quick and easy transfers.
                </p>
                <button class="btn btn-primary" onclick="showAddBeneficiaryModal()">
                    <i class="fas fa-plus me-2"></i>Add Your First Beneficiary
                </button>
            </div>
            <?php endif; ?>
        </div>

        </div> <!-- End Content Container -->

    </div>

    <!-- Add Beneficiary Modal -->
    <div class="modal fade" id="addBeneficiaryModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">
                        <i class="fas fa-plus me-2"></i>Add New Beneficiary
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <form method="POST" id="addBeneficiaryForm">
                    <input type="hidden" name="action" value="add_beneficiary">
                    <div class="modal-body">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">Beneficiary Name <span class="text-danger">*</span></label>
                                    <input type="text" name="name" class="form-control" required>
                                    <div class="form-text">Full name of the recipient</div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">Account Number <span class="text-danger">*</span></label>
                                    <input type="text" name="account_number" class="form-control" required>
                                    <div class="form-text">Recipient's account number</div>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">Bank Name <span class="text-danger">*</span></label>
                                    <input type="text" name="bank_name" class="form-control" required>
                                    <div class="form-text">Name of the recipient's bank</div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">Bank Code / Routing Number</label>
                                    <input type="text" name="bank_code" class="form-control">
                                    <div class="form-text">SWIFT/IBAN/Routing code (optional)</div>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">Country</label>
                                    <select name="country" class="form-control">
                                        <option value="USA">United States</option>
                                        <option value="UK">United Kingdom</option>
                                        <option value="Canada">Canada</option>
                                        <option value="Australia">Australia</option>
                                        <option value="Germany">Germany</option>
                                        <option value="France">France</option>
                                        <option value="Italy">Italy</option>
                                        <option value="Spain">Spain</option>
                                        <option value="Netherlands">Netherlands</option>
                                        <option value="Switzerland">Switzerland</option>
                                        <option value="Belgium">Belgium</option>
                                        <option value="Austria">Austria</option>
                                        <option value="Sweden">Sweden</option>
                                        <option value="Norway">Norway</option>
                                        <option value="Denmark">Denmark</option>
                                        <option value="Finland">Finland</option>
                                        <option value="Japan">Japan</option>
                                        <option value="Singapore">Singapore</option>
                                        <option value="Hong Kong">Hong Kong</option>
                                        <option value="South Korea">South Korea</option>
                                        <option value="China">China</option>
                                        <option value="India">India</option>
                                        <option value="UAE">United Arab Emirates</option>
                                        <option value="Saudi Arabia">Saudi Arabia</option>
                                        <option value="Qatar">Qatar</option>
                                        <option value="Kuwait">Kuwait</option>
                                        <option value="Bahrain">Bahrain</option>
                                        <option value="South Africa">South Africa</option>
                                        <option value="Nigeria">Nigeria</option>
                                        <option value="Kenya">Kenya</option>
                                        <option value="Egypt">Egypt</option>
                                        <option value="Morocco">Morocco</option>
                                        <option value="Brazil">Brazil</option>
                                        <option value="Mexico">Mexico</option>
                                        <option value="Argentina">Argentina</option>
                                        <option value="Chile">Chile</option>
                                        <option value="Colombia">Colombia</option>
                                        <option value="Peru">Peru</option>
                                        <option value="New Zealand">New Zealand</option>
                                        <option value="Ireland">Ireland</option>
                                        <option value="Portugal">Portugal</option>
                                        <option value="Greece">Greece</option>
                                        <option value="Turkey">Turkey</option>
                                        <option value="Israel">Israel</option>
                                        <option value="Russia">Russia</option>
                                        <option value="Poland">Poland</option>
                                        <option value="Czech Republic">Czech Republic</option>
                                        <option value="Hungary">Hungary</option>
                                        <option value="Romania">Romania</option>
                                        <option value="Bulgaria">Bulgaria</option>
                                        <option value="Croatia">Croatia</option>
                                        <option value="Slovenia">Slovenia</option>
                                        <option value="Slovakia">Slovakia</option>
                                        <option value="Lithuania">Lithuania</option>
                                        <option value="Latvia">Latvia</option>
                                        <option value="Estonia">Estonia</option>
                                        <option value="Other">Other</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">Currency</label>
                                    <select name="currency" class="form-control" id="currencySelect">
                                        <option value="USD">USD - US Dollar</option>
                                        <option value="EUR">EUR - Euro</option>
                                        <option value="GBP">GBP - British Pound</option>
                                        <option value="CAD">CAD - Canadian Dollar</option>
                                        <option value="AUD">AUD - Australian Dollar</option>
                                        <option value="CHF">CHF - Swiss Franc</option>
                                        <option value="JPY">JPY - Japanese Yen</option>
                                        <option value="CNY">CNY - Chinese Yuan</option>
                                        <option value="INR">INR - Indian Rupee</option>
                                        <option value="SGD">SGD - Singapore Dollar</option>
                                        <option value="HKD">HKD - Hong Kong Dollar</option>
                                        <option value="KRW">KRW - South Korean Won</option>
                                        <option value="AED">AED - UAE Dirham</option>
                                        <option value="SAR">SAR - Saudi Riyal</option>
                                        <option value="QAR">QAR - Qatari Riyal</option>
                                        <option value="KWD">KWD - Kuwaiti Dinar</option>
                                        <option value="BHD">BHD - Bahraini Dinar</option>
                                        <option value="ZAR">ZAR - South African Rand</option>
                                        <option value="NGN">NGN - Nigerian Naira</option>
                                        <option value="KES">KES - Kenyan Shilling</option>
                                        <option value="EGP">EGP - Egyptian Pound</option>
                                        <option value="MAD">MAD - Moroccan Dirham</option>
                                        <option value="BRL">BRL - Brazilian Real</option>
                                        <option value="MXN">MXN - Mexican Peso</option>
                                        <option value="ARS">ARS - Argentine Peso</option>
                                        <option value="CLP">CLP - Chilean Peso</option>
                                        <option value="COP">COP - Colombian Peso</option>
                                        <option value="PEN">PEN - Peruvian Sol</option>
                                        <option value="NZD">NZD - New Zealand Dollar</option>
                                        <option value="SEK">SEK - Swedish Krona</option>
                                        <option value="NOK">NOK - Norwegian Krone</option>
                                        <option value="DKK">DKK - Danish Krone</option>
                                        <option value="PLN">PLN - Polish Zloty</option>
                                        <option value="CZK">CZK - Czech Koruna</option>
                                        <option value="HUF">HUF - Hungarian Forint</option>
                                        <option value="RON">RON - Romanian Leu</option>
                                        <option value="BGN">BGN - Bulgarian Lev</option>
                                        <option value="HRK">HRK - Croatian Kuna</option>
                                        <option value="RUB">RUB - Russian Ruble</option>
                                        <option value="TRY">TRY - Turkish Lira</option>
                                        <option value="ILS">ILS - Israeli Shekel</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                        <div class="mb-3">
                            <div class="form-check">
                                <input type="checkbox" name="is_favorite" class="form-check-input" id="is_favorite">
                                <label class="form-check-label" for="is_favorite">
                                    Add to favorites for quick access
                                </label>
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-plus me-2"></i>Add Beneficiary
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Hidden forms for actions -->
    <form id="toggleFavoriteForm" method="POST" style="display: none;">
        <input type="hidden" name="action" value="toggle_favorite">
        <input type="hidden" name="beneficiary_id" id="toggleFavoriteId">
        <input type="hidden" name="is_favorite" id="toggleFavoriteValue">
    </form>

    <form id="deleteBeneficiaryForm" method="POST" style="display: none;">
        <input type="hidden" name="action" value="delete_beneficiary">
        <input type="hidden" name="beneficiary_id" id="deleteBeneficiaryId">
    </form>

    <!-- User Footer Component -->
    <?php require_once '../shared/user_footer.php'; ?>
</div>

<!-- Include Beneficiaries JavaScript -->
<script src="beneficiaries.js"></script>
