-- Enhanced User Deletion Security Tables

-- Table to store user archives
CREATE TABLE IF NOT EXISTS user_archive (
    id INT AUTO_INCREMENT PRIMARY KEY,
    original_user_id INT NOT NULL,
    username VARCHAR(50) NOT NULL,
    email VARCHAR(100) NOT NULL,
    first_name <PERSON><PERSON><PERSON><PERSON>(50),
    last_name <PERSON><PERSON><PERSON><PERSON>(50),
    balance DECIMAL(15,2) DEFAULT 0.00,
    status VARCHAR(20),
    created_at DATETIME,
    archived_at DATETIME NOT NULL,
    archived_by INT NOT NULL,
    archive_reason TEXT,
    related_data_summary JSON,
    INDEX idx_original_user_id (original_user_id),
    INDEX idx_archived_by (archived_by),
    INDEX idx_archived_at (archived_at)
);

-- Table to store deletion confirmation codes
CREATE TABLE IF NOT EXISTS deletion_codes (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    code VA<PERSON><PERSON><PERSON>(20) NOT NULL,
    generated_by INT NOT NULL,
    generated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    expires_at DATETIME NOT NULL,
    used_at DATETIME NULL,
    UNIQUE KEY unique_user_code (user_id),
    INDEX idx_code (code),
    INDEX idx_expires_at (expires_at)
);

-- Add deletion tracking fields to accounts table (check if columns exist first)
ALTER TABLE accounts 
ADD COLUMN deleted_at DATETIME NULL,
ADD COLUMN deleted_by INT NULL,
ADD COLUMN deletion_reason TEXT NULL;

-- Enhanced audit log for deletion activities
CREATE TABLE IF NOT EXISTS deletion_audit (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    action_type ENUM('soft_delete', 'archive', 'hard_delete', 'restore') NOT NULL,
    performed_by INT NOT NULL,
    performed_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    reason TEXT,
    user_data_snapshot JSON,
    related_data_summary JSON,
    ip_address VARCHAR(45),
    user_agent TEXT,
    INDEX idx_user_id (user_id),
    INDEX idx_performed_by (performed_by),
    INDEX idx_performed_at (performed_at),
    INDEX idx_action_type (action_type)
);
