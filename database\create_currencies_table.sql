-- Create currencies table for currency management system
-- Run this script to add currency management functionality

USE online_banking;

-- Create currencies table
CREATE TABLE IF NOT EXISTS currencies (
    id INT PRIMARY KEY AUTO_INCREMENT,
    code VARCHAR(3) NOT NULL UNIQUE,
    name VARCHAR(50) NOT NULL,
    symbol VARCHAR(10) NOT NULL,
    status ENUM('active', 'inactive') DEFAULT 'active',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_code (code),
    INDEX idx_status (status)
);

-- Insert common currencies with proper symbols
INSERT INTO currencies (code, name, symbol, status) VALUES
('USD', 'US Dollar', '$', 'active'),
('EUR', 'Euro', '€', 'active'),
('GBP', 'British Pound', '£', 'active'),
('CAD', 'Canadian Dollar', 'C$', 'active'),
('AUD', 'Australian Dollar', 'A$', 'active'),
('JPY', 'Japanese Yen', '¥', 'active'),
('CHF', 'Swiss Franc', 'CHF', 'active'),
('CNY', 'Chinese Yuan', '¥', 'active'),
('INR', 'Indian Rupee', '₹', 'active'),
('NGN', 'Nigerian Naira', '₦', 'active'),
('ZAR', 'South African Rand', 'R', 'active'),
('KES', 'Kenyan Shilling', 'KSh', 'active'),
('GHS', 'Ghanaian Cedi', '₵', 'active'),
('EGP', 'Egyptian Pound', 'E£', 'active'),
('MAD', 'Moroccan Dirham', 'MAD', 'active'),
('TND', 'Tunisian Dinar', 'TND', 'active'),
('BRL', 'Brazilian Real', 'R$', 'active'),
('MXN', 'Mexican Peso', '$', 'active'),
('ARS', 'Argentine Peso', '$', 'active'),
('CLP', 'Chilean Peso', '$', 'active'),
('COP', 'Colombian Peso', '$', 'active'),
('PEN', 'Peruvian Sol', 'S/', 'active'),
('UYU', 'Uruguayan Peso', '$U', 'active'),
('RUB', 'Russian Ruble', '₽', 'active'),
('TRY', 'Turkish Lira', '₺', 'active'),
('PLN', 'Polish Zloty', 'zł', 'active'),
('CZK', 'Czech Koruna', 'Kč', 'active'),
('HUF', 'Hungarian Forint', 'Ft', 'active'),
('RON', 'Romanian Leu', 'lei', 'active'),
('BGN', 'Bulgarian Lev', 'лв', 'active'),
('HRK', 'Croatian Kuna', 'kn', 'active'),
('SEK', 'Swedish Krona', 'kr', 'active'),
('NOK', 'Norwegian Krone', 'kr', 'active'),
('DKK', 'Danish Krone', 'kr', 'active'),
('ISK', 'Icelandic Krona', 'kr', 'active'),
('SGD', 'Singapore Dollar', 'S$', 'active'),
('HKD', 'Hong Kong Dollar', 'HK$', 'active'),
('TWD', 'Taiwan Dollar', 'NT$', 'active'),
('KRW', 'South Korean Won', '₩', 'active'),
('THB', 'Thai Baht', '฿', 'active'),
('MYR', 'Malaysian Ringgit', 'RM', 'active'),
('IDR', 'Indonesian Rupiah', 'Rp', 'active'),
('PHP', 'Philippine Peso', '₱', 'active'),
('VND', 'Vietnamese Dong', '₫', 'active'),
('LAK', 'Lao Kip', '₭', 'active'),
('KHR', 'Cambodian Riel', '៛', 'active'),
('MMK', 'Myanmar Kyat', 'K', 'active'),
('BDT', 'Bangladeshi Taka', '৳', 'active'),
('PKR', 'Pakistani Rupee', '₨', 'active'),
('LKR', 'Sri Lankan Rupee', '₨', 'active'),
('NPR', 'Nepalese Rupee', '₨', 'active'),
('AFN', 'Afghan Afghani', '؋', 'active'),
('IRR', 'Iranian Rial', '﷼', 'active'),
('IQD', 'Iraqi Dinar', 'ع.د', 'active'),
('JOD', 'Jordanian Dinar', 'JD', 'active'),
('KWD', 'Kuwaiti Dinar', 'KD', 'active'),
('LBP', 'Lebanese Pound', 'ل.ل', 'active'),
('OMR', 'Omani Rial', 'ر.ع.', 'active'),
('QAR', 'Qatari Riyal', 'ر.ق', 'active'),
('SAR', 'Saudi Riyal', 'ر.س', 'active'),
('AED', 'UAE Dirham', 'د.إ', 'active'),
('YER', 'Yemeni Rial', '﷼', 'active'),
('ILS', 'Israeli Shekel', '₪', 'active'),
('AMD', 'Armenian Dram', '֏', 'active'),
('AZN', 'Azerbaijani Manat', '₼', 'active'),
('GEL', 'Georgian Lari', '₾', 'active'),
('KZT', 'Kazakhstani Tenge', '₸', 'active'),
('KGS', 'Kyrgyzstani Som', 'лв', 'active'),
('TJS', 'Tajikistani Somoni', 'ЅМ', 'active'),
('TMT', 'Turkmenistani Manat', 'T', 'active'),
('UZS', 'Uzbekistani Som', 'лв', 'active'),
('BYN', 'Belarusian Ruble', 'Br', 'active'),
('MDL', 'Moldovan Leu', 'L', 'active'),
('UAH', 'Ukrainian Hryvnia', '₴', 'active'),
('ALL', 'Albanian Lek', 'L', 'active'),
('BAM', 'Bosnia-Herzegovina Convertible Mark', 'KM', 'active'),
('MKD', 'Macedonian Denar', 'ден', 'active'),
('RSD', 'Serbian Dinar', 'Дин.', 'active'),
('EUR', 'Euro', '€', 'active');

-- Add currency management setting to super admin settings
INSERT INTO super_admin_settings (setting_key, setting_value, setting_description, setting_type) VALUES
('admin_display_currency', 'USD', 'Currency used for displaying amounts in admin dashboard', 'text')
ON DUPLICATE KEY UPDATE setting_description = 'Currency used for displaying amounts in admin dashboard';

-- Update system settings to reference currency management
UPDATE system_settings SET description = 'Default currency for new accounts (managed via Currency Management)' WHERE setting_key = 'currency';

-- Create index for faster currency lookups
CREATE INDEX idx_currencies_active ON currencies (status, code);

-- Show success message
SELECT 'Currencies table created successfully!' as message;
SELECT COUNT(*) as total_currencies_added FROM currencies;
