<?php
/**
 * Debug <PERSON> User - Check admin status and session issues
 */

require_once '../config/config.php';

echo "<h2>🔍 Debug James User Login Issue</h2>";

try {
    $db = getDB();
    
    // Check user data for james
    $email = '<EMAIL>';
    $username = '<EMAIL>';
    
    echo "<h3>User Database Information</h3>";
    
    // Check by email
    $sql = "SELECT id, username, first_name, last_name, email, is_admin, account_status, created_at 
            FROM accounts WHERE email = ?";
    $result = $db->query($sql, [$email]);
    
    if ($result && $result->num_rows > 0) {
        $user = $result->fetch_assoc();
        echo "<h4>Found by Email ($email):</h4>";
        echo "<table border='1' cellpadding='5' cellspacing='0'>";
        echo "<tr><th>Field</th><th>Value</th></tr>";
        foreach ($user as $key => $value) {
            echo "<tr><td><strong>$key</strong></td><td>" . htmlspecialchars($value) . "</td></tr>";
        }
        echo "</table>";
        
        $user_id = $user['id'];
        $is_admin = $user['is_admin'];
        
        echo "<h4>🔍 Analysis:</h4>";
        echo "<ul>";
        echo "<li><strong>User ID:</strong> $user_id</li>";
        echo "<li><strong>Is Admin:</strong> " . ($is_admin ? 'YES (1)' : 'NO (0)') . "</li>";
        echo "<li><strong>Account Status:</strong> " . htmlspecialchars($user['account_status']) . "</li>";
        echo "</ul>";
        
        // Check security settings
        require_once 'includes/user-2fa-functions.php';
        $security_settings = getUserSecuritySettings($user_id);
        
        echo "<h4>Security Settings:</h4>";
        if ($security_settings) {
            echo "<table border='1' cellpadding='5' cellspacing='0'>";
            echo "<tr><th>Setting</th><th>Value</th></tr>";
            echo "<tr><td>OTP Enabled</td><td>" . ($security_settings['otp_enabled'] ? 'YES' : 'NO') . "</td></tr>";
            echo "<tr><td>2FA Required</td><td>" . ($security_settings['require_2fa'] ? 'YES' : 'NO') . "</td></tr>";
            echo "<tr><td>Google 2FA</td><td>" . ($security_settings['google_2fa_enabled'] ? 'YES' : 'NO') . "</td></tr>";
            echo "</table>";
            
            // Test the shouldUserVerifyOTP function
            $should_verify = shouldUserVerifyOTP($user_id);
            echo "<p><strong>Should Verify OTP:</strong> " . ($should_verify ? 'YES' : 'NO') . "</p>";
        } else {
            echo "<p style='color: red;'>❌ No security settings found</p>";
        }
        
    } else {
        echo "<p style='color: red;'>❌ User not found by email: $email</p>";
    }
    
    // Also check by username
    echo "<h3>Check by Username</h3>";
    $sql2 = "SELECT id, username, first_name, last_name, email, is_admin, account_status 
             FROM accounts WHERE username = ?";
    $result2 = $db->query($sql2, [$username]);
    
    if ($result2 && $result2->num_rows > 0) {
        $user2 = $result2->fetch_assoc();
        echo "<h4>Found by Username ($username):</h4>";
        echo "<table border='1' cellpadding='5' cellspacing='0'>";
        echo "<tr><th>Field</th><th>Value</th></tr>";
        foreach ($user2 as $key => $value) {
            echo "<tr><td><strong>$key</strong></td><td>" . htmlspecialchars($value) . "</td></tr>";
        }
        echo "</table>";
    } else {
        echo "<p style='color: orange;'>⚠️ User not found by username: $username</p>";
    }
    
    echo "<h3>🔧 Session Logic Analysis</h3>";
    echo "<div style='background-color: #f8f9fa; padding: 15px; border: 1px solid #dee2e6; border-radius: 5px;'>";
    echo "<h4>Current Login Logic Issue:</h4>";
    echo "<ol>";
    echo "<li>User logs in successfully (we see 'Welcome back, james!')</li>";
    echo "<li>Session variables are set including is_admin flag</li>";
    echo "<li>If user.is_admin = 1, then \$_SESSION['is_admin_session'] = true is set</li>";
    echo "<li>When dashboard calls isLoggedIn(), it checks:</li>";
    echo "<ul>";
    echo "<li>If \$_SESSION['is_admin_session'] is NOT set → return true (regular user)</li>";
    echo "<li>If \$_SESSION['is_admin_session'] IS set → return false (admin session, not regular user)</li>";
    echo "</ul>";
    echo "<li>This causes admin users to be redirected back to login when accessing dashboard</li>";
    echo "</ol>";
    echo "</div>";
    
    echo "<h3>🛠️ Proposed Fix</h3>";
    echo "<div style='background-color: #d4edda; padding: 15px; border: 1px solid #c3e6cb; border-radius: 5px;'>";
    echo "<h4>The issue is in the login logic:</h4>";
    echo "<p>We should NOT set \$_SESSION['is_admin_session'] = true for regular admin users who are logging into the user dashboard.</p>";
    echo "<p>The 'is_admin_session' flag should only be set when admins log in through the admin login portal.</p>";
    echo "<p><strong>Solution:</strong> Remove the admin session flag setting from the user login path.</p>";
    echo "</div>";
    
} catch (Exception $e) {
    echo "<p style='color: red;'>Error: " . htmlspecialchars($e->getMessage()) . "</p>";
}

echo "<hr>";
echo "<p><a href='login.php'>Back to Login</a> | <a href='admin/user-security-management.php'>Admin Panel</a></p>";
?>
