-- Upgrade Super Admin 2FA to Dual Authentication System
-- Adds support for both 6-digit security codes and Google Authenticator

-- Add new columns to support dual authentication
ALTER TABLE `super_admin_2fa_settings` 
ADD COLUMN IF NOT EXISTS `security_code_hash` VARCHAR(255) DEFAULT NULL COMMENT 'Hashed 6-digit security code',
ADD COLUMN IF NOT EXISTS `security_code_enabled` TINYINT(1) DEFAULT 0 COMMENT 'Whether 6-digit security code is enabled',
ADD COLUMN IF NOT EXISTS `auth_method` ENUM('none', 'security_code', 'google_2fa', 'both') DEFAULT 'none' COMMENT 'Active authentication method',
ADD COLUMN IF NOT EXISTS `security_code_created_at` TIMESTAMP NULL DEFAULT NULL COMMENT 'When security code was created',
ADD COLUMN IF NOT EXISTS `security_code_last_used` TIMESTAMP NULL DEFAULT NULL COMMENT 'When security code was last used';

-- Add indexes for performance
ALTER TABLE `super_admin_2fa_settings`
ADD INDEX IF NOT EXISTS `idx_auth_method` (`auth_method`),
ADD INDEX IF NOT EXISTS `idx_security_code_enabled` (`security_code_enabled`);

-- Update existing records to set proper auth_method based on current google_2fa_enabled status
UPDATE `super_admin_2fa_settings` 
SET `auth_method` = CASE 
    WHEN `google_2fa_enabled` = 1 AND `google_2fa_secret` IS NOT NULL THEN 'google_2fa'
    ELSE 'none'
END
WHERE `auth_method` = 'none';

-- Add new settings for dual authentication system
INSERT INTO `super_admin_settings` (`setting_key`, `setting_value`, `setting_description`, `setting_type`) VALUES
('dual_auth_enabled', '1', 'Enable dual authentication system (6-digit code + Google 2FA)', 'boolean'),
('security_code_length', '6', 'Length of security code (default: 6 digits)', 'number'),
('security_code_expiry_days', '90', 'Days before security code expires and needs to be changed', 'number'),
('allow_both_auth_methods', '1', 'Allow super admins to use both authentication methods', 'boolean'),
('default_auth_method', 'security_code', 'Default authentication method for new super admins', 'text')
ON DUPLICATE KEY UPDATE 
`setting_value` = VALUES(`setting_value`),
`setting_description` = VALUES(`setting_description`);

-- Create audit log entries for the upgrade
INSERT INTO `super_admin_2fa_audit` (`super_admin_username`, `action`, `details`, `success`) 
SELECT 
    `super_admin_username`,
    'system_upgrade',
    'Upgraded to dual authentication system',
    1
FROM `super_admin_2fa_settings`;

-- Create a view for easy authentication status checking
CREATE OR REPLACE VIEW `super_admin_auth_status` AS
SELECT 
    `super_admin_username`,
    `auth_method`,
    `google_2fa_enabled`,
    `security_code_enabled`,
    CASE 
        WHEN `auth_method` = 'none' THEN 'No Authentication'
        WHEN `auth_method` = 'security_code' THEN '6-Digit Security Code'
        WHEN `auth_method` = 'google_2fa' THEN 'Google Authenticator'
        WHEN `auth_method` = 'both' THEN 'Both Methods Available'
        ELSE 'Unknown'
    END as `auth_method_display`,
    `failed_attempts`,
    `locked_until`,
    `created_at`,
    `updated_at`
FROM `super_admin_2fa_settings`;
