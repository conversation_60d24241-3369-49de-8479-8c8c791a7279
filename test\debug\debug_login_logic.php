<?php
/**
 * Debug Login Logic - Analyze the current 2FA behavior
 */

require_once '../config/config.php';
require_once '../includes/user-2fa-functions.php';

echo "<h2>Debug Login Logic Analysis</h2>";

// Test different scenarios based on your description
$scenarios = [
    [
        'name' => 'OTP ON, 2FA OFF (Should work - go to OTP)',
        'otp_enabled' => 1,
        'require_2fa' => 0,
        'google_2fa_enabled' => 0,
        'expected_behavior' => 'Go to OTP verification',
        'your_observation' => 'Works correctly'
    ],
    [
        'name' => 'OTP OFF, 2FA ON (Should skip OTP)',
        'otp_enabled' => 0,
        'require_2fa' => 1,
        'google_2fa_enabled' => 0,
        'expected_behavior' => 'Skip OTP, go to dashboard',
        'your_observation' => 'Still goes to OTP verification (PROBLEM)'
    ],
    [
        'name' => 'OTP OFF, 2FA OFF (Should skip OTP)',
        'otp_enabled' => 0,
        'require_2fa' => 0,
        'google_2fa_enabled' => 0,
        'expected_behavior' => 'Skip OTP, go to dashboard',
        'your_observation' => 'Login page loops (PROBLEM)'
    ],
    [
        'name' => 'OTP ON, 2FA ON (Should go to OTP)',
        'otp_enabled' => 1,
        'require_2fa' => 1,
        'google_2fa_enabled' => 0,
        'expected_behavior' => 'Go to OTP verification',
        'your_observation' => 'Unknown'
    ]
];

echo "<h3>Current Logic Analysis</h3>";
echo "<table border='1' cellpadding='8' cellspacing='0' style='border-collapse: collapse; width: 100%;'>";
echo "<tr style='background-color: #f0f0f0;'>";
echo "<th>Scenario</th><th>OTP</th><th>2FA Req</th><th>Current Logic Result</th><th>Expected</th><th>Your Observation</th><th>Status</th>";
echo "</tr>";

foreach ($scenarios as $scenario) {
    // Current logic from shouldUserVerifyOTP function
    $current_logic_result = ($scenario['otp_enabled'] == 1 && $scenario['require_2fa'] == 1) || 
                           ($scenario['google_2fa_enabled'] == 1);
    
    $current_behavior = $current_logic_result ? 'Go to OTP verification' : 'Skip OTP, go to dashboard';
    
    // Determine if this matches expected behavior
    $matches_expected = ($current_behavior === $scenario['expected_behavior']);
    $status_color = $matches_expected ? 'green' : 'red';
    $status_text = $matches_expected ? '✓ Correct' : '✗ Wrong';
    
    echo "<tr>";
    echo "<td>" . htmlspecialchars($scenario['name']) . "</td>";
    echo "<td>" . ($scenario['otp_enabled'] ? 'ON' : 'OFF') . "</td>";
    echo "<td>" . ($scenario['require_2fa'] ? 'ON' : 'OFF') . "</td>";
    echo "<td><strong>" . htmlspecialchars($current_behavior) . "</strong></td>";
    echo "<td>" . htmlspecialchars($scenario['expected_behavior']) . "</td>";
    echo "<td><em>" . htmlspecialchars($scenario['your_observation']) . "</em></td>";
    echo "<td style='color: $status_color;'><strong>$status_text</strong></td>";
    echo "</tr>";
}

echo "</table>";

echo "<h3>Problem Analysis</h3>";
echo "<div style='background-color: #fff3cd; padding: 15px; border: 1px solid #ffeaa7; border-radius: 5px;'>";
echo "<h4>🔍 Issues Identified:</h4>";
echo "<ol>";
echo "<li><strong>Scenario 2 Problem:</strong> When OTP=OFF and 2FA=ON, current logic returns FALSE (skip OTP), but you observe it still goes to OTP verification. This suggests there might be another piece of code forcing OTP verification.</li>";
echo "<li><strong>Scenario 3 Problem:</strong> When both are OFF, current logic returns FALSE (skip OTP), but you observe login loops. This suggests the 'direct login' path might have an issue.</li>";
echo "</ol>";
echo "</div>";

echo "<h3>Recommended Logic Fix</h3>";
echo "<div style='background-color: #d1ecf1; padding: 15px; border: 1px solid #bee5eb; border-radius: 5px;'>";
echo "<h4>🔧 Correct Logic Should Be:</h4>";
echo "<p><strong>User should go to OTP verification ONLY when:</strong></p>";
echo "<ul>";
echo "<li>OTP is enabled (regardless of 2FA requirement setting)</li>";
echo "<li>OR Google 2FA is enabled</li>";
echo "</ul>";
echo "<p><strong>User should skip OTP verification when:</strong></p>";
echo "<ul>";
echo "<li>OTP is disabled AND Google 2FA is disabled</li>";
echo "</ul>";
echo "</div>";

echo "<h3>Test Corrected Logic</h3>";
echo "<table border='1' cellpadding='8' cellspacing='0' style='border-collapse: collapse; width: 100%;'>";
echo "<tr style='background-color: #f0f0f0;'>";
echo "<th>Scenario</th><th>OTP</th><th>2FA Req</th><th>Corrected Logic Result</th><th>Expected</th><th>Match?</th>";
echo "</tr>";

foreach ($scenarios as $scenario) {
    // Corrected logic: OTP verification needed if OTP is enabled OR Google 2FA is enabled
    $corrected_logic_result = ($scenario['otp_enabled'] == 1) || ($scenario['google_2fa_enabled'] == 1);
    
    $corrected_behavior = $corrected_logic_result ? 'Go to OTP verification' : 'Skip OTP, go to dashboard';
    
    // Check if corrected logic matches expected
    $matches_expected = ($corrected_behavior === $scenario['expected_behavior']);
    $status_color = $matches_expected ? 'green' : 'red';
    $status_text = $matches_expected ? '✓ Correct' : '✗ Wrong';
    
    echo "<tr>";
    echo "<td>" . htmlspecialchars($scenario['name']) . "</td>";
    echo "<td>" . ($scenario['otp_enabled'] ? 'ON' : 'OFF') . "</td>";
    echo "<td>" . ($scenario['require_2fa'] ? 'ON' : 'OFF') . "</td>";
    echo "<td><strong>" . htmlspecialchars($corrected_behavior) . "</strong></td>";
    echo "<td>" . htmlspecialchars($scenario['expected_behavior']) . "</td>";
    echo "<td style='color: $status_color;'><strong>$status_text</strong></td>";
    echo "</tr>";
}

echo "</table>";

echo "<h3>Additional Investigation Needed</h3>";
echo "<div style='background-color: #f8d7da; padding: 15px; border: 1px solid #f5c6cb; border-radius: 5px;'>";
echo "<h4>🔍 Need to Check:</h4>";
echo "<ol>";
echo "<li><strong>Login Loop Issue:</strong> When both OTP and 2FA are OFF, why does the login page loop? Check if there's an issue in the 'direct login' code path.</li>";
echo "<li><strong>Forced OTP Issue:</strong> When OTP=OFF but 2FA=ON, why does it still go to OTP? Check if there's other code that forces OTP verification.</li>";
echo "<li><strong>Default Settings:</strong> Check if new users get default settings that might cause issues.</li>";
echo "</ol>";
echo "</div>";

// Test with a real user if available
echo "<h3>Test with Real User Data</h3>";
try {
    $db = getDB();
    $sql = "SELECT a.id, a.username, a.first_name, uss.otp_enabled, uss.require_2fa, uss.google_2fa_enabled 
            FROM accounts a 
            LEFT JOIN user_security_settings uss ON a.id = uss.user_id 
            WHERE a.is_admin = 0 
            LIMIT 5";
    $result = $db->query($sql);
    
    if ($result && $result->num_rows > 0) {
        echo "<table border='1' cellpadding='5' cellspacing='0' style='border-collapse: collapse;'>";
        echo "<tr style='background-color: #f0f0f0;'><th>User</th><th>OTP</th><th>2FA Req</th><th>Google 2FA</th><th>Current Logic</th><th>Corrected Logic</th></tr>";
        
        while ($user = $result->fetch_assoc()) {
            $otp_enabled = $user['otp_enabled'] ?? 0;
            $require_2fa = $user['require_2fa'] ?? 0;
            $google_2fa = $user['google_2fa_enabled'] ?? 0;
            
            $current_result = ($otp_enabled == 1 && $require_2fa == 1) || ($google_2fa == 1);
            $corrected_result = ($otp_enabled == 1) || ($google_2fa == 1);
            
            echo "<tr>";
            echo "<td>" . htmlspecialchars($user['username']) . "</td>";
            echo "<td>" . ($otp_enabled ? 'ON' : 'OFF') . "</td>";
            echo "<td>" . ($require_2fa ? 'ON' : 'OFF') . "</td>";
            echo "<td>" . ($google_2fa ? 'ON' : 'OFF') . "</td>";
            echo "<td>" . ($current_result ? 'OTP' : 'Direct') . "</td>";
            echo "<td>" . ($corrected_result ? 'OTP' : 'Direct') . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    } else {
        echo "<p>No users found to test with.</p>";
    }
} catch (Exception $e) {
    echo "<p style='color: red;'>Error: " . htmlspecialchars($e->getMessage()) . "</p>";
}

echo "<hr>";
echo "<p><a href='test_2fa_login_flow.php'>Run Full 2FA Test</a> | <a href='login.php'>Test Login</a></p>";
?>
