<?php
/**
 * Super Admin Appearance Settings
 * Manage system appearance and branding settings
 */

// Include required files
require_once '../config/config.php';
require_once '../config/database.php';
require_once '../config/super_admin_settings.php';

$page_title = 'Appearance Settings';
$page_subtitle = 'Manage system appearance, branding, and visual settings';

// Include header
include 'includes/header.php';

/**
 * Update a super admin setting (Super Admin version)
 * Uses super admin session authentication instead of regular admin authentication
 */
function updateSuperAdminSettingForSuperAdmin($key, $value, $reason = null) {
    // Check super admin authentication using the super admin session system
    if (!isset($_SESSION['super_admin_logged_in']) || $_SESSION['super_admin_logged_in'] !== true) {
        throw new Exception("Access denied. Super admin privileges required.");
    }

    try {
        $db = getDB();

        // Insert or update the setting
        $sql = "INSERT INTO super_admin_settings (setting_key, setting_value, setting_type, setting_description, updated_at)
                VALUES (?, ?, 'text', ?, NOW())
                ON DUPLICATE KEY UPDATE
                setting_value = VALUES(setting_value),
                setting_description = VALUES(setting_description),
                updated_at = VALUES(updated_at)";

        $description = $reason ?? 'Appearance setting update';

        $result = $db->query($sql, [$key, $value, $description]);

        if (!$result) {
            throw new Exception("Failed to update setting: $key");
        }

        // Log the action
        logSuperAdminAction('setting_update', "Updated setting: $key", [
            'setting_key' => $key,
            'setting_value' => $value,
            'reason' => $reason
        ]);

        return true;
    } catch (Exception $e) {
        error_log("Error updating super admin setting: " . $e->getMessage());
        throw $e;
    }
}

$success = '';
$errors = [];

// Handle form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    try {
        $db = getDB();
        
        // Handle logo upload
        if (isset($_FILES['site_logo']) && $_FILES['site_logo']['error'] === UPLOAD_ERR_OK) {
            $upload_dir = '../uploads/branding/';
            if (!is_dir($upload_dir)) {
                mkdir($upload_dir, 0755, true);
            }
            
            $file_extension = strtolower(pathinfo($_FILES['site_logo']['name'], PATHINFO_EXTENSION));
            $allowed_extensions = ['jpg', 'jpeg', 'png', 'gif', 'svg'];
            
            if (in_array($file_extension, $allowed_extensions)) {
                $logo_filename = 'logo_' . time() . '.' . $file_extension;
                $logo_path = $upload_dir . $logo_filename;
                
                if (move_uploaded_file($_FILES['site_logo']['tmp_name'], $logo_path)) {
                    updateSuperAdminSettingForSuperAdmin('site_logo', 'uploads/branding/' . $logo_filename, 'Logo upload via appearance settings');
                }
            } else {
                $errors[] = 'Invalid logo file format. Please use JPG, PNG, GIF, or SVG.';
            }
        }
        
        // Handle favicon upload
        if (isset($_FILES['site_favicon']) && $_FILES['site_favicon']['error'] === UPLOAD_ERR_OK) {
            $upload_dir = '../uploads/branding/';
            if (!is_dir($upload_dir)) {
                mkdir($upload_dir, 0755, true);
            }
            
            $file_extension = strtolower(pathinfo($_FILES['site_favicon']['name'], PATHINFO_EXTENSION));
            $allowed_extensions = ['ico', 'png', 'jpg', 'jpeg'];
            
            if (in_array($file_extension, $allowed_extensions)) {
                $favicon_filename = 'favicon_' . time() . '.' . $file_extension;
                $favicon_path = $upload_dir . $favicon_filename;
                
                if (move_uploaded_file($_FILES['site_favicon']['tmp_name'], $favicon_path)) {
                    updateSuperAdminSettingForSuperAdmin('site_favicon', 'uploads/branding/' . $favicon_filename, 'Favicon upload via appearance settings');
                }
            } else {
                $errors[] = 'Invalid favicon file format. Please use ICO, PNG, or JPG.';
            }
        }
        
        // Update other appearance settings
        $appearance_settings = [
            'theme_color' => $_POST['theme_color'] ?? '#206bc4',
            'secondary_color' => $_POST['secondary_color'] ?? '#6c757d',
            'login_background_style' => $_POST['login_background_style'] ?? 'gradient',
            'card_header_gradient' => isset($_POST['enable_card_gradient']) ? '1' : '0',
            'admin_theme' => $_POST['admin_theme'] ?? 'light',
            'enable_animations' => isset($_POST['enable_animations']) ? '1' : '0',
            'sidebar_style' => $_POST['sidebar_style'] ?? 'default',
            'header_style' => $_POST['header_style'] ?? 'fixed',
            'show_kyc_section' => isset($_POST['show_kyc_section']) ? '1' : '0',
            'show_account_info_section' => isset($_POST['show_account_info_section']) ? '1' : '0',
            'show_recent_transactions_section' => isset($_POST['show_recent_transactions_section']) ? '1' : '0'
        ];
        
        $updated_count = 0;
        foreach ($appearance_settings as $key => $value) {
            updateSuperAdminSettingForSuperAdmin($key, $value, $_POST['change_reason'] ?? 'Appearance settings update');
            $updated_count++;
        }
        
        if (empty($errors)) {
            $success = "Successfully updated appearance settings ($updated_count settings).";
            logSuperAdminAction('appearance_settings_update', "Updated $updated_count appearance settings");
        }
        
    } catch (Exception $e) {
        $errors[] = "Error updating appearance settings: " . $e->getMessage();
        logSuperAdminAction('appearance_settings_error', "Failed to update appearance settings: " . $e->getMessage());
    }
}

// Get current settings
$current_settings = getSuperAdminSettings();
?>

<div class="row mb-4">
    <div class="col-12">
        <?php if ($success): ?>
            <div class="alert alert-success alert-dismissible fade show" role="alert">
                <i class="fas fa-check-circle me-2"></i>
                <?php echo htmlspecialchars($success); ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        <?php endif; ?>
        
        <?php if (!empty($errors)): ?>
            <div class="alert alert-danger alert-dismissible fade show" role="alert">
                <i class="fas fa-exclamation-triangle me-2"></i>
                <ul class="mb-0">
                    <?php foreach ($errors as $error): ?>
                        <li><?php echo htmlspecialchars($error); ?></li>
                    <?php endforeach; ?>
                </ul>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        <?php endif; ?>
    </div>
</div>

<!-- Branding Settings -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card border-0 shadow-sm">
            <div class="card-header" style="background: linear-gradient(135deg, #4361ee 0%, #3f37c9 100%); color: white;">
                <h5 class="card-title mb-0">
                    <i class="fas fa-image"></i> Branding & Logo
                </h5>
            </div>
            <div class="card-body">
                <form method="POST" enctype="multipart/form-data">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">Site Logo</label>
                                <?php if (!empty($current_settings['site_logo'])): ?>
                                    <div class="mb-2">
                                        <img src="../<?php echo htmlspecialchars($current_settings['site_logo']); ?>" 
                                             alt="Current Logo" style="max-height: 60px; max-width: 200px;">
                                        <small class="text-muted d-block">Current logo</small>
                                    </div>
                                <?php endif; ?>
                                <input type="file" name="site_logo" class="form-control" accept=".jpg,.jpeg,.png,.gif,.svg">
                                <small class="form-text text-muted">Upload a new logo (JPG, PNG, GIF, SVG). Recommended size: 200x60px</small>
                            </div>
                        </div>
                        
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">Site Favicon</label>
                                <?php if (!empty($current_settings['site_favicon'])): ?>
                                    <div class="mb-2">
                                        <img src="../<?php echo htmlspecialchars($current_settings['site_favicon']); ?>" 
                                             alt="Current Favicon" style="width: 32px; height: 32px;">
                                        <small class="text-muted d-block">Current favicon</small>
                                    </div>
                                <?php endif; ?>
                                <input type="file" name="site_favicon" class="form-control" accept=".ico,.png,.jpg,.jpeg">
                                <small class="form-text text-muted">Upload a new favicon (ICO, PNG, JPG). Recommended size: 32x32px</small>
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">Primary Theme Color</label>
                                <input type="color" name="theme_color" class="form-control form-control-color"
                                       value="<?php echo htmlspecialchars($current_settings['theme_color'] ?? '#206bc4'); ?>">
                                <small class="form-text text-muted">Main color used throughout the system</small>
                            </div>
                        </div>
                        
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">Secondary Color</label>
                                <input type="color" name="secondary_color" class="form-control form-control-color" 
                                       value="<?php echo htmlspecialchars($current_settings['secondary_color'] ?? '#6c757d'); ?>">
                                <small class="form-text text-muted">Secondary color for accents and highlights</small>
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">Login Background Style</label>
                                <select name="login_background_style" class="form-select">
                                    <option value="gradient" <?php echo ($current_settings['login_background_style'] ?? 'gradient') === 'gradient' ? 'selected' : ''; ?>>Gradient Background</option>
                                    <option value="image" <?php echo ($current_settings['login_background_style'] ?? 'gradient') === 'image' ? 'selected' : ''; ?>>Image Background</option>
                                    <option value="solid" <?php echo ($current_settings['login_background_style'] ?? 'gradient') === 'solid' ? 'selected' : ''; ?>>Solid Color</option>
                                </select>
                            </div>
                        </div>
                        
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">Admin Panel Theme</label>
                                <select name="admin_theme" class="form-select">
                                    <option value="light" <?php echo ($current_settings['admin_theme'] ?? 'light') === 'light' ? 'selected' : ''; ?>>Light Theme</option>
                                    <option value="dark" <?php echo ($current_settings['admin_theme'] ?? 'light') === 'dark' ? 'selected' : ''; ?>>Dark Theme</option>
                                    <option value="auto" <?php echo ($current_settings['admin_theme'] ?? 'light') === 'auto' ? 'selected' : ''; ?>>Auto (System Preference)</option>
                                </select>
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">Sidebar Style</label>
                                <select name="sidebar_style" class="form-select">
                                    <option value="default" <?php echo ($current_settings['sidebar_style'] ?? 'default') === 'default' ? 'selected' : ''; ?>>Default</option>
                                    <option value="compact" <?php echo ($current_settings['sidebar_style'] ?? 'default') === 'compact' ? 'selected' : ''; ?>>Compact</option>
                                    <option value="minimal" <?php echo ($current_settings['sidebar_style'] ?? 'default') === 'minimal' ? 'selected' : ''; ?>>Minimal</option>
                                </select>
                            </div>
                        </div>
                        
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">Header Style</label>
                                <select name="header_style" class="form-select">
                                    <option value="fixed" <?php echo ($current_settings['header_style'] ?? 'fixed') === 'fixed' ? 'selected' : ''; ?>>Fixed Header</option>
                                    <option value="static" <?php echo ($current_settings['header_style'] ?? 'fixed') === 'static' ? 'selected' : ''; ?>>Static Header</option>
                                </select>
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-check mb-3">
                                <input type="checkbox" name="enable_animations" class="form-check-input" id="enable_animations"
                                       <?php echo ($current_settings['enable_animations'] ?? '1') === '1' ? 'checked' : ''; ?>>
                                <label class="form-check-label" for="enable_animations">
                                    Enable UI Animations
                                </label>
                                <small class="form-text text-muted d-block">Enable smooth transitions and animations throughout the interface</small>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-check mb-3">
                                <input type="checkbox" name="enable_card_gradient" class="form-check-input" id="enable_card_gradient"
                                       <?php echo ($current_settings['card_header_gradient'] ?? '1') === '1' ? 'checked' : ''; ?>>
                                <label class="form-check-label" for="enable_card_gradient">
                                    Enable Card Header Gradients
                                </label>
                                <small class="form-text text-muted d-block">Use gradient backgrounds for card headers instead of solid colors</small>
                            </div>
                        </div>
                    </div>

                    <!-- Dashboard Sections Control -->
                    <div class="row">
                        <div class="col-12">
                            <h6 class="text-muted mb-3">Dashboard Sections</h6>
                        </div>
                        <div class="col-md-4">
                            <div class="form-check mb-3">
                                <input type="checkbox" name="show_kyc_section" class="form-check-input" id="show_kyc_section"
                                       <?php echo ($current_settings['show_kyc_section'] ?? '1') === '1' ? 'checked' : ''; ?>>
                                <label class="form-check-label" for="show_kyc_section">
                                    Show KYC Status Section
                                </label>
                                <small class="form-text text-muted d-block">Display the KYC verification status section on user dashboards</small>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="form-check mb-3">
                                <input type="checkbox" name="show_account_info_section" class="form-check-input" id="show_account_info_section"
                                       <?php echo ($current_settings['show_account_info_section'] ?? '1') === '1' ? 'checked' : ''; ?>>
                                <label class="form-check-label" for="show_account_info_section">
                                    Show Account Info Section
                                </label>
                                <small class="form-text text-muted d-block">Display the account information section on user dashboards</small>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="form-check mb-3">
                                <input type="checkbox" name="show_recent_transactions_section" class="form-check-input" id="show_recent_transactions_section"
                                       <?php echo ($current_settings['show_recent_transactions_section'] ?? '1') === '1' ? 'checked' : ''; ?>>
                                <label class="form-check-label" for="show_recent_transactions_section">
                                    Show Recent Transactions Section
                                </label>
                                <small class="form-text text-muted d-block">Display the recent transactions section on user dashboards</small>
                            </div>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label class="form-label">Change Reason</label>
                        <input type="text" name="change_reason" class="form-control" 
                               placeholder="Brief description of changes made" maxlength="255">
                        <small class="form-text text-muted">Optional: Describe the reason for these changes (for audit log)</small>
                    </div>
                    
                    <div class="d-flex justify-content-between">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save me-2"></i>Save Appearance Settings
                        </button>
                        <a href="dashboard.php" class="btn btn-secondary">
                            <i class="fas fa-arrow-left me-2"></i>Back to Dashboard
                        </a>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<?php include 'includes/footer.php'; ?>
