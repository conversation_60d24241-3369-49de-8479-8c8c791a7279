<?php
/**
 * Test file to verify dashboard statistics
 */

// Include authentication
require_once '../super-admin/includes/auth.php';

// Get stats
$stats = getSuperAdminStats();

echo "<h1>Super Admin Dashboard Statistics Test</h1>";
echo "<h2>Current Statistics:</h2>";
echo "<ul>";
echo "<li><strong>Total Users (Regular):</strong> " . $stats['total_users'] . "</li>";
echo "<li><strong>Active Users:</strong> " . $stats['active_users'] . "</li>";
echo "<li><strong>Total Admins:</strong> " . $stats['total_admins'] . "</li>";
echo "<li><strong>Transactions Today:</strong> " . $stats['transactions_today'] . "</li>";
echo "<li><strong>System Settings:</strong> " . $stats['system_settings'] . "</li>";
echo "<li><strong>Audit Entries (24h):</strong> " . $stats['audit_entries_24h'] . "</li>";
echo "</ul>";

echo "<h2>Expected Values:</h2>";
echo "<ul>";
echo "<li><strong>Total Users (Regular):</strong> 3 (jane_smith, testuser, james)</li>";
echo "<li><strong>Total Admins:</strong> 3 (1 from accounts + 2 from admin_users)</li>";
echo "<li><strong>Audit Entries:</strong> ~49 total entries</li>";
echo "</ul>";
?>
