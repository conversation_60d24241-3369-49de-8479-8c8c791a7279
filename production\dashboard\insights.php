<?php
require_once '../config/config.php';
requireLogin();

$page_title = 'Financial Insights';
$site_name = getBankName();

// Get user financial data and analytics
try {
    $db = getDB();
    $user_id = $_SESSION['user_id'];

    // Get current account balance
    $balance_sql = "SELECT balance FROM accounts WHERE id = ?";
    $balance_result = $db->query($balance_sql, [$user_id]);
    $current_balance = $balance_result->fetch_assoc()['balance'];

    // Get monthly spending analytics (last 6 months)
    $monthly_sql = "SELECT
                        DATE_FORMAT(created_at, '%Y-%m') as month,
                        DATE_FORMAT(created_at, '%M %Y') as month_name,
                        SUM(CASE WHEN sender_id = ? THEN amount ELSE 0 END) as spent,
                        SUM(CASE WHEN recipient_id = ? THEN amount ELSE 0 END) as received,
                        COUNT(CASE WHEN sender_id = ? THEN 1 END) as sent_count,
                        COUNT(CASE WHEN recipient_id = ? THEN 1 END) as received_count
                    FROM transfers
                    WHERE (sender_id = ? OR recipient_id = ?)
                    AND status = 'completed'
                    AND created_at >= DATE_SUB(NOW(), INTERVAL 6 MONTH)
                    GROUP BY DATE_FORMAT(created_at, '%Y-%m')
                    ORDER BY month DESC";

    $monthly_result = $db->query($monthly_sql, [$user_id, $user_id, $user_id, $user_id, $user_id, $user_id]);
    $monthly_data = [];
    while ($row = $monthly_result->fetch_assoc()) {
        $monthly_data[] = $row;
    }

    // Get spending by category (based on description patterns)
    $category_sql = "SELECT
                        CASE
                            WHEN LOWER(description) LIKE '%grocery%' OR LOWER(description) LIKE '%food%' OR LOWER(description) LIKE '%restaurant%' THEN 'Food & Dining'
                            WHEN LOWER(description) LIKE '%gas%' OR LOWER(description) LIKE '%fuel%' OR LOWER(description) LIKE '%transport%' THEN 'Transportation'
                            WHEN LOWER(description) LIKE '%shop%' OR LOWER(description) LIKE '%store%' OR LOWER(description) LIKE '%retail%' THEN 'Shopping'
                            WHEN LOWER(description) LIKE '%bill%' OR LOWER(description) LIKE '%utility%' OR LOWER(description) LIKE '%electric%' THEN 'Bills & Utilities'
                            WHEN LOWER(description) LIKE '%entertainment%' OR LOWER(description) LIKE '%movie%' OR LOWER(description) LIKE '%game%' THEN 'Entertainment'
                            WHEN LOWER(description) LIKE '%health%' OR LOWER(description) LIKE '%medical%' OR LOWER(description) LIKE '%doctor%' THEN 'Healthcare'
                            ELSE 'Other'
                        END as category,
                        SUM(amount) as total_amount,
                        COUNT(*) as transaction_count
                     FROM transfers
                     WHERE sender_id = ?
                     AND status = 'completed'
                     AND created_at >= DATE_SUB(NOW(), INTERVAL 3 MONTH)
                     GROUP BY category
                     ORDER BY total_amount DESC";

    $category_result = $db->query($category_sql, [$user_id]);
    $spending_categories = [];
    while ($row = $category_result->fetch_assoc()) {
        $spending_categories[] = $row;
    }

    // Get recent large transactions
    $large_transactions_sql = "SELECT t.*,
                                     CASE
                                         WHEN t.sender_id = ? THEN 'sent'
                                         ELSE 'received'
                                     END as direction,
                                     CASE
                                         WHEN t.sender_id = ? THEN t.recipient_name
                                         ELSE (SELECT CONCAT(first_name, ' ', last_name) FROM accounts WHERE id = t.sender_id)
                                     END as other_party
                              FROM transfers t
                              WHERE (t.sender_id = ? OR t.recipient_id = ?)
                              AND t.status = 'completed'
                              AND t.amount >= 500
                              ORDER BY t.created_at DESC
                              LIMIT 5";

    $large_result = $db->query($large_transactions_sql, [$user_id, $user_id, $user_id, $user_id]);
    $large_transactions = [];
    while ($row = $large_result->fetch_assoc()) {
        $large_transactions[] = $row;
    }

    // Calculate financial metrics
    $current_month_sql = "SELECT
                            SUM(CASE WHEN sender_id = ? THEN amount ELSE 0 END) as spent_this_month,
                            SUM(CASE WHEN recipient_id = ? THEN amount ELSE 0 END) as received_this_month
                          FROM transfers
                          WHERE (sender_id = ? OR recipient_id = ?)
                          AND status = 'completed'
                          AND MONTH(created_at) = MONTH(CURRENT_DATE())
                          AND YEAR(created_at) = YEAR(CURRENT_DATE())";

    $current_result = $db->query($current_month_sql, [$user_id, $user_id, $user_id, $user_id]);
    $current_month_data = $current_result->fetch_assoc();

    // Calculate previous month for comparison
    $previous_month_sql = "SELECT
                             SUM(CASE WHEN sender_id = ? THEN amount ELSE 0 END) as spent_last_month,
                             SUM(CASE WHEN recipient_id = ? THEN amount ELSE 0 END) as received_last_month
                           FROM transfers
                           WHERE (sender_id = ? OR recipient_id = ?)
                           AND status = 'completed'
                           AND MONTH(created_at) = MONTH(DATE_SUB(CURRENT_DATE(), INTERVAL 1 MONTH))
                           AND YEAR(created_at) = YEAR(DATE_SUB(CURRENT_DATE(), INTERVAL 1 MONTH))";

    $previous_result = $db->query($previous_month_sql, [$user_id, $user_id, $user_id, $user_id]);
    $previous_month_data = $previous_result->fetch_assoc();

    // Calculate percentage changes
    $spending_change = 0;
    $income_change = 0;

    if ($previous_month_data['spent_last_month'] > 0) {
        $spending_change = (($current_month_data['spent_this_month'] - $previous_month_data['spent_last_month']) / $previous_month_data['spent_last_month']) * 100;
    }

    if ($previous_month_data['received_last_month'] > 0) {
        $income_change = (($current_month_data['received_this_month'] - $previous_month_data['received_last_month']) / $previous_month_data['received_last_month']) * 100;
    }

} catch (Exception $e) {
    error_log("Insights page error: " . $e->getMessage());
    $current_balance = 0;
    $monthly_data = [];
    $spending_categories = [];
    $large_transactions = [];
    $current_month_data = ['spent_this_month' => 0, 'received_this_month' => 0];
    $previous_month_data = ['spent_last_month' => 0, 'received_last_month' => 0];
    $spending_change = 0;
    $income_change = 0;
}

// Include header
require_once '../includes/dashboard/header.php';

// Include sidebar
require_once '../includes/dashboard/sidebar.php';
?>

<!-- Main Content -->
<div class="main-content">
    <!-- Top Bar -->
    <div class="top-bar">
        <h1>Financial Insights</h1>
        <div class="top-bar-actions">
            <button class="btn-outline" onclick="exportInsights()">
                <svg width="16" height="16" fill="currentColor" viewBox="0 0 16 16">
                    <path d="M.5 9.9a.5.5 0 01.5.5v2.5a1 1 0 001 1h12a1 1 0 001-1v-2.5a.5.5 0 011 0v2.5a2 2 0 01-2 2H2a2 2 0 01-2-2v-2.5a.5.5 0 01.5-.5z"/>
                    <path d="M7.646 11.854a.5.5 0 00.708 0l3-3a.5.5 0 00-.708-.708L8.5 10.293V1.5a.5.5 0 00-1 0v8.793L5.354 8.146a.5.5 0 10-.708.708l3 3z"/>
                </svg>
                Export Report
            </button>
            <button class="btn-primary" onclick="window.location.reload()">
                <svg width="16" height="16" fill="currentColor" viewBox="0 0 16 16">
                    <path fill-rule="evenodd" d="M8 3a5 5 0 104.546 2.914.5.5 0 00-.908-.417A4 4 0 118 4v1z"/>
                    <path d="M8 4.466V.534a.25.25 0 01.41-.192l2.36 1.966c.12.1.12.284 0 .384L8.41 4.658A.25.25 0 018 4.466z"/>
                </svg>
                Refresh
            </button>
            <div style="width: 40px; height: 40px; border-radius: 50%; background: #6366f1; display: flex; align-items: center; justify-content: center; color: white; font-weight: 600;">
                <?php echo strtoupper(substr($_SESSION['first_name'] ?? 'U', 0, 1)); ?>
            </div>
        </div>
    </div>

    <!-- Financial Overview Cards -->
    <div class="stats-grid" style="margin-bottom: 2rem;">
        <div class="stat-card">
            <h3 class="stat-title">Current Balance</h3>
            <p class="stat-value">$<?php echo number_format($current_balance, 2); ?></p>
            <p class="stat-change">Available funds</p>
        </div>
        <div class="stat-card">
            <h3 class="stat-title">This Month Spent</h3>
            <p class="stat-value">$<?php echo number_format($current_month_data['spent_this_month'], 2); ?></p>
            <p class="stat-change" style="color: <?php echo $spending_change > 0 ? '#ef4444' : '#10b981'; ?>;">
                <?php echo $spending_change > 0 ? '+' : ''; ?><?php echo number_format($spending_change, 1); ?>% vs last month
            </p>
        </div>
        <div class="stat-card">
            <h3 class="stat-title">This Month Received</h3>
            <p class="stat-value">$<?php echo number_format($current_month_data['received_this_month'], 2); ?></p>
            <p class="stat-change" style="color: <?php echo $income_change > 0 ? '#10b981' : '#ef4444'; ?>;">
                <?php echo $income_change > 0 ? '+' : ''; ?><?php echo number_format($income_change, 1); ?>% vs last month
            </p>
        </div>
        <div class="stat-card">
            <h3 class="stat-title">Net Flow</h3>
            <p class="stat-value" style="color: <?php echo ($current_month_data['received_this_month'] - $current_month_data['spent_this_month']) >= 0 ? '#10b981' : '#ef4444'; ?>;">
                $<?php echo number_format($current_month_data['received_this_month'] - $current_month_data['spent_this_month'], 2); ?>
            </p>
            <p class="stat-change">This month</p>
        </div>
    </div>

    <!-- Main Dashboard Grid -->
    <div class="dashboard-grid">
        <div class="main-section">
            <!-- Monthly Trends Chart -->
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">Monthly Trends (Last 6 Months)</h3>
                    <div style="display: flex; gap: 1rem;">
                        <div style="display: flex; align-items: center; gap: 0.5rem;">
                            <div style="width: 12px; height: 12px; background: #ef4444; border-radius: 2px;"></div>
                            <span style="font-size: 0.875rem; color: #6b7280;">Spent</span>
                        </div>
                        <div style="display: flex; align-items: center; gap: 0.5rem;">
                            <div style="width: 12px; height: 12px; background: #10b981; border-radius: 2px;"></div>
                            <span style="font-size: 0.875rem; color: #6b7280;">Received</span>
                        </div>
                    </div>
                </div>
                <div class="card-body" style="padding: 2rem;">
                    <?php if (!empty($monthly_data)): ?>
                        <div style="display: flex; align-items: end; gap: 1rem; height: 200px; padding: 1rem 0;">
                            <?php
                            $max_amount = 0;
                            foreach ($monthly_data as $month) {
                                $max_amount = max($max_amount, $month['spent'], $month['received']);
                            }
                            $max_amount = max($max_amount, 1); // Prevent division by zero
                            ?>
                            <?php foreach (array_reverse($monthly_data) as $month): ?>
                                <div style="flex: 1; display: flex; flex-direction: column; align-items: center; gap: 0.5rem;">
                                    <div style="display: flex; gap: 4px; align-items: end; height: 150px;">
                                        <div style="width: 20px; background: #ef4444; height: <?php echo ($month['spent'] / $max_amount) * 150; ?>px; border-radius: 2px 2px 0 0;" title="Spent: $<?php echo number_format($month['spent'], 2); ?>"></div>
                                        <div style="width: 20px; background: #10b981; height: <?php echo ($month['received'] / $max_amount) * 150; ?>px; border-radius: 2px 2px 0 0;" title="Received: $<?php echo number_format($month['received'], 2); ?>"></div>
                                    </div>
                                    <div style="font-size: 0.75rem; color: #6b7280; text-align: center; writing-mode: vertical-rl; text-orientation: mixed;">
                                        <?php echo date('M', strtotime($month['month'] . '-01')); ?>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        </div>
                        <div style="text-align: center; margin-top: 1rem;">
                            <p style="font-size: 0.875rem; color: #6b7280;">Hover over bars to see exact amounts</p>
                        </div>
                    <?php else: ?>
                        <div style="text-align: center; padding: 3rem 1rem;">
                            <div style="font-size: 3rem; color: #e5e7eb; margin-bottom: 1rem;">📊</div>
                            <h3 style="color: #6b7280; margin-bottom: 0.5rem;">No Data Available</h3>
                            <p style="color: #9ca3af;">Start making transactions to see your financial trends.</p>
                        </div>
                    <?php endif; ?>
                </div>
            </div>

            <!-- Spending Categories -->
            <div class="card" style="margin-top: 2rem;">
                <div class="card-header">
                    <h3 class="card-title">Spending by Category (Last 3 Months)</h3>
                </div>
                <div class="card-body">
                    <?php if (!empty($spending_categories)): ?>
                        <div style="display: flex; flex-direction: column; gap: 1rem;">
                            <?php
                            $total_spending = array_sum(array_column($spending_categories, 'total_amount'));
                            foreach ($spending_categories as $category):
                                $percentage = $total_spending > 0 ? ($category['total_amount'] / $total_spending) * 100 : 0;
                            ?>
                                <div style="display: flex; align-items: center; gap: 1rem;">
                                    <div style="flex: 1;">
                                        <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 0.5rem;">
                                            <span style="font-weight: 500;"><?php echo htmlspecialchars($category['category']); ?></span>
                                            <span style="font-weight: 600;">$<?php echo number_format($category['total_amount'], 2); ?></span>
                                        </div>
                                        <div style="background: #f3f4f6; height: 8px; border-radius: 4px; overflow: hidden;">
                                            <div style="background: #6366f1; height: 100%; width: <?php echo $percentage; ?>%; border-radius: 4px;"></div>
                                        </div>
                                        <div style="display: flex; justify-content: space-between; margin-top: 0.25rem;">
                                            <span style="font-size: 0.75rem; color: #6b7280;"><?php echo $category['transaction_count']; ?> transactions</span>
                                            <span style="font-size: 0.75rem; color: #6b7280;"><?php echo number_format($percentage, 1); ?>%</span>
                                        </div>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        </div>
                    <?php else: ?>
                        <div style="text-align: center; padding: 2rem 1rem;">
                            <div style="font-size: 2rem; color: #e5e7eb; margin-bottom: 1rem;">🏷️</div>
                            <h3 style="color: #6b7280; margin-bottom: 0.5rem;">No Spending Data</h3>
                            <p style="color: #9ca3af;">Make some transactions to see spending categories.</p>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>

        <!-- Sidebar Section -->
        <div class="sidebar-section">
            <!-- Large Transactions -->
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">Large Transactions</h3>
                    <span style="font-size: 0.75rem; color: #6b7280;">$500+</span>
                </div>
                <div style="padding: 1rem;">
                    <?php if (!empty($large_transactions)): ?>
                        <div style="display: flex; flex-direction: column; gap: 0.75rem;">
                            <?php foreach ($large_transactions as $transaction): ?>
                                <div style="display: flex; align-items: center; gap: 0.75rem; padding: 0.75rem; border: 1px solid #e5e7eb; border-radius: 8px;">
                                    <div style="width: 32px; height: 32px; border-radius: 50%; background: <?php echo $transaction['direction'] === 'sent' ? '#ef4444' : '#10b981'; ?>; display: flex; align-items: center; justify-content: center; color: white; font-size: 0.75rem;">
                                        <?php if ($transaction['direction'] === 'sent'): ?>
                                            <svg width="12" height="12" fill="currentColor" viewBox="0 0 16 16">
                                                <path d="M15.854.146a.5.5 0 01.11.54L13.026 8.03A4.5 4.5 0 018 12.5a4.5 4.5 0 115.026-7.47L15.964.686a.5.5 0 01-.11-.54z"/>
                                            </svg>
                                        <?php else: ?>
                                            <svg width="12" height="12" fill="currentColor" viewBox="0 0 16 16">
                                                <path d="M8 15A7 7 0 118 1a7 7 0 010 14zm0 1A8 8 0 108 0a8 8 0 000 16z"/>
                                                <path d="M8 4a.5.5 0 01.5.5v3h3a.5.5 0 010 1h-3v3a.5.5 0 01-1 0v-3h-3a.5.5 0 010-1h3v-3A.5.5 0 018 4z"/>
                                            </svg>
                                        <?php endif; ?>
                                    </div>
                                    <div style="flex: 1;">
                                        <div style="font-weight: 500; font-size: 0.875rem; margin-bottom: 0.25rem;">
                                            <?php echo $transaction['direction'] === 'sent' ? '-' : '+'; ?>$<?php echo number_format($transaction['amount'], 2); ?>
                                        </div>
                                        <div style="font-size: 0.75rem; color: #6b7280;">
                                            <?php echo htmlspecialchars($transaction['other_party']); ?>
                                        </div>
                                        <div style="font-size: 0.75rem; color: #9ca3af;">
                                            <?php echo date('M j', strtotime($transaction['created_at'])); ?>
                                        </div>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        </div>
                    <?php else: ?>
                        <div style="text-align: center; padding: 1rem;">
                            <div style="font-size: 2rem; color: #e5e7eb; margin-bottom: 0.5rem;">💰</div>
                            <p style="color: #6b7280; font-size: 0.875rem;">No large transactions</p>
                        </div>
                    <?php endif; ?>
                </div>
            </div>

            <!-- Financial Tips -->
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">Financial Tips</h3>
                </div>
                <div style="padding: 1.5rem;">
                    <div style="display: flex; flex-direction: column; gap: 1rem;">
                        <div style="display: flex; align-items: flex-start; gap: 0.75rem;">
                            <div style="width: 8px; height: 8px; border-radius: 50%; background: #6366f1; margin-top: 0.5rem; flex-shrink: 0;"></div>
                            <div>
                                <div style="font-weight: 500; margin-bottom: 0.25rem;">Track Your Spending</div>
                                <div style="font-size: 0.875rem; color: #6b7280;">Monitor your monthly expenses to identify saving opportunities</div>
                            </div>
                        </div>
                        <div style="display: flex; align-items: flex-start; gap: 0.75rem;">
                            <div style="width: 8px; height: 8px; border-radius: 50%; background: #6366f1; margin-top: 0.5rem; flex-shrink: 0;"></div>
                            <div>
                                <div style="font-weight: 500; margin-bottom: 0.25rem;">Set Budget Goals</div>
                                <div style="font-size: 0.875rem; color: #6b7280;">Create monthly budgets for different spending categories</div>
                            </div>
                        </div>
                        <div style="display: flex; align-items: flex-start; gap: 0.75rem;">
                            <div style="width: 8px; height: 8px; border-radius: 50%; background: #6366f1; margin-top: 0.5rem; flex-shrink: 0;"></div>
                            <div>
                                <div style="font-weight: 500; margin-bottom: 0.25rem;">Review Regularly</div>
                                <div style="font-size: 0.875rem; color: #6b7280;">Check your insights weekly to stay on track</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Quick Actions -->
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">Quick Actions</h3>
                </div>
                <div style="padding: 1.5rem;">
                    <div style="display: flex; flex-direction: column; gap: 0.75rem;">
                        <button class="btn-primary" onclick="window.location.href='../transfers/'" style="width: 100%;">
                            <svg width="16" height="16" fill="currentColor" viewBox="0 0 16 16" style="margin-right: 0.5rem;">
                                <path d="M15.854.146a.5.5 0 01.11.54L13.026 8.03A4.5 4.5 0 018 12.5a4.5 4.5 0 115.026-7.47L15.964.686a.5.5 0 01-.11-.54z"/>
                            </svg>
                            Make Transfer
                        </button>
                        <button class="btn-outline" onclick="window.location.href='../transfers/history.php'" style="width: 100%;">
                            <svg width="16" height="16" fill="currentColor" viewBox="0 0 16 16" style="margin-right: 0.5rem;">
                                <path fill-rule="evenodd" d="M1.5 1.5A.5.5 0 012 1h12a.5.5 0 01.5.5v2a.5.5 0 01-.128.334L10 8.692V13.5a.5.5 0 01-.342.474l-3 1A.5.5 0 016 14.5V8.692L1.628 3.834A.5.5 0 011.5 3.5v-2z"/>
                            </svg>
                            View History
                        </button>
                        <button class="btn-outline" onclick="exportInsights()" style="width: 100%;">
                            <svg width="16" height="16" fill="currentColor" viewBox="0 0 16 16" style="margin-right: 0.5rem;">
                                <path d="M.5 9.9a.5.5 0 01.5.5v2.5a1 1 0 001 1h12a1 1 0 001-1v-2.5a.5.5 0 011 0v2.5a2 2 0 01-2 2H2a2 2 0 01-2-2v-2.5a.5.5 0 01.5-.5z"/>
                                <path d="M7.646 11.854a.5.5 0 00.708 0l3-3a.5.5 0 00-.708-.708L8.5 10.293V1.5a.5.5 0 00-1 0v8.793L5.354 8.146a.5.5 0 10-.708.708l3 3z"/>
                            </svg>
                            Export Report
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function exportInsights() {
    alert('Export insights functionality coming soon!');
}
</script>

<?php
// Include footer
require_once '../includes/dashboard/footer.php';
?>
