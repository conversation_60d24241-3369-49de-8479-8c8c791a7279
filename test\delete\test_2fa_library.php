<?php
/**
 * Test script to verify Google2FA library functionality
 * This script tests the Google Authenticator integration before implementing it in the super admin system
 */

// Include the Google2FA library
require_once 'vendor/GoogleAuthenticator/Google2FA.php';

$message = '';
$error = '';
$secret = '';
$qr_code_url = '';

try {
    // Test Google2FA library
    $google2fa = new Google2FA();
    
    if (isset($_POST['action'])) {
        switch ($_POST['action']) {
            case 'generate_secret':
                $secret = $google2fa->generateSecretKey();
                $company = 'SecureBank Online - Test';
                $holder = '<EMAIL>';
                $qr_code_url = $google2fa->getQRCodeUrl($company, $holder, $secret);
                $message = "Secret generated successfully! Secret: $secret";
                break;
                
            case 'verify_code':
                $test_secret = $_POST['test_secret'] ?? '';
                $test_code = $_POST['test_code'] ?? '';
                
                if (empty($test_secret) || empty($test_code)) {
                    $error = 'Please provide both secret and verification code.';
                } else {
                    $isValid = $google2fa->verifyKey($test_secret, $test_code);
                    if ($isValid) {
                        $message = 'Verification successful! The code is valid.';
                    } else {
                        $error = 'Verification failed! The code is invalid or expired.';
                    }
                }
                break;
        }
    }
    
} catch (Exception $e) {
    $error = 'Error: ' . $e->getMessage();
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Google 2FA Library Test</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            background: #f8f9fa;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        .test-container {
            max-width: 800px;
            margin: 50px auto;
            padding: 20px;
        }
        .test-card {
            background: white;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            padding: 30px;
            margin-bottom: 20px;
        }
        .qr-code {
            max-width: 200px;
            border: 1px solid #ddd;
            border-radius: 5px;
            padding: 10px;
            background: white;
        }
        .secret-display {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            padding: 15px;
            font-family: monospace;
            word-break: break-all;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <div class="test-card">
            <h1 class="text-center mb-4">
                <i class="fas fa-mobile-alt text-primary"></i> 
                Google 2FA Library Test
            </h1>
            
            <?php if ($error): ?>
                <div class="alert alert-danger">
                    <i class="fas fa-exclamation-triangle"></i> <?php echo htmlspecialchars($error); ?>
                </div>
            <?php endif; ?>
            
            <?php if ($message): ?>
                <div class="alert alert-success">
                    <i class="fas fa-check-circle"></i> <?php echo htmlspecialchars($message); ?>
                </div>
            <?php endif; ?>
            
            <div class="row">
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header">
                            <h5><i class="fas fa-key"></i> Generate Secret & QR Code</h5>
                        </div>
                        <div class="card-body">
                            <form method="POST">
                                <input type="hidden" name="action" value="generate_secret">
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-qrcode"></i> Generate New Secret
                                </button>
                            </form>
                            
                            <?php if ($secret): ?>
                                <hr>
                                <h6>Generated Secret:</h6>
                                <div class="secret-display"><?php echo htmlspecialchars($secret); ?></div>
                                
                                <?php if ($qr_code_url): ?>
                                    <h6>QR Code:</h6>
                                    <div class="text-center">
                                        <img src="<?php echo htmlspecialchars($qr_code_url); ?>" 
                                             alt="QR Code" class="qr-code">
                                    </div>
                                    <p class="small text-muted mt-2">
                                        Scan this QR code with Google Authenticator app to test.
                                    </p>
                                <?php endif; ?>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
                
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header">
                            <h5><i class="fas fa-check-circle"></i> Verify Code</h5>
                        </div>
                        <div class="card-body">
                            <form method="POST">
                                <input type="hidden" name="action" value="verify_code">
                                
                                <div class="mb-3">
                                    <label for="test_secret" class="form-label">Secret Key:</label>
                                    <input type="text" class="form-control font-monospace" 
                                           id="test_secret" name="test_secret" 
                                           value="<?php echo htmlspecialchars($secret); ?>"
                                           placeholder="Enter secret key" required>
                                </div>
                                
                                <div class="mb-3">
                                    <label for="test_code" class="form-label">6-Digit Code:</label>
                                    <input type="text" class="form-control text-center" 
                                           id="test_code" name="test_code" 
                                           placeholder="000000" maxlength="6" 
                                           pattern="\d{6}" required>
                                </div>
                                
                                <button type="submit" class="btn btn-success">
                                    <i class="fas fa-check"></i> Verify Code
                                </button>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="test-card">
            <h5><i class="fas fa-info-circle text-info"></i> Test Instructions</h5>
            <ol>
                <li><strong>Generate Secret:</strong> Click "Generate New Secret" to create a new secret key and QR code.</li>
                <li><strong>Setup Authenticator:</strong> Open Google Authenticator app on your phone and scan the QR code.</li>
                <li><strong>Verify Code:</strong> Enter the 6-digit code from your app in the verification form.</li>
                <li><strong>Test Result:</strong> The system will verify if the code is valid.</li>
            </ol>
            
            <div class="alert alert-info mt-3">
                <h6><i class="fas fa-download"></i> Google Authenticator Apps:</h6>
                <p class="mb-2">Download the Google Authenticator app:</p>
                <a href="https://play.google.com/store/apps/details?id=com.google.android.apps.authenticator2" 
                   target="_blank" class="btn btn-sm btn-outline-success me-2">
                    <i class="fab fa-android"></i> Android
                </a>
                <a href="https://apps.apple.com/app/google-authenticator/id388497605" 
                   target="_blank" class="btn btn-sm btn-outline-primary">
                    <i class="fab fa-apple"></i> iOS
                </a>
            </div>
        </div>
        
        <div class="test-card">
            <h5><i class="fas fa-cogs text-warning"></i> Library Information</h5>
            <div class="row">
                <div class="col-md-6">
                    <strong>Library Path:</strong><br>
                    <code>vendor/GoogleAuthenticator/Google2FA.php</code>
                </div>
                <div class="col-md-6">
                    <strong>Library Status:</strong><br>
                    <span class="badge bg-success">
                        <i class="fas fa-check"></i> Loaded Successfully
                    </span>
                </div>
            </div>
            
            <hr>
            
            <div class="row">
                <div class="col-md-6">
                    <strong>Secret Length:</strong><br>
                    <?php echo $secret ? strlen($secret) . ' characters' : 'Not generated'; ?>
                </div>
                <div class="col-md-6">
                    <strong>QR Code:</strong><br>
                    <?php echo $qr_code_url ? 'Generated' : 'Not generated'; ?>
                </div>
            </div>
        </div>
        
        <div class="text-center">
            <a href="setup_super_admin_2fa.php" class="btn btn-primary">
                <i class="fas fa-arrow-right"></i> Proceed to Super Admin 2FA Setup
            </a>
            <a href="super-admin/login.php" class="btn btn-secondary">
                <i class="fas fa-shield-alt"></i> Go to Super Admin Login
            </a>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Auto-focus on verification code input
        document.getElementById('test_code').addEventListener('input', function(e) {
            // Only allow numeric input
            e.target.value = e.target.value.replace(/[^0-9]/g, '');
        });
        
        // Auto-submit when 6 digits are entered
        document.getElementById('test_code').addEventListener('input', function(e) {
            if (e.target.value.length === 6) {
                // Small delay to allow user to see the complete code
                setTimeout(() => {
                    // Optional: auto-submit the form
                    // e.target.form.submit();
                }, 500);
            }
        });
    </script>
</body>
</html>
