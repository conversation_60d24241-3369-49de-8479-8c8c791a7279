<?php
/**
 * Test Script for User Details Modal Fix
 * Tests the fixed viewUserDetails function in user-status-management.php
 */

require_once '../config/config.php';
requireAdmin();

echo "<!DOCTYPE html>
<html>
<head>
    <title>User Details Modal - Fix Testing</title>
    <link href='https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css' rel='stylesheet'>
    <link href='https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css' rel='stylesheet'>
    <script src='https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js'></script>
    <script src='https://code.jquery.com/jquery-3.6.0.min.js'></script>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background-color: #f8f9fa; }
        .test-section { margin: 20px 0; padding: 20px; border: 1px solid #ddd; border-radius: 8px; background: white; }
        .success { background-color: #d4edda; border-color: #c3e6cb; color: #155724; }
        .error { background-color: #f8d7da; border-color: #f5c6cb; color: #721c24; }
        .info { background-color: #d1ecf1; border-color: #bee5eb; color: #0c5460; }
        .avatar-demo { 
            background: linear-gradient(135deg, #4f46e5 0%, #7c3aed 100%); 
            color: white; 
            width: 32px; 
            height: 32px; 
            border-radius: 50%; 
            display: inline-flex; 
            align-items: center; 
            justify-content: center; 
            font-weight: 600; 
            font-size: 0.75rem;
            margin-right: 10px;
        }
    </style>
</head>
<body>";

echo "<div class='container-fluid'>
<h1>🔧 User Details Modal - Fix Testing</h1>
<p class='text-muted'>Testing the fixed viewUserDetails function that now dynamically creates modal content</p>";

// Test the user details modal
echo "<div class='test-section'>
<h2>🔍 User Details Modal Test</h2>
<p>Click the buttons below to test the user details modal with different users</p>";

try {
    $db = getDB();
    
    // Get sample users
    $users_query = "SELECT id, first_name, last_name, username, email, account_status 
                    FROM accounts 
                    WHERE is_admin = 0 
                    ORDER BY id 
                    LIMIT 5";
    $users_result = $db->query($users_query);
    
    if ($users_result && $users_result->num_rows > 0) {
        echo "<div class='row'>";
        while ($user = $users_result->fetch_assoc()) {
            $initials = strtoupper(substr($user['first_name'], 0, 1) . substr($user['last_name'], 0, 1));
            $status_color = match($user['account_status'] ?? 'Active') {
                'Active' => 'success',
                'Dormant/Inactive' => 'warning',
                'INACTIVE' => 'secondary',
                'Disabled' => 'danger',
                'Suspend' => 'danger',
                default => 'info'
            };
            
            echo "<div class='col-md-6 col-lg-4 mb-3'>
                <div class='card'>
                    <div class='card-body'>
                        <div class='d-flex align-items-center mb-3'>
                            <div class='avatar-demo'>$initials</div>
                            <div>
                                <div class='fw-bold'>{$user['first_name']} {$user['last_name']}</div>
                                <small class='text-muted'>@{$user['username']}</small>
                            </div>
                        </div>
                        <div class='mb-2'>
                            <span class='badge bg-$status_color'>{$user['account_status']}</span>
                        </div>
                        <div class='mb-2'>
                            <small class='text-muted'>{$user['email']}</small>
                        </div>
                        <button class='btn btn-primary btn-sm w-100' onclick='viewUserDetails({$user['id']})'>
                            <i class='fas fa-eye me-2'></i>View Details
                        </button>
                    </div>
                </div>
            </div>";
        }
        echo "</div>";
        
        echo "<div class='alert alert-info mt-3'>
        <strong>Instructions:</strong>
        <ul class='mb-0'>
            <li>Click any \"View Details\" button to test the modal</li>
            <li>The modal should load user information dynamically</li>
            <li>Check for status history, transactions, and security info</li>
            <li>Verify that the modal displays properly without JavaScript errors</li>
        </ul>
        </div>";
        
    } else {
        echo "<div class='alert alert-warning'>No test users available</div>";
    }
} catch (Exception $e) {
    echo "<div class='alert alert-danger'>Database error: " . htmlspecialchars($e->getMessage()) . "</div>";
}

echo "</div>";

// Add comparison section
echo "<div class='test-section'>
<h2>📊 Before vs After Comparison</h2>
<div class='row'>
    <div class='col-md-6'>
        <div class='card border-danger'>
            <div class='card-header bg-danger text-white'>
                <h5 class='mb-0'>❌ Before (Broken)</h5>
            </div>
            <div class='card-body'>
                <p><strong>Issue:</strong> JavaScript tried to populate individual elements that didn't exist:</p>
                <pre><code>document.getElementById('details_user_name')
document.getElementById('details_username')
document.getElementById('details_email')
// etc... (elements didn't exist)</code></pre>
                <p><strong>Result:</strong> JavaScript errors and empty modal</p>
            </div>
        </div>
    </div>
    <div class='col-md-6'>
        <div class='card border-success'>
            <div class='card-header bg-success text-white'>
                <h5 class='mb-0'>✅ After (Fixed)</h5>
            </div>
            <div class='card-body'>
                <p><strong>Solution:</strong> Dynamically creates complete HTML content:</p>
                <pre><code>const userDetailsHTML = \`...\`;
document.getElementById('userDetailsContent')
    .innerHTML = userDetailsHTML;</code></pre>
                <p><strong>Result:</strong> Fully functional modal with all user details</p>
            </div>
        </div>
    </div>
</div>
</div>";

// Navigation links
echo "<div class='test-section'>
<h2>🔗 Quick Navigation</h2>
<div class='row'>
    <div class='col-md-6'>
        <div class='card'>
            <div class='card-body text-center'>
                <i class='fas fa-users fa-2x text-primary mb-3'></i>
                <h5>User Status Management</h5>
                <p class='text-muted'>Test the actual page with fixed modal</p>
                <a href='user-status-management.php' class='btn btn-primary'>Open Page</a>
            </div>
        </div>
    </div>
    <div class='col-md-6'>
        <div class='card'>
            <div class='card-body text-center'>
                <i class='fas fa-cog fa-2x text-primary mb-3'></i>
                <h5>All Fixes Test</h5>
                <p class='text-muted'>Comprehensive test suite</p>
                <a href='test_all_fixes.php' class='btn btn-success'>Open Test Suite</a>
            </div>
        </div>
    </div>
</div>
</div>";

echo "</div>"; // Close container

// Add the modal HTML (copied from user-status-management.php)
echo "
<!-- User Details Modal -->
<div class='modal modal-blur fade' id='userDetailsModal' tabindex='-1' role='dialog' aria-hidden='true'>
    <div class='modal-dialog modal-lg modal-dialog-centered' role='document'>
        <div class='modal-content'>
            <div class='modal-header'>
                <h5 class='modal-title'>User Account Details</h5>
                <button type='button' class='btn-close' data-bs-dismiss='modal' aria-label='Close'></button>
            </div>
            <div class='modal-body' id='userDetailsContent'>
                <!-- User details will be loaded here -->
            </div>
            <div class='modal-footer'>
                <button type='button' class='btn btn-secondary' data-bs-dismiss='modal'>Close</button>
            </div>
        </div>
    </div>
</div>";

// Add the JavaScript functions (copied from user-status-management.php)
echo "<script>
function viewUserDetails(userId) {
    // Show loading state
    document.getElementById('userDetailsContent').innerHTML = \`
        <div class=\"text-center py-5\">
            <div class=\"spinner-border text-primary\" role=\"status\">
                <span class=\"visually-hidden\">Loading...</span>
            </div>
            <p class=\"mt-3 text-muted\">Loading user details...</p>
        </div>
    \`;
    
    // Show the modal first
    \$('#userDetailsModal').modal('show');
    
    // Fetch user details via AJAX
    fetch('ajax/get_user_details.php?user_id=' + userId)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                const user = data.user;
                const fullName = user.first_name + ' ' + user.last_name;
                const initials = fullName.split(' ').map(n => n[0]).join('').toUpperCase();
                
                // Build transactions HTML
                let transactionsHTML = '';
                if (data.transactions && data.transactions.length > 0) {
                    data.transactions.forEach((transaction, index) => {
                        const typeColor = transaction.transaction_type === 'credit' ? 'success' : 'danger';
                        const amount = parseFloat(transaction.amount || 0).toLocaleString('en-US', {minimumFractionDigits: 2});
                        const date = new Date(transaction.created_at).toLocaleString();
                        transactionsHTML += \`
                            <tr>
                                <td>\${index + 1}</td>
                                <td><span class=\"badge bg-\${typeColor} badge-sm\">\${transaction.transaction_type}</span></td>
                                <td>\$\${amount}</td>
                                <td><small class=\"text-muted\">\${transaction.description || 'No description'}</small></td>
                                <td><small class=\"text-muted\">\${date}</small></td>
                            </tr>
                        \`;
                    });
                } else {
                    transactionsHTML = '<tr><td colspan=\"5\" class=\"text-center text-muted\">No recent transactions found.</td></tr>';
                }
                
                // Build status history HTML
                let statusHistoryHTML = '';
                if (data.status_history && data.status_history.length > 0) {
                    data.status_history.forEach((history, index) => {
                        const date = new Date(history.changed_at).toLocaleString();
                        const changedBy = history.changed_by_first_name ? 
                            \`\${history.changed_by_first_name} \${history.changed_by_last_name}\` : 'System';
                        statusHistoryHTML += \`
                            <tr>
                                <td>\${index + 1}</td>
                                <td><span class=\"badge bg-secondary badge-sm\">\${history.old_status || 'N/A'}</span></td>
                                <td><span class=\"badge bg-primary badge-sm\">\${history.new_status}</span></td>
                                <td><small class=\"text-muted\">\${history.reason}</small></td>
                                <td><small class=\"text-muted\">\${changedBy}</small></td>
                                <td><small class=\"text-muted\">\${date}</small></td>
                            </tr>
                        \`;
                    });
                } else {
                    statusHistoryHTML = '<tr><td colspan=\"6\" class=\"text-center text-muted\">No status changes found.</td></tr>';
                }
                
                // Create the complete user details HTML
                const userDetailsHTML = \`
                    <div class=\"row g-3\">
                        <div class=\"col-md-4\">
                            <div class=\"text-center\">
                                <div class=\"avatar avatar-xl mx-auto mb-3\" style=\"background: linear-gradient(135deg, #4f46e5 0%, #7c3aed 100%); color: white; font-size: 1.5rem; border: 3px solid rgba(79, 70, 229, 0.2); width: 80px; height: 80px; display: flex; align-items: center; justify-content: center; border-radius: 50%; font-weight: 600;\">
                                    \${initials}
                                </div>
                                <h4 class=\"mb-1 fw-bold\">\${fullName}</h4>
                                <p class=\"text-muted mb-2\">@\${user.username}</p>
                                <span class=\"badge bg-\${getStatusColor(user.account_status || 'Active')} px-3 py-2\">
                                    <i class=\"fas fa-circle me-1\"></i>\${user.account_status || 'Active'}
                                </span>
                                <div class=\"mt-3\">
                                    <div class=\"text-muted small\">Account Balance</div>
                                    <div class=\"h4 text-success\">\$\${parseFloat(user.balance || 0).toLocaleString('en-US', {minimumFractionDigits: 2})}</div>
                                </div>
                            </div>
                        </div>
                        <div class=\"col-md-8\">
                            <div class=\"card border-0 shadow-sm\">
                                <div class=\"card-body\">
                                    <h6 class=\"card-title text-primary mb-3\">
                                        <i class=\"fas fa-user me-2\"></i>Personal Information
                                    </h6>
                                    <div class=\"row g-2\">
                                        <div class=\"col-sm-6\">
                                            <label class=\"form-label text-muted small\">Email Address</label>
                                            <div class=\"fw-bold\">\${user.email}</div>
                                        </div>
                                        <div class=\"col-sm-6\">
                                            <label class=\"form-label text-muted small\">Account Number</label>
                                            <div class=\"fw-bold\"><code>\${user.account_number}</code></div>
                                        </div>
                                        <div class=\"col-sm-6\">
                                            <label class=\"form-label text-muted small\">Phone</label>
                                            <div class=\"fw-bold\">\${user.phone || 'Not provided'}</div>
                                        </div>
                                        <div class=\"col-sm-6\">
                                            <label class=\"form-label text-muted small\">Date of Birth</label>
                                            <div class=\"fw-bold\">\${user.date_of_birth ? new Date(user.date_of_birth).toLocaleDateString() : 'Not provided'}</div>
                                        </div>
                                        <div class=\"col-12\">
                                            <label class=\"form-label text-muted small\">Address</label>
                                            <div class=\"fw-bold\">\${user.address || 'Not provided'}</div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            <div class=\"card border-0 shadow-sm mt-3\">
                                <div class=\"card-body\">
                                    <h6 class=\"card-title text-primary mb-3\">
                                        <i class=\"fas fa-shield-alt me-2\"></i>Security Information
                                    </h6>
                                    <div class=\"row g-2\">
                                        <div class=\"col-sm-6\">
                                            <label class=\"form-label text-muted small\">OTP Enabled</label>
                                            <div>\${data.security && data.security.otp_enabled ? '<span class=\"badge bg-success\">Enabled</span>' : '<span class=\"badge bg-danger\">Disabled</span>'}</div>
                                        </div>
                                        <div class=\"col-sm-6\">
                                            <label class=\"form-label text-muted small\">2FA Required</label>
                                            <div>\${data.security && data.security.require_2fa ? '<span class=\"badge bg-warning\">Required</span>' : '<span class=\"badge bg-secondary\">Optional</span>'}</div>
                                        </div>
                                        <div class=\"col-sm-6\">
                                            <label class=\"form-label text-muted small\">Failed Attempts</label>
                                            <div class=\"fw-bold\">\${data.security ? (data.security.failed_attempts || '0') : 'N/A'}</div>
                                        </div>
                                        <div class=\"col-sm-6\">
                                            <label class=\"form-label text-muted small\">Account Status</label>
                                            <div class=\"fw-bold\">\${data.security && data.security.locked_until ? 'Locked until ' + new Date(data.security.locked_until).toLocaleString() : 'Not locked'}</div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class=\"row mt-4\">
                        <div class=\"col-12\">
                            <div class=\"card border-0 shadow-sm\">
                                <div class=\"card-body\">
                                    <h6 class=\"card-title text-primary mb-3\">
                                        <i class=\"fas fa-history me-2\"></i>Status Change History
                                    </h6>
                                    <div class=\"table-responsive\">
                                        <table class=\"table table-sm\">
                                            <thead>
                                                <tr>
                                                    <th>#</th>
                                                    <th>From</th>
                                                    <th>To</th>
                                                    <th>Reason</th>
                                                    <th>Changed By</th>
                                                    <th>Date</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                \${statusHistoryHTML}
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class=\"row mt-3\">
                        <div class=\"col-12\">
                            <div class=\"card border-0 shadow-sm\">
                                <div class=\"card-body\">
                                    <h6 class=\"card-title text-primary mb-3\">
                                        <i class=\"fas fa-exchange-alt me-2\"></i>Recent Transactions
                                    </h6>
                                    <div class=\"table-responsive\">
                                        <table class=\"table table-sm\">
                                            <thead>
                                                <tr>
                                                    <th>#</th>
                                                    <th>Type</th>
                                                    <th>Amount</th>
                                                    <th>Description</th>
                                                    <th>Date</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                \${transactionsHTML}
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                \`;
                
                // Insert the HTML into the modal
                document.getElementById('userDetailsContent').innerHTML = userDetailsHTML;
                
            } else {
                document.getElementById('userDetailsContent').innerHTML = \`
                    <div class=\"text-center py-5\">
                        <div class=\"text-danger mb-3\">
                            <i class=\"fas fa-exclamation-triangle fa-3x\"></i>
                        </div>
                        <h5 class=\"text-danger\">Error Loading User Details</h5>
                        <p class=\"text-muted\">\${data.error || data.message || 'Unknown error occurred'}</p>
                        <button class=\"btn btn-primary\" onclick=\"viewUserDetails(\${userId})\">
                            <i class=\"fas fa-redo me-2\"></i>Try Again
                        </button>
                    </div>
                \`;
            }
        })
        .catch(error => {
            console.error('Error:', error);
            document.getElementById('userDetailsContent').innerHTML = \`
                <div class=\"text-center py-5\">
                    <div class=\"text-danger mb-3\">
                        <i class=\"fas fa-exclamation-triangle fa-3x\"></i>
                    </div>
                    <h5 class=\"text-danger\">Connection Error</h5>
                    <p class=\"text-muted\">Failed to load user details. Please check your connection and try again.</p>
                    <button class=\"btn btn-primary\" onclick=\"viewUserDetails(\${userId})\">
                        <i class=\"fas fa-redo me-2\"></i>Try Again
                    </button>
                </div>
            \`;
        });
}

// Helper function to get status color
function getStatusColor(status) {
    const statusColors = {
        'Active': 'success',
        'Dormant/Inactive': 'warning',
        'INACTIVE': 'secondary',
        'Disabled': 'danger',
        'Suspend': 'danger',
        'Suspended': 'danger',
        'active': 'success',
        'inactive': 'warning',
        'suspended': 'danger',
        'disabled': 'danger'
    };
    return statusColors[status] || 'info';
}
</script>";

echo "</body></html>";
?>
