<?php
/**
 * Generate PDF Receipt for Transfer
 * Creates a downloadable PDF receipt for completed transfers
 */

// Enable error reporting for debugging
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Start session and check authentication
session_start();
if (!isset($_SESSION['user_id'])) {
    header('Location: ../../auth/login.php');
    exit();
}

// Include database connection
require_once '../../config/config.php';

// Get database connection
$db = getDB();
$user_id = $_SESSION['user_id'];

// Get transfer ID from URL
$transfer_id = intval($_GET['id'] ?? 0);

if (!$transfer_id) {
    die('Invalid transfer ID');
}

// Get transfer details with sender information
$transfer_sql = "SELECT t.*, 
                        a.first_name as sender_first_name, 
                        a.last_name as sender_last_name,
                        a.account_number as sender_account,
                        a.email as sender_email
                 FROM transfers t 
                 JOIN accounts a ON t.sender_id = a.id 
                 WHERE t.id = ? AND t.sender_id = ?";

$transfer = $db->query($transfer_sql, [$transfer_id, $user_id]);

if (!$transfer || $transfer->num_rows === 0) {
    die('Transfer not found or access denied');
}

$transfer = $transfer->fetch_assoc();

// Format data for display
$sender_name = trim($transfer['sender_first_name'] . ' ' . $transfer['sender_last_name']);
$transfer_date = formatDate($transfer['created_at'], 'F j, Y g:i A');
$transfer_type_display = ucwords(str_replace('-', ' ', $transfer['transfer_type']));

// Set headers for PDF download
header('Content-Type: text/html; charset=UTF-8');
?>
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>Transfer Receipt - <?php echo htmlspecialchars($transfer['transaction_id']); ?></title>
    <style>
        @page {
            margin: 0.5in;
            size: A4 portrait;
        }
        
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 0;
            font-size: 11px;
            line-height: 1.4;
            color: #000;
        }
        
        .receipt-container {
            max-width: 100%;
            margin: 0;
            background: white;
        }
        
        .bank-header {
            text-align: center;
            border-bottom: 3px solid #000;
            padding-bottom: 15px;
            margin-bottom: 20px;
        }
        
        .bank-name {
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 5px;
            text-transform: uppercase;
            letter-spacing: 1px;
        }
        
        .bank-address {
            font-size: 9px;
            color: #333;
            margin-bottom: 10px;
        }
        
        .receipt-title {
            font-size: 16px;
            font-weight: bold;
            color: #000;
            margin: 10px 0;
        }
        
        .receipt-section {
            margin-bottom: 20px;
            border: 1px solid #ddd;
            padding: 15px;
        }
        
        .section-title {
            font-size: 12px;
            font-weight: bold;
            color: #333;
            margin-bottom: 10px;
            border-bottom: 1px solid #eee;
            padding-bottom: 5px;
        }
        
        .receipt-row {
            display: flex;
            justify-content: space-between;
            margin-bottom: 8px;
            padding: 3px 0;
        }
        
        .receipt-label {
            font-weight: 600;
            color: #555;
            width: 40%;
        }
        
        .receipt-value {
            color: #000;
            font-weight: 500;
            width: 60%;
            text-align: right;
        }
        
        .amount-highlight {
            background: #f0f9ff;
            padding: 10px;
            border-radius: 5px;
            border: 1px solid #0ea5e9;
        }
        
        .amount-large {
            font-size: 14px;
            font-weight: bold;
            color: #0369a1;
        }
        
        .status-completed {
            color: #059669;
            font-weight: bold;
        }
        
        .reference-number {
            font-family: 'Courier New', monospace;
            font-weight: bold;
            color: #1e40af;
        }
        
        .receipt-footer {
            margin-top: 30px;
            padding-top: 15px;
            border-top: 2px solid #000;
            text-align: center;
            font-size: 9px;
            color: #666;
        }
        
        .footer-note {
            margin: 5px 0;
        }
        
        .account-info {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
            padding: 10px;
            background: #f8f9fa;
            border-radius: 5px;
        }
        
        .print-date {
            text-align: right;
            font-size: 9px;
            color: #666;
            margin-bottom: 10px;
        }
    </style>
</head>
<body>
    <div class="receipt-container">
        <!-- Print Date -->
        <div class="print-date">
            Generated on: <?php echo date('F j, Y g:i A'); ?>
        </div>
        
        <!-- Bank Header -->
        <div class="bank-header">
            <div class="bank-name">Online Banking System</div>
            <div class="bank-address">
                123 Banking Street, Financial District<br>
                Phone: +**************** | Email: <EMAIL>
            </div>
            <div class="receipt-title">TRANSFER RECEIPT</div>
        </div>
        
        <!-- Account Information -->
        <div class="account-info">
            <div>
                <strong>Account Holder:</strong> <?php echo htmlspecialchars($sender_name); ?>
            </div>
            <div>
                <strong>Account Number:</strong> ****<?php echo substr($transfer['sender_account'], -4); ?>
            </div>
        </div>
        
        <!-- Transaction Details -->
        <div class="receipt-section">
            <div class="section-title">📋 TRANSACTION DETAILS</div>
            
            <div class="receipt-row">
                <span class="receipt-label">Reference Number:</span>
                <span class="receipt-value reference-number"><?php echo htmlspecialchars($transfer['transaction_id']); ?></span>
            </div>
            
            <div class="receipt-row">
                <span class="receipt-label">Transfer Type:</span>
                <span class="receipt-value"><?php echo htmlspecialchars($transfer_type_display); ?></span>
            </div>
            
            <div class="receipt-row">
                <span class="receipt-label">Date & Time:</span>
                <span class="receipt-value"><?php echo $transfer_date; ?></span>
            </div>
            
            <div class="receipt-row">
                <span class="receipt-label">Status:</span>
                <span class="receipt-value status-completed">✅ COMPLETED</span>
            </div>
        </div>
        
        <!-- Recipient Information -->
        <div class="receipt-section">
            <div class="section-title">🎯 RECIPIENT INFORMATION</div>
            
            <div class="receipt-row">
                <span class="receipt-label">Name:</span>
                <span class="receipt-value"><?php echo htmlspecialchars($transfer['recipient_name']); ?></span>
            </div>
            
            <div class="receipt-row">
                <span class="receipt-label">Account Number:</span>
                <span class="receipt-value">****<?php echo substr($transfer['recipient_account'], -4); ?></span>
            </div>
            
            <?php if ($transfer['transfer_type'] === 'local'): ?>
            <div class="receipt-row">
                <span class="receipt-label">Bank:</span>
                <span class="receipt-value">External Bank</span>
            </div>
            <?php endif; ?>
        </div>
        
        <!-- Amount Details -->
        <div class="receipt-section">
            <div class="section-title">💰 AMOUNT DETAILS</div>
            
            <div class="receipt-row">
                <span class="receipt-label">Transfer Amount:</span>
                <span class="receipt-value amount-large"><?php echo $transfer['currency']; ?> <?php echo number_format($transfer['amount'], 2); ?></span>
            </div>
            
            <?php if ($transfer['fee'] > 0): ?>
            <div class="receipt-row">
                <span class="receipt-label">Transfer Fee:</span>
                <span class="receipt-value"><?php echo $transfer['currency']; ?> <?php echo number_format($transfer['fee'], 2); ?></span>
            </div>
            
            <div class="amount-highlight">
                <div class="receipt-row" style="margin-bottom: 0;">
                    <span class="receipt-label"><strong>Total Debited:</strong></span>
                    <span class="receipt-value amount-large"><?php echo $transfer['currency']; ?> <?php echo number_format($transfer['amount'] + $transfer['fee'], 2); ?></span>
                </div>
            </div>
            <?php else: ?>
            <div class="receipt-row">
                <span class="receipt-label">Transfer Fee:</span>
                <span class="receipt-value status-completed">FREE</span>
            </div>
            <?php endif; ?>
        </div>
        
        <!-- Purpose (if provided) -->
        <?php if (!empty($transfer['description'])): ?>
        <div class="receipt-section">
            <div class="section-title">📝 PURPOSE</div>
            <div style="padding: 10px; background: #f8f9fa; border-radius: 5px; font-style: italic;">
                "<?php echo htmlspecialchars($transfer['description']); ?>"
            </div>
        </div>
        <?php endif; ?>
        
        <!-- Receipt Footer -->
        <div class="receipt-footer">
            <div class="footer-note">
                <strong>Important:</strong> This is a computer-generated receipt and does not require a signature.
            </div>
            <div class="footer-note">
                For any queries, please contact our customer support at +**************** or visit your nearest branch.
            </div>
            <div class="footer-note">
                <strong>Transaction Reference:</strong> <?php echo htmlspecialchars($transfer['transaction_id']); ?>
            </div>
        </div>
    </div>
    
    <script>
        // Auto-print when page loads
        window.onload = function() {
            window.print();
        };
    </script>
</body>
</html>
