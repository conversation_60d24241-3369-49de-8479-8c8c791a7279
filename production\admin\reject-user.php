<?php
require_once '../config/config.php';
requireAdmin();

if (empty($_GET['id'])) {
    redirect('users.php');
}

$user_id = intval($_GET['id']);

try {
    $db = getDB();
    
    // Get user details before deletion
    $sql = "SELECT * FROM accounts WHERE id = ? AND status = 'pending'";
    $result = $db->query($sql, [$user_id]);
    
    if ($result && $result->num_rows === 1) {
        $user = $result->fetch_assoc();
        
        // Delete user
        $delete_sql = "DELETE FROM accounts WHERE id = ?";
        $db->query($delete_sql, [$user_id]);
        
        // Log activity
        logActivity($_SESSION['user_id'], 'Rejected user registration', 'accounts', $user_id, null, [
            'username' => $user['username'],
            'email' => $user['email'],
            'account_number' => $user['account_number']
        ]);
        
        // Set success message
        $_SESSION['success'] = 'User registration rejected successfully. Account deleted.';
    } else {
        $_SESSION['error'] = 'User not found or not pending approval.';
    }
} catch (Exception $e) {
    error_log("Reject user error: " . $e->getMessage());
    $_SESSION['error'] = 'An error occurred. Please try again.';
}

redirect('users.php');
