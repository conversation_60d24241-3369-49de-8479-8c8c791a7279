<?php
/**
 * Standalone SMTP Test with Port 465
 * Clean test without config dependencies - for <EMAIL>
 */

// Enable error reporting
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

echo "<!DOCTYPE html>";
echo "<html><head><title>Standalone SMTP Test - Port 465</title>";
echo "<style>
body { font-family: Arial, sans-serif; margin: 20px; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); min-height: 100vh; }
.container { max-width: 800px; margin: 0 auto; background: white; padding: 30px; border-radius: 15px; box-shadow: 0 8px 32px rgba(0,0,0,0.3); }
.header { background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%); color: white; padding: 30px; text-align: center; border-radius: 10px; margin-bottom: 20px; }
.success { background: linear-gradient(135deg, #27ae60 0%, #2ecc71 100%); color: white; padding: 20px; border-radius: 10px; margin: 15px 0; }
.error { background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%); color: white; padding: 20px; border-radius: 10px; margin: 15px 0; }
.info { background: #e7f3ff; border: 1px solid #b3d9ff; color: #0c5460; padding: 20px; border-radius: 10px; margin: 15px 0; }
.debug { background: #f8f9fa; padding: 10px; margin: 5px 0; border-left: 3px solid #007bff; font-size: 11px; }
pre { background: #2d3748; color: #e2e8f0; padding: 15px; border-radius: 8px; font-size: 12px; overflow-x: auto; }
</style>";
echo "</head><body>";

echo "<div class='container'>";
echo "<div class='header'>";
echo "<h1>🏦 Standalone SMTP Test</h1>";
echo "<p>Testing Hostinger SMTP with Port 465 (SSL) - No Config Dependencies</p>";
echo "</div>";

$test_email = '<EMAIL>';

// SMTP Configuration - Hostinger with Port 465 (SSL)
$smtp_host = 'smtp.hostinger.com';
$smtp_port = 465; // SSL port
$smtp_username = '<EMAIL>';
$smtp_password = 'Money2025@Demo#';
$smtp_encryption = 'ssl'; // SSL for port 465
$from_email = '<EMAIL>';
$from_name = 'Online Banking System';

echo "<div class='info'>";
echo "<h3>📧 SMTP Configuration (Port 465 - SSL)</h3>";
echo "<strong>Host:</strong> $smtp_host<br>";
echo "<strong>Port:</strong> $smtp_port (SSL)<br>";
echo "<strong>Username:</strong> $smtp_username<br>";
echo "<strong>Encryption:</strong> " . strtoupper($smtp_encryption) . "<br>";
echo "<strong>From:</strong> $from_email<br>";
echo "<strong>Target:</strong> $test_email<br>";
echo "</div>";

try {
    // Check if PHPMailer exists in vendor directory
    $phpmailer_path = __DIR__ . '/vendor/phpmailer/phpmailer/src/PHPMailer.php';
    $exception_path = __DIR__ . '/vendor/phpmailer/phpmailer/src/Exception.php';
    $smtp_path = __DIR__ . '/vendor/phpmailer/phpmailer/src/SMTP.php';
    
    if (!file_exists($phpmailer_path)) {
        throw new Exception("PHPMailer not found at: $phpmailer_path");
    }
    
    // Load PHPMailer manually (avoiding autoloader issues)
    require_once $exception_path;
    require_once $smtp_path;
    require_once $phpmailer_path;
    
    echo "<div class='success'>✅ PHPMailer loaded successfully</div>";
    
    // Create PHPMailer instance
    $mail = new PHPMailer\PHPMailer\PHPMailer(true);
    
    // Enable SMTP debugging
    $mail->SMTPDebug = 2;
    $mail->Debugoutput = function($str, $level) {
        echo "<div class='debug'>SMTP Debug (Level $level): " . htmlspecialchars($str) . "</div>";
    };
    
    echo "<h3>📤 Attempting SMTP Connection...</h3>";
    echo "<div style='background: #f8f9fa; padding: 15px; border-radius: 5px; margin: 15px 0; max-height: 300px; overflow-y: auto;'>";
    
    // Server settings for Port 465 (SSL)
    $mail->isSMTP();
    $mail->Host = $smtp_host;
    $mail->SMTPAuth = true;
    $mail->Username = $smtp_username;
    $mail->Password = $smtp_password;
    $mail->SMTPSecure = PHPMailer\PHPMailer\PHPMailer::ENCRYPTION_SMTPS; // SSL
    $mail->Port = $smtp_port;
    
    // Recipients
    $mail->setFrom($from_email, $from_name);
    $mail->addAddress($test_email, 'Demo Developer');
    
    // Content
    $mail->isHTML(true);
    $mail->Subject = '🏦 HOSTINGER SMTP TEST - PORT 465 (SSL) - ' . date('Y-m-d H:i:s');
    $mail->Body = '
    <!DOCTYPE html>
    <html>
    <head>
        <meta charset="UTF-8">
        <title>Hostinger SMTP Test - Port 465</title>
    </head>
    <body style="font-family: Arial, sans-serif; margin: 0; padding: 20px; background-color: #f0f2f5;">
        <div style="max-width: 600px; margin: 0 auto; background-color: white; border-radius: 12px; overflow: hidden; box-shadow: 0 4px 12px rgba(0,0,0,0.15);">
            
            <!-- Header -->
            <div style="background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%); color: white; padding: 30px; text-align: center;">
                <h1 style="margin: 0; font-size: 2.2em;">🏦 Online Banking System</h1>
                <h2 style="margin: 10px 0 0 0; font-weight: normal; opacity: 0.9;">HOSTINGER SMTP SUCCESS!</h2>
                <p style="margin: 5px 0 0 0; opacity: 0.8;">Port 465 (SSL) Working Perfectly</p>
            </div>
            
            <!-- Main Content -->
            <div style="padding: 30px;">
                
                <!-- Success Alert -->
                <div style="background: linear-gradient(135deg, #27ae60 0%, #2ecc71 100%); color: white; padding: 20px; border-radius: 8px; text-align: center; margin-bottom: 25px;">
                    <h2 style="margin: 0 0 10px 0;">🎉 HOSTINGER SMTP WORKING!</h2>
                    <p style="margin: 0; font-size: 1.1em;">Port 465 with SSL encryption is working perfectly!</p>
                </div>
                
                <!-- Test Information -->
                <h3 style="color: #2c3e50; margin: 0 0 15px 0; border-bottom: 2px solid #3498db; padding-bottom: 8px;">📋 Connection Details</h3>
                <div style="background: #f8f9fa; padding: 20px; border-radius: 8px; border-left: 4px solid #3498db;">
                    <table style="width: 100%; border-collapse: collapse;">
                        <tr>
                            <td style="padding: 8px 0; color: #7f8c8d; width: 30%; font-weight: bold;">Recipient:</td>
                            <td style="padding: 8px 0; color: #2c3e50;">' . $test_email . '</td>
                        </tr>
                        <tr>
                            <td style="padding: 8px 0; color: #7f8c8d; font-weight: bold;">Test Date:</td>
                            <td style="padding: 8px 0; color: #2c3e50;">' . date('Y-m-d H:i:s') . '</td>
                        </tr>
                        <tr>
                            <td style="padding: 8px 0; color: #7f8c8d; font-weight: bold;">SMTP Host:</td>
                            <td style="padding: 8px 0; color: #2c3e50;">' . $smtp_host . '</td>
                        </tr>
                        <tr>
                            <td style="padding: 8px 0; color: #7f8c8d; font-weight: bold;">SMTP Port:</td>
                            <td style="padding: 8px 0; color: #2c3e50;">' . $smtp_port . ' (SSL)</td>
                        </tr>
                        <tr>
                            <td style="padding: 8px 0; color: #7f8c8d; font-weight: bold;">Encryption:</td>
                            <td style="padding: 8px 0; color: #2c3e50;">SSL (SMTPS)</td>
                        </tr>
                        <tr>
                            <td style="padding: 8px 0; color: #7f8c8d; font-weight: bold;">Authentication:</td>
                            <td style="padding: 8px 0; color: #2c3e50;">✅ Successful</td>
                        </tr>
                    </table>
                </div>
                
                <!-- What This Confirms -->
                <h3 style="color: #2c3e50; margin: 25px 0 15px 0; border-bottom: 2px solid #e74c3c; padding-bottom: 8px;">✅ What This Confirms</h3>
                <div style="background: #d4edda; padding: 20px; border-radius: 8px; border-left: 4px solid #28a745;">
                    <ul style="margin: 0; padding-left: 20px; color: #155724; line-height: 1.8;">
                        <li><strong>Hostinger SMTP Working:</strong> Port 465 with SSL is fully operational</li>
                        <li><strong>Authentication Success:</strong> Username and password are correct</li>
                        <li><strong>SSL Encryption:</strong> Secure connection established successfully</li>
                        <li><strong>Email Delivery:</strong> Messages can be sent to external addresses</li>
                        <li><strong>Works Offline/Online:</strong> SMTP works regardless of localhost/production</li>
                        <li><strong>Banking System Ready:</strong> All email features will work perfectly</li>
                    </ul>
                </div>
                
                <!-- Technical Details -->
                <h3 style="color: #2c3e50; margin: 25px 0 15px 0; border-bottom: 2px solid #f39c12; padding-bottom: 8px;">🔧 Technical Details</h3>
                <div style="background: #fff3cd; padding: 20px; border-radius: 8px; border-left: 4px solid #ffc107;">
                    <ul style="margin: 0; padding-left: 20px; color: #856404; line-height: 1.6;">
                        <li><strong>Port 465:</strong> SSL/TLS implicit encryption (recommended for security)</li>
                        <li><strong>Port 587:</strong> STARTTLS explicit encryption (alternative option)</li>
                        <li><strong>Hostinger Support:</strong> Both ports work, 465 often more reliable</li>
                        <li><strong>Firewall Friendly:</strong> Port 465 typically not blocked by firewalls</li>
                        <li><strong>Production Ready:</strong> This configuration works in all environments</li>
                    </ul>
                </div>
                
                <!-- Success Message -->
                <div style="background: linear-gradient(135deg, #6c5ce7 0%, #a29bfe 100%); color: white; padding: 20px; border-radius: 8px; text-align: center; margin-top: 25px;">
                    <h3 style="margin: 0 0 10px 0;">🚀 EMAIL SYSTEM FULLY OPERATIONAL!</h3>
                    <p style="margin: 0; opacity: 0.9;">Your banking system can now send emails reliably using Hostinger SMTP!</p>
                </div>
            </div>
            
            <!-- Footer -->
            <div style="background: #2c3e50; color: white; padding: 20px; text-align: center;">
                <p style="margin: 0; opacity: 0.8; font-size: 0.9em;">This email confirms Hostinger SMTP Port 465 is working perfectly!</p>
                <p style="margin: 5px 0 0 0; opacity: 0.6; font-size: 0.8em;">&copy; ' . date('Y') . ' Online Banking System. All rights reserved.</p>
            </div>
        </div>
    </body>
    </html>';
    
    // Send the email
    $result = $mail->send();
    
    echo "</div>"; // Close debug output div
    
    if ($result) {
        echo "<div class='success'>";
        echo "<h2>🎉 EMAIL SENT SUCCESSFULLY!</h2>";
        echo "<p>Hostinger SMTP test email sent to <strong>$test_email</strong></p>";
        echo "<p><strong>Port:</strong> 465 (SSL)</p>";
        echo "<p><strong>Encryption:</strong> SSL (SMTPS)</p>";
        echo "<p><strong>Time:</strong> " . date('Y-m-d H:i:s') . "</p>";
        echo "</div>";
        
        echo "<div class='info'>";
        echo "<h3>📧 Check Your Email:</h3>";
        echo "<ol>";
        echo "<li>Check inbox at <strong><EMAIL></strong></li>";
        echo "<li>Look for: <em>HOSTINGER SMTP TEST - PORT 465 (SSL)</em></li>";
        echo "<li>Check spam/junk folder if not in inbox</li>";
        echo "<li>Email should have full HTML formatting</li>";
        echo "<li>This confirms your SMTP is working perfectly!</li>";
        echo "</ol>";
        echo "</div>";
        
    } else {
        echo "<div class='error'>";
        echo "<h2>❌ EMAIL SENDING FAILED</h2>";
        echo "<p>Check the debug output above for details.</p>";
        echo "</div>";
    }
    
} catch (Exception $e) {
    echo "</div>"; // Close debug output div if open
    
    echo "<div class='error'>";
    echo "<h2>❌ SMTP ERROR</h2>";
    echo "<p><strong>Error:</strong> " . htmlspecialchars($e->getMessage()) . "</p>";
    echo "</div>";
    
    echo "<h3>📄 Error Details:</h3>";
    echo "<pre>" . htmlspecialchars($e->getTraceAsString()) . "</pre>";
}

// Test with Port 587 as backup
echo "<hr>";
echo "<h2>🔄 Backup Test: Port 587 (TLS)</h2>";

try {
    $mail2 = new PHPMailer\PHPMailer\PHPMailer(true);
    
    // Server settings for Port 587 (TLS)
    $mail2->isSMTP();
    $mail2->Host = $smtp_host;
    $mail2->SMTPAuth = true;
    $mail2->Username = $smtp_username;
    $mail2->Password = $smtp_password;
    $mail2->SMTPSecure = PHPMailer\PHPMailer\PHPMailer::ENCRYPTION_STARTTLS; // TLS
    $mail2->Port = 587;
    $mail2->SMTPDebug = 0; // Minimal debug for backup test
    
    // Recipients
    $mail2->setFrom($from_email, $from_name);
    $mail2->addAddress($test_email, 'Demo Developer');
    
    // Content
    $mail2->isHTML(true);
    $mail2->Subject = '🏦 BACKUP TEST - PORT 587 (TLS) - ' . date('H:i:s');
    $mail2->Body = '<div style="font-family: Arial, sans-serif; padding: 20px; background: #e7f3ff; border-radius: 10px;"><h2>🔄 Backup SMTP Test - Port 587</h2><p>This is a backup test using port 587 with TLS encryption.</p><p><strong>Time:</strong> ' . date('Y-m-d H:i:s') . '</p></div>';
    
    $result2 = $mail2->send();
    
    if ($result2) {
        echo "<div class='success'>✅ Backup test (Port 587) also successful!</div>";
    } else {
        echo "<div class='info'>ℹ️ Backup test (Port 587) failed, but Port 465 is working</div>";
    }
    
} catch (Exception $e) {
    echo "<div class='info'>ℹ️ Backup test (Port 587) failed: " . htmlspecialchars($e->getMessage()) . "</div>";
}

// Log the test
$log_entry = "=== STANDALONE SMTP TEST - PORT 465 ===\n";
$log_entry .= "To: $test_email\n";
$log_entry .= "Time: " . date('Y-m-d H:i:s') . "\n";
$log_entry .= "Host: $smtp_host\n";
$log_entry .= "Port: $smtp_port (SSL)\n";
$log_entry .= "Result: " . (isset($result) && $result ? 'SUCCESS' : 'FAILED') . "\n";
$log_entry .= "Backup 587: " . (isset($result2) && $result2 ? 'SUCCESS' : 'FAILED') . "\n";
$log_entry .= "========================================\n\n";

if (!is_dir('logs')) {
    mkdir('logs', 0755, true);
}
file_put_contents('logs/standalone_smtp_465.log', $log_entry, FILE_APPEND | LOCK_EX);

echo "<div class='info'>";
echo "<h3>📄 Test Summary:</h3>";
echo "<ul>";
echo "<li><strong>Target:</strong> $test_email</li>";
echo "<li><strong>Primary Test:</strong> Port 465 (SSL) - " . (isset($result) && $result ? 'SUCCESS' : 'FAILED') . "</li>";
echo "<li><strong>Backup Test:</strong> Port 587 (TLS) - " . (isset($result2) && $result2 ? 'SUCCESS' : 'FAILED') . "</li>";
echo "<li><strong>Time:</strong> " . date('Y-m-d H:i:s') . "</li>";
echo "<li><strong>Log:</strong> logs/standalone_smtp_465.log</li>";
echo "</ul>";
echo "</div>";

echo "</div>";
echo "</body></html>";
?>
