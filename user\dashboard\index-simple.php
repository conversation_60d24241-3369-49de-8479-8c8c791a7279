<?php
/**
 * Simple User Dashboard - Working Version
 */

// Enable error reporting for debugging
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Start session
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    header('Location: ../../auth/login.php');
    exit();
}

// Include database connection
require_once '../../config/config.php';

// Get user data
try {
    $db = getDB();
    $user_id = $_SESSION['user_id'];

    // Get user account information
    $user_sql = "SELECT a.*,
                        COALESCE(a.balance, 0) as balance,
                        DATE_FORMAT(a.created_at, '%M %Y') as member_since,
                        DATEDIFF(NOW(), a.created_at) as days_member
                 FROM accounts a
                 WHERE a.id = ?";
    
    $user_result = $db->query($user_sql, [$user_id]);
    $user = $user_result->fetch_assoc();

    if (!$user) {
        throw new Exception("User account not found");
    }

    $current_balance = $user['balance'] ?? 0;

    // Get virtual cards
    $cards_result = $db->query("SELECT * FROM virtual_cards WHERE account_id = ? ORDER BY created_at DESC LIMIT 1", [$user_id]);
    $primary_card = $cards_result ? $cards_result->fetch_assoc() : null;

    // Get recent transactions
    $transactions_result = $db->query("SELECT * FROM account_transactions WHERE account_id = ? ORDER BY created_at DESC LIMIT 5", [$user_id]);
    $recent_transactions = [];
    if ($transactions_result) {
        while ($transaction = $transactions_result->fetch_assoc()) {
            $recent_transactions[] = $transaction;
        }
    }

    // Get monthly stats
    $current_month = date('Y-m');
    $monthly_stats_result = $db->query("SELECT 
                                          SUM(CASE WHEN transaction_type IN ('credit', 'deposit') THEN amount ELSE 0 END) as total_credits,
                                          SUM(CASE WHEN transaction_type IN ('debit', 'withdrawal') THEN amount ELSE 0 END) as total_debits,
                                          COUNT(*) as transaction_count
                                        FROM account_transactions 
                                        WHERE account_id = ? 
                                        AND DATE_FORMAT(created_at, '%Y-%m') = ?", [$user_id, $current_month]);
    $monthly_stats = $monthly_stats_result ? $monthly_stats_result->fetch_assoc() : [];

    $total_credits = $monthly_stats['total_credits'] ?? 0;
    $total_debits = $monthly_stats['total_debits'] ?? 0;
    $transaction_count = $monthly_stats['transaction_count'] ?? 0;

} catch (Exception $e) {
    error_log("Dashboard error: " . $e->getMessage());
    // Fallback data
    $user = ['first_name' => 'User', 'last_name' => '', 'email' => '', 'account_number' => '', 'account_type' => 'savings', 'status' => 'active', 'member_since' => date('F Y'), 'days_member' => 0];
    $current_balance = 0;
    $total_credits = $total_debits = $transaction_count = 0;
    $recent_transactions = [];
    $primary_card = null;
}

// formatCurrency function already exists in config.php
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Dashboard - PremierBank Pro</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <style>
        .dashboard-header {
            background: linear-gradient(135deg, #206bc4 0%, #1a5490 100%);
            color: white;
            border-radius: 20px;
            margin-bottom: 2rem;
        }
        .info-card {
            border: none;
            border-radius: 16px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
            border-top: 4px solid #206bc4;
            transition: transform 0.3s ease;
        }
        .info-card:hover {
            transform: translateY(-4px);
        }
        .card-icon {
            width: 40px;
            height: 40px;
            background: #206bc4;
            border-radius: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
        }
    </style>
</head>
<body class="bg-light">
    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar -->
            <div class="col-md-3 col-lg-2 px-0">
                <?php require_once '../shared/sidebar.php'; ?>
            </div>
            
            <!-- Main Content -->
            <div class="col-md-9 col-lg-10">
                <div class="p-4">
                    <!-- Dashboard Header -->
                    <div class="dashboard-header p-4 mb-4">
                        <div class="row align-items-center">
                            <div class="col-md-8">
                                <div class="display-4 fw-bold mb-3"><?php echo formatCurrency($current_balance); ?></div>
                                <h3 class="mb-2"><?php echo htmlspecialchars($user['first_name'] . ' ' . $user['last_name']); ?></h3>
                                <p class="mb-1">Account: <?php echo htmlspecialchars($user['account_number']); ?></p>
                                <span class="badge bg-light text-dark"><?php echo ucfirst($user['status']); ?></span>
                            </div>
                            <div class="col-md-4 text-md-end">
                                <h4><?php echo ucfirst($user['account_type']); ?> Account</h4>
                                <p class="mb-0">Member since <?php echo htmlspecialchars($user['member_since']); ?></p>
                            </div>
                        </div>
                    </div>

                    <!-- Quick Actions -->
                    <div class="row mb-4">
                        <div class="col-12">
                            <div class="d-flex gap-2">
                                <a href="../transfers/" class="btn btn-primary">
                                    <i class="fas fa-paper-plane me-2"></i>Send Money
                                </a>
                                <a href="../cards/" class="btn btn-success">
                                    <i class="fas fa-plus me-2"></i>New Card
                                </a>
                                <a href="../transactions/" class="btn btn-outline-primary">
                                    <i class="fas fa-history me-2"></i>View Transactions
                                </a>
                            </div>
                        </div>
                    </div>

                    <!-- Dashboard Cards -->
                    <div class="row g-4">
                        <!-- Balance Card -->
                        <div class="col-lg-4 col-md-6">
                            <div class="card info-card h-100">
                                <div class="card-body">
                                    <div class="d-flex justify-content-between align-items-center mb-3">
                                        <h5 class="card-title mb-0">Account Balance</h5>
                                        <div class="card-icon">
                                            <i class="fas fa-wallet"></i>
                                        </div>
                                    </div>
                                    <div class="h3 text-primary mb-3"><?php echo formatCurrency($current_balance); ?></div>
                                    <div class="text-success">
                                        <i class="fas fa-check-circle"></i>
                                        Available for transactions
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Recent Transactions -->
                        <div class="col-lg-4 col-md-6">
                            <div class="card info-card h-100">
                                <div class="card-body">
                                    <div class="d-flex justify-content-between align-items-center mb-3">
                                        <h5 class="card-title mb-0">Recent Transactions</h5>
                                        <div class="card-icon">
                                            <i class="fas fa-history"></i>
                                        </div>
                                    </div>
                                    <?php if (empty($recent_transactions)): ?>
                                        <p class="text-muted">No transactions found.</p>
                                    <?php else: ?>
                                        <?php foreach (array_slice($recent_transactions, 0, 3) as $transaction): ?>
                                            <div class="d-flex justify-content-between align-items-center py-2 border-bottom">
                                                <div>
                                                    <div class="fw-bold"><?php echo ucfirst($transaction['transaction_type']); ?></div>
                                                    <small class="text-muted"><?php echo htmlspecialchars($transaction['description']); ?></small>
                                                </div>
                                                <div class="fw-bold <?php echo $transaction['transaction_type'] === 'credit' ? 'text-success' : 'text-danger'; ?>">
                                                    <?php echo ($transaction['transaction_type'] === 'credit' ? '+' : '-') . formatCurrency($transaction['amount']); ?>
                                                </div>
                                            </div>
                                        <?php endforeach; ?>
                                    <?php endif; ?>
                                    <a href="../transactions/" class="btn btn-outline-primary btn-sm mt-3 w-100">View All</a>
                                </div>
                            </div>
                        </div>

                        <!-- Virtual Card -->
                        <div class="col-lg-4 col-md-6">
                            <div class="card info-card h-100">
                                <div class="card-body">
                                    <div class="d-flex justify-content-between align-items-center mb-3">
                                        <h5 class="card-title mb-0">Virtual Card</h5>
                                        <div class="card-icon">
                                            <i class="fas fa-credit-card"></i>
                                        </div>
                                    </div>
                                    <?php if ($primary_card): ?>
                                        <div class="card bg-primary text-white p-3 mb-3" style="border-radius: 12px;">
                                            <div style="font-family: monospace; font-size: 1.1rem; letter-spacing: 2px;">
                                                **** **** **** <?php echo substr($primary_card['card_number'], -4); ?>
                                            </div>
                                            <div class="d-flex justify-content-between mt-2">
                                                <small><?php echo htmlspecialchars($primary_card['card_holder_name']); ?></small>
                                                <small><?php echo date('m/y', strtotime($primary_card['expiry_date'])); ?></small>
                                            </div>
                                        </div>
                                        <div class="text-success">
                                            <i class="fas fa-check-circle"></i>
                                            Balance: <?php echo formatCurrency($primary_card['card_balance']); ?>
                                        </div>
                                    <?php else: ?>
                                        <div class="text-center py-3">
                                            <i class="fas fa-credit-card fa-3x text-muted mb-3"></i>
                                            <p class="text-muted">No virtual cards found.</p>
                                            <a href="../cards/" class="btn btn-primary btn-sm">Apply for Card</a>
                                        </div>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </div>

                        <!-- Monthly Statistics -->
                        <div class="col-lg-6 col-md-6">
                            <div class="card info-card h-100">
                                <div class="card-body">
                                    <div class="d-flex justify-content-between align-items-center mb-3">
                                        <h5 class="card-title mb-0">This Month's Activity</h5>
                                        <div class="card-icon">
                                            <i class="fas fa-chart-bar"></i>
                                        </div>
                                    </div>
                                    <div class="row text-center">
                                        <div class="col-6">
                                            <div class="bg-light p-3 rounded">
                                                <div class="h4 text-success mb-1"><?php echo formatCurrency($total_credits); ?></div>
                                                <small class="text-muted">Credits</small>
                                            </div>
                                        </div>
                                        <div class="col-6">
                                            <div class="bg-light p-3 rounded">
                                                <div class="h4 text-danger mb-1"><?php echo formatCurrency($total_debits); ?></div>
                                                <small class="text-muted">Debits</small>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="mt-3 text-center">
                                        <span class="badge bg-info"><?php echo $transaction_count; ?> transactions this month</span>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Profile Summary -->
                        <div class="col-lg-6 col-md-6">
                            <div class="card info-card h-100">
                                <div class="card-body">
                                    <div class="d-flex justify-content-between align-items-center mb-3">
                                        <h5 class="card-title mb-0">Profile Summary</h5>
                                        <div class="card-icon">
                                            <i class="fas fa-user"></i>
                                        </div>
                                    </div>
                                    <div class="d-flex align-items-center mb-3">
                                        <div class="bg-primary text-white rounded-circle d-flex align-items-center justify-content-center me-3" style="width: 50px; height: 50px; font-weight: 700;">
                                            <?php echo strtoupper(substr($user['first_name'], 0, 1) . substr($user['last_name'], 0, 1)); ?>
                                        </div>
                                        <div>
                                            <div class="fw-bold"><?php echo htmlspecialchars($user['first_name'] . ' ' . $user['last_name']); ?></div>
                                            <small class="text-muted"><?php echo htmlspecialchars($user['email']); ?></small>
                                        </div>
                                    </div>
                                    <div class="row text-center">
                                        <div class="col-6">
                                            <div class="bg-light p-2 rounded">
                                                <div class="fw-bold text-primary"><?php echo $user['days_member']; ?></div>
                                                <small class="text-muted">Days Member</small>
                                            </div>
                                        </div>
                                        <div class="col-6">
                                            <div class="bg-light p-2 rounded">
                                                <div class="fw-bold text-primary"><?php echo $primary_card ? 1 : 0; ?></div>
                                                <small class="text-muted">Active Cards</small>
                                            </div>
                                        </div>
                                    </div>
                                    <a href="../profile/" class="btn btn-outline-primary btn-sm mt-3 w-100">Edit Profile</a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
