/**
 * Enhanced User Dashboard CSS
 * Professional Banking Interface Styles
 */

/* Dashboard Hero Section */
.dashboard-hero {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
    border-radius: 16px;
    padding: 2rem;
    color: white;
    margin-bottom: 2rem;
    box-shadow: 0 8px 25px rgba(0,0,0,0.15);
    position: relative;
    overflow: hidden;
}

/* Removed full-width hero section - using regular dashboard-hero instead */

.dashboard-hero::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, rgba(255,255,255,0.1) 0%, transparent 50%);
    pointer-events: none;
}

.hero-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    position: relative;
    z-index: 1;
}

.hero-main {
    flex: 1;
}

.hero-balance {
    font-family: 'Courier New', 'Monaco', 'Menlo', monospace;
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
    text-shadow: 0 2px 4px rgba(0,0,0,0.3);
    letter-spacing: 1px;
}

.hero-welcome {
    font-size: 1.25rem;
    font-weight: 500;
    margin-bottom: 0.5rem;
    opacity: 0.95;
}

.hero-account-info {
    font-size: 0.95rem;
    opacity: 0.85;
    font-weight: 400;
}

.hero-status {
    text-align: right;
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.account-status, .last-login {
    display: flex;
    flex-direction: column;
    align-items: flex-end;
    gap: 0.25rem;
}

.status-label, .time-label {
    font-size: 0.8rem;
    opacity: 0.8;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.status-badge {
    padding: 0.25rem 0.75rem;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.status-active {
    background: rgba(34, 197, 94, 0.2);
    color: #dcfce7;
    border: 1px solid rgba(34, 197, 94, 0.3);
}

.status-pending {
    background: rgba(251, 191, 36, 0.2);
    color: #fef3c7;
    border: 1px solid rgba(251, 191, 36, 0.3);
}

.status-suspended {
    background: rgba(239, 68, 68, 0.2);
    color: #fecaca;
    border: 1px solid rgba(239, 68, 68, 0.3);
}

.time-value {
    font-size: 0.9rem;
    font-weight: 500;
    opacity: 0.95;
}

/* Responsive Design for Hero Section */
@media (max-width: 768px) {
    .hero-content {
        flex-direction: column;
        text-align: center;
        gap: 1.5rem;
    }

    .hero-status {
        text-align: center;
        flex-direction: row;
        justify-content: center;
        gap: 2rem;
    }

    .account-status, .last-login {
        align-items: center;
    }

    .hero-balance {
        font-size: 2rem;
    }

    /* Mobile hero section adjustments removed */
}

/* Virtual Card Styles - Actual Credit Card Dimensions */
.virtual-card-container {
    perspective: 1000px;
    margin-bottom: 2rem;
}

.virtual-card {
    width: 320px; /* 85.60mm converted to pixels at 96 DPI */
    height: 203px; /* 53.98mm converted to pixels at 96 DPI */
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
    border-radius: 16px;
    padding: 20px;
    color: white;
    position: relative;
    box-shadow: 0 8px 25px rgba(0,0,0,0.15);
    transform-style: preserve-3d;
    transition: transform 0.3s ease;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

.virtual-card:hover {
    transform: rotateY(5deg) rotateX(5deg);
}

.virtual-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, rgba(255,255,255,0.1) 0%, transparent 50%);
    border-radius: 16px;
    pointer-events: none;
}

.card-logo {
    position: absolute;
    top: 20px;
    right: 20px;
    width: 40px;
    height: 40px;
    background: rgba(255,255,255,0.2);
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 20px;
}

.card-number {
    font-family: 'Courier New', monospace;
    font-size: 18px;
    font-weight: 600;
    letter-spacing: 3px;
    margin: 40px 0 20px 0;
    text-shadow: 0 1px 2px rgba(0,0,0,0.3);
}

.card-details {
    display: flex;
    justify-content: space-between;
    align-items: flex-end;
    margin-top: auto;
}

.card-holder {
    font-size: 12px;
    text-transform: uppercase;
    letter-spacing: 1px;
    opacity: 0.9;
}

.card-expiry {
    font-size: 12px;
    font-family: 'Courier New', monospace;
    opacity: 0.9;
}

/* Balance Display Section */
.balance-overview {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.5rem;
    margin-bottom: 2rem;
}

.balance-card {
    background: var(--background-white);
    border-radius: 12px;
    padding: 1.5rem;
    box-shadow: 0 2px 10px rgba(0,0,0,0.08);
    border: 1px solid var(--border-color);
    text-align: center;
    transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.balance-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.12);
}

/* New Balance Cards Design */
.balance-overview-new {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 1.5rem;
    margin-bottom: 2rem;
}

.balance-card-new {
    background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
    border-radius: 16px;
    padding: 1.5rem;
    box-shadow: 0 4px 20px rgba(0,0,0,0.08);
    border: 1px solid #e2e8f0;
    display: flex;
    align-items: center;
    gap: 1rem;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

/* Removed top border from balance cards */

.balance-card-new:hover {
    transform: translateY(-4px);
    box-shadow: 0 8px 30px rgba(0,0,0,0.12);
    border-color: var(--primary-color);
}

.balance-icon {
    width: 60px;
    height: 60px;
    border-radius: 12px;
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.5rem;
    flex-shrink: 0;
}

.balance-icon.crypto {
    background: linear-gradient(135deg, #f7931a 0%, #ff9500 100%);
}

.balance-icon.card {
    background: linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%);
}

.balance-info {
    flex: 1;
}

.balance-label {
    font-size: 0.9rem;
    color: #64748b;
    font-weight: 500;
    margin-bottom: 0.25rem;
}

.balance-amount {
    font-size: 1.75rem;
    font-weight: 700;
    color: #1e293b;
    margin-bottom: 0.25rem;
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
}

.balance-subtitle {
    font-size: 0.8rem;
    color: #94a3b8;
    font-weight: 400;
}

.balance-label {
    font-size: 0.9rem;
    color: var(--text-secondary);
    margin-bottom: 0.5rem;
    font-weight: 500;
}

.balance-amount {
    font-family: 'Courier New', 'Monaco', 'Menlo', monospace;
    font-size: 1.8rem;
    font-weight: 700;
    color: var(--text-primary);
    margin-bottom: 0.25rem;
    letter-spacing: 0.5px;
}

.balance-amount.available {
    color: var(--success-color);
}

.balance-amount.wallet {
    color: var(--primary-color);
}

.balance-amount.card {
    color: var(--warning-color);
}

/* Professional Banking Table Styles */
.transactions-section {
    background: var(--background-white);
    border-radius: 12px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.08);
    border: 1px solid var(--border-color);
    margin-bottom: 2rem;
    overflow: hidden;
}

.transactions-header {
    background: var(--background-light);
    padding: 1.5rem;
    border-bottom: 1px solid var(--border-color);
}

.transactions-header h3 {
    margin: 0;
    color: var(--text-primary);
    font-size: 1.25rem;
    font-weight: 600;
}

.transactions-table {
    width: 100%;
    border-collapse: collapse;
    font-size: 0.9rem;
}

.transactions-table th {
    background: var(--background-light);
    padding: 1rem;
    text-align: left;
    font-weight: 600;
    color: var(--text-primary);
    border-bottom: 2px solid var(--border-color);
    font-size: 0.85rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.transactions-table td {
    padding: 1rem;
    border-bottom: 1px solid var(--border-light);
    vertical-align: middle;
}

.transactions-table tbody tr:nth-child(even) {
    background: rgba(0,0,0,0.02);
}

.transactions-table tbody tr:hover {
    background: rgba(var(--primary-color-rgb), 0.05);
}

.transaction-number {
    font-weight: 600;
    color: var(--text-secondary);
    font-size: 0.8rem;
}

.transaction-description {
    font-weight: 500;
    color: var(--text-primary);
}

.transaction-type {
    padding: 0.25rem 0.75rem;
    border-radius: 20px;
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.transaction-type.admin-credit {
    background: rgba(34, 197, 94, 0.1);
    color: #16a34a;
}

.transaction-type.admin-debit {
    background: rgba(239, 68, 68, 0.1);
    color: #dc2626;
}

.transaction-type.user-credit {
    background: rgba(34, 197, 94, 0.1);
    color: #16a34a;
}

.transaction-type.user-debit {
    background: rgba(239, 68, 68, 0.1);
    color: #dc2626;
}

.transaction-type.user-transfer {
    background: rgba(59, 130, 246, 0.1);
    color: #2563eb;
}

.transaction-amount {
    font-weight: 700;
    font-family: 'Courier New', monospace;
}

.transaction-amount.credit {
    color: var(--success-color);
}

.transaction-amount.debit {
    color: var(--danger-color);
}

.transaction-balance {
    font-weight: 600;
    font-family: 'Courier New', monospace;
    color: var(--text-primary);
}

.transaction-status {
    padding: 0.25rem 0.75rem;
    border-radius: 20px;
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: uppercase;
}

.transaction-status.completed {
    background: rgba(34, 197, 94, 0.1);
    color: #16a34a;
}

.transaction-status.pending {
    background: rgba(251, 191, 36, 0.1);
    color: #d97706;
}

.transaction-status.failed {
    background: rgba(239, 68, 68, 0.1);
    color: #dc2626;
}

/* Quick Links Section */
.quick-links {
    background: var(--background-white);
    border-radius: 12px;
    padding: 1.5rem;
    box-shadow: 0 2px 10px rgba(0,0,0,0.08);
    border: 1px solid var(--border-color);
    margin-bottom: 2rem;
}

.quick-links h3 {
    margin-bottom: 1.5rem;
    color: var(--text-primary);
    font-size: 1.25rem;
    font-weight: 600;
}

.quick-links-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
}

.quick-link {
    display: flex;
    align-items: center;
    padding: 1rem;
    background: var(--background-light);
    border-radius: 8px;
    text-decoration: none;
    color: var(--text-primary);
    transition: all 0.2s ease;
    border: 1px solid var(--border-light);
}

.quick-link:hover {
    background: var(--primary-color);
    color: white;
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
    text-decoration: none;
}

.quick-link:hover i {
    color: white;
}

.quick-link i {
    font-size: 1.25rem;
    margin-right: 0.75rem;
    width: 20px;
    text-align: center;
    color: var(--primary-color);
}

.quick-link span {
    font-weight: 500;
}

/* Card Tabs */
.card-tabs-container {
    margin-bottom: 2rem;
}

.card-tabs {
    display: flex;
    gap: 0.5rem;
    margin-bottom: 1.5rem;
    border-bottom: 2px solid #e2e8f0;
    padding-bottom: 0.5rem;
}

.card-tab {
    background: #f8fafc;
    border: 1px solid #e2e8f0;
    border-radius: 8px 8px 0 0;
    padding: 0.75rem 1.5rem;
    font-size: 0.9rem;
    font-weight: 500;
    color: #64748b;
    cursor: pointer;
    transition: all 0.3s ease;
    border-bottom: none;
}

.card-tab:hover {
    background: #e2e8f0;
    color: #475569;
}

.card-tab.active {
    background: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
    box-shadow: 0 2px 8px rgba(var(--primary-color-rgb), 0.3);
}

.card-content-container {
    position: relative;
}

.card-content {
    display: none;
}

.card-content.active {
    display: block;
}

/* New Card Flip Container - Optimized for 14-inch screens */
.card-flip-container-new {
    perspective: 1000px;
    width: 100%;
    max-width: 320px; /* Reduced from 337px for better fit */
    height: 202px; /* Proportionally reduced from 213px */
    margin: 0 auto 20px;
    /* Maintain aspect ratio: 85.60mm x 53.98mm */
    aspect-ratio: 85.60 / 53.98;
}

/* Full Width Card Display */
.card-display-full {
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 1rem 0;
}

.card-flip-container-full {
    perspective: 1000px;
    width: 100%;
    max-width: 400px; /* Larger for full display */
    height: 252px; /* Proportionally larger */
    margin: 0 auto;
    /* Maintain aspect ratio: 85.60mm x 53.98mm */
    aspect-ratio: 85.60 / 53.98;
}

/* KYC Documents Section */
.kyc-documents-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 1.5rem;
    margin-top: 1rem;
}

.kyc-document-item {
    border: 1px solid #e5e7eb;
    border-radius: 12px;
    padding: 1.25rem;
    text-align: center;
    transition: all 0.3s ease;
    background: #ffffff;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    position: relative;
    overflow: hidden;
}

.kyc-document-item:hover {
    border-color: #206bc4;
    background: #f8fafc;
    transform: translateY(-3px);
    box-shadow: 0 4px 12px rgba(32, 107, 196, 0.15);
}



.document-preview {
    position: relative;
    margin-bottom: 0.75rem;
}

.document-image {
    width: 100%;
    max-width: 250px;
    height: 180px;
    object-fit: cover;
    border-radius: 10px;
    cursor: pointer;
    transition: all 0.3s ease;
    border: 2px solid #e5e7eb;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.document-image:hover {
    transform: scale(1.08);
    border-color: #206bc4;
    box-shadow: 0 4px 8px rgba(32, 107, 196, 0.2);
}

.document-file-icon {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 100px;
    color: #6b7280;
}

.document-overlay {
    position: absolute;
    top: 5px;
    right: 5px;
}

.document-info {
    text-align: center;
    margin-top: 1rem;
    padding-top: 0.75rem;
    border-top: 1px solid #f3f4f6;
}

.document-type {
    font-weight: 600;
    color: #1f2937;
    font-size: 0.9rem;
    margin-bottom: 0.5rem;
    line-height: 1.3;
}

.document-date {
    font-size: 0.75rem;
    color: #6b7280;
    font-weight: 500;
}

/* Document Modal */
.document-modal {
    display: none;
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.8);
}

.document-modal-content {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    max-width: 90%;
    max-height: 90%;
}

.document-modal img {
    width: 100%;
    height: auto;
    border-radius: 8px;
}

.document-modal-close {
    position: absolute;
    top: 10px;
    right: 25px;
    color: white;
    font-size: 35px;
    font-weight: bold;
    cursor: pointer;
}

.document-modal-close:hover {
    color: #ccc;
}

.card-flip-inner {
    position: relative;
    width: 100%;
    height: 100%;
    text-align: center;
    transition: transform 0.6s;
    transform-style: preserve-3d;
    cursor: pointer;
}

.card-flip-inner.flipped {
    transform: rotateY(180deg);
}

.card-front,
.card-back {
    position: absolute;
    width: 100%;
    height: 100%;
    backface-visibility: hidden;
    background: linear-gradient(135deg, var(--primary-color, #206bc4) 0%, var(--primary-dark, #1a5490) 100%);
    border-radius: 12px; /* Slightly smaller radius for standard card look */
    padding: 16px; /* Reduced padding to prevent text cutoff */
    color: white;
    box-shadow: 0 8px 25px rgba(32, 107, 196, 0.3);
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
    overflow: hidden;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    box-sizing: border-box; /* Ensure padding is included in dimensions */
}

.card-back {
    transform: rotateY(180deg);
}

.card-pattern {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, rgba(255,255,255,0.1) 0%, transparent 50%);
    border-radius: 16px;
    pointer-events: none;
}

.card-header-content {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 0.75rem;
    min-height: 24px; /* Ensure space for logo */
}

.card-brand-logo {
    height: 24px;
    filter: brightness(0) invert(1);
}

.status-badge {
    background: rgba(255, 255, 255, 0.2);
    color: white;
    padding: 0.2rem 0.6rem;
    border-radius: 8px;
    font-size: 0.7rem;
    font-weight: 600;
    backdrop-filter: blur(10px);
}

.card-chip {
    margin-bottom: 1rem;
    display: flex;
    justify-content: flex-start; /* Position chip on the left */
}

.chip-image {
    height: 28px;
    width: 36px;
}

.card-number {
    font-family: 'Monaco', 'Menlo', monospace;
    font-size: 1rem;
    font-weight: 600;
    letter-spacing: 2px;
    margin-bottom: 1rem;
    text-align: center;
    line-height: 1.2;
}

.card-footer-content {
    display: flex;
    justify-content: space-between;
    align-items: flex-end;
    margin-top: auto; /* Push to bottom */
}

.card-holder,
.card-expiry {
    text-align: left;
    flex: 1;
}

.card-expiry {
    text-align: right;
}

.card-label {
    font-size: 0.6rem;
    opacity: 0.8;
    margin-bottom: 0.2rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    line-height: 1;
}

.card-value {
    font-size: 0.8rem;
    font-weight: 600;
    text-transform: uppercase;
    line-height: 1;
}

.card-bank-name {
    position: absolute;
    bottom: 8px;
    left: 15px;
    font-size: 0.7rem;
    font-weight: 600;
    opacity: 0.9;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    line-height: 1;
}

/* Enhanced Virtual Card Logo Styling */
.card-brand-section {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
}

.card-brand-logo {
    height: 28px;
    width: auto;
    filter: brightness(0) invert(1); /* Make logos white for dark card backgrounds */
}

.company-logo-main {
    height: 35px; /* Increased size */
    width: auto;
    max-width: 90px; /* Increased max width */
    filter: brightness(0) invert(1); /* Make company logo white for dark card backgrounds */
    opacity: 0.9;
}

/* Chip positioning on right side */
.card-chip-right {
    position: absolute;
    top: 20px;
    right: 20px;
    z-index: 2;
}

.card-chip-right .chip-image {
    height: 35px;
    width: auto;
}

/* Enhanced Virtual Card Design - Fixed Alignment */
.card-flip-container-full {
    width: 350px;
    height: 220px;
    max-width: 100%;
    margin: 0 auto;
    perspective: 1000px;
}

.virtual-card-container {
    perspective: 1000px;
}

.card-front, .card-back {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, var(--primary-color, #206bc4) 0%, var(--primary-dark, #1a5490) 100%);
    border-radius: 20px;
    padding: 25px;
    box-shadow: 0 15px 35px rgba(32, 107, 196, 0.3);
    transition: all 0.3s ease;
    overflow: hidden;
    backface-visibility: hidden;
    transform-style: preserve-3d;
    box-sizing: border-box;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
}

.card-back {
    transform: rotateY(180deg);
}

.card-front:hover {
    transform: translateY(-8px);
    box-shadow: 0 25px 50px rgba(32, 107, 196, 0.4);
}

/* Centered Card Number - Fixed Single Line */
.card-number-centered {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    font-family: 'Courier New', monospace;
    font-size: 1.3rem;
    font-weight: 600;
    letter-spacing: 3px;
    text-align: center;
    color: white;
    z-index: 2;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    width: auto;
    max-width: 90%;
}

/* Enhanced Card Background Pattern */
.card-pattern {
    position: absolute;
    top: -50px;
    right: -50px;
    width: 200px;
    height: 200px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 50%;
    z-index: 1;
}

.card-pattern::before {
    content: '';
    position: absolute;
    bottom: -100px;
    left: -100px;
    width: 150px;
    height: 150px;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 50%;
}

/* Card Header Content */
.card-header-content {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    position: relative;
    z-index: 2;
    margin-bottom: 20px;
}

/* Card Status */
.card-status {
    position: absolute;
    top: 0;
    right: 0;
}

.status-badge {
    background: rgba(255, 255, 255, 0.2);
    padding: 4px 12px;
    border-radius: 20px;
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    color: white;
}

.flip-indicator {
    position: absolute;
    bottom: 6px;
    right: 10px;
    font-size: 0.6rem;
    opacity: 0.6;
    line-height: 1;
}

/* Card Back Specific Styles */
.magnetic-strip {
    background: #000;
    height: 40px;
    width: 100%;
    margin: 20px 0;
}

.card-back-content {
    padding: 0;
    flex: 1;
    display: flex;
    flex-direction: column;
}

.signature-strip {
    background: #fff;
    height: 35px;
    margin-bottom: 15px;
    border-radius: 4px;
    position: relative;
    display: flex;
    align-items: center;
    justify-content: flex-end;
    padding-right: 10px;
}

.cvv-box {
    background: #000;
    color: #fff;
    padding: 4px 12px;
    border-radius: 3px;
    font-size: 0.9rem;
    font-weight: bold;
    font-family: monospace;
}

.card-back-details {
    font-size: 0.8rem;
    line-height: 1.6;
    margin-bottom: auto;
}

.card-back-details div {
    margin-bottom: 0.5rem;
}

.bank-info {
    font-size: 0.7rem;
    opacity: 0.8;
    margin-top: auto;
}

/* Card Flip Container - Legacy Support */
.card-flip-container {
    perspective: 1000px;
    width: 320px;
    height: 203px;
    margin: 0 auto 20px;
}

/* Responsive Design for 14-inch screens and smaller */
@media (max-width: 1366px) {
    /* Optimize layout for 14-inch screens (1366x768) */
    .row-cards {
        margin-left: -0.5rem;
        margin-right: -0.5rem;
    }

    .row-cards > .col-lg-7,
    .row-cards > .col-lg-5 {
        padding-left: 0.5rem;
        padding-right: 0.5rem;
    }

    /* Adjust card flip container for better fit */
    .card-flip-container-new {
        max-width: 300px;
        height: 190px;
    }

    /* Optimize card details section */
    .card-details-section {
        padding: 1rem;
    }

    .detail-item {
        margin-bottom: 0.75rem;
    }

    /* Reduce crypto accounts card padding */
    .card-body {
        padding: 1rem;
    }

    /* Optimize balance overview for smaller screens */
    .balance-overview-new {
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: 1rem;
    }

    /* Optimize KYC documents grid for 14-inch screens */
    .kyc-documents-grid {
        grid-template-columns: repeat(auto-fit, minmax(240px, 1fr));
        gap: 1rem;
    }

    .document-image {
        max-width: 220px;
        height: 160px;
    }

    /* Optimize virtual card for 14-inch screens */
    .virtual-card {
        width: 300px;
        height: 190px;
        padding: 18px;
    }

    .card-flip-container-full {
        width: 300px;
        height: 190px;
        max-width: 100%;
    }

    /* Optimize crypto section for 14-inch screens */
    .crypto-section .list-group-item {
        padding: 0.5rem 0.75rem;
    }

    .crypto-wallet-address {
        font-size: 0.75rem;
        max-width: 150px;
    }

    .crypto-balance {
        font-size: 0.85rem;
    }
}

/* Specific optimizations for 14-inch screens (1366px and below) */
@media (max-width: 1400px) and (min-width: 1200px) {
    /* Optimize for 14-inch screens specifically */
    .container-xl {
        max-width: 1320px;
        padding-left: 1rem;
        padding-right: 1rem;
    }

    /* Ensure virtual cards fit well */
    .card-flip-container-full {
        width: 320px;
        height: 200px;
        max-width: 100%;
    }

    /* Optimize KYC grid for this specific range */
    .kyc-documents-grid {
        grid-template-columns: repeat(auto-fit, minmax(260px, 1fr));
        gap: 1.2rem;
    }
}

@media (max-width: 1200px) {
    /* Stack cards vertically on smaller screens */
    .row-cards .col-lg-7,
    .row-cards .col-lg-5 {
        flex: 0 0 100%;
        max-width: 100%;
    }

    .row-cards .col-lg-5 {
        margin-top: 1.5rem;
    }
}

@media (max-width: 768px) {
    .virtual-card {
        width: 280px;
        height: 178px;
        padding: 16px;
    }

    .card-flip-container {
        width: 280px;
        height: 178px;
    }

    .card-number {
        font-size: 16px;
        letter-spacing: 2px;
    }

    .balance-overview {
        grid-template-columns: 1fr;
    }

    .balance-overview-new {
        grid-template-columns: 1fr;
    }

    .transactions-table {
        font-size: 0.8rem;
    }

    .transactions-table th,
    .transactions-table td {
        padding: 0.75rem 0.5rem;
    }

    .quick-links-grid {
        grid-template-columns: 1fr;
    }
}

@media (max-width: 480px) {
    .virtual-card {
        width: 100%;
        max-width: 280px;
        height: 160px;
    }

    .card-flip-container {
        width: 100%;
        max-width: 280px;
        height: 160px;
    }
    
    .transactions-table th:nth-child(n+4),
    .transactions-table td:nth-child(n+4) {
        display: none;
    }
}

/* Card Details Section - Optimized for 14-inch screens */
.card-details-section {
    padding: 1.25rem;
    background: #f8f9fa;
    border-radius: 12px;
    border: 1px solid #e9ecef;
    height: 100%;
}

.card-details-section h5 {
    color: var(--primary-color);
    font-weight: 600;
    margin-bottom: 1rem;
    padding-bottom: 0.5rem;
    border-bottom: 2px solid var(--primary-color);
    font-size: 1.1rem;
}

.detail-item {
    margin-bottom: 0.75rem;
}

.detail-label {
    font-size: 0.7rem;
    font-weight: 600;
    color: #6c757d;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    margin-bottom: 0.2rem;
    display: block;
}

.detail-value {
    font-size: 0.85rem;
    font-weight: 500;
    color: #212529;
    padding: 0.4rem 0.6rem;
    background: white;
    border-radius: 6px;
    border: 1px solid #dee2e6;
    line-height: 1.2;
}

.detail-value {
    font-family: 'Monaco', 'Menlo', 'Courier New', monospace;
    letter-spacing: 0.5px;
}

.detail-value.font-monospace {
    letter-spacing: 1px;
}

.detail-value.text-success {
    color: #198754 !important;
    font-weight: 600;
}

.detail-value.text-primary {
    color: var(--primary-color) !important;
    font-weight: 600;
}

/* Card and Details Layout */
.card-flip-container {
    max-width: 100% !important;
    width: 100% !important;
}

@media (max-width: 768px) {
    .card-flip-container {
        width: 240px !important;
        height: 150px !important;
        margin: 0 auto 15px !important;
    }

    .card-details-section {
        margin-top: 1rem;
    }
}

@media (max-width: 480px) {
    .card-flip-container {
        width: 200px !important;
        height: 125px !important;
    }
}

/* Service Cards */
.service-card {
    background: white;
    border-radius: 16px;
    padding: 2rem;
    box-shadow: 0 4px 20px rgba(0,0,0,0.08);
    border: 1px solid #e9ecef;
    transition: all 0.3s ease;
    height: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
    text-align: center;
}

.service-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 30px rgba(0,0,0,0.12);
    border-color: var(--primary-color);
}

.service-icon {
    width: 80px;
    height: 80px;
    background: linear-gradient(135deg, var(--primary-color) 0%, #7c3aed 100%);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 1.5rem;
    box-shadow: 0 4px 15px rgba(79, 70, 229, 0.3);
}

.service-icon i {
    font-size: 2rem;
    color: white;
}

.service-content h5 {
    color: var(--text-primary);
    font-weight: 600;
    margin-bottom: 1rem;
    font-size: 1.25rem;
}

.service-content p {
    margin-bottom: 1.5rem;
    line-height: 1.6;
}

.service-card .btn {
    margin-top: auto;
}

/* Transaction Receipt Modal Styles */
.receipt-modal {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 9999;
    background: rgba(0, 0, 0, 0.5);
    backdrop-filter: blur(5px);
}

.modal-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    cursor: pointer;
}

.receipt-container {
    position: relative;
    max-width: 600px;
    margin: 2rem auto;
    background: white;
    border-radius: 12px;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
    overflow: hidden;
    max-height: 90vh;
    display: flex;
    flex-direction: column;
}

.receipt-header {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
    color: white;
    padding: 1.5rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
    position: relative;
}

.bank-logo {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    font-weight: 600;
    font-size: 1.1rem;
}

.bank-logo i {
    font-size: 1.5rem;
}

.receipt-title {
    position: absolute;
    left: 50%;
    transform: translateX(-50%);
    font-weight: 700;
    font-size: 1rem;
    letter-spacing: 1px;
}

.receipt-close {
    background: rgba(255, 255, 255, 0.2);
    border: none;
    color: white;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s ease;
}

.receipt-close:hover {
    background: rgba(255, 255, 255, 0.3);
    transform: scale(1.1);
}

.receipt-body {
    flex: 1;
    overflow-y: auto;
    padding: 0;
}

.receipt-content {
    padding: 2rem;
    font-family: 'Courier New', monospace;
    line-height: 1.6;
}

.receipt-footer {
    border-top: 1px solid #e9ecef;
    padding: 1rem 1.5rem;
    background: #f8f9fa;
}

.receipt-actions {
    display: flex;
    gap: 1rem;
    justify-content: flex-end;
}

.receipt-actions .btn {
    padding: 0.5rem 1rem;
    border-radius: 6px;
    font-size: 0.9rem;
    font-weight: 500;
    border: none;
    cursor: pointer;
    transition: all 0.3s ease;
}

.receipt-actions .btn-secondary {
    background: #6c757d;
    color: white;
}

.receipt-actions .btn-secondary:hover {
    background: #5a6268;
}

.receipt-actions .btn-primary {
    background: var(--primary-color);
    color: white;
}

.receipt-actions .btn-primary:hover {
    background: var(--primary-dark);
}

/* Receipt Content Styles */
.receipt-section {
    margin-bottom: 1.5rem;
    padding-bottom: 1rem;
    border-bottom: 1px dashed #dee2e6;
}

.receipt-section:last-child {
    border-bottom: none;
    margin-bottom: 0;
}

.receipt-section h6 {
    font-family: 'Inter', sans-serif;
    font-weight: 600;
    color: var(--primary-color);
    margin-bottom: 0.75rem;
    font-size: 0.9rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.receipt-row {
    display: flex;
    justify-content: space-between;
    margin-bottom: 0.5rem;
    font-size: 0.9rem;
}

.receipt-row:last-child {
    margin-bottom: 0;
}

.receipt-label {
    color: #6c757d;
    font-weight: 500;
}

.receipt-value {
    color: #212529;
    font-weight: 600;
    text-align: right;
}

.receipt-amount {
    font-size: 1.1rem;
    font-weight: 700;
}

.receipt-amount.credit {
    color: #28a745;
}

.receipt-amount.debit {
    color: #dc3545;
}

/* Cryptocurrency Accounts Styling */
.crypto-wallet-address {
    cursor: pointer;
    transition: all 0.2s ease;
    padding: 2px 4px;
    border-radius: 4px;
    display: inline-block;
    max-width: 100%;
    word-break: break-all;
}

.crypto-wallet-address:hover {
    background-color: #f8f9fa;
    color: #206bc4 !important;
    transform: scale(1.02);
}

.crypto-balance {
    font-family: 'Monaco', 'Menlo', monospace;
    font-size: 0.9rem;
    word-break: break-word;
}

/* Crypto section responsive improvements */
.crypto-section .list-group-item {
    padding: 0.75rem 1rem;
    border: none;
    border-bottom: 1px solid #e9ecef;
}

.crypto-section .list-group-item:last-child {
    border-bottom: none;
}

.crypto-section .d-flex {
    gap: 0.5rem;
    align-items: flex-start;
}

.crypto-section .text-end {
    min-width: 120px;
    flex-shrink: 0;
}

/* Recent Transactions Header Styling */
.transactions-header {
    margin-bottom: 1.5rem;
    padding-bottom: 1rem;
    border-bottom: 1px solid #e9ecef;
}

.transactions-actions {
    display: flex;
    gap: 0.5rem;
}

.transactions-actions .btn {
    font-size: 0.875rem;
    padding: 0.375rem 0.75rem;
    white-space: nowrap;
}

/* Responsive Design for Modal */
@media (max-width: 768px) {
    .receipt-container {
        margin: 1rem;
        max-height: 95vh;
    }

    /* Crypto section mobile optimizations */
    .crypto-section .d-flex {
        flex-direction: column;
        gap: 0.25rem;
    }

    .crypto-section .text-end {
        text-align: left !important;
        min-width: auto;
    }

    .crypto-wallet-address {
        font-size: 0.75rem;
        max-width: 200px;
    }

    /* Responsive transactions header */
    .transactions-header {
        flex-direction: column;
        align-items: flex-start !important;
        gap: 1rem;
    }

    .transactions-actions {
        width: 100%;
        justify-content: center;
    }

    .transactions-actions .btn {
        flex: 1;
        max-width: 150px;
    }
}

    .receipt-header {
        padding: 1rem;
    }

    .receipt-title {
        font-size: 0.9rem;
    }

    .receipt-content {
        padding: 1.5rem;
    }

    .receipt-actions {
        flex-direction: column;
    }

    .receipt-actions .btn {
        width: 100%;
    }
}
