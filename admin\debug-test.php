<?php
/**
 * Admin Dashboard Debug Test Page
 * This page will help identify what's causing the blank admin dashboard
 */

// Enable all error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);

echo "<!DOCTYPE html><html><head><title>Admin Debug Test</title></head><body>";
echo "<h1>🔍 Admin Dashboard Debug Test</h1>";

// Test 1: Basic PHP functionality
echo "<h2>1. Basic PHP Test</h2>";
echo "✅ PHP is working - Version: " . PHP_VERSION . "<br>";
echo "✅ Current time: " . date('Y-m-d H:i:s') . "<br>";

// Test 2: File path resolution
echo "<h2>2. File Path Test</h2>";
echo "Current directory: " . __DIR__ . "<br>";
echo "Parent directory: " . dirname(__DIR__) . "<br>";

$files_to_check = [
    '../config/config.php',
    '../config/database.php',
    '../config/dynamic-css.php',
    '../config/debug-system.php',
    'includes/admin-header.php'
];

foreach ($files_to_check as $file) {
    $full_path = __DIR__ . '/' . $file;
    if (file_exists($full_path)) {
        echo "✅ {$file} exists<br>";
    } else {
        echo "❌ {$file} NOT FOUND (looking for: {$full_path})<br>";
    }
}

// Test 3: Include files one by one
echo "<h2>3. File Include Test</h2>";

try {
    echo "Testing config.php...<br>";
    require_once '../config/config.php';
    echo "✅ config.php loaded successfully<br>";
} catch (Exception $e) {
    echo "❌ config.php error: " . $e->getMessage() . "<br>";
} catch (Error $e) {
    echo "❌ config.php fatal error: " . $e->getMessage() . "<br>";
}

try {
    echo "Testing database.php...<br>";
    require_once '../config/database.php';
    echo "✅ database.php loaded successfully<br>";
} catch (Exception $e) {
    echo "❌ database.php error: " . $e->getMessage() . "<br>";
} catch (Error $e) {
    echo "❌ database.php fatal error: " . $e->getMessage() . "<br>";
}

try {
    echo "Testing dynamic-css.php...<br>";
    require_once '../config/dynamic-css.php';
    echo "✅ dynamic-css.php loaded successfully<br>";
} catch (Exception $e) {
    echo "❌ dynamic-css.php error: " . $e->getMessage() . "<br>";
} catch (Error $e) {
    echo "❌ dynamic-css.php fatal error: " . $e->getMessage() . "<br>";
}

// Test 4: Database connection
echo "<h2>4. Database Connection Test</h2>";
try {
    $db = getDB();
    echo "✅ Database connection successful<br>";
    
    $result = $db->query("SELECT 1 as test");
    if ($result) {
        echo "✅ Database query test successful<br>";
    } else {
        echo "❌ Database query failed<br>";
    }
} catch (Exception $e) {
    echo "❌ Database error: " . $e->getMessage() . "<br>";
} catch (Error $e) {
    echo "❌ Database fatal error: " . $e->getMessage() . "<br>";
}

// Test 5: Dynamic CSS generation
echo "<h2>5. Dynamic CSS Test</h2>";
try {
    $css = generateDynamicCSS();
    echo "✅ Dynamic CSS generated (" . strlen($css) . " characters)<br>";
    echo "<details><summary>View Generated CSS</summary><pre>" . htmlspecialchars($css) . "</pre></details>";
} catch (Exception $e) {
    echo "❌ Dynamic CSS error: " . $e->getMessage() . "<br>";
} catch (Error $e) {
    echo "❌ Dynamic CSS fatal error: " . $e->getMessage() . "<br>";
}

// Test 6: Admin header include test
echo "<h2>6. Admin Header Include Test</h2>";
try {
    echo "Attempting to include admin header...<br>";
    ob_start();
    include 'includes/admin-header.php';
    $header_output = ob_get_clean();
    
    if (strlen($header_output) > 0) {
        echo "✅ Admin header included successfully (" . strlen($header_output) . " characters)<br>";
        echo "<details><summary>View Header Output</summary><pre>" . htmlspecialchars(substr($header_output, 0, 1000)) . "...</pre></details>";
    } else {
        echo "⚠️ Admin header included but produced no output<br>";
    }
} catch (Exception $e) {
    echo "❌ Admin header error: " . $e->getMessage() . "<br>";
    echo "File: " . $e->getFile() . "<br>";
    echo "Line: " . $e->getLine() . "<br>";
} catch (Error $e) {
    echo "❌ Admin header fatal error: " . $e->getMessage() . "<br>";
    echo "File: " . $e->getFile() . "<br>";
    echo "Line: " . $e->getLine() . "<br>";
}

// Test 7: Memory and system info
echo "<h2>7. System Information</h2>";
echo "Memory usage: " . formatBytes(memory_get_usage(true)) . "<br>";
echo "Peak memory: " . formatBytes(memory_get_peak_usage(true)) . "<br>";
echo "Memory limit: " . ini_get('memory_limit') . "<br>";
echo "Max execution time: " . ini_get('max_execution_time') . "<br>";

function formatBytes($bytes, $precision = 2) {
    $units = array('B', 'KB', 'MB', 'GB', 'TB');
    for ($i = 0; $bytes > 1024 && $i < count($units) - 1; $i++) {
        $bytes /= 1024;
    }
    return round($bytes, $precision) . ' ' . $units[$i];
}

echo "<h2>✅ Debug Test Complete</h2>";
echo "<p>Check the results above to identify any issues. All ✅ items are working correctly.</p>";
echo "</body></html>";
?>
