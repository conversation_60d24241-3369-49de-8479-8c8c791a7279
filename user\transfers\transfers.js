/**
 * Transfer Page JavaScript
 * Handles money transfer functionality with OTP verification
 */

// Global variables
let currentTransferType = null;
let transferData = {};
let otpModal = null;
let successModal = null;

// Initialize page when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    console.log('💰 Transfer page loaded');
    initializeTransferPage();
});

/**
 * Initialize transfer page functionality
 */
function initializeTransferPage() {
    // Initialize Bootstrap components
    initializeBootstrapComponents();
    
    // Setup form validation and events
    setupFormEvents();
    
    // Setup amount calculation
    setupAmountCalculation();
    
    // Setup account validation
    setupAccountValidation();
    
    console.log('✅ Transfer page initialized');
}

/**
 * Initialize Bootstrap components
 */
function initializeBootstrapComponents() {
    // Initialize modals (with fallback)
    if (typeof bootstrap !== 'undefined') {
        otpModal = new bootstrap.Modal(document.getElementById('otpModal'));
        successModal = new bootstrap.Modal(document.getElementById('successModal'));

        // Initialize tooltips
        var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
        var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
            return new bootstrap.Tooltip(tooltipTriggerEl);
        });
    } else {
        console.warn('Bootstrap not loaded, using fallback modal functions');
        // Create fallback modal objects
        otpModal = {
            show: function() { showModalFallback('otpModal'); },
            hide: function() { hideModalFallback('otpModal'); }
        };
        successModal = {
            show: function() { showModalFallback('successModal'); },
            hide: function() { hideModalFallback('successModal'); }
        };
    }
}

/**
 * Fallback modal show function
 */
function showModalFallback(modalId) {
    const modal = document.getElementById(modalId);
    if (modal) {
        modal.style.display = 'block';
        modal.classList.add('show');
        document.body.classList.add('modal-open');

        // Create backdrop
        const backdrop = document.createElement('div');
        backdrop.className = 'modal-backdrop fade show';
        backdrop.id = modalId + '-backdrop';
        document.body.appendChild(backdrop);
    }
}

/**
 * Fallback modal hide function
 */
function hideModalFallback(modalId) {
    const modal = document.getElementById(modalId);
    const backdrop = document.getElementById(modalId + '-backdrop');

    if (modal) {
        modal.style.display = 'none';
        modal.classList.remove('show');
    }

    if (backdrop) {
        backdrop.remove();
    }

    document.body.classList.remove('modal-open');
}

/**
 * Select transfer type and show form
 */
function selectTransferType(type) {
    currentTransferType = type;
    
    // Update UI
    document.querySelectorAll('.transfer-type-card').forEach(card => {
        card.classList.remove('selected');
    });
    
    document.querySelector(`[data-type="${type}"]`).classList.add('selected');
    
    // Show form section
    document.getElementById('transferFormSection').style.display = 'block';
    document.getElementById('transferType').value = type;
    
    // Update form title and fields based on type
    updateFormForTransferType(type);
    
    // Scroll to form
    document.getElementById('transferFormSection').scrollIntoView({ 
        behavior: 'smooth',
        block: 'start'
    });
    
    console.log(`📋 Selected transfer type: ${type}`);
}

/**
 * Update form fields based on transfer type
 */
function updateFormForTransferType(type) {
    const formTitle = document.getElementById('transferFormTitle');
    const localBankFields = document.getElementById('localBankFields');
    
    switch (type) {
        case 'inter-bank':
            formTitle.innerHTML = '<i class="fas fa-university me-2"></i>Inter-Bank Transfer Details';
            localBankFields.style.display = 'none';
            // Remove required attributes from local bank fields
            setFieldsRequired(localBankFields, false);
            break;
            
        case 'local-bank':
            formTitle.innerHTML = '<i class="fas fa-building me-2"></i>Local Bank Transfer Details';
            localBankFields.style.display = 'block';
            // Add required attributes to local bank fields
            setFieldsRequired(localBankFields, true);
            break;
            
        default:
            formTitle.innerHTML = '<i class="fas fa-edit me-2"></i>Transfer Details';
            localBankFields.style.display = 'none';
            setFieldsRequired(localBankFields, false);
    }
}

/**
 * Set required attribute for fields in a container
 */
function setFieldsRequired(container, required) {
    const requiredFields = container.querySelectorAll('input[name="beneficiary_bank"]');
    requiredFields.forEach(field => {
        if (required) {
            field.setAttribute('required', 'required');
        } else {
            field.removeAttribute('required');
        }
    });
}

/**
 * Reset transfer form and go back to type selection
 */
function resetTransferForm() {
    // Hide form section
    document.getElementById('transferFormSection').style.display = 'none';
    
    // Clear form
    document.getElementById('transferForm').reset();
    
    // Clear selection
    document.querySelectorAll('.transfer-type-card').forEach(card => {
        card.classList.remove('selected');
    });
    
    currentTransferType = null;
    transferData = {};
    
    // Scroll to top
    document.querySelector('.transfer-types-section').scrollIntoView({ 
        behavior: 'smooth',
        block: 'start'
    });
    
    console.log('🔄 Transfer form reset');
}

/**
 * Quick transfer to beneficiary (from always-visible section)
 */
function quickTransferToBeneficiary(beneficiary) {
    console.log('🚀 Quick transfer to beneficiary:', beneficiary);

    // Determine transfer type based on whether it's internal
    const transferType = beneficiary.internal_user_id ? 'inter-bank' : 'local-bank';

    // Select the transfer type and show form
    selectTransferType(transferType);

    // Wait for form to be visible, then populate beneficiary data
    setTimeout(() => {
        selectBeneficiary(beneficiary);
    }, 300);
}

/**
 * Select beneficiary from quick selection
 */
function selectBeneficiary(beneficiary) {
    console.log('👤 Selected beneficiary:', beneficiary);
    
    // Fill form fields
    document.getElementById('beneficiaryAccount').value = beneficiary.account_number;
    document.getElementById('beneficiaryName').value = beneficiary.name;
    
    if (beneficiary.bank_name) {
        document.getElementById('beneficiaryBank').value = beneficiary.bank_name;
    }
    
    if (beneficiary.bank_code) {
        document.getElementById('routingCode').value = beneficiary.bank_code;
    }
    
    // If it's an internal user and we're not in inter-bank mode, switch to inter-bank
    if (beneficiary.internal_user_id && currentTransferType !== 'inter-bank') {
        selectTransferType('inter-bank');
        
        // Re-fill the fields after type change
        setTimeout(() => {
            document.getElementById('beneficiaryAccount').value = beneficiary.account_number;
            document.getElementById('beneficiaryName').value = beneficiary.name;
        }, 100);
    }
    
    // Highlight selected beneficiary
    document.querySelectorAll('.beneficiary-quick-option').forEach(option => {
        option.classList.remove('selected');
    });
    
    // Find and highlight the selected option
    const selectedOption = Array.from(document.querySelectorAll('.beneficiary-quick-option'))
        .find(option => option.textContent.includes(beneficiary.account_number.slice(-4)));
    
    if (selectedOption) {
        selectedOption.classList.add('selected');
    }
}

/**
 * Show all beneficiaries modal
 */
function showAllBeneficiaries() {
    // This would open a modal with all beneficiaries
    // For now, redirect to beneficiaries page
    window.open('../beneficiaries/', '_blank');
}

/**
 * Setup form events
 */
function setupFormEvents() {
    const form = document.getElementById('transferForm');
    if (!form) return;
    
    form.addEventListener('submit', function(e) {
        e.preventDefault();
        handleTransferSubmit();
    });
    
    // Setup real-time validation
    const requiredFields = form.querySelectorAll('input[required]');
    requiredFields.forEach(field => {
        field.addEventListener('blur', validateField);
        field.addEventListener('input', clearFieldError);
    });
}

/**
 * Setup amount calculation and validation
 */
function setupAmountCalculation() {
    const amountInput = document.getElementById('transferAmount');
    if (!amountInput) return;
    
    amountInput.addEventListener('input', function() {
        updateTransferSummary();
        validateAmount();
    });
}

/**
 * Update transfer summary
 */
function updateTransferSummary() {
    const amount = parseFloat(document.getElementById('transferAmount').value) || 0;
    const currency = document.querySelector('.input-group-text').textContent;
    
    // Calculate fee (free for inter-bank, small fee for local bank)
    let fee = 0;
    if (currentTransferType === 'local-bank' && amount > 0) {
        fee = Math.max(2.50, amount * 0.001); // $2.50 or 0.1%, whichever is higher
    }
    
    const total = amount + fee;
    
    // Update summary display
    document.getElementById('summaryAmount').textContent = amount > 0 ? 
        `${currency} ${amount.toFixed(2)}` : '-';
    
    document.getElementById('summaryFee').textContent = fee > 0 ? 
        `${currency} ${fee.toFixed(2)}` : 'Free';
    
    document.getElementById('summaryTotal').textContent = total > 0 ? 
        `${currency} ${total.toFixed(2)}` : '-';
}

/**
 * Validate amount
 */
function validateAmount() {
    const amountInput = document.getElementById('transferAmount');
    const amount = parseFloat(amountInput.value) || 0;
    
    // Get available balance based on selected source account
    const sourceAccount = document.querySelector('input[name="source_account"]:checked').value;
    let availableBalance = 0;
    
    if (sourceAccount === 'main') {
        // Extract balance from the label (this is a simplified approach)
        const balanceText = document.querySelector('label[for="source_main"] .balance-amount').textContent;
        availableBalance = parseFloat(balanceText.replace(/[^0-9.]/g, ''));
    } else if (sourceAccount === 'virtual_card') {
        const balanceText = document.querySelector('label[for="source_virtual"] .balance-amount').textContent;
        availableBalance = parseFloat(balanceText.replace(/[^0-9.]/g, ''));
    }
    
    clearFieldError(amountInput);
    
    if (amount <= 0) {
        showFieldError(amountInput, 'Amount must be greater than 0');
        return false;
    }
    
    if (amount < 1) {
        showFieldError(amountInput, 'Minimum transfer amount is $1.00');
        return false;
    }
    
    if (amount > availableBalance) {
        showFieldError(amountInput, 'Amount exceeds available balance');
        return false;
    }
    
    return true;
}

/**
 * Show account validation loading state
 */
function showAccountValidationLoading() {
    const feedback = getOrCreateAccountFeedback();
    feedback.className = 'form-text text-info';
    feedback.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>Validating account...';
}

/**
 * Show account validation success
 */
function showAccountValidationSuccess(message, subtitle) {
    const feedback = getOrCreateAccountFeedback();
    feedback.className = 'form-text text-success';
    feedback.innerHTML = `<i class="fas fa-check-circle me-1"></i>${message}<br><small>${subtitle}</small>`;
}

/**
 * Show account validation info
 */
function showAccountValidationInfo(message, subtitle) {
    const feedback = getOrCreateAccountFeedback();
    feedback.className = 'form-text text-muted';
    feedback.innerHTML = `<i class="fas fa-info-circle me-1"></i>${message}<br><small>${subtitle}</small>`;
}

/**
 * Clear account validation
 */
function clearAccountValidation() {
    const feedback = document.getElementById('account-validation-feedback');
    if (feedback) {
        feedback.remove();
    }
}

/**
 * Get or create account feedback element
 */
function getOrCreateAccountFeedback() {
    let feedback = document.getElementById('account-validation-feedback');
    if (!feedback) {
        feedback = document.createElement('div');
        feedback.id = 'account-validation-feedback';

        const input = document.getElementById('beneficiaryAccount');
        input.parentNode.appendChild(feedback);
    }
    return feedback;
}

/**
 * Show suggestion to switch to inter-bank transfer
 */
function showSwitchToInterBankSuggestion() {
    const suggestion = document.createElement('div');
    suggestion.className = 'alert alert-info alert-dismissible fade show mt-2';
    suggestion.innerHTML = `
        <i class="fas fa-lightbulb me-2"></i>
        <strong>Suggestion:</strong> This is an internal user. Switch to Inter-Bank Transfer for instant, free transfers.
        <button type="button" class="btn btn-sm btn-primary ms-2" onclick="selectTransferType('inter-bank')">
            Switch to Inter-Bank
        </button>
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;

    const formSection = document.querySelector('.form-section');
    formSection.appendChild(suggestion);

    // Auto-remove after 10 seconds
    setTimeout(() => {
        if (suggestion.parentNode) {
            suggestion.remove();
        }
    }, 10000);
}

/**
 * Handle transfer form submission
 */
function handleTransferSubmit() {
    console.log('📤 Processing transfer submission...');

    // Validate form
    if (!validateTransferForm()) {
        console.log('❌ Form validation failed');
        return;
    }

    // Collect transfer data
    collectTransferData();

    // Check if OTP is required for this transfer type
    if (currentTransferType === 'local-bank') {
        // Show OTP modal for local bank transfers
        showOTPModal();
    } else {
        // Process inter-bank transfer directly (no OTP required)
        processTransfer();
    }
}

/**
 * Validate transfer form
 */
function validateTransferForm() {
    const form = document.getElementById('transferForm');
    let isValid = true;

    // Clear previous errors
    clearAllFormErrors();

    // Validate required fields
    const requiredFields = form.querySelectorAll('input[required], select[required]');
    requiredFields.forEach(field => {
        if (!field.value.trim()) {
            showFieldError(field, 'This field is required');
            isValid = false;
        }
    });

    // Validate amount
    if (!validateAmount()) {
        isValid = false;
    }

    // Validate account number format
    const accountNumber = document.getElementById('beneficiaryAccount').value.trim();
    if (accountNumber && !/^[0-9]{8,20}$/.test(accountNumber)) {
        showFieldError(document.getElementById('beneficiaryAccount'), 'Account number must be 8-20 digits');
        isValid = false;
    }

    return isValid;
}

/**
 * Collect transfer data from form
 */
function collectTransferData() {
    const form = document.getElementById('transferForm');
    const formData = new FormData(form);

    transferData = {
        transfer_type: currentTransferType,
        source_account: formData.get('source_account'),
        beneficiary_account: formData.get('beneficiary_account'),
        beneficiary_name: formData.get('beneficiary_name'),
        beneficiary_bank: formData.get('beneficiary_bank') || '',
        routing_code: formData.get('routing_code') || '',
        account_type: formData.get('account_type') || '',
        amount: parseFloat(formData.get('amount')),
        narration: formData.get('narration') || 'Money Transfer',
        currency: document.querySelector('.input-group-text').textContent
    };

    console.log('📋 Transfer data collected:', transferData);
}

/**
 * Show OTP verification modal
 */
function showOTPModal() {
    console.log('🔐 Showing OTP modal...');

    // Generate and send OTP
    generateOTP()
        .then(() => {
            otpModal.show();

            // Focus on OTP input
            setTimeout(() => {
                document.getElementById('otpCode').focus();
            }, 300);
        })
        .catch(error => {
            console.error('OTP generation failed:', error);
            showErrorMessage('Failed to generate OTP. Please try again.');
        });
}

/**
 * Generate OTP for transfer verification
 */
function generateOTP() {
    return fetch('generate-transfer-otp.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            transfer_data: transferData
        })
    })
    .then(response => {
        // Check if response is ok and has content
        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }

        return response.text().then(text => {
            console.log('🔍 Raw OTP generation response:', text);

            if (!text.trim()) {
                throw new Error('Empty response from server');
            }

            try {
                return JSON.parse(text);
            } catch (e) {
                console.error('Invalid JSON response:', text);
                throw new Error('Invalid response format from server');
            }
        });
    })
    .then(data => {
        if (!data.success) {
            throw new Error(data.message || 'Failed to generate OTP');
        }
        console.log('✅ OTP generated successfully');
        return data;
    });
}

/**
 * Resend OTP
 */
function resendOTP() {
    const resendBtn = document.querySelector('.otp-actions .btn-outline-secondary');
    const originalText = resendBtn.innerHTML;

    resendBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>Sending...';
    resendBtn.disabled = true;

    generateOTP()
        .then(() => {
            showSuccessMessage('New verification code sent to your email');

            // Clear OTP input
            document.getElementById('otpCode').value = '';
            document.getElementById('otpCode').focus();
        })
        .catch(error => {
            console.error('OTP resend failed:', error);
            showErrorMessage('Failed to resend OTP. Please try again.');
        })
        .finally(() => {
            resendBtn.innerHTML = originalText;
            resendBtn.disabled = false;
        });
}

/**
 * Verify OTP and process transfer
 */
function verifyOTPAndTransfer() {
    const otpCode = document.getElementById('otpCode').value.trim();

    if (!otpCode || otpCode.length !== 6) {
        showFieldError(document.getElementById('otpCode'), 'Please enter a valid 6-digit code');
        return;
    }

    const verifyBtn = document.querySelector('.otp-actions .btn-primary');
    const originalText = verifyBtn.innerHTML;

    verifyBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>Verifying...';
    verifyBtn.disabled = true;

    // Verify OTP and process transfer
    fetch('process-transfer.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            ...transferData,
            otp_code: otpCode
        })
    })
    .then(response => {
        // Check if response is ok and has content (same as processTransfer)
        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }

        return response.text().then(text => {
            console.log('🔍 Raw OTP verification response:', text);

            if (!text.trim()) {
                throw new Error('Empty response from server');
            }

            try {
                return JSON.parse(text);
            } catch (e) {
                console.error('Invalid JSON response:', text);
                throw new Error('Invalid response format from server');
            }
        });
    })
    .then(data => {
        if (data.success) {
            // Hide OTP modal
            otpModal.hide();

            // Show success modal
            showTransferSuccess(data);
        } else {
            throw new Error(data.message || 'Transfer failed');
        }
    })
    .catch(error => {
        console.error('Transfer failed:', error);
        showFieldError(document.getElementById('otpCode'), error.message);
    })
    .finally(() => {
        verifyBtn.innerHTML = originalText;
        verifyBtn.disabled = false;
    });
}

/**
 * Process transfer (for inter-bank transfers without OTP)
 */
function processTransfer() {
    const submitBtn = document.getElementById('submitTransfer');
    const originalText = submitBtn.innerHTML;

    submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>Processing...';
    submitBtn.disabled = true;

    fetch('process-transfer.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify(transferData)
    })
    .then(response => {
        // Check if response is ok and has content
        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }

        // Check if response has content
        return response.text().then(text => {
            if (!text.trim()) {
                throw new Error('Empty response from server');
            }

            try {
                return JSON.parse(text);
            } catch (e) {
                console.error('Invalid JSON response:', text);
                throw new Error('Invalid response format from server');
            }
        });
    })
    .then(data => {
        if (data.success) {
            showTransferSuccess(data);
        } else {
            throw new Error(data.message || 'Transfer failed');
        }
    })
    .catch(error => {
        console.error('Transfer failed:', error);
        showErrorMessage(error.message);
    })
    .finally(() => {
        submitBtn.innerHTML = originalText;
        submitBtn.disabled = false;
    });
}

/**
 * Show transfer success modal
 */
function showTransferSuccess(data) {
    console.log('🎉 Transfer successful:', data);

    // Update success message
    const message = `Your ${currentTransferType.replace('-', ' ')} transfer of ${transferData.currency} ${transferData.amount.toFixed(2)} to ${transferData.beneficiary_name} has been processed successfully.`;
    document.getElementById('successMessage').textContent = message;

    // Update receipt email status
    const emailStatus = document.getElementById('receiptEmailStatus');
    if (emailStatus) {
        if (data.receipt_email_sent) {
            emailStatus.innerHTML = '<i class="fas fa-check-circle text-success me-2"></i>Receipt email sent successfully';
            emailStatus.className = 'alert alert-success mb-3';
        } else {
            emailStatus.innerHTML = '<i class="fas fa-exclamation-triangle text-warning me-2"></i>Receipt email could not be sent';
            emailStatus.className = 'alert alert-warning mb-3';
        }
        emailStatus.style.display = 'block';
    }

    // Store receipt data for download
    window.transferReceiptData = data;

    // Show success modal
    successModal.show();

    // Reset form after success
    setTimeout(() => {
        resetTransferForm();
    }, 1000);
}

/**
 * Download transfer receipt as PDF
 */
function downloadReceipt() {
    if (!window.transferReceiptData) {
        showErrorMessage('Receipt data not available');
        return;
    }

    // Open PDF receipt in new window for printing/saving
    const receiptWindow = window.open('generate-receipt-pdf.php?id=' + window.transferReceiptData.transfer_id, '_blank');

    if (!receiptWindow) {
        showErrorMessage('Please allow popups to download receipt');
    }
}

/**
 * View transfer receipt (HTML version)
 */
function viewReceipt() {
    if (!window.transferReceiptData) {
        showErrorMessage('Receipt data not available');
        return;
    }

    // Open HTML receipt in new window
    const receiptWindow = window.open('transfer-receipt.php?id=' + window.transferReceiptData.transfer_id, '_blank');

    if (!receiptWindow) {
        showErrorMessage('Please allow popups to view receipt');
    }
}

/**
 * Close success modal and reset
 */
function closeSuccessModal() {
    successModal.hide();

    // Optionally redirect to transactions page
    // window.location.href = '../transactions/';
}

/**
 * Show field error
 */
function showFieldError(field, message) {
    field.classList.add('is-invalid');

    // Remove existing error
    const existingError = field.parentNode.querySelector('.invalid-feedback');
    if (existingError) {
        existingError.remove();
    }

    // Add new error
    const errorDiv = document.createElement('div');
    errorDiv.className = 'invalid-feedback';
    errorDiv.textContent = message;
    field.parentNode.appendChild(errorDiv);
}

/**
 * Clear field error
 */
function clearFieldError(field) {
    if (typeof field === 'object' && field.target) {
        field = field.target; // Handle event object
    }

    field.classList.remove('is-invalid');

    const errorDiv = field.parentNode.querySelector('.invalid-feedback');
    if (errorDiv) {
        errorDiv.remove();
    }
}

/**
 * Clear all form errors
 */
function clearAllFormErrors() {
    const form = document.getElementById('transferForm');
    if (!form) return;

    // Remove invalid classes
    form.querySelectorAll('.is-invalid').forEach(field => {
        field.classList.remove('is-invalid');
    });

    // Remove error messages
    form.querySelectorAll('.invalid-feedback').forEach(error => {
        error.remove();
    });
}

/**
 * Validate individual field
 */
function validateField(event) {
    const field = event.target;
    const value = field.value.trim();

    clearFieldError(field);

    if (field.hasAttribute('required') && !value) {
        showFieldError(field, 'This field is required');
        return false;
    }

    // Specific validations
    switch (field.name) {
        case 'beneficiary_account':
            if (value && !/^[0-9]{8,20}$/.test(value)) {
                showFieldError(field, 'Account number must be 8-20 digits');
                return false;
            }
            break;

        case 'amount':
            return validateAmount();

        case 'beneficiary_name':
            if (value && value.length < 2) {
                showFieldError(field, 'Name must be at least 2 characters');
                return false;
            }
            break;
    }

    return true;
}

/**
 * Show success message
 */
function showSuccessMessage(message) {
    const alertDiv = document.createElement('div');
    alertDiv.className = 'alert alert-success alert-dismissible fade show';
    alertDiv.innerHTML = `
        <i class="fas fa-check-circle me-2"></i>${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;

    const container = document.querySelector('.content-container');
    container.insertBefore(alertDiv, container.firstChild);

    // Auto dismiss after 5 seconds
    setTimeout(() => {
        if (alertDiv.parentNode) {
            alertDiv.remove();
        }
    }, 5000);
}

/**
 * Show error message
 */
function showErrorMessage(message) {
    const alertDiv = document.createElement('div');
    alertDiv.className = 'alert alert-danger alert-dismissible fade show';
    alertDiv.innerHTML = `
        <i class="fas fa-exclamation-circle me-2"></i>${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;

    const container = document.querySelector('.content-container');
    container.insertBefore(alertDiv, container.firstChild);

    // Auto dismiss after 8 seconds
    setTimeout(() => {
        if (alertDiv.parentNode) {
            alertDiv.remove();
        }
    }, 8000);
}

/**
 * Format currency for display
 */
function formatCurrency(amount, currency = 'USD') {
    return new Intl.NumberFormat('en-US', {
        style: 'currency',
        currency: currency
    }).format(amount);
}

/**
 * Debounce function for performance
 */
function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

/**
 * Setup account number validation
 */
function setupAccountValidation() {
    const accountInput = document.getElementById('beneficiaryAccount');
    if (!accountInput) return;
    
    let validationTimeout;
    
    accountInput.addEventListener('input', function() {
        clearTimeout(validationTimeout);
        const accountNumber = this.value.trim();
        
        clearAccountValidation();
        
        if (accountNumber.length >= 8) {
            validationTimeout = setTimeout(() => {
                validateAccountNumber(accountNumber);
            }, 500);
        }
    });
}

/**
 * Validate account number for internal users
 */
function validateAccountNumber(accountNumber) {
    showAccountValidationLoading();
    
    // Check if account exists in our system
    fetch('validate-account.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: `account_number=${encodeURIComponent(accountNumber)}`
    })
    .then(response => response.json())
    .then(data => {
        if (data.exists && data.is_internal) {
            showAccountValidationSuccess(`✅ Internal user: ${data.name}`, 'Inter-bank transfer available');
            
            // Auto-fill name if empty
            const nameField = document.getElementById('beneficiaryName');
            if (!nameField.value) {
                nameField.value = data.name;
            }
            
            // Suggest switching to inter-bank if not already
            if (currentTransferType !== 'inter-bank') {
                showSwitchToInterBankSuggestion();
            }
        } else {
            showAccountValidationInfo('Account format valid', 'External bank transfer');
        }
    })
    .catch(error => {
        console.error('Account validation error:', error);
        showAccountValidationInfo('Account format valid', 'Unable to verify account');
    });
}
