<?php
/**
 * Comprehensive Test Script for All Admin Fixes
 * Tests: 1) User status history fix, 2) Avatar styling, 3) OTP generation
 */

require_once '../config/config.php';
requireAdmin();

echo "<!DOCTYPE html>
<html>
<head>
    <title>Admin Fixes - Comprehensive Testing</title>
    <link href='https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css' rel='stylesheet'>
    <link href='https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css' rel='stylesheet'>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background-color: #f8f9fa; }
        .test-section { margin: 20px 0; padding: 20px; border: 1px solid #ddd; border-radius: 8px; background: white; }
        .success { background-color: #d4edda; border-color: #c3e6cb; color: #155724; }
        .error { background-color: #f8d7da; border-color: #f5c6cb; color: #721c24; }
        .info { background-color: #d1ecf1; border-color: #bee5eb; color: #0c5460; }
        .test-button { padding: 8px 16px; margin: 5px; border: none; border-radius: 6px; cursor: pointer; font-weight: 500; }
        .btn-primary { background: linear-gradient(135deg, #4f46e5 0%, #7c3aed 100%); color: white; border: none; }
        .btn-success { background-color: #28a745; color: white; }
        .btn-warning { background-color: #ffc107; color: black; }
        pre { background: #f8f9fa; padding: 15px; border-radius: 6px; overflow-x: auto; }
        table { border-collapse: collapse; width: 100%; margin: 15px 0; }
        th, td { border: 1px solid #ddd; padding: 12px; text-align: left; }
        th { background-color: #f8f9fa; font-weight: 600; }
        .avatar-demo { 
            background: linear-gradient(135deg, #4f46e5 0%, #7c3aed 100%); 
            color: white; 
            width: 32px; 
            height: 32px; 
            border-radius: 50%; 
            display: inline-flex; 
            align-items: center; 
            justify-content: center; 
            font-weight: 600; 
            font-size: 0.75rem;
            margin-right: 10px;
        }
    </style>
</head>
<body>";

echo "<div class='container-fluid'>
<h1>🔧 Admin System - Comprehensive Fix Testing</h1>
<p class='text-muted'>Testing all three fixes: User status history, Avatar styling, and OTP generation</p>";

// Test 1: User Status History Fix
echo "<div class='test-section'>
<h2>🔍 Test 1: User Status History Fix</h2>
<p>Testing the fixed get_user_details.php endpoint</p>";

try {
    $db = getDB();
    
    // Get a sample user
    $sample_user_query = "SELECT id, first_name, last_name, username FROM accounts WHERE is_admin = 0 LIMIT 1";
    $sample_user_result = $db->query($sample_user_query);
    
    if ($sample_user_result && $sample_user_result->num_rows > 0) {
        $sample_user = $sample_user_result->fetch_assoc();
        $user_id = $sample_user['id'];
        
        echo "<div class='alert alert-info'>
        <strong>Testing with:</strong> {$sample_user['first_name']} {$sample_user['last_name']} (ID: $user_id)
        </div>";
        
        // Test the AJAX endpoint directly
        $test_url = "ajax/get_user_details.php?user_id=$user_id";
        echo "<p><strong>Testing endpoint:</strong> <code>$test_url</code></p>";
        
        echo "<button class='test-button btn-primary' onclick='testUserDetails($user_id)'>Test User Details API</button>";
        echo "<div id='user-details-result' class='mt-3'></div>";
        
    } else {
        echo "<div class='alert alert-warning'>No test users available</div>";
    }
} catch (Exception $e) {
    echo "<div class='alert alert-danger'>Database error: " . htmlspecialchars($e->getMessage()) . "</div>";
}

echo "</div>";

// Test 2: Avatar Styling Demo
echo "<div class='test-section'>
<h2>🎨 Test 2: Avatar Styling Demo</h2>
<p>Demonstrating the new primary color avatar styling</p>";

try {
    $users_query = "SELECT first_name, last_name, username FROM accounts WHERE is_admin = 0 LIMIT 5";
    $users_result = $db->query($users_query);
    
    if ($users_result && $users_result->num_rows > 0) {
        echo "<div class='row'>";
        while ($user = $users_result->fetch_assoc()) {
            $initials = strtoupper(substr($user['first_name'], 0, 1) . substr($user['last_name'], 0, 1));
            echo "<div class='col-md-4 mb-3'>
                <div class='card'>
                    <div class='card-body'>
                        <div class='d-flex align-items-center'>
                            <div class='avatar-demo'>$initials</div>
                            <div>
                                <div class='fw-bold'>{$user['first_name']} {$user['last_name']}</div>
                                <small class='text-muted'>@{$user['username']}</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>";
        }
        echo "</div>";
        
        echo "<div class='alert alert-success'>
        <strong>✅ Avatar Styling:</strong> Primary color gradient applied successfully
        </div>";
    }
} catch (Exception $e) {
    echo "<div class='alert alert-danger'>Error loading users: " . htmlspecialchars($e->getMessage()) . "</div>";
}

echo "</div>";

// Test 3: OTP Generation Test
echo "<div class='test-section'>
<h2>🔑 Test 3: OTP Generation Test</h2>
<p>Testing the OTP generation functionality from admin/users.php</p>";

try {
    $users_query = "SELECT id, first_name, last_name, username, email FROM accounts WHERE is_admin = 0 LIMIT 3";
    $users_result = $db->query($users_query);
    
    if ($users_result && $users_result->num_rows > 0) {
        echo "<table class='table'>
        <thead>
            <tr>
                <th>User</th>
                <th>Email</th>
                <th>Action</th>
                <th>Result</th>
            </tr>
        </thead>
        <tbody>";
        
        while ($user = $users_result->fetch_assoc()) {
            $initials = strtoupper(substr($user['first_name'], 0, 1) . substr($user['last_name'], 0, 1));
            echo "<tr>
                <td>
                    <div class='d-flex align-items-center'>
                        <div class='avatar-demo'>$initials</div>
                        <div>
                            <div class='fw-bold'>{$user['first_name']} {$user['last_name']}</div>
                            <small class='text-muted'>@{$user['username']}</small>
                        </div>
                    </div>
                </td>
                <td>{$user['email']}</td>
                <td>
                    <button class='test-button btn-primary' onclick='testOTPGeneration({$user['id']})'>
                        <i class='fas fa-key'></i> Generate OTP
                    </button>
                </td>
                <td id='otp-result-{$user['id']}'></td>
            </tr>";
        }
        
        echo "</tbody></table>";
    }
} catch (Exception $e) {
    echo "<div class='alert alert-danger'>Error loading users: " . htmlspecialchars($e->getMessage()) . "</div>";
}

echo "</div>";

// Test 4: Navigation Links
echo "<div class='test-section'>
<h2>🔗 Test 4: Quick Navigation</h2>
<p>Links to test the actual pages</p>

<div class='row'>
    <div class='col-md-4'>
        <div class='card'>
            <div class='card-body text-center'>
                <i class='fas fa-users fa-2x text-primary mb-3'></i>
                <h5>User Status Management</h5>
                <p class='text-muted'>Test the fixed user details view</p>
                <a href='user-status-management.php' class='btn btn-primary'>Open Page</a>
            </div>
        </div>
    </div>
    <div class='col-md-4'>
        <div class='card'>
            <div class='card-body text-center'>
                <i class='fas fa-list fa-2x text-primary mb-3'></i>
                <h5>Users List</h5>
                <p class='text-muted'>Test the OTP generation</p>
                <a href='users.php' class='btn btn-primary'>Open Page</a>
            </div>
        </div>
    </div>
    <div class='col-md-4'>
        <div class='card'>
            <div class='card-body text-center'>
                <i class='fas fa-eye fa-2x text-primary mb-3'></i>
                <h5>View User (Working)</h5>
                <p class='text-muted'>Compare with working OTP</p>
                <a href='view-user.php?id=1' class='btn btn-success'>Open Page</a>
            </div>
        </div>
    </div>
</div>

</div>";

echo "</div>"; // Close container

// JavaScript for testing
echo "<script>
// Test User Details API
function testUserDetails(userId) {
    const resultDiv = document.getElementById('user-details-result');
    resultDiv.innerHTML = '<div class=\"alert alert-info\"><i class=\"fas fa-spinner fa-spin\"></i> Testing user details API...</div>';
    
    fetch('ajax/get_user_details.php?user_id=' + userId)
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            resultDiv.innerHTML = `
                <div class='alert alert-success'>
                    <h5>✅ User Details API Working!</h5>
                    <p><strong>User:</strong> \${data.user.first_name} \${data.user.last_name}</p>
                    <p><strong>Status:</strong> \${data.user.status || 'N/A'}</p>
                    <p><strong>Status History Records:</strong> \${data.status_history ? data.status_history.length : 0}</p>
                    <p><strong>Virtual Cards:</strong> \${data.virtual_cards_count}</p>
                    <p><strong>Crypto Wallets:</strong> \${data.crypto_wallets_count}</p>
                    <p><strong>Recent Transactions:</strong> \${data.transactions ? data.transactions.length : 0}</p>
                </div>
            `;
        } else {
            resultDiv.innerHTML = `
                <div class='alert alert-danger'>
                    <h5>❌ API Error</h5>
                    <p><strong>Error:</strong> \${data.error || data.message}</p>
                </div>
            `;
        }
    })
    .catch(error => {
        resultDiv.innerHTML = `
            <div class='alert alert-danger'>
                <h5>❌ Network Error</h5>
                <p><strong>Error:</strong> \${error.message}</p>
            </div>
        `;
    });
}

// Test OTP Generation
function testOTPGeneration(userId) {
    const resultCell = document.getElementById('otp-result-' + userId);
    resultCell.innerHTML = '<i class=\"fas fa-spinner fa-spin text-primary\"></i> Generating...';
    
    fetch('ajax/generate-otp.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: 'user_id=' + userId
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            resultCell.innerHTML = `
                <div class='text-success'>
                    <i class='fas fa-check-circle'></i>
                    <strong>\${data.otp_code}</strong><br>
                    <small>Email: \${data.email_sent ? 'Sent' : 'Failed'}</small>
                </div>
            `;
        } else {
            resultCell.innerHTML = `
                <div class='text-danger'>
                    <i class='fas fa-times-circle'></i>
                    <small>\${data.message}</small>
                </div>
            `;
        }
    })
    .catch(error => {
        resultCell.innerHTML = `
            <div class='text-danger'>
                <i class='fas fa-exclamation-triangle'></i>
                <small>Network Error</small>
            </div>
        `;
    });
}
</script>";

echo "</body></html>";
?>
