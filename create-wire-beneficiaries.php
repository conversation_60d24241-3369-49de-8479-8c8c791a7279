<?php
/**
 * Create Wire Transfer Beneficiaries System
 * This script creates the wire_beneficiaries table and inserts test data
 */

require_once 'config/config.php';

try {
    $db = getDB();
    
    echo "<h2>Creating Wire Transfer Beneficiaries System</h2>";
    
    // 1. Create wire_beneficiaries table
    echo "<h3>1. Creating wire_beneficiaries table...</h3>";
    
    $create_table_sql = "
    CREATE TABLE IF NOT EXISTS wire_beneficiaries (
        id INT AUTO_INCREMENT PRIMARY KEY,
        user_id INT NOT NULL,
        name VARCHAR(100) NOT NULL,
        beneficiary_country VARCHAR(100) NOT NULL,
        beneficiary_account_number VARCHAR(50) NOT NULL,
        beneficiary_account_name VARCHAR(100) NOT NULL,
        beneficiary_address TEXT NOT NULL,
        bank_name VARCHAR(100) NOT NULL,
        bank_address TEXT NOT NULL,
        bank_city VARCHAR(100) NOT NULL,
        bank_country VARCHAR(100) NOT NULL,
        swift_code VARCHAR(20) NOT NULL,
        routing_code VARCHAR(20) NULL,
        iban VARCHAR(50) NULL,
        is_favorite BOOLEAN DEFAULT FALSE,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (user_id) REFERENCES accounts(id) ON DELETE CASCADE,
        INDEX idx_user_id (user_id),
        INDEX idx_favorite (is_favorite)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
    
    $db->query($create_table_sql);
    echo "✅ Wire beneficiaries table created successfully<br>";
    
    // 2. Insert test beneficiaries for admin user (ID = 1)
    echo "<h3>2. Creating test beneficiaries...</h3>";
    
    $test_beneficiaries = [
        [
            'name' => 'John Smith (UK)',
            'beneficiary_country' => 'United Kingdom',
            'beneficiary_account_number' => '*********01234',
            'beneficiary_account_name' => 'John Smith',
            'beneficiary_address' => '123 Main Street, London, SW1A 1AA',
            'bank_name' => 'Barclays Bank',
            'bank_address' => '1 Churchill Place, London',
            'bank_city' => 'London',
            'bank_country' => 'United Kingdom',
            'swift_code' => 'BARCGB22',
            'routing_code' => null,
            'iban' => '**********************',
            'is_favorite' => 1
        ],
        [
            'name' => 'Maria Schmidt (Germany)',
            'beneficiary_country' => 'Germany',
            'beneficiary_account_number' => '**********************',
            'beneficiary_account_name' => 'Maria Schmidt',
            'beneficiary_address' => 'Hauptstraße 123, 10115 Berlin',
            'bank_name' => 'Deutsche Bank AG',
            'bank_address' => 'Taunusanlage 12, 60325 Frankfurt am Main',
            'bank_city' => 'Frankfurt am Main',
            'bank_country' => 'Germany',
            'swift_code' => 'DEUTDEFF',
            'routing_code' => null,
            'iban' => '**********************',
            'is_favorite' => 1
        ],
        [
            'name' => 'Robert Johnson (USA)',
            'beneficiary_country' => 'United States',
            'beneficiary_account_number' => '*********0',
            'beneficiary_account_name' => 'Robert Johnson',
            'beneficiary_address' => '456 Oak Avenue, New York, NY 10001',
            'bank_name' => 'JPMorgan Chase Bank',
            'bank_address' => '270 Park Avenue, New York',
            'bank_city' => 'New York',
            'bank_country' => 'United States',
            'swift_code' => 'CHASUS33',
            'routing_code' => '*********',
            'iban' => null,
            'is_favorite' => 0
        ],
        [
            'name' => 'Sarah Wilson (Canada)',
            'beneficiary_country' => 'Canada',
            'beneficiary_account_number' => '1234567',
            'beneficiary_account_name' => 'Sarah Wilson',
            'beneficiary_address' => '789 Maple Street, Toronto, ON M5V 3A8',
            'bank_name' => 'Royal Bank of Canada',
            'bank_address' => '200 Bay Street, Toronto',
            'bank_city' => 'Toronto',
            'bank_country' => 'Canada',
            'swift_code' => 'ROYCCAT2',
            'routing_code' => '003',
            'iban' => null,
            'is_favorite' => 0
        ],
        [
            'name' => 'David Brown (Australia)',
            'beneficiary_country' => 'Australia',
            'beneficiary_account_number' => '*********',
            'beneficiary_account_name' => 'David Brown',
            'beneficiary_address' => '321 Collins Street, Melbourne, VIC 3000',
            'bank_name' => 'Australia and New Zealand Banking Group',
            'bank_address' => '833 Collins Street, Docklands',
            'bank_city' => 'Melbourne',
            'bank_country' => 'Australia',
            'swift_code' => 'ANZBAU3M',
            'routing_code' => '013',
            'iban' => null,
            'is_favorite' => 0
        ]
    ];
    
    $insert_sql = "INSERT INTO wire_beneficiaries (
        user_id, name, beneficiary_country, beneficiary_account_number, 
        beneficiary_account_name, beneficiary_address, bank_name, bank_address, 
        bank_city, bank_country, swift_code, routing_code, iban, is_favorite
    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";
    
    foreach ($test_beneficiaries as $beneficiary) {
        $db->query($insert_sql, [
            1, // admin user_id
            $beneficiary['name'],
            $beneficiary['beneficiary_country'],
            $beneficiary['beneficiary_account_number'],
            $beneficiary['beneficiary_account_name'],
            $beneficiary['beneficiary_address'],
            $beneficiary['bank_name'],
            $beneficiary['bank_address'],
            $beneficiary['bank_city'],
            $beneficiary['bank_country'],
            $beneficiary['swift_code'],
            $beneficiary['routing_code'],
            $beneficiary['iban'],
            $beneficiary['is_favorite']
        ]);
        
        echo "✅ Created beneficiary: " . $beneficiary['name'] . "<br>";
    }
    
    echo "<br><h3>3. Summary</h3>";
    echo "✅ Wire beneficiaries table created<br>";
    echo "✅ " . count($test_beneficiaries) . " test beneficiaries added<br>";
    echo "✅ System ready for testing<br>";
    
    echo "<br><h3>4. Next Steps</h3>";
    echo "1. Update wire transfer form to include beneficiary selection<br>";
    echo "2. Add JavaScript for auto-filling form fields<br>";
    echo "3. Test the integration<br>";
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "<br>";
    echo "Stack trace: " . $e->getTraceAsString();
}
?>
