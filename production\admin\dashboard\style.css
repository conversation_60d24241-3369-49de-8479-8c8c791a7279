/* Admin Dashboard Specific Styles */

/* Dashboard Statistics Cards */
.admin-stat-card {
    background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
    border: 1px solid #e2e8f0;
    border-radius: 16px;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.admin-stat-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, #3b82f6, #8b5cf6, #ef4444, #10b981);
    opacity: 0;
    transition: opacity 0.3s ease;
}

.admin-stat-card:hover::before {
    opacity: 1;
}

.admin-stat-card:hover {
    transform: translateY(-4px);
    box-shadow: 0 20px 40px rgba(0,0,0,0.1);
    border-color: #cbd5e1;
}

.admin-stat-card .h1 {
    font-size: 2.5rem;
    font-weight: 700;
    background: linear-gradient(135deg, #1e293b, #475569);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

/* Quick Actions Grid */
.admin-quick-actions {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 1.5rem;
}

.quick-action-item {
    background: white;
    border: 2px solid #f1f5f9;
    border-radius: 16px;
    padding: 2rem;
    text-decoration: none;
    color: inherit;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
    display: flex;
    align-items: center;
    gap: 1rem;
}

.quick-action-item::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.4), transparent);
    transition: left 0.5s ease;
}

.quick-action-item:hover::before {
    left: 100%;
}

.quick-action-item:hover {
    transform: translateY(-2px);
    box-shadow: 0 12px 30px rgba(0,0,0,0.1);
    border-color: #3b82f6;
    color: inherit;
    text-decoration: none;
}

.quick-action-icon {
    width: 60px;
    height: 60px;
    border-radius: 16px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    flex-shrink: 0;
    box-shadow: 0 8px 20px rgba(0,0,0,0.15);
}

.quick-action-item .font-weight-medium {
    font-size: 1.1rem;
    font-weight: 600;
    margin-bottom: 0.25rem;
}

.quick-action-item .text-muted {
    font-size: 0.875rem;
    color: #64748b;
}

/* Recent Activity Tables */
.card-table {
    margin-bottom: 0;
}

.card-table th {
    background: #f8fafc;
    border-bottom: 2px solid #e2e8f0;
    font-weight: 600;
    color: #475569;
    padding: 1rem;
    font-size: 0.875rem;
    text-transform: uppercase;
    letter-spacing: 0.025em;
}

.card-table td {
    padding: 1rem;
    vertical-align: middle;
    border-bottom: 1px solid #f1f5f9;
}

.card-table tbody tr {
    transition: background-color 0.2s ease;
}

.card-table tbody tr:hover {
    background: #f8fafc;
}

/* User Avatar in Tables */
.user-avatar {
    width: 40px;
    height: 40px;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 600;
    font-size: 14px;
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
    margin-right: 0.75rem;
    flex-shrink: 0;
}

/* Status Badges */
.status-badge {
    padding: 0.375rem 0.75rem;
    border-radius: 8px;
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.025em;
    display: inline-flex;
    align-items: center;
    gap: 0.25rem;
}

.status-badge.active {
    background: linear-gradient(135deg, #dcfce7, #bbf7d0);
    color: #166534;
    border: 1px solid #86efac;
}

.status-badge.suspended {
    background: linear-gradient(135deg, #fee2e2, #fecaca);
    color: #991b1b;
    border: 1px solid #fca5a5;
}

.status-badge.pending {
    background: linear-gradient(135deg, #fef3c7, #fde68a);
    color: #92400e;
    border: 1px solid #fcd34d;
}

.status-badge.completed {
    background: linear-gradient(135deg, #dcfce7, #bbf7d0);
    color: #166534;
    border: 1px solid #86efac;
}

.status-badge.failed {
    background: linear-gradient(135deg, #fee2e2, #fecaca);
    color: #991b1b;
    border: 1px solid #fca5a5;
}

/* Dashboard Header */
.page-header {
    background: linear-gradient(135deg, #f8fafc 0%, #ffffff 100%);
    border-bottom: 1px solid #e2e8f0;
    margin-bottom: 2rem;
}

.page-title {
    background: linear-gradient(135deg, #1e293b, #475569);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    font-weight: 700;
}

.page-pretitle {
    color: #64748b;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.05em;
    font-size: 0.75rem;
}

/* Card Headers */
.card-header {
    background: linear-gradient(135deg, #f8fafc 0%, #ffffff 100%);
    border-bottom: 2px solid #e2e8f0;
    padding: 1.5rem;
}

.card-title {
    font-weight: 600;
    color: #1e293b;
    margin: 0;
}

/* Empty States */
.empty {
    padding: 3rem 1rem;
    text-align: center;
}

.empty-title {
    color: #64748b;
    font-weight: 500;
    margin: 0;
}

/* Responsive Design */
@media (max-width: 768px) {
    .admin-quick-actions {
        grid-template-columns: 1fr;
    }
    
    .quick-action-item {
        padding: 1.5rem;
        flex-direction: column;
        text-align: center;
    }
    
    .quick-action-icon {
        margin-bottom: 1rem;
    }
    
    .admin-stat-card .h1 {
        font-size: 2rem;
    }
    
    .card-table th,
    .card-table td {
        padding: 0.75rem 0.5rem;
        font-size: 0.875rem;
    }
    
    .user-avatar {
        width: 32px;
        height: 32px;
        font-size: 12px;
        margin-right: 0.5rem;
    }
}

@media (max-width: 576px) {
    .admin-stat-card .h1 {
        font-size: 1.75rem;
    }
    
    .quick-action-item {
        padding: 1rem;
    }
    
    .quick-action-icon {
        width: 48px;
        height: 48px;
    }
}

/* Dark Mode Support */
@media (prefers-color-scheme: dark) {
    .admin-stat-card {
        background: linear-gradient(135deg, #1f2937 0%, #111827 100%);
        border-color: #374151;
    }
    
    .admin-stat-card .h1 {
        background: linear-gradient(135deg, #f9fafb, #e5e7eb);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
    }
    
    .quick-action-item {
        background: #1f2937;
        border-color: #374151;
        color: #f9fafb;
    }
    
    .quick-action-item:hover {
        border-color: #3b82f6;
        color: #f9fafb;
    }
    
    .card-table th {
        background: #374151;
        color: #e5e7eb;
        border-bottom-color: #4b5563;
    }
    
    .card-table td {
        color: #e5e7eb;
        border-bottom-color: #374151;
    }
    
    .card-table tbody tr:hover {
        background: #374151;
    }
    
    .page-header {
        background: linear-gradient(135deg, #1f2937 0%, #111827 100%);
        border-bottom-color: #374151;
    }
    
    .page-title {
        background: linear-gradient(135deg, #f9fafb, #e5e7eb);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
    }
    
    .card-header {
        background: linear-gradient(135deg, #374151 0%, #1f2937 100%);
        border-bottom-color: #4b5563;
    }
    
    .card-title {
        color: #e5e7eb;
    }
}
