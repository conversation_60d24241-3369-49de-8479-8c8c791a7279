<?php
/**
 * Virtual Card Top-Up API Endpoint
 * Handles virtual card balance top-up operations
 */

session_start();
require_once '../../config/config.php';

// Set JSON response header
header('Content-Type: application/json');

// Check if user is logged in and is admin
if (!isset($_SESSION['user_id']) || !isset($_SESSION['is_admin']) || !$_SESSION['is_admin']) {
    echo json_encode(['success' => false, 'message' => 'Unauthorized access']);
    exit();
}

// Check if request is POST
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    echo json_encode(['success' => false, 'message' => 'Invalid request method']);
    exit();
}

// Validate required fields
$required_fields = ['card_id', 'amount'];
foreach ($required_fields as $field) {
    if (!isset($_POST[$field]) || empty($_POST[$field])) {
        echo json_encode(['success' => false, 'message' => "Missing required field: $field"]);
        exit();
    }
}

$card_id = intval($_POST['card_id']);
$amount = floatval($_POST['amount']);
$description = $_POST['description'] ?? 'Virtual card top-up';
$admin_id = $_SESSION['user_id'];

// Validate amount
if ($amount <= 0) {
    echo json_encode(['success' => false, 'message' => 'Amount must be greater than zero']);
    exit();
}

if ($amount > 50000) {
    echo json_encode(['success' => false, 'message' => 'Amount cannot exceed $50,000']);
    exit();
}

try {
    $db = getDB();
    
    // Start transaction
    $db->query("START TRANSACTION");
    
    // Get card details
    $card_query = "SELECT vc.*, a.first_name, a.last_name, a.username 
                   FROM virtual_cards vc 
                   LEFT JOIN accounts a ON vc.account_id = a.id 
                   WHERE vc.card_id = ?";
    $card_result = $db->query($card_query, [$card_id]);
    
    if ($card_result->num_rows === 0) {
        throw new Exception("Virtual card not found");
    }
    
    $card = $card_result->fetch_assoc();
    
    // Check if card is active
    if ($card['status'] !== 'active') {
        throw new Exception("Cannot top up inactive card");
    }
    
    // Calculate new balance
    $current_balance = floatval($card['card_balance']);
    $new_balance = $current_balance + $amount;
    
    // Check if new balance exceeds reasonable limits
    if ($new_balance > 100000) {
        throw new Exception("Card balance cannot exceed $100,000");
    }
    
    // Update card balance
    $update_balance = "UPDATE virtual_cards SET card_balance = ?, updated_at = NOW() WHERE card_id = ?";
    $balance_result = $db->query($update_balance, [$new_balance, $card_id]);
    
    if (!$balance_result) {
        throw new Exception("Failed to update card balance");
    }
    
    // Generate reference number
    $reference_number = 'VCOP' . date('Ymd') . str_pad($card_id, 6, '0', STR_PAD_LEFT) . mt_rand(100, 999);
    
    // Record transaction in virtual_card_transactions table
    $transaction_sql = "INSERT INTO virtual_card_transactions (
                            card_id, account_id, transaction_type, amount, currency, 
                            description, reference_number, status, processed_by, 
                            created_at, updated_at
                        ) VALUES (?, ?, 'credit', ?, ?, ?, ?, 'completed', ?, NOW(), NOW())";
    
    $transaction_result = $db->query($transaction_sql, [
        $card_id,
        $card['account_id'],
        $amount,
        $card['currency'] ?? 'USD',
        $description,
        $reference_number,
        $admin_id
    ]);
    
    if (!$transaction_result) {
        throw new Exception("Failed to record transaction");
    }
    
    // Log admin activity
    $activity_description = "Topped up virtual card #{$card_id} with " . formatCurrency($amount) . " (New balance: " . formatCurrency($new_balance) . ")";
    logActivity($admin_id, $activity_description, 'virtual_cards', $card_id);
    
    // Commit transaction
    $db->query("COMMIT");
    
    // Return success response
    echo json_encode([
        'success' => true,
        'message' => 'Card topped up successfully',
        'data' => [
            'card_id' => $card_id,
            'previous_balance' => formatCurrency($current_balance),
            'amount_added' => formatCurrency($amount),
            'new_balance' => formatCurrency($new_balance),
            'reference_number' => $reference_number,
            'card_holder' => $card['first_name'] . ' ' . $card['last_name']
        ]
    ]);
    
} catch (Exception $e) {
    // Rollback transaction on error
    if (isset($db)) {
        $db->query("ROLLBACK");
    }
    
    // Log error
    error_log("Virtual card top-up error: " . $e->getMessage());
    
    // Return error response
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ]);
}
?>
