<?php
/**
 * User Dashboard Redesign - Comprehensive Banking Interface
 * Displays ALL user information from database analysis
 */

// Set page variables
$page_title = 'Dashboard';
$additional_css = ['user-dashboard-redesign.css'];

// Include header template with dynamic CSS
require_once '../templates/user/header.php';

// Include database connection and check authentication
require_once '../config/config.php';
requireLogin();

// Get comprehensive user data
try {
    $db = getDB();
    $user_id = $_SESSION['user_id'];

    // Get complete user account information
    $user_sql = "SELECT a.*,
                        COALESCE(a.balance, 0) as balance,
                        DATE_FORMAT(a.created_at, '%M %Y') as member_since,
                        DATEDIFF(NOW(), a.created_at) as days_member,
                        DATE_FORMAT(a.last_login, '%M %d, %Y at %h:%i %p') as last_login_formatted
                 FROM accounts a
                 WHERE a.id = ? AND a.deleted_at IS NULL";

    $user_result = $db->query($user_sql, [$user_id]);
    $user = $user_result->fetch_assoc();

    if (!$user) {
        throw new Exception("User account not found");
    }

    // Get current balance
    $current_balance = $user['balance'] ?? 0;

    // Get virtual cards information
    $cards_sql = "SELECT vc.*,
                         DATE_FORMAT(vc.expiry_date, '%m/%y') as expiry_formatted,
                         DATE_FORMAT(vc.created_at, '%M %d, %Y') as created_formatted
                  FROM virtual_cards vc
                  WHERE vc.account_id = ?
                  ORDER BY vc.created_at DESC";

    $cards_result = $db->query($cards_sql, [$user_id]);
    $virtual_cards = [];
    if ($cards_result) {
        while ($card = $cards_result->fetch_assoc()) {
            $virtual_cards[] = $card;
        }
    }
    $primary_card = $virtual_cards[0] ?? null;

    // Get comprehensive transaction history
    $transactions_sql = "SELECT at.*,
                               DATE_FORMAT(at.created_at, '%M %d, %Y') as transaction_date,
                               DATE_FORMAT(at.created_at, '%h:%i %p') as transaction_time,
                               CASE
                                   WHEN at.transaction_type IN ('credit', 'transfer_in', 'deposit') THEN 'credit'
                                   ELSE 'debit'
                               END as direction
                        FROM account_transactions at
                        WHERE at.account_id = ?
                        ORDER BY at.created_at DESC
                        LIMIT 10";

    $transactions_result = $db->query($transactions_sql, [$user_id]);
    $recent_transactions = [];
    if ($transactions_result) {
        while ($transaction = $transactions_result->fetch_assoc()) {
            $recent_transactions[] = $transaction;
        }
    }

    // Get monthly statistics
    $current_month = date('Y-m');
    $monthly_stats_sql = "SELECT
                            SUM(CASE WHEN transaction_type IN ('credit', 'transfer_in', 'deposit') THEN amount ELSE 0 END) as total_credits,
                            SUM(CASE WHEN transaction_type IN ('debit', 'transfer_out', 'withdrawal') THEN amount ELSE 0 END) as total_debits,
                            COUNT(*) as transaction_count,
                            AVG(amount) as avg_transaction
                          FROM account_transactions
                          WHERE account_id = ?
                          AND DATE_FORMAT(created_at, '%Y-%m') = ?
                          AND status = 'completed'";

    $stats_result = $db->query($monthly_stats_sql, [$user_id, $current_month]);
    $monthly_stats = $stats_result ? $stats_result->fetch_assoc() : [];

    $total_credits = $monthly_stats['total_credits'] ?? 0;
    $total_debits = $monthly_stats['total_debits'] ?? 0;
    $transaction_count = $monthly_stats['transaction_count'] ?? 0;
    $avg_transaction = $monthly_stats['avg_transaction'] ?? 0;

    // Get account activity/audit information
    $activity_sql = "SELECT
                        'Transaction' as activity_type,
                        CONCAT(UPPER(SUBSTRING(transaction_type, 1, 1)), SUBSTRING(transaction_type, 2)) as activity_title,
                        CONCAT('$', FORMAT(amount, 2), ' - ', description) as activity_description,
                        created_at,
                        DATE_FORMAT(created_at, '%h:%i %p') as activity_time
                     FROM account_transactions
                     WHERE account_id = ?
                     UNION ALL
                     SELECT
                        'Card' as activity_type,
                        'Virtual Card Created' as activity_title,
                        CONCAT('Card ending in ', RIGHT(card_number, 4)) as activity_description,
                        created_at,
                        DATE_FORMAT(created_at, '%h:%i %p') as activity_time
                     FROM virtual_cards
                     WHERE account_id = ?
                     ORDER BY created_at DESC
                     LIMIT 8";

    $activity_result = $db->query($activity_sql, [$user_id, $user_id]);
    $recent_activity = [];
    if ($activity_result) {
        while ($activity = $activity_result->fetch_assoc()) {
            $recent_activity[] = $activity;
        }
    }

    // Calculate balance change (mock calculation for demo)
    $balance_change = ($total_credits - $total_debits);
    $balance_change_percent = $current_balance > 0 ? (($balance_change / $current_balance) * 100) : 0;

} catch (Exception $e) {
    error_log("Dashboard error: " . $e->getMessage());
    // Fallback data
    $user = [
        'first_name' => 'User', 
        'last_name' => '', 
        'email' => '', 
        'account_number' => '',
        'account_type' => 'savings',
        'status' => 'active',
        'kyc_status' => 'pending',
        'member_since' => date('F Y'),
        'days_member' => 0
    ];
    $current_balance = 0;
    $total_credits = $total_debits = $transaction_count = $avg_transaction = 0;
    $recent_transactions = [];
    $virtual_cards = [];
    $primary_card = null;
    $recent_activity = [];
    $balance_change = 0;
    $balance_change_percent = 0;
}

// Helper functions
function formatCurrency($amount) {
    return '$' . number_format($amount, 2);
}

function getStatusBadgeClass($status) {
    switch (strtolower($status)) {
        case 'active': return 'active';
        case 'pending': return 'pending';
        case 'suspended': case 'closed': return 'suspended';
        default: return 'pending';
    }
}

function maskCardNumber($number) {
    if (strlen($number) < 4) return $number;
    return '**** **** **** ' . substr($number, -4);
}

function getTransactionIcon($type) {
    switch ($type) {
        case 'credit':
        case 'transfer_in':
        case 'deposit':
            return 'fas fa-arrow-down';
        case 'debit':
        case 'transfer_out':
        case 'withdrawal':
            return 'fas fa-arrow-up';
        default:
            return 'fas fa-exchange-alt';
    }
}

function getActivityIcon($type) {
    switch ($type) {
        case 'Transaction': return 'fas fa-exchange-alt';
        case 'Card': return 'fas fa-credit-card';
        default: return 'fas fa-bell';
    }
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo htmlspecialchars($page_title . ' - ' . getBankName()); ?></title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
    
    <!-- Dynamic CSS Variables -->
    <style>
        <?php 
        require_once '../config/dynamic-css.php';
        echo getInlineDynamicCSS();
        ?>
    </style>
    
    <!-- Custom CSS -->
    <link rel="stylesheet" href="../assets/css/user-dashboard-redesign.css">
</head>
<body>
    <div class="dashboard-container">
        <!-- Include Sidebar (keeping the existing one user loves) -->
        <?php require_once '../templates/user/sidebar.php'; ?>
        
        <!-- Main Content -->
        <main class="main-content">
            <!-- Dashboard Header -->
            <header class="dashboard-header">
                <div class="header-content">
                    <div class="welcome-section">
                        <h1>Welcome back, <?php echo htmlspecialchars($user['first_name']); ?>!</h1>
                        <p>Here's what's happening with your account today.</p>
                    </div>
                    <div class="header-actions">
                        <a href="../transactions/transfer.php" class="quick-action-btn">
                            <i class="fas fa-paper-plane"></i>
                            Send Money
                        </a>
                        <a href="../cards/apply.php" class="quick-action-btn" style="background: var(--accent-color, #10b981);">
                            <i class="fas fa-plus"></i>
                            New Card
                        </a>
                    </div>
                </div>
            </header>

            <!-- Dashboard Grid -->
            <div class="dashboard-grid">
                <!-- Account Overview -->
                <section class="account-overview">
                    <div class="overview-content">
                        <div class="account-balance">
                            <?php echo formatCurrency($current_balance); ?>
                        </div>
                        <div class="account-info">
                            <div class="account-details">
                                <h3><?php echo htmlspecialchars($user['first_name'] . ' ' . $user['last_name']); ?></h3>
                                <div class="account-number"><?php echo htmlspecialchars($user['account_number']); ?></div>
                                <span class="status-badge <?php echo getStatusBadgeClass($user['status']); ?>">
                                    <?php echo ucfirst($user['status']); ?>
                                </span>
                            </div>
                            <div class="account-type">
                                <h4><?php echo ucfirst($user['account_type']); ?> Account</h4>
                                <p>Member since <?php echo htmlspecialchars($user['member_since']); ?></p>
                            </div>
                        </div>
                    </div>
                </section>

                <!-- Balance Card -->
                <section class="dashboard-card balance-card">
                    <div class="card-header">
                        <h2 class="card-title">Account Balance</h2>
                        <div class="card-icon">
                            <i class="fas fa-wallet"></i>
                        </div>
                    </div>
                    <div class="balance-amount"><?php echo formatCurrency($current_balance); ?></div>
                    <div class="balance-change <?php echo $balance_change >= 0 ? 'positive' : 'negative'; ?>">
                        <i class="fas fa-arrow-<?php echo $balance_change >= 0 ? 'up' : 'down'; ?>"></i>
                        <?php echo formatCurrency(abs($balance_change)); ?> this month
                        <span>(<?php echo number_format(abs($balance_change_percent), 1); ?>%)</span>
                    </div>
                </section>

                <!-- Recent Transactions -->
                <section class="dashboard-card transactions-card">
                    <div class="card-header">
                        <h2 class="card-title">Recent Transactions</h2>
                        <div class="card-icon">
                            <i class="fas fa-history"></i>
                        </div>
                    </div>
                    <div class="transactions-list">
                        <?php if (empty($recent_transactions)): ?>
                            <p class="text-muted">No transactions found.</p>
                        <?php else: ?>
                            <?php foreach (array_slice($recent_transactions, 0, 5) as $transaction): ?>
                                <div class="transaction-item">
                                    <div class="transaction-info">
                                        <div class="transaction-icon <?php echo $transaction['direction']; ?>">
                                            <i class="<?php echo getTransactionIcon($transaction['transaction_type']); ?>"></i>
                                        </div>
                                        <div class="transaction-details">
                                            <h4><?php echo ucfirst(str_replace('_', ' ', $transaction['transaction_type'])); ?></h4>
                                            <p><?php echo htmlspecialchars($transaction['description']); ?></p>
                                            <small><?php echo $transaction['transaction_date']; ?> at <?php echo $transaction['transaction_time']; ?></small>
                                        </div>
                                    </div>
                                    <div class="transaction-amount <?php echo $transaction['direction']; ?>">
                                        <?php echo ($transaction['direction'] === 'credit' ? '+' : '-') . formatCurrency($transaction['amount']); ?>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        <?php endif; ?>
                    </div>
                    <a href="../transactions/" class="btn btn-outline-primary btn-sm mt-3">View All Transactions</a>
                </section>

                <!-- Virtual Cards -->
                <section class="dashboard-card cards-card">
                    <div class="card-header">
                        <h2 class="card-title">Virtual Cards</h2>
                        <div class="card-icon">
                            <i class="fas fa-credit-card"></i>
                        </div>
                    </div>
                    <?php if ($primary_card): ?>
                        <div class="virtual-card-display">
                            <div class="card-number"><?php echo maskCardNumber($primary_card['card_number']); ?></div>
                            <div class="card-details">
                                <div class="card-holder"><?php echo htmlspecialchars($primary_card['card_holder_name']); ?></div>
                                <div class="card-expiry"><?php echo $primary_card['expiry_formatted']; ?></div>
                            </div>
                            <div class="card-balance">
                                Balance: <?php echo formatCurrency($primary_card['card_balance']); ?>
                            </div>
                        </div>
                        <p class="text-muted">
                            <i class="fas fa-shield-alt"></i>
                            Status: <span class="status-badge <?php echo getStatusBadgeClass($primary_card['status']); ?>">
                                <?php echo ucfirst($primary_card['status']); ?>
                            </span>
                        </p>
                    <?php else: ?>
                        <div class="text-center py-4">
                            <i class="fas fa-credit-card fa-3x text-muted mb-3"></i>
                            <p class="text-muted">No virtual cards found.</p>
                            <a href="../cards/apply.php" class="btn btn-primary btn-sm">Apply for Card</a>
                        </div>
                    <?php endif; ?>
                    <?php if (count($virtual_cards) > 1): ?>
                        <p class="text-muted mt-2">
                            <i class="fas fa-info-circle"></i>
                            You have <?php echo count($virtual_cards); ?> virtual cards total.
                        </p>
                    <?php endif; ?>
                </section>

                <!-- Monthly Statistics -->
                <section class="dashboard-card stats-card">
                    <div class="card-header">
                        <h2 class="card-title">This Month</h2>
                        <div class="card-icon">
                            <i class="fas fa-chart-bar"></i>
                        </div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-value credit"><?php echo formatCurrency($total_credits); ?></div>
                        <div class="stat-label">Total Credits</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-value debit"><?php echo formatCurrency($total_debits); ?></div>
                        <div class="stat-label">Total Debits</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-value"><?php echo $transaction_count; ?></div>
                        <div class="stat-label">Transactions</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-value"><?php echo formatCurrency($avg_transaction); ?></div>
                        <div class="stat-label">Average Amount</div>
                    </div>
                </section>

                <!-- User Profile -->
                <section class="dashboard-card profile-card">
                    <div class="card-header">
                        <h2 class="card-title">Profile</h2>
                        <div class="card-icon">
                            <i class="fas fa-user"></i>
                        </div>
                    </div>
                    <div class="profile-info">
                        <div class="profile-avatar">
                            <?php echo strtoupper(substr($user['first_name'], 0, 1) . substr($user['last_name'], 0, 1)); ?>
                        </div>
                        <div class="profile-details">
                            <h3><?php echo htmlspecialchars($user['first_name'] . ' ' . $user['last_name']); ?></h3>
                            <p><?php echo htmlspecialchars($user['email']); ?></p>
                            <p>
                                KYC Status: 
                                <span class="status-badge <?php echo getStatusBadgeClass($user['kyc_status']); ?>">
                                    <?php echo ucfirst($user['kyc_status']); ?>
                                </span>
                            </p>
                        </div>
                    </div>
                    <div class="profile-stats">
                        <div class="profile-stat">
                            <div class="profile-stat-value"><?php echo $user['days_member']; ?></div>
                            <div class="profile-stat-label">Days Member</div>
                        </div>
                        <div class="profile-stat">
                            <div class="profile-stat-value"><?php echo count($virtual_cards); ?></div>
                            <div class="profile-stat-label">Active Cards</div>
                        </div>
                    </div>
                    <a href="../profile/" class="btn btn-outline-primary btn-sm mt-3">Edit Profile</a>
                </section>

                <!-- Recent Activity -->
                <section class="dashboard-card activity-card">
                    <div class="card-header">
                        <h2 class="card-title">Recent Activity</h2>
                        <div class="card-icon">
                            <i class="fas fa-bell"></i>
                        </div>
                    </div>
                    <div class="activity-list">
                        <?php if (empty($recent_activity)): ?>
                            <p class="text-muted">No recent activity.</p>
                        <?php else: ?>
                            <?php foreach ($recent_activity as $activity): ?>
                                <div class="activity-item">
                                    <div class="activity-icon">
                                        <i class="<?php echo getActivityIcon($activity['activity_type']); ?>"></i>
                                    </div>
                                    <div class="activity-content">
                                        <div class="activity-title"><?php echo htmlspecialchars($activity['activity_title']); ?></div>
                                        <div class="activity-description"><?php echo htmlspecialchars($activity['activity_description']); ?></div>
                                    </div>
                                    <div class="activity-time"><?php echo $activity['activity_time']; ?></div>
                                </div>
                            <?php endforeach; ?>
                        <?php endif; ?>
                    </div>
                </section>
            </div>
        </main>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- Custom JS -->
    <script src="../assets/js/user-dashboard.js"></script>
</body>
</html>
