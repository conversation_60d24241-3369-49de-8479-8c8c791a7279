# Technology Stack

## Backend
- **Language**: PHP 7.4+
- **Database**: MySQL 5.7+ / MariaDB 10.3+
- **Session Management**: Native PHP sessions with custom timeout handling
- **Password Hashing**: PHP `password_hash()` with `PASSWORD_DEFAULT`

## Dependencies (Composer)
- **PHPMailer**: Email functionality (`phpmailer/phpmailer: ^6.8`)
- **Google2FA**: Two-factor authentication (`pragmarx/google2fa: ^8.0`)

## Frontend
- **CSS Framework**: Bootstrap 5.3.0
- **Icons**: Font Awesome 6.4.0
- **Fonts**: Google Fonts (Inter)
- **JavaScript**: Vanilla JS with Bootstrap components

## Database Architecture
- **Primary Tables**: accounts, transfers, beneficiaries, tickets, audit_logs
- **Security Tables**: login_attempts, otp_codes, user_security_settings
- **System Tables**: system_settings, super_admin_settings, exchange_rates
- **Financial Tables**: virtual_cards, account_transactions, crypto_accounts

## Configuration Structure
- **Main Config**: `config/config.php` - Application constants and helper functions
- **Database**: `config/database.php` - Database connection and query wrapper
- **Email**: `config/email.php` - SMTP configuration
- **Specialized Classes**: AuditLogger, EmailManager, SessionManager, InputValidator

## Common Commands

### Database Setup
```bash
# Import main schema
mysql -u username -p database_name < database/schema.sql

# Import additional tables
mysql -u username -p database_name < database/create_otp_table.sql
mysql -u username -p database_name < database/create_super_admin_2fa_table.sql
mysql -u username -p database_name < database/create_user_security_settings.sql
```

### Composer Management
```bash
# Install dependencies
composer install --optimize-autoloader

# Update dependencies
composer update

# Production optimization
composer install --no-dev --optimize-autoloader
```

### Development Setup
```bash
# Start local development (MAMP/XAMPP)
# Ensure PHP 7.4+ and MySQL are running
# Point document root to project folder

# Check PHP version
php -v

# Test database connection
php -f config/database.php
```

## Security Considerations
- All user input sanitized via `sanitizeInput()` function
- SQL queries use prepared statements through custom DB wrapper
- Session timeout enforced (default 30 minutes)
- CSRF protection through session validation
- File upload restrictions and validation
- Audit logging for all critical operations