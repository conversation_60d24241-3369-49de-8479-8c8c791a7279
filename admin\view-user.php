<?php
require_once '../config/config.php';
requireAdmin();

// Get user ID from URL
$user_id = intval($_GET['id'] ?? 0);

if ($user_id <= 0) {
    header('Location: users.php');
    exit();
}

$page_title = 'User Details';
$user = null;
$user_stats = [];
$recent_transactions = [];
$virtual_cards = [];
$crypto_accounts = [];
$beneficiaries = [];
$support_tickets = [];
$login_attempts = [];
$otp_history = [];
$user_documents = [];
$kyc_application = null;
$document_history = [];

try {
    $db = getDB();

    // Get user details
    $user_query = "SELECT * FROM accounts WHERE id = ? AND is_admin = 0";
    $user_result = $db->query($user_query, [$user_id]);

    if ($user_result->num_rows === 0) {
        header('Location: users.php');
        exit();
    }

    $user = $user_result->fetch_assoc();

    // Get user statistics
    $stats_query = "SELECT
        COUNT(CASE WHEN at.transaction_type = 'credit' THEN 1 END) as total_credits,
        COUNT(CASE WHEN at.transaction_type = 'debit' THEN 1 END) as total_debits,
        SUM(CASE WHEN at.transaction_type = 'credit' THEN at.amount ELSE 0 END) as total_credited,
        SUM(CASE WHEN at.transaction_type = 'debit' THEN at.amount ELSE 0 END) as total_debited,
        COUNT(CASE WHEN at.created_at >= DATE_SUB(NOW(), INTERVAL 30 DAY) THEN 1 END) as monthly_transactions
        FROM account_transactions at
        WHERE at.account_id = ?";
    $stats_result = $db->query($stats_query, [$user_id]);
    $user_stats = $stats_result->fetch_assoc();

    // Get recent transactions (last 10) - combining all transaction types
    $transactions_query = "SELECT 'account' as source, id, transaction_type, amount, currency, description,
                          reference_number, status, created_at, NULL as card_number, NULL as crypto_type
                          FROM account_transactions
                          WHERE account_id = ?
                          ORDER BY created_at DESC
                          LIMIT 10";
    $transactions_result = $db->query($transactions_query, [$user_id]);
    while ($transaction = $transactions_result->fetch_assoc()) {
        $recent_transactions[] = $transaction;
    }

    // Get virtual card transactions (with error handling)
    $virtual_card_transactions = [];
    try {
        $vcard_trans_query = "SELECT vct.*, vc.card_number
                             FROM virtual_card_transactions vct
                             LEFT JOIN virtual_cards vc ON vct.card_id = vc.card_id
                             WHERE vct.account_id = ?
                             ORDER BY vct.created_at DESC
                             LIMIT 10";
        $vcard_trans_result = $db->query($vcard_trans_query, [$user_id]);
        while ($transaction = $vcard_trans_result->fetch_assoc()) {
            $transaction['source'] = 'virtual_card';
            $virtual_card_transactions[] = $transaction;
        }
    } catch (Exception $e) {
        error_log("Virtual card transactions query error: " . $e->getMessage());
    }

    // Get crypto transactions (with error handling)
    $crypto_transactions = [];
    try {
        $crypto_trans_query = "SELECT ct.*, cw.cryptocurrency as crypto_type
                              FROM crypto_transactions ct
                              LEFT JOIN crypto_wallets cw ON ct.wallet_id = cw.id
                              WHERE ct.account_id = ?
                              ORDER BY ct.created_at DESC
                              LIMIT 10";
        $crypto_trans_result = $db->query($crypto_trans_query, [$user_id]);
        while ($transaction = $crypto_trans_result->fetch_assoc()) {
            $transaction['source'] = 'crypto';
            $crypto_transactions[] = $transaction;
        }
    } catch (Exception $e) {
        error_log("Crypto transactions query error: " . $e->getMessage());
    }

    // Combine all transactions and sort by date
    $all_transactions = array_merge($recent_transactions, $virtual_card_transactions, $crypto_transactions);
    usort($all_transactions, function($a, $b) {
        return strtotime($b['created_at']) - strtotime($a['created_at']);
    });
    $recent_transactions = array_slice($all_transactions, 0, 10);

    // Get virtual cards (with error handling) - using account_id column
    try {
        $cards_query = "SELECT * FROM virtual_cards WHERE account_id = ? ORDER BY created_at DESC";
        $cards_result = $db->query($cards_query, [$user_id]);
        while ($card = $cards_result->fetch_assoc()) {
            $virtual_cards[] = $card;
        }
    } catch (Exception $e) {
        // Table might not exist, continue without error
        error_log("Virtual cards query error: " . $e->getMessage());
    }

    // Get crypto accounts (with error handling) - using crypto_wallets table
    try {
        $crypto_query = "SELECT * FROM crypto_wallets WHERE account_id = ? ORDER BY cryptocurrency";
        $crypto_result = $db->query($crypto_query, [$user_id]);
        while ($crypto = $crypto_result->fetch_assoc()) {
            $crypto_accounts[] = $crypto;
        }
    } catch (Exception $e) {
        // Try alternative table name
        try {
            $crypto_query = "SELECT * FROM crypto_accounts WHERE user_id = ? ORDER BY crypto_type";
            $crypto_result = $db->query($crypto_query, [$user_id]);
            while ($crypto = $crypto_result->fetch_assoc()) {
                $crypto_accounts[] = $crypto;
            }
        } catch (Exception $e2) {
            // Neither table exists, continue without error
            error_log("Crypto accounts query error: " . $e->getMessage() . " | " . $e2->getMessage());
        }
    }

    // Get beneficiaries (with error handling)
    try {
        $beneficiaries_query = "SELECT * FROM beneficiaries WHERE user_id = ? ORDER BY name";
        $beneficiaries_result = $db->query($beneficiaries_query, [$user_id]);
        while ($beneficiary = $beneficiaries_result->fetch_assoc()) {
            $beneficiaries[] = $beneficiary;
        }
    } catch (Exception $e) {
        // Table might not exist, continue without error
    }

    // Get support tickets (with error handling)
    try {
        $tickets_query = "SELECT * FROM tickets WHERE user_id = ? ORDER BY created_at DESC LIMIT 5";
        $tickets_result = $db->query($tickets_query, [$user_id]);
        while ($ticket = $tickets_result->fetch_assoc()) {
            $support_tickets[] = $ticket;
        }
    } catch (Exception $e) {
        // Table might not exist, continue without error
    }

    // Get recent login attempts (with error handling)
    try {
        $login_query = "SELECT * FROM login_attempts WHERE username = ? ORDER BY attempted_at DESC LIMIT 10";
        $login_result = $db->query($login_query, [$user['username']]);
        while ($attempt = $login_result->fetch_assoc()) {
            $login_attempts[] = $attempt;
        }
    } catch (Exception $e) {
        // Table might not exist, continue without error
    }

    // Get OTP history (with error handling)
    try {
        $otp_query = "SELECT otp_code, created_at, expires_at, used, used_at, source FROM user_otps WHERE user_id = ? ORDER BY created_at DESC LIMIT 10";
        $otp_result = $db->query($otp_query, [$user_id]);
        while ($otp = $otp_result->fetch_assoc()) {
            $otp_history[] = $otp;
        }
    } catch (Exception $e) {
        // Table might not exist, continue without error
        error_log("OTP history query error: " . $e->getMessage());
    }

    // Get user documents (with error handling)
    try {
        $docs_query = "SELECT * FROM user_documents WHERE user_id = ? ORDER BY created_at DESC";
        $docs_result = $db->query($docs_query, [$user_id]);
        while ($doc = $docs_result->fetch_assoc()) {
            $user_documents[] = $doc;
        }
    } catch (Exception $e) {
        // Table might not exist, continue without error
        error_log("User documents query error: " . $e->getMessage());
    }

    // Get KYC application (with error handling)
    try {
        $kyc_query = "SELECT * FROM kyc_applications WHERE user_id = ? ORDER BY created_at DESC LIMIT 1";
        $kyc_result = $db->query($kyc_query, [$user_id]);
        if ($kyc_result->num_rows > 0) {
            $kyc_application = $kyc_result->fetch_assoc();
        }
    } catch (Exception $e) {
        // Table might not exist, continue without error
        error_log("KYC application query error: " . $e->getMessage());
    }

    // Get document verification history (with error handling)
    try {
        $doc_history_query = "SELECT dvh.*, ud.document_name, ud.document_type, a.first_name, a.last_name
                             FROM document_verification_history dvh
                             LEFT JOIN user_documents ud ON dvh.document_id = ud.id
                             LEFT JOIN accounts a ON dvh.performed_by = a.id
                             WHERE ud.user_id = ?
                             ORDER BY dvh.created_at DESC
                             LIMIT 20";
        $doc_history_result = $db->query($doc_history_query, [$user_id]);
        while ($history = $doc_history_result->fetch_assoc()) {
            $document_history[] = $history;
        }
    } catch (Exception $e) {
        // Table might not exist, continue without error
        error_log("Document history query error: " . $e->getMessage());
    }

    // Get cheque deposits (with error handling)
    $cheque_deposits = [];
    try {
        $cheque_query = "SELECT * FROM cheque_deposits WHERE account_id = ? ORDER BY created_at DESC";
        $cheque_result = $db->query($cheque_query, [$user_id]);
        while ($cheque = $cheque_result->fetch_assoc()) {
            $cheque_deposits[] = $cheque;
        }
    } catch (Exception $e) {
        // Table might not exist, continue without error
        error_log("Cheque deposits query error: " . $e->getMessage());
    }

    // Count cheque documents
    $cheque_documents_count = 0;
    try {
        $cheque_docs_query = "SELECT COUNT(*) as count FROM user_documents WHERE user_id = ? AND document_type = 'cheque'";
        $cheque_docs_result = $db->query($cheque_docs_query, [$user_id]);
        $cheque_documents_count = $cheque_docs_result->fetch_assoc()['count'];
    } catch (Exception $e) {
        error_log("Cheque documents count error: " . $e->getMessage());
    }

} catch (Exception $e) {
    error_log("Error loading user details: " . $e->getMessage());
    header('Location: users.php');
    exit();
}

include 'includes/admin-header.php';
?>

<!-- Include View User CSS -->
<link rel="stylesheet" href="../assets/admin/css/view-user.css">

<?php
// Helper function for file size formatting
function formatFileSize($bytes) {
    if ($bytes >= 1073741824) {
        return number_format($bytes / 1073741824, 2) . ' GB';
    } elseif ($bytes >= 1048576) {
        return number_format($bytes / 1048576, 2) . ' MB';
    } elseif ($bytes >= 1024) {
        return number_format($bytes / 1024, 2) . ' KB';
    } else {
        return $bytes . ' bytes';
    }
}
?>

<!-- Credit Card Styles -->
<style>
.credit-card {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 15px;
    padding: 20px;
    color: white;
    position: relative;
    min-height: 200px;
    box-shadow: 0 8px 25px rgba(0,0,0,0.15);
    overflow: hidden;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.credit-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 12px 35px rgba(0,0,0,0.2);
}

.credit-card::before {
    content: '';
    position: absolute;
    top: -50%;
    right: -50%;
    width: 100%;
    height: 100%;
    background: radial-gradient(circle, rgba(255,255,255,0.1) 0%, transparent 70%);
    border-radius: 50%;
}

.visa-card {
    background: linear-gradient(135deg, #1a237e 0%, #3949ab 100%);
}

.mastercard-card {
    background: linear-gradient(135deg, #ff6b35 0%, #f7931e 100%);
}

.card-logo {
    z-index: 2;
    position: relative;
}

.visa-logo {
    font-weight: bold;
    font-size: 1.2rem;
    letter-spacing: 2px;
    color: white;
    text-shadow: 0 2px 4px rgba(0,0,0,0.3);
}

.mastercard-logo {
    font-weight: bold;
    font-size: 1rem;
    letter-spacing: 1px;
    color: white;
    text-shadow: 0 2px 4px rgba(0,0,0,0.3);
}

.generic-logo {
    font-weight: bold;
    font-size: 1rem;
    letter-spacing: 1px;
    color: white;
    text-shadow: 0 2px 4px rgba(0,0,0,0.3);
}

.card-number {
    font-family: 'Courier New', monospace;
    font-size: 1.4rem;
    font-weight: bold;
    letter-spacing: 3px;
    margin: 20px 0;
    text-shadow: 0 2px 4px rgba(0,0,0,0.3);
}

.card-label {
    font-size: 0.7rem;
    letter-spacing: 1px;
    opacity: 0.8;
    text-transform: uppercase;
}

.card-holder-name {
    font-size: 0.9rem;
    font-weight: 600;
    letter-spacing: 1px;
    text-shadow: 0 1px 2px rgba(0,0,0,0.3);
}

.card-expiry-date {
    font-family: 'Courier New', monospace;
    font-size: 0.9rem;
    font-weight: 600;
    text-shadow: 0 1px 2px rgba(0,0,0,0.3);
}

.card-balance-section {
    margin-top: 15px;
    padding-top: 15px;
    border-top: 1px solid rgba(255,255,255,0.2);
}

.card-status .badge {
    font-size: 0.7rem;
    padding: 4px 8px;
}
</style>

<!-- Breadcrumb -->
<nav aria-label="breadcrumb" style="margin-bottom: 1rem;">
    <ol class="breadcrumb">
        <li class="breadcrumb-item"><a href="index.php">Admin</a></li>
        <li class="breadcrumb-item"><a href="users.php">Users</a></li>
        <li class="breadcrumb-item active" aria-current="page">User Details</li>
    </ol>
</nav>

<!-- Page Header -->
<div class="page-header d-print-none mb-4">
    <div class="row g-2 align-items-center">
            <div class="col">
                <div class="d-flex align-items-center">
                    <span class="avatar avatar-lg me-3" style="background: linear-gradient(135deg, #206bc4, #1a5490);">
                        <?php echo strtoupper(substr($user['first_name'], 0, 1) . substr($user['last_name'], 0, 1)); ?>
                    </span>
                    <div>
                        <h2 class="page-title mb-1">
                            <?php echo htmlspecialchars($user['first_name'] . ' ' . $user['last_name']); ?>
                        </h2>
                        <div class="text-muted">
                            <span class="me-3">@<?php echo htmlspecialchars($user['username']); ?></span>
                            <span class="me-3"><?php echo htmlspecialchars($user['account_number']); ?></span>
                            <span class="badge bg-<?php echo $user['status'] === 'active' ? 'green' : ($user['status'] === 'suspended' ? 'red' : 'gray'); ?>-lt">
                                <?php echo ucfirst($user['status']); ?>
                            </span>
                            <?php if (!empty($cheque_deposits) || $cheque_documents_count > 0): ?>
                            <span class="badge bg-warning-lt ms-2">
                                <i class="fas fa-money-check me-1"></i>
                                Has Cheques (<?php echo count($cheque_deposits) + $cheque_documents_count; ?>)
                            </span>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-auto ms-auto d-print-none">
                <div class="btn-list">
                    <a href="edit-user.php?id=<?php echo $user['id']; ?>" class="btn btn-primary">
                        <i class="fas fa-edit me-2"></i>
                        Edit User
                    </a>
                    <a href="users.php" class="btn btn-secondary">
                        <i class="fas fa-arrow-left me-2"></i>
                        Back to Users
                    </a>
                </div>
            </div>
        </div>
</div>

<!-- User Overview Cards -->
<div class="row row-cards mb-4">
    <div class="col-sm-6 col-lg-3">
        <div class="card card-sm">
            <div class="card-body">
                <div class="row align-items-center">
                    <div class="col-auto">
                        <span class="bg-primary text-white avatar">
                            <i class="fas fa-wallet"></i>
                        </span>
                    </div>
                    <div class="col">
                        <div class="font-weight-medium">
                            <?php echo formatCurrency($user['balance'], $user['currency']); ?>
                        </div>
                        <div class="text-muted">Current Balance</div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-sm-6 col-lg-3">
        <div class="card card-sm">
            <div class="card-body">
                <div class="row align-items-center">
                    <div class="col-auto">
                        <span class="bg-success text-white avatar">
                            <i class="fas fa-arrow-up"></i>
                        </span>
                    </div>
                    <div class="col">
                        <div class="font-weight-medium">
                            <?php echo formatCurrency($user_stats['total_credited'] ?? 0, $user['currency']); ?>
                        </div>
                        <div class="text-muted">Total Credited</div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-sm-6 col-lg-3">
        <div class="card card-sm">
            <div class="card-body">
                <div class="row align-items-center">
                    <div class="col-auto">
                        <span class="bg-danger text-white avatar">
                            <i class="fas fa-arrow-down"></i>
                        </span>
                    </div>
                    <div class="col">
                        <div class="font-weight-medium">
                            <?php echo formatCurrency($user_stats['total_debited'] ?? 0, $user['currency']); ?>
                        </div>
                        <div class="text-muted">Total Debited</div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-sm-6 col-lg-3">
        <div class="card card-sm">
            <div class="card-body">
                <div class="row align-items-center">
                    <div class="col-auto">
                        <span class="bg-<?php echo (!empty($cheque_deposits) || $cheque_documents_count > 0) ? 'warning' : 'info'; ?> text-white avatar">
                            <i class="fas fa-<?php echo (!empty($cheque_deposits) || $cheque_documents_count > 0) ? 'money-check' : 'chart-line'; ?>"></i>
                        </span>
                    </div>
                    <div class="col">
                        <div class="font-weight-medium">
                            <?php 
                            if (!empty($cheque_deposits) || $cheque_documents_count > 0) {
                                echo count($cheque_deposits) + $cheque_documents_count;
                            } else {
                                echo number_format($user_stats['monthly_transactions'] ?? 0);
                            }
                            ?>
                        </div>
                        <div class="text-muted">
                            <?php 
                            if (!empty($cheque_deposits) || $cheque_documents_count > 0) {
                                echo 'Cheque Activity';
                            } else {
                                echo 'Monthly Activity';
                            }
                            ?>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Personal and Account Information Section -->
<div class="row row-cards mb-4">
    <!-- Personal Information -->
    <div class="col-lg-6">
        <div class="card h-100">
            <div class="card-header border-0" style="background: var(--card-header-gradient, linear-gradient(135deg, var(--primary-color, #206bc4) 0%, var(--primary-dark, #1a5490) 100%));">
                <h3 class="card-title text-white mb-0">
                    <i class="fas fa-user me-2"></i>
                    Personal Information
                </h3>
            </div>
            <div class="card-body p-0">
                <div class="table-responsive">
                    <table class="table table-borderless mb-0">
                        <tbody>
                            <tr style="background-color: #f8f9fa;">
                                <td class="fw-medium text-muted py-3 px-3" style="width: 30%;">
                                    <i class="fas fa-user me-2 text-primary"></i>Full Name:
                                </td>
                                <td class="py-3 px-3"><?php echo htmlspecialchars($user['first_name'] . ' ' . $user['last_name']); ?></td>
                            </tr>
                            <tr>
                                <td class="fw-medium text-muted py-3 px-3">
                                    <i class="fas fa-envelope me-2 text-primary"></i>Email:
                                </td>
                                <td class="py-3 px-3"><?php echo htmlspecialchars($user['email']); ?></td>
                            </tr>
                            <tr style="background-color: #f8f9fa;">
                                <td class="fw-medium text-muted py-3 px-3">
                                    <i class="fas fa-phone me-2 text-primary"></i>Phone:
                                </td>
                                <td class="py-3 px-3"><?php echo htmlspecialchars($user['phone'] ?? 'Not provided'); ?></td>
                            </tr>
                            <tr>
                                <td class="fw-medium text-muted py-3 px-3">
                                    <i class="fas fa-birthday-cake me-2 text-primary"></i>Date of Birth:
                                </td>
                                <td class="py-3 px-3"><?php echo $user['date_of_birth'] ? formatDate($user['date_of_birth'], 'M j, Y') : 'Not provided'; ?></td>
                            </tr>
                            <tr style="background-color: #f8f9fa;">
                                <td class="fw-medium text-muted py-3 px-3">
                                    <i class="fas fa-venus-mars me-2 text-primary"></i>Gender:
                                </td>
                                <td class="py-3 px-3"><?php echo ucfirst($user['gender'] ?? 'Not specified'); ?></td>
                            </tr>
                            <tr>
                                <td class="fw-medium text-muted py-3 px-3">
                                    <i class="fas fa-heart me-2 text-primary"></i>Marital Status:
                                </td>
                                <td class="py-3 px-3"><?php echo ucfirst($user['marital_status'] ?? 'Not specified'); ?></td>
                            </tr>
                            <tr style="background-color: #f8f9fa;">
                                <td class="fw-medium text-muted py-3 px-3">
                                    <i class="fas fa-briefcase me-2 text-primary"></i>Occupation:
                                </td>
                                <td class="py-3 px-3"><?php echo htmlspecialchars($user['occupation'] ?? 'Not provided'); ?></td>
                            </tr>
                            <tr>
                                <td class="fw-medium text-muted py-3 px-3">
                                    <i class="fas fa-id-card me-2 text-primary"></i>KYC Status:
                                </td>
                                <td class="py-3 px-3">
                                    <span class="badge bg-<?php echo $user['kyc_status'] === 'verified' ? 'success' : ($user['kyc_status'] === 'rejected' ? 'danger' : 'warning'); ?>">
                                        <?php echo ucfirst($user['kyc_status']); ?>
                                    </span>
                                </td>
                            </tr>
                            <?php if (!empty($user['address'])): ?>
                            <tr style="background-color: #f8f9fa;">
                                <td class="fw-medium text-muted py-3 px-3">
                                    <i class="fas fa-map-marker-alt me-2 text-primary"></i>Address:
                                </td>
                                <td class="py-3 px-3"><?php echo htmlspecialchars($user['address']); ?></td>
                            </tr>
                            <?php endif; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <!-- Account Information -->
    <div class="col-lg-6">
        <div class="card h-100">
            <div class="card-header border-0" style="background: var(--card-header-gradient, linear-gradient(135deg, var(--primary-color, #206bc4) 0%, var(--primary-dark, #1a5490) 100%));">
                <h3 class="card-title text-white mb-0">
                    <i class="fas fa-university me-2"></i>
                    Account Information
                </h3>
            </div>
            <div class="card-body p-0">
                <div class="table-responsive">
                    <table class="table table-borderless mb-0">
                        <tbody>
                            <tr style="background-color: #f8f9fa;">
                                <td class="fw-medium text-muted py-3 px-3" style="width: 40%;">
                                    <i class="fas fa-hashtag me-2 text-primary"></i>Account Number:
                                </td>
                                <td class="py-3 px-3">
                                    <span class="font-monospace" title="Click to copy" onclick="copyToClipboard('<?php echo htmlspecialchars($user['account_number']); ?>')"><?php echo htmlspecialchars($user['account_number']); ?></span>
                                </td>
                            </tr>
                            <tr>
                                <td class="fw-medium text-muted py-3 px-3">
                                    <i class="fas fa-piggy-bank me-2 text-primary"></i>Account Type:
                                </td>
                                <td class="py-3 px-3"><?php echo ucfirst($user['account_type']); ?></td>
                            </tr>
                            <tr style="background-color: #f8f9fa;">
                                <td class="fw-medium text-muted py-3 px-3">
                                    <i class="fas fa-dollar-sign me-2 text-primary"></i>Currency:
                                </td>
                                <td class="py-3 px-3"><?php echo $user['currency'] ?? 'USD'; ?></td>
                            </tr>
                            <tr>
                                <td class="fw-medium text-muted py-3 px-3">
                                    <i class="fas fa-wallet me-2 text-primary"></i>Current Balance:
                                </td>
                                <td class="py-3 px-3">
                                    <strong class="text-primary"><?php echo formatCurrency($user['balance'], $user['currency']); ?></strong>
                                </td>
                            </tr>
                            <tr style="background-color: #f8f9fa;">
                                <td class="fw-medium text-muted py-3 px-3">
                                    <i class="fas fa-toggle-on me-2 text-primary"></i>Account Status:
                                </td>
                                <td class="py-3 px-3">
                                    <span class="badge bg-<?php echo $user['status'] === 'active' ? 'success' : ($user['status'] === 'suspended' ? 'danger' : 'secondary'); ?>">
                                        <?php echo ucfirst($user['status']); ?>
                                    </span>
                                </td>
                            </tr>
                            <tr>
                                <td class="fw-medium text-muted py-3 px-3">
                                    <i class="fas fa-calendar-plus me-2 text-primary"></i>Joined:
                                </td>
                                <td class="py-3 px-3"><?php echo formatDate($user['created_at'], 'M j, Y g:i A'); ?></td>
                            </tr>
                            <tr style="background-color: #f8f9fa;">
                                <td class="fw-medium text-muted py-3 px-3">
                                    <i class="fas fa-sign-in-alt me-2 text-primary"></i>Last Login:
                                </td>
                                <td class="py-3 px-3">
                                    <?php echo $user['last_login'] ? formatDate($user['last_login'], 'M j, Y g:i A') : 'Never'; ?>
                                </td>
                            </tr>
                            <tr>
                                <td class="fw-medium text-muted py-3 px-3">
                                    <i class="fas fa-edit me-2 text-primary"></i>Last Updated:
                                </td>
                                <td class="py-3 px-3"><?php echo formatDate($user['updated_at'], 'M j, Y g:i A'); ?></td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Security & Authentication Section - Modern Design -->
<div class="row row-cards mb-4">
    <div class="col-12">
        <div class="card border-0 shadow-sm">
            <div class="card-header border-0" style="background: var(--card-header-gradient, linear-gradient(135deg, var(--primary-color, #206bc4) 0%, var(--primary-dark, #1a5490) 100%));">
                <div class="d-flex align-items-center">
                    <div class="avatar avatar-sm bg-white bg-opacity-20 me-3">
                        <i class="fas fa-shield-alt text-white"></i>
                    </div>
                    <div>
                        <h3 class="card-title mb-0 text-white">Security & Authentication</h3>
                        <small class="text-white-50">Manage user authentication and security settings</small>
                    </div>
                </div>
            </div>
            <div class="card-body p-0">
                <!-- Current OTP Status -->
                <?php
                $current_otp = null;
                foreach ($otp_history as $otp) {
                    if (!$otp['used'] && strtotime($otp['expires_at']) > time()) {
                        $current_otp = $otp;
                        break;
                    }
                }
                ?>

                <div class="row g-0">
                    <!-- OTP Management Section -->
                    <div class="col-lg-8 border-end">
                        <div class="p-4">
                            <?php if ($current_otp): ?>
                            <!-- Active OTP Display -->
                            <div class="security-card active-otp mb-4">
                                <div class="security-card-header">
                                    <div class="d-flex align-items-center">
                                        <div class="security-icon bg-success">
                                            <i class="fas fa-key"></i>
                                        </div>
                                        <div class="ms-3">
                                            <h5 class="mb-1">Active OTP Code</h5>
                                            <p class="text-muted mb-0">Ready for user authentication</p>
                                        </div>
                                        <div class="ms-auto">
                                            <span class="badge bg-success-subtle text-success">Active</span>
                                        </div>
                                    </div>
                                </div>
                                <div class="security-card-body">
                                    <div class="otp-display">
                                        <div class="otp-code-container">
                                            <label class="form-label text-muted small">OTP Code</label>
                                            <div class="otp-code" onclick="copyOTPCode('<?php echo htmlspecialchars($current_otp['otp_code']); ?>')">
                                                <span class="otp-digits"><?php echo htmlspecialchars($current_otp['otp_code']); ?></span>
                                                <i class="fas fa-copy otp-copy-icon"></i>
                                            </div>
                                        </div>
                                        <div class="otp-meta">
                                            <div class="row g-3">
                                                <div class="col-6">
                                                    <div class="meta-item">
                                                        <i class="fas fa-clock text-warning"></i>
                                                        <span>Expires: <?php echo formatDate($current_otp['expires_at'], 'g:i A'); ?></span>
                                                    </div>
                                                </div>
                                                <div class="col-6">
                                                    <div class="meta-item">
                                                        <i class="fas fa-user-shield text-info"></i>
                                                        <span>Source: <?php echo ucfirst($current_otp['source'] ?? 'login'); ?></span>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="alert alert-info-subtle border-0 mt-3">
                                        <i class="fas fa-info-circle me-2"></i>
                                        <strong>Admin Note:</strong> Provide this code to the user if they cannot access their email for login verification.
                                    </div>
                                </div>
                            </div>
                            <?php else: ?>
                            <!-- No Active OTP -->
                            <div class="security-card no-otp mb-4">
                                <div class="security-card-header">
                                    <div class="d-flex align-items-center">
                                        <div class="security-icon bg-secondary">
                                            <i class="fas fa-key-skeleton"></i>
                                        </div>
                                        <div class="ms-3">
                                            <h5 class="mb-1">No Active OTP</h5>
                                            <p class="text-muted mb-0">Generate a new OTP for user support</p>
                                        </div>
                                        <div class="ms-auto">
                                            <span class="badge bg-secondary-subtle text-secondary">Inactive</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <?php endif; ?>

                            <!-- OTP Actions -->
                            <div class="security-actions">
                                <div class="row g-3">
                                    <div class="col-md-6">
                                        <div class="action-card" onclick="generateOTPForUser(<?php echo $user['id']; ?>)"
                                             style="background: #fff; border: 1px solid #e9ecef; border-radius: 10px; padding: 1.25rem; cursor: pointer; transition: all 0.3s ease; height: 100%; display: flex; align-items: center; gap: 1rem;">
                                            <div class="action-icon" style="width: 36px; height: 36px; border-radius: 8px; display: flex; align-items: center; justify-content: center; color: white; font-size: 1rem; flex-shrink: 0; background: var(--card-gradient, linear-gradient(135deg, var(--primary-color, #206bc4) 0%, var(--primary-dark, #1a5490) 100%));">
                                                <i class="fas fa-plus"></i>
                                            </div>
                                            <div class="action-content">
                                                <h6 style="margin: 0 0 0.25rem; font-weight: 600; color: #212529;">Generate OTP</h6>
                                                <p style="margin: 0; font-size: 0.875rem; color: #6c757d;">Create new verification code</p>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="action-card" onclick="showOTPHistory(<?php echo $user['id']; ?>)"
                                             style="background: #fff; border: 1px solid #e9ecef; border-radius: 10px; padding: 1.25rem; cursor: pointer; transition: all 0.3s ease; height: 100%; display: flex; align-items: center; gap: 1rem;">
                                            <div class="action-icon" style="width: 36px; height: 36px; border-radius: 8px; display: flex; align-items: center; justify-content: center; color: white; font-size: 1rem; flex-shrink: 0; background: var(--card-gradient, linear-gradient(135deg, var(--primary-color, #206bc4) 0%, var(--primary-dark, #1a5490) 100%));">
                                                <i class="fas fa-history"></i>
                                            </div>
                                            <div class="action-content">
                                                <h6 style="margin: 0 0 0.25rem; font-weight: 600; color: #212529;">OTP History</h6>
                                                <p style="margin: 0; font-size: 0.875rem; color: #6c757d;">View recent codes</p>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Login Activity Section -->
                    <div class="col-lg-4">
                        <div class="p-4">
                            <div class="d-flex align-items-center mb-3">
                                <div class="security-icon me-3" style="width: 36px; height: 36px; border-radius: 8px; display: flex; align-items: center; justify-content: center; color: white; background: linear-gradient(135deg, #4f46e5 0%, #7c3aed 100%);">
                                    <i class="fas fa-sign-in-alt"></i>
                                </div>
                                <div>
                                    <h6 class="mb-0">Login Activity</h6>
                                    <small class="text-muted">Last 5 attempts</small>
                                </div>
                            </div>

                            <div class="login-activity-list">
                                <?php if (!empty($login_attempts)): ?>
                                    <?php foreach (array_slice($login_attempts, 0, 5) as $attempt): ?>
                                    <div class="login-attempt-item">
                                        <div class="d-flex align-items-center">
                                            <div class="attempt-status bg-<?php echo $attempt['success'] ? 'success' : 'danger'; ?>">
                                                <i class="fas fa-<?php echo $attempt['success'] ? 'check' : 'times'; ?>"></i>
                                            </div>
                                            <div class="attempt-details ms-3 flex-fill">
                                                <div class="attempt-result"><?php echo $attempt['success'] ? 'Successful Login' : 'Failed Login'; ?></div>
                                                <div class="attempt-time"><?php echo formatDate($attempt['attempted_at'], 'M j, g:i A'); ?></div>
                                            </div>
                                            <div class="attempt-ip">
                                                <small class="text-muted"><?php echo htmlspecialchars($attempt['ip_address']); ?></small>
                                            </div>
                                        </div>
                                    </div>
                                    <?php endforeach; ?>
                                <?php else: ?>
                                <div class="text-center py-4">
                                    <div class="empty-state">
                                        <i class="fas fa-info-circle text-muted mb-2"></i>
                                        <p class="text-muted mb-0">No login attempts recorded</p>
                                    </div>
                                </div>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Financial Information - Full Width Recent Transactions -->
<div class="row row-cards mt-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header border-0" style="background: linear-gradient(135deg, #4f46e5 0%, #7c3aed 100%);">
                <h3 class="card-title text-white mb-0">
                    <i class="fas fa-exchange-alt me-2"></i>
                    Recent Transactions
                </h3>
                <div class="card-actions">
                    <a href="transactions.php?user_id=<?php echo $user['id']; ?>" class="btn btn-sm btn-light">
                        View All
                    </a>
                </div>
            </div>
            <div class="card-body">
                <?php if (!empty($recent_transactions)): ?>
                <div class="table-responsive">
                    <table class="table table-vcenter">
                        <thead>
                            <tr>
                                <th>Date</th>
                                <th>Type</th>
                                <th>Amount</th>
                                <th>Description</th>
                                <th>Status</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($recent_transactions as $transaction): ?>
                            <?php
                            $type = $transaction['transaction_type'];
                            $rowClass = $type === 'credit' ? 'table-success-subtle' : 'table-dark-subtle';
                            ?>
                            <tr class="<?php echo $rowClass; ?>">
                                <td>
                                    <div><?php echo formatDate($transaction['created_at'], 'M j, Y'); ?></div>
                                    <small class="text-muted"><?php echo formatDate($transaction['created_at'], 'g:i A'); ?></small>
                                </td>
                                <td>
                                    <?php
                                    $source = $transaction['source'] ?? 'account';
                                    $type = $transaction['transaction_type'];
                                    $badge_color = $type === 'credit' ? 'success' : 'danger';
                                    ?>
                                    <span class="badge bg-<?php echo $badge_color; ?>-lt">
                                        <?php echo ucfirst($type); ?>
                                    </span>
                                    <?php if ($source !== 'account'): ?>
                                    <br><small class="text-muted">
                                        <?php if ($source === 'virtual_card'): ?>
                                        <i class="fas fa-credit-card me-1"></i>Card
                                        <?php elseif ($source === 'crypto'): ?>
                                        <i class="fab fa-bitcoin me-1"></i>Crypto
                                        <?php endif; ?>
                                    </small>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <span class="fw-bold <?php echo $type === 'credit' ? 'text-success' : 'text-danger'; ?>">
                                        <?php
                                        $currency = $transaction['currency'] ?? 'USD';
                                        if ($source === 'crypto' && !empty($transaction['crypto_type'])) {
                                            echo ($type === 'credit' ? '+' : '-') . number_format($transaction['amount'], 8) . ' ' . strtoupper($transaction['crypto_type']);
                                        } else {
                                            echo ($type === 'credit' ? '+' : '-') . formatCurrency($transaction['amount'], $currency);
                                        }
                                        ?>
                                    </span>
                                    <?php if ($source === 'virtual_card' && !empty($transaction['card_number'])): ?>
                                    <br><small class="text-muted">**** <?php echo substr($transaction['card_number'], -4); ?></small>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <div class="text-truncate" style="max-width: 200px;" title="<?php echo htmlspecialchars($transaction['description'] ?? ''); ?>">
                                        <?php echo htmlspecialchars($transaction['description'] ?? 'No description'); ?>
                                    </div>
                                    <?php if (!empty($transaction['reference_number'])): ?>
                                    <small class="text-muted font-monospace"><?php echo htmlspecialchars($transaction['reference_number']); ?></small>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <?php
                                    $status = $transaction['status'] ?? 'completed';
                                    $status_color = $status === 'completed' || $status === 'confirmed' ? 'success' : 'warning';
                                    ?>
                                    <span class="badge bg-<?php echo $status_color; ?>-lt">
                                        <?php echo ucfirst($status); ?>
                                    </span>
                                </td>
                            </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
                <?php else: ?>
                <div class="text-center text-muted py-4">
                    <i class="fas fa-receipt mb-2" style="font-size: 2rem;"></i>
                    <p>No transactions found</p>
                </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<!-- Virtual Cards & Crypto Accounts -->
<div class="row row-cards mt-4">
    <!-- Virtual Cards -->
    <div class="col-lg-7">
        <div class="card">
            <div class="card-header border-0" style="background: var(--card-header-gradient, linear-gradient(135deg, var(--primary-color, #206bc4) 0%, var(--primary-dark, #1a5490) 100%));">
                <h3 class="card-title text-white mb-0">
                    <i class="fas fa-credit-card me-2"></i>
                    Virtual Cards
                </h3>
            </div>
            <div class="card-body">
                <?php if (!empty($virtual_cards)): ?>
                <div class="row g-3">
                    <?php foreach ($virtual_cards as $index => $card): ?>
                    <div class="col-12">
                        <!-- Enhanced Credit Card with Flip Functionality -->
                        <div class="card-flip-container" style="perspective: 1000px; width: 340px; height: 215px; margin: 0 auto 20px;">
                            <div class="card-flip-inner" id="card-<?php echo $index; ?>" onclick="flipCard(<?php echo $index; ?>)" style="
                                position: relative;
                                width: 100%;
                                height: 100%;
                                text-align: center;
                                transition: transform 0.6s;
                                transform-style: preserve-3d;
                                cursor: pointer;
                            ">
                                <!-- Front of Card -->
                                <div class="card-front" style="
                                    position: absolute;
                                    width: 100%;
                                    height: 100%;
                                    backface-visibility: hidden;
                                    background: var(--card-gradient, linear-gradient(135deg, var(--primary-color, #206bc4) 0%, var(--primary-dark, #1a5490) 100%));
                                    border-radius: 16px;
                                    padding: 20px;
                                    color: white;
                                    box-shadow: 0 8px 25px rgba(32, 107, 196, 0.3);
                                    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
                                    overflow: hidden;
                                ">
                                    <!-- Card Background Pattern -->
                                    <div style="position: absolute; top: 0; left: 0; right: 0; bottom: 0; background: linear-gradient(45deg, rgba(255,255,255,0.1) 0%, transparent 50%); border-radius: 16px; pointer-events: none;"></div>

                                    <!-- Card Header -->
                                    <div class="d-flex justify-content-between align-items-start mb-3">
                                        <div class="card-logo">
                                            <?php
                                            $cardType = strtolower($card['card_type'] ?? 'visa');
                                            if ($cardType === 'visa'): ?>
                                                <img src="../icon/visa.png" alt="Visa" style="height: 24px; filter: brightness(0) invert(1);">
                                            <?php elseif ($cardType === 'mastercard'): ?>
                                                <img src="../icon/mastercard.png" alt="MasterCard" style="height: 24px;">
                                            <?php else: ?>
                                                <img src="../icon/visa.png" alt="Card" style="height: 24px; filter: brightness(0) invert(1);">
                                            <?php endif; ?>
                                        </div>
                                        <div class="card-status">
                                            <span class="badge bg-white text-primary px-2 py-1" style="font-size: 0.7rem; border-radius: 8px;">
                                                <?php echo ucfirst($card['status']); ?>
                                            </span>
                                        </div>
                                    </div>

                                    <!-- Chip -->
                                    <div class="mb-3">
                                        <img src="../icon/chip.png" alt="Chip" style="height: 32px; width: 40px;">
                                    </div>

                                    <!-- Card Number -->
                                    <div class="card-number mb-3" style="font-family: 'Monaco', 'Menlo', monospace; font-size: 18px; font-weight: 600; letter-spacing: 2px;">
                                        <?php echo chunk_split($card['card_number'], 4, ' '); ?>
                                    </div>

                                    <!-- Card Footer -->
                                    <div class="d-flex justify-content-between align-items-end">
                                        <div>
                                            <div style="font-size: 8px; opacity: 0.8; margin-bottom: 2px;">CARD HOLDER</div>
                                            <div style="font-size: 12px; font-weight: 600; text-transform: uppercase;">
                                                <?php echo htmlspecialchars($card['card_holder_name']); ?>
                                            </div>
                                        </div>
                                        <div class="text-end">
                                            <div style="font-size: 8px; opacity: 0.8; margin-bottom: 2px;">EXPIRES</div>
                                            <div style="font-size: 12px; font-weight: 600;">
                                                <?php
                                                if (!empty($card['expiry_date'])) {
                                                    echo date('m/y', strtotime($card['expiry_date']));
                                                } else {
                                                    echo '--/--';
                                                }
                                                ?>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- Click to flip indicator -->
                                    <div style="position: absolute; bottom: 5px; right: 10px; font-size: 10px; opacity: 0.6;">
                                        Click to flip
                                    </div>
                                </div>

                                <!-- Back of Card -->
                                <div class="card-back" style="
                                    position: absolute;
                                    width: 100%;
                                    height: 100%;
                                    backface-visibility: hidden;
                                    background: var(--card-gradient, linear-gradient(135deg, var(--primary-color, #206bc4) 0%, var(--primary-dark, #1a5490) 100%));
                                    border-radius: 16px;
                                    color: white;
                                    box-shadow: 0 8px 25px rgba(32, 107, 196, 0.3);
                                    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
                                    transform: rotateY(180deg);
                                    overflow: hidden;
                                ">
                                    <!-- Magnetic Strip -->
                                    <div style="background: #000; height: 40px; margin: 20px 0; width: 100%;"></div>

                                    <!-- Signature Strip and CVV -->
                                    <div style="padding: 0 20px;">
                                        <div style="background: #fff; height: 30px; margin-bottom: 10px; border-radius: 4px; position: relative;">
                                            <div style="position: absolute; right: 10px; top: 50%; transform: translateY(-50%); background: #000; color: #fff; padding: 2px 8px; border-radius: 2px; font-size: 12px; font-weight: bold;">
                                                <?php echo $card['cvv'] ?? '***'; ?>
                                            </div>
                                        </div>

                                        <!-- Card Details -->
                                        <div style="font-size: 11px; line-height: 1.4; margin-top: 20px;">
                                            <div><strong>Card Number:</strong> <?php echo $card['card_number']; ?></div>
                                            <div><strong>Valid From:</strong> <?php echo date('m/y', strtotime($card['created_at'])); ?></div>
                                            <div><strong>Valid Thru:</strong> <?php echo date('m/y', strtotime($card['expiry_date'])); ?></div>
                                            <div><strong>CVV:</strong> <?php echo $card['cvv'] ?? '***'; ?></div>
                                        </div>

                                        <!-- Bank Info -->
                                        <div style="position: absolute; bottom: 15px; left: 20px; right: 20px; font-size: 10px; opacity: 0.8;">
                                            <div>PremierBank Pro</div>
                                            <div>Customer Service: 1-800-PREMIER</div>
                                        </div>
                                    </div>

                                    <!-- Click to flip indicator -->
                                    <div style="position: absolute; bottom: 5px; right: 10px; font-size: 10px; opacity: 0.6;">
                                        Click to flip
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Card Actions -->
                        <div class="text-center mt-2">
                            <small class="text-muted">
                                <i class="fas fa-info-circle me-1"></i>
                                Card Limit: $<?php echo number_format($card['card_limit'] ?? 5000, 2); ?> |
                                Balance: $<?php echo number_format($card['balance'] ?? 0, 2); ?>
                            </small>
                        </div>
                    </div>
                    <?php endforeach; ?>
                </div>
                <?php else: ?>
                <div class="text-center text-muted">
                    <i class="fas fa-credit-card mb-2"></i>
                    <p>No virtual cards</p>
                </div>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <!-- Crypto Accounts -->
    <div class="col-lg-5">
        <div class="card">
            <div class="card-header border-0" style="background: linear-gradient(135deg, #4f46e5 0%, #7c3aed 100%);">
                <h3 class="card-title text-white mb-0">
                    <i class="fab fa-bitcoin me-2"></i>
                    Cryptocurrency Accounts
                </h3>
            </div>
            <div class="card-body">
                <?php if (!empty($crypto_accounts)): ?>
                <div class="list-group list-group-flush">
                    <?php foreach ($crypto_accounts as $crypto): ?>
                    <div class="list-group-item">
                        <div class="d-flex justify-content-between align-items-start">
                            <div>
                                <div class="fw-bold">
                                    <?php
                                    // Handle both table structures
                                    $crypto_type = $crypto['cryptocurrency'] ?? $crypto['crypto_type'] ?? 'Unknown';
                                    echo strtoupper($crypto_type);
                                    ?>
                                </div>
                                <small class="text-muted font-monospace">
                                    <?php echo htmlspecialchars($crypto['wallet_address'] ?? 'No address'); ?>
                                </small>
                                <?php if (!empty($crypto['wallet_name'])): ?>
                                <div class="text-muted small"><?php echo htmlspecialchars($crypto['wallet_name']); ?></div>
                                <?php endif; ?>
                            </div>
                            <div class="text-end">
                                <div class="fw-bold">
                                    <?php
                                    $balance = $crypto['wallet_balance'] ?? $crypto['balance'] ?? 0;
                                    echo number_format($balance, 8);
                                    ?>
                                    <?php echo strtoupper($crypto_type); ?>
                                </div>
                                <?php if (!empty($crypto['usd_equivalent'])): ?>
                                <small class="text-muted">≈ $<?php echo number_format($crypto['usd_equivalent'], 2); ?></small>
                                <?php endif; ?>
                                <div>
                                    <span class="badge bg-<?php echo $crypto['status'] === 'active' ? 'success' : 'danger'; ?>-lt">
                                        <?php echo ucfirst($crypto['status']); ?>
                                    </span>
                                </div>
                            </div>
                        </div>
                    </div>
                    <?php endforeach; ?>
                </div>
                <?php else: ?>
                <div class="text-center text-muted">
                    <i class="fab fa-bitcoin mb-2"></i>
                    <p>No cryptocurrency accounts</p>
                </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<?php include 'includes/user-documents-section.php'; ?>

<!-- Beneficiaries Section -->
<div class="row row-cards mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header border-0" style="background: linear-gradient(135deg, #4f46e5 0%, #7c3aed 100%);">
                <h3 class="card-title text-white mb-0">
                    <i class="fas fa-users me-2"></i>
                    Beneficiaries
                </h3>
            </div>
            <div class="card-body">
                <?php if (!empty($beneficiaries)): ?>
                <div class="list-group list-group-flush">
                    <?php foreach ($beneficiaries as $beneficiary): ?>
                    <div class="list-group-item">
                        <div class="d-flex justify-content-between align-items-start">
                            <div>
                                <div class="fw-bold"><?php echo htmlspecialchars($beneficiary['name']); ?></div>
                                <small class="text-muted font-monospace"><?php echo htmlspecialchars($beneficiary['account_number']); ?></small>
                                <div class="text-muted"><?php echo htmlspecialchars($beneficiary['bank_name']); ?></div>
                            </div>
                            <span class="badge bg-success-lt">
                                Active
                            </span>
                        </div>
                    </div>
                    <?php endforeach; ?>
                </div>
                <?php else: ?>
                <div class="text-center text-muted py-4">
                    <i class="fas fa-users mb-2" style="font-size: 2rem;"></i>
                    <p>No beneficiaries added</p>
                </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<!-- Support & Communication -->
<?php if (!empty($support_tickets)): ?>
<div class="row row-cards mt-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header border-0" style="background: linear-gradient(135deg, #4f46e5 0%, #7c3aed 100%);">
                <h3 class="card-title text-white mb-0">
                    <i class="fas fa-headset me-2"></i>
                    Recent Support Tickets
                </h3>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-vcenter">
                        <thead>
                            <tr>
                                <th>Ticket ID</th>
                                <th>Subject</th>
                                <th>Priority</th>
                                <th>Status</th>
                                <th>Created</th>
                                <th>Last Updated</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($support_tickets as $ticket): ?>
                            <tr>
                                <td>
                                    <span class="font-monospace">#<?php echo $ticket['id']; ?></span>
                                </td>
                                <td>
                                    <div class="text-truncate" style="max-width: 250px;" title="<?php echo htmlspecialchars($ticket['subject']); ?>">
                                        <?php echo htmlspecialchars($ticket['subject']); ?>
                                    </div>
                                </td>
                                <td>
                                    <span class="badge bg-<?php echo $ticket['priority'] === 'high' ? 'danger' : ($ticket['priority'] === 'medium' ? 'warning' : 'info'); ?>-lt">
                                        <?php echo ucfirst($ticket['priority']); ?>
                                    </span>
                                </td>
                                <td>
                                    <span class="badge bg-<?php echo $ticket['status'] === 'closed' ? 'success' : ($ticket['status'] === 'open' ? 'danger' : 'warning'); ?>-lt">
                                        <?php echo ucfirst($ticket['status']); ?>
                                    </span>
                                </td>
                                <td>
                                    <div><?php echo formatDate($ticket['created_at'], 'M j, Y'); ?></div>
                                    <small class="text-muted"><?php echo formatDate($ticket['created_at'], 'g:i A'); ?></small>
                                </td>
                                <td>
                                    <div><?php echo formatDate($ticket['updated_at'], 'M j, Y'); ?></div>
                                    <small class="text-muted"><?php echo formatDate($ticket['updated_at'], 'g:i A'); ?></small>
                                </td>
                            </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>
<?php endif; ?>

<!-- OTP History Modal -->
<div class="modal modal-blur fade" id="otpHistoryModal" tabindex="-1" role="dialog" aria-hidden="true">
    <div class="modal-dialog modal-lg modal-dialog-centered" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">OTP History</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="table-responsive">
                    <table class="table table-vcenter">
                        <thead>
                            <tr>
                                <th>Code</th>
                                <th>Source</th>
                                <th>Created</th>
                                <th>Expires</th>
                                <th>Status</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($otp_history as $otp): ?>
                            <tr>
                                <td>
                                    <span class="font-monospace"><?php echo htmlspecialchars($otp['otp_code']); ?></span>
                                </td>
                                <td>
                                    <span class="badge bg-<?php echo $otp['source'] === 'admin' ? 'warning' : 'primary'; ?>-lt">
                                        <?php echo ucfirst($otp['source'] ?? 'login'); ?>
                                    </span>
                                </td>
                                <td>
                                    <div><?php echo formatDate($otp['created_at'], 'M j, Y'); ?></div>
                                    <small class="text-muted"><?php echo formatDate($otp['created_at'], 'g:i A'); ?></small>
                                </td>
                                <td>
                                    <div><?php echo formatDate($otp['expires_at'], 'M j, Y'); ?></div>
                                    <small class="text-muted"><?php echo formatDate($otp['expires_at'], 'g:i A'); ?></small>
                                </td>
                                <td>
                                    <?php if ($otp['used']): ?>
                                    <span class="badge bg-success-lt">Used</span>
                                    <?php elseif (strtotime($otp['expires_at']) < time()): ?>
                                    <span class="badge bg-danger-lt">Expired</span>
                                    <?php else: ?>
                                    <span class="badge bg-warning-lt">Active</span>
                                    <?php endif; ?>
                                </td>
                            </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
            </div>
        </div>
    </div>
</div>

<!-- Document Preview Modal -->
<div class="modal modal-blur fade" id="documentPreviewModal" tabindex="-1" role="dialog" aria-hidden="true">
    <div class="modal-dialog modal-lg modal-dialog-centered" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Document Preview</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body" id="documentPreviewContent">
                <!-- Document details will be loaded here -->
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
            </div>
        </div>
    </div>
</div>

<script>
// OTP Management Functions
// Copy OTP code with visual feedback
function copyOTPCode(otpCode) {
    navigator.clipboard.writeText(otpCode).then(function() {
        showToast('OTP code copied to clipboard!', 'success');

        // Add visual feedback to the OTP code element
        const otpElement = event.currentTarget;
        otpElement.style.transform = 'scale(1.05)';
        otpElement.style.borderColor = '#28a745';

        setTimeout(() => {
            otpElement.style.transform = '';
            otpElement.style.borderColor = '';
        }, 300);
    }).catch(function(err) {
        console.error('Could not copy text: ', err);
        showToast('Failed to copy OTP code', 'danger');
    });
}

// Debug function to check if buttons are working
function debugButtonClick(buttonName) {
    console.log('Button clicked:', buttonName);
    showToast('Button clicked: ' + buttonName, 'info');
}

// Add hover effects to action cards
document.addEventListener('DOMContentLoaded', function() {
    const actionCards = document.querySelectorAll('.action-card');

    actionCards.forEach(card => {
        card.addEventListener('mouseenter', function() {
            this.style.borderColor = '#0d6efd';
            this.style.boxShadow = '0 2px 8px rgba(13, 110, 253, 0.15)';
            this.style.transform = 'translateY(-1px)';
        });

        card.addEventListener('mouseleave', function() {
            this.style.borderColor = '#e9ecef';
            this.style.boxShadow = '';
            this.style.transform = '';
        });
    });

    console.log('Security & Authentication section initialized');
    console.log('Found action cards:', actionCards.length);
});

// Document Management Functions will be loaded from separate file

// viewDocument function will be loaded from separate file

// All document-related functions will be loaded from separate file

// Enhanced styling for better visual appeal
document.addEventListener('DOMContentLoaded', function() {
    // Add smooth animations to cards
    const cards = document.querySelectorAll('.card');
    cards.forEach((card, index) => {
        card.style.animationDelay = (index * 0.1) + 's';
        card.classList.add('fade-in');
    });

    // Add hover effects to list items
    const listItems = document.querySelectorAll('.list-group-item');
    listItems.forEach(item => {
        item.addEventListener('mouseenter', function() {
            this.style.backgroundColor = '#f8f9fa';
        });
        item.addEventListener('mouseleave', function() {
            this.style.backgroundColor = '';
        });
    });
});

// Copy to clipboard function
function copyToClipboard(text) {
    navigator.clipboard.writeText(text).then(function() {
        showToast('Copied to clipboard!', 'success');
    }).catch(function(err) {
        console.error('Could not copy text: ', err);
        // Fallback for older browsers
        const textArea = document.createElement('textarea');
        textArea.value = text;
        document.body.appendChild(textArea);
        textArea.select();
        document.execCommand('copy');
        document.body.removeChild(textArea);
        showToast('Copied to clipboard!', 'success');
    });
}

// Show toast notification
function showToast(message, type = 'info') {
    const toast = document.createElement('div');
    toast.className = `alert alert-${type} position-fixed`;
    toast.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 250px; animation: slideInRight 0.3s ease;';
    toast.innerHTML = `<i class="fas fa-${type === 'success' ? 'check' : 'info'}-circle me-2"></i>${message}`;
    document.body.appendChild(toast);

    setTimeout(() => {
        toast.style.animation = 'slideOutRight 0.3s ease';
        setTimeout(() => toast.remove(), 300);
    }, 3000);
}

// Generate OTP for user with modern modal
function generateOTPForUser(userId) {
    // Show loading state
    const button = event.target;
    const originalText = button.innerHTML;
    button.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>Generating...';
    button.disabled = true;

    fetch('ajax/generate-otp.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: `user_id=${userId}`
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showModernOTPModal(data);
            // Update the page content without full reload
            updateOTPDisplay(data);
        } else {
            showToast(data.message || 'Failed to generate OTP', 'danger');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showToast('An error occurred while generating OTP', 'danger');
    })
    .finally(() => {
        // Restore button state
        button.innerHTML = originalText;
        button.disabled = false;
    });
}

// Show OTP modal
function showOTPModal(data) {
    const modal = document.getElementById('otpModal');
    const modalBody = modal.querySelector('.modal-body');

    modalBody.innerHTML = `
        <div class="text-center mb-4">
            <div class="avatar avatar-xl bg-success text-white mb-3">
                <i class="fas fa-key"></i>
            </div>
            <h4>OTP Generated Successfully</h4>
            <p class="text-muted">New OTP code has been generated for ${data.user_name}</p>
        </div>

        <div class="card bg-light">
            <div class="card-body text-center">
                <label class="form-label">OTP Code</label>
                <div class="input-group input-group-lg">
                    <input type="text" class="form-control text-center font-monospace"
                           value="${data.otp_code}" readonly id="otpCodeInput">
                    <button class="btn btn-primary" onclick="copyToClipboard('${data.otp_code}')">
                        <i class="fas fa-copy"></i>
                    </button>
                </div>
                <small class="text-muted">Expires in ${data.expires_in}</small>
            </div>
        </div>

        <div class="mt-3">
            <div class="row">
                <div class="col-6">
                    <strong>Email Status:</strong>
                </div>
                <div class="col-6">
                    <span class="badge bg-${data.email_sent ? 'success' : 'warning'}">
                        ${data.email_sent ? 'Sent' : 'Failed'}
                    </span>
                </div>
            </div>
            <div class="row">
                <div class="col-6">
                    <strong>User Email:</strong>
                </div>
                <div class="col-6">
                    <small class="text-muted">${data.user_email}</small>
                </div>
            </div>
        </div>

        <div class="alert alert-info mt-3">
            <i class="fas fa-info-circle me-2"></i>
            <strong>Instructions:</strong> Provide this OTP code to the user if they cannot access their email for login verification.
        </div>
    `;

    const bootstrapModal = new bootstrap.Modal(modal);
    bootstrapModal.show();
}

// Show modern OTP modal
function showModernOTPModal(data) {
    const modal = document.getElementById('modernOTPModal');
    const modalBody = modal.querySelector('.modal-body');

    modalBody.innerHTML = `
        <div class="text-center mb-4">
            <div class="otp-success-icon">
                <i class="fas fa-check-circle"></i>
            </div>
            <h4 class="mt-3">OTP Generated Successfully</h4>
            <p class="text-muted">New verification code created for ${data.user_name}</p>
        </div>

        <div class="otp-code-display">
            <label class="form-label text-center d-block">Verification Code</label>
            <div class="otp-code-container" onclick="copyToClipboard('${data.otp_code}')">
                <span class="otp-digits">${data.otp_code}</span>
                <i class="fas fa-copy otp-copy-icon"></i>
            </div>
            <small class="text-muted d-block text-center mt-2">Click to copy • Expires in ${data.expires_in}</small>
        </div>

        <div class="otp-details mt-4">
            <div class="row g-3">
                <div class="col-6">
                    <div class="detail-item">
                        <i class="fas fa-envelope ${data.email_sent ? 'text-success' : 'text-warning'}"></i>
                        <span>Email ${data.email_sent ? 'Sent' : 'Failed'}</span>
                    </div>
                </div>
                <div class="col-6">
                    <div class="detail-item">
                        <i class="fas fa-clock text-info"></i>
                        <span>10 minutes</span>
                    </div>
                </div>
            </div>
        </div>

        <div class="alert alert-info-modern mt-4">
            <i class="fas fa-lightbulb me-2"></i>
            <strong>Admin Tip:</strong> Share this code with the user if they can't access their email for login verification.
        </div>
    `;

    const bootstrapModal = new bootstrap.Modal(modal);
    bootstrapModal.show();
}

// Update OTP display in the main interface
function updateOTPDisplay(data) {
    // This would update the OTP section without page reload
    setTimeout(() => {
        location.reload();
    }, 3000); // Reload after 3 seconds to show the new OTP
}

// Show OTP History with working functionality
function showOTPHistory(userId) {
    const modal = document.getElementById('otpHistoryModal');
    const modalBody = modal.querySelector('.modal-body');

    // Show loading state
    modalBody.innerHTML = `
        <div class="text-center py-4">
            <div class="spinner-border text-primary" role="status">
                <span class="visually-hidden">Loading...</span>
            </div>
            <p class="mt-2 text-muted">Loading OTP history...</p>
        </div>
    `;

    const bootstrapModal = new bootstrap.Modal(modal);
    bootstrapModal.show();

    // Fetch OTP history
    fetch(`ajax/get-otp-history.php?user_id=${userId}`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                displayOTPHistory(data.history);
            } else {
                modalBody.innerHTML = `
                    <div class="text-center py-4">
                        <i class="fas fa-exclamation-triangle text-warning mb-3" style="font-size: 2rem;"></i>
                        <p class="text-muted">${data.message || 'Failed to load OTP history'}</p>
                    </div>
                `;
            }
        })
        .catch(error => {
            console.error('Error:', error);
            modalBody.innerHTML = `
                <div class="text-center py-4">
                    <i class="fas fa-times-circle text-danger mb-3" style="font-size: 2rem;"></i>
                    <p class="text-muted">Error loading OTP history</p>
                </div>
            `;
        });
}

// Display OTP history in modal
function displayOTPHistory(history) {
    const modalBody = document.querySelector('#otpHistoryModal .modal-body');

    if (!history || history.length === 0) {
        modalBody.innerHTML = `
            <div class="text-center py-4">
                <i class="fas fa-history text-muted mb-3" style="font-size: 2rem;"></i>
                <p class="text-muted">No OTP history found</p>
            </div>
        `;
        return;
    }

    let historyHTML = `
        <div class="otp-history-list">
    `;

    history.forEach(otp => {
        const isActive = !otp.used && new Date(otp.expires_at) > new Date();
        const isExpired = !otp.used && new Date(otp.expires_at) <= new Date();

        let statusClass = 'success';
        let statusText = 'Used';
        let statusIcon = 'check';

        if (isActive) {
            statusClass = 'warning';
            statusText = 'Active';
            statusIcon = 'clock';
        } else if (isExpired) {
            statusClass = 'danger';
            statusText = 'Expired';
            statusIcon = 'times';
        }

        historyHTML += `
            <div class="otp-history-item">
                <div class="d-flex align-items-center">
                    <div class="otp-status bg-${statusClass}">
                        <i class="fas fa-${statusIcon}"></i>
                    </div>
                    <div class="otp-info ms-3 flex-fill">
                        <div class="otp-code-small">${otp.otp_code}</div>
                        <div class="otp-meta">
                            <span class="me-3"><i class="fas fa-calendar me-1"></i>${formatDate(otp.created_at)}</span>
                            <span class="me-3"><i class="fas fa-user-tag me-1"></i>${otp.source || 'login'}</span>
                        </div>
                    </div>
                    <div class="otp-status-badge">
                        <span class="badge bg-${statusClass}-subtle text-${statusClass}">${statusText}</span>
                    </div>
                </div>
            </div>
        `;
    });

    historyHTML += `</div>`;
    modalBody.innerHTML = historyHTML;
}

// Helper function to format dates
function formatDate(dateString) {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
        month: 'short',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit'
    });
}
</script>

<!-- Modern OTP Modal -->
<div class="modal fade" id="modernOTPModal" tabindex="-1" aria-labelledby="modernOTPModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content border-0 shadow-lg">
            <div class="modal-header border-0 pb-0">
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body px-4 pb-4">
                <!-- Content will be populated by JavaScript -->
            </div>
        </div>
    </div>
</div>

<!-- OTP History Modal -->
<div class="modal fade" id="otpHistoryModal" tabindex="-1" aria-labelledby="otpHistoryModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg modal-dialog-centered">
        <div class="modal-content border-0 shadow-lg">
            <div class="modal-header bg-gradient-primary text-white">
                <h5 class="modal-title text-white" id="otpHistoryModalLabel">
                    <i class="fas fa-history me-2"></i>OTP History
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body p-0">
                <!-- Content will be populated by JavaScript -->
            </div>
            <div class="modal-footer border-0">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
            </div>
        </div>
    </div>
</div>

<style>
/* Modern Modal Styles */
.otp-success-icon {
    width: 80px;
    height: 80px;
    background: linear-gradient(135deg, #28a745, #20c997);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto;
    color: white;
    font-size: 2rem;
    animation: successPulse 0.6s ease-out;
}

@keyframes successPulse {
    0% { transform: scale(0); opacity: 0; }
    50% { transform: scale(1.1); opacity: 0.8; }
    100% { transform: scale(1); opacity: 1; }
}

.otp-code-display {
    background: linear-gradient(135deg, #f8f9fa, #e9ecef);
    border-radius: 16px;
    padding: 2rem;
    text-align: center;
}

.otp-code-container {
    background: white;
    border: 2px solid #e9ecef;
    border-radius: 12px;
    padding: 1.5rem;
    display: inline-flex;
    align-items: center;
    gap: 1rem;
    cursor: pointer;
    transition: all 0.3s ease;
    margin: 1rem 0;
}

.otp-code-container:hover {
    border-color: #0d6efd;
    box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.25);
    transform: scale(1.02);
}

.otp-digits {
    font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', monospace;
    font-size: 2rem;
    font-weight: 700;
    color: #0d6efd;
    letter-spacing: 0.3rem;
}

.otp-copy-icon {
    color: #6c757d;
    font-size: 1.2rem;
    transition: all 0.3s ease;
}

.otp-code-container:hover .otp-copy-icon {
    color: #0d6efd;
    transform: scale(1.1);
}

.otp-details {
    background: white;
    border-radius: 12px;
    padding: 1.5rem;
}

.detail-item {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    font-weight: 500;
    color: #495057;
}

.detail-item i {
    width: 20px;
    text-align: center;
    font-size: 1.1rem;
}

.alert-info-modern {
    background: linear-gradient(135deg, #e3f2fd, #bbdefb);
    border: none;
    border-radius: 12px;
    color: #1565c0;
    border-left: 4px solid #2196f3;
}

/* OTP History Styles */
.otp-history-list {
    max-height: 400px;
    overflow-y: auto;
    padding: 1rem;
}

.otp-history-item {
    background: white;
    border: 1px solid #e9ecef;
    border-radius: 12px;
    padding: 1rem;
    margin-bottom: 0.75rem;
    transition: all 0.3s ease;
}

.otp-history-item:hover {
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    transform: translateY(-1px);
}

.otp-status {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 0.875rem;
    flex-shrink: 0;
}

.otp-code-small {
    font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', monospace;
    font-weight: 600;
    font-size: 1.1rem;
    color: #495057;
    letter-spacing: 0.1rem;
}

.otp-meta {
    font-size: 0.875rem;
    color: #6c757d;
    margin-top: 0.25rem;
}

.otp-meta i {
    width: 14px;
    text-align: center;
}

.otp-status-badge .badge {
    font-size: 0.75rem;
    padding: 0.375rem 0.75rem;
    border-radius: 6px;
}

.bg-success-subtle { background-color: #d1e7dd !important; }
.bg-warning-subtle { background-color: #fff3cd !important; }
.bg-danger-subtle { background-color: #f8d7da !important; }
.text-success { color: #198754 !important; }
.text-warning { color: #f57c00 !important; }
.text-danger { color: #dc3545 !important; }

/* Animation Styles */
@keyframes slideInRight {
    from { transform: translateX(100%); opacity: 0; }
    to { transform: translateX(0); opacity: 1; }
}

@keyframes slideOutRight {
    from { transform: translateX(0); opacity: 1; }
    to { transform: translateX(100%); opacity: 0; }
}

@keyframes fadeInUp {
    from { transform: translateY(20px); opacity: 0; }
    to { transform: translateY(0); opacity: 1; }
}

.modal-content {
    animation: fadeInUp 0.3s ease-out;
}

/* Responsive Styles */
@media (max-width: 768px) {
    .otp-digits {
        font-size: 1.5rem;
        letter-spacing: 0.2rem;
    }

    .otp-code-container {
        padding: 1rem;
        flex-direction: column;
        gap: 0.75rem;
    }

    .otp-details {
        padding: 1rem;
    }

    .detail-item {
        justify-content: center;
        text-align: center;
    }
}
</style>

</script>

<!-- Include Document Management JavaScript -->
<script src="js/document-management.js"></script>

<!-- Card Flip Functionality -->
<script>
function flipCard(cardIndex) {
    const card = document.getElementById('card-' + cardIndex);
    if (card) {
        if (card.style.transform === 'rotateY(180deg)') {
            card.style.transform = 'rotateY(0deg)';
        } else {
            card.style.transform = 'rotateY(180deg)';
        }
    }
}

// Add hover effects for cards
document.addEventListener('DOMContentLoaded', function() {
    const cards = document.querySelectorAll('.card-flip-container');
    cards.forEach(function(card) {
        card.addEventListener('mouseenter', function() {
            this.style.transform = 'scale(1.02)';
            this.style.transition = 'transform 0.3s ease';
        });

        card.addEventListener('mouseleave', function() {
            this.style.transform = 'scale(1)';
        });
    });
});
</script>

<style>
/* Custom animations and styling */
@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.fade-in {
    animation: fadeIn 0.6s ease-out forwards;
}

/* Enhanced card styling */
.card {
    border: 1px solid #e9ecef;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
}

.card:hover {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    transform: translateY(-2px);
}

/* Better list group styling */
.list-group-item {
    border: none;
    border-bottom: 1px solid #f1f3f4;
    transition: background-color 0.2s ease;
}

.list-group-item:last-child {
    border-bottom: none;
}

/* Enhanced badge styling */
.badge {
    font-weight: 500;
    letter-spacing: 0.025em;
}

/* Better table styling */
.table th {
    font-weight: 600;
    color: #374151;
    border-bottom: 2px solid #e5e7eb;
}

/* Improved avatar gradient - using CSS variables */
.avatar {
    background: var(--card-header-gradient, linear-gradient(135deg, var(--primary-color, #206bc4) 0%, var(--primary-dark, #1a5490) 100%)) !important;
    color: white;
    font-weight: 600;
}

/* Stats Cards - Override hardcoded background colors with primary color */
.row-cards .card-sm .avatar.bg-primary,
.row-cards .card-sm .avatar.bg-success,
.row-cards .card-sm .avatar.bg-danger,
.row-cards .card-sm .avatar.bg-warning,
.row-cards .card-sm .avatar.bg-info {
    background: var(--primary-color, #206bc4) !important;
}

/* Enhanced alert styling */
.alert {
    border: none;
    border-radius: 8px;
}

/* Better modal styling */
.modal-content {
    border: none;
    border-radius: 12px;
    box-shadow: 0 10px 40px rgba(0, 0, 0, 0.1);
}

/* Responsive improvements */
@media (max-width: 768px) {
    .page-header .btn-list {
        flex-direction: column;
        gap: 0.5rem;
    }

    .page-header .btn-list .btn {
        width: 100%;
    }
}
</style>

<?php include 'includes/admin-footer.php'; ?>