<?php
/**
 * Get Latest OTP for Testing
 */

// Include database connection
require_once 'config/config.php';

try {
    $db = getDB();
    
    // Get the latest OTP for user ID 5 (<PERSON>)
    $otp_query = "SELECT otp_code, purpose, expires_at, created_at 
                  FROM user_otps 
                  WHERE user_id = 5 
                  AND (purpose = 'transfer' OR source = 'transfer')
                  AND expires_at > NOW()
                  ORDER BY created_at DESC 
                  LIMIT 1";
    
    $result = $db->query($otp_query);
    
    if ($result && $result->num_rows > 0) {
        $otp = $result->fetch_assoc();
        echo "<h1>🔐 Latest Transfer OTP</h1>";
        echo "<div style='font-family: monospace; font-size: 24px; background: #f0f0f0; padding: 20px; border: 2px solid #007bff; text-align: center;'>";
        echo "<strong>OTP CODE: " . $otp['otp_code'] . "</strong>";
        echo "</div>";
        echo "<p><strong>Purpose:</strong> " . $otp['purpose'] . "</p>";
        echo "<p><strong>Expires:</strong> " . $otp['expires_at'] . "</p>";
        echo "<p><strong>Created:</strong> " . $otp['created_at'] . "</p>";
    } else {
        echo "<h1>❌ No Valid OTP Found</h1>";
        echo "<p>No transfer OTP found for user ID 5 or OTP has expired.</p>";
    }
    
} catch (Exception $e) {
    echo "<h1>❌ Error</h1>";
    echo "<p>" . $e->getMessage() . "</p>";
}
?>
