/**
 * User Dashboard Redesign - Modern Banking Interface
 * Integrates with centralized color management system
 * Uses dynamic CSS variables from database
 */

/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', <PERSON><PERSON>, sans-serif;
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
    color: var(--text-primary, #1f2937);
    line-height: 1.6;
    overflow-x: hidden;
}

/* Dashboard Layout */
.dashboard-container {
    display: flex;
    min-height: 100vh;
    position: relative;
}

/* Sidebar remains as is - user loves it */
.banking-sidebar {
    width: 280px;
    background: linear-gradient(180deg, var(--primary-color, #206bc4) 0%, var(--primary-dark, #1a5490) 100%);
    color: white;
    position: fixed;
    height: 100vh;
    left: 0;
    top: 0;
    z-index: 1000;
    overflow-y: auto;
    box-shadow: 4px 0 20px rgba(0, 0, 0, 0.1);
}

/* Main Content Area - Completely Redesigned */
.main-content {
    margin-left: 280px;
    flex: 1;
    padding: 0;
    background: transparent;
    min-height: 100vh;
}

/* Header Section */
.dashboard-header {
    background: white;
    padding: 2rem 2.5rem;
    border-bottom: 1px solid var(--border-color, #e5e7eb);
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
    position: sticky;
    top: 0;
    z-index: 100;
}

.header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    max-width: 1400px;
    margin: 0 auto;
}

.welcome-section h1 {
    font-size: 2.5rem;
    font-weight: 700;
    color: var(--text-primary, #1f2937);
    margin-bottom: 0.5rem;
}

.welcome-section p {
    color: var(--text-secondary, #6b7280);
    font-size: 1.1rem;
}

.header-actions {
    display: flex;
    gap: 1rem;
    align-items: center;
}

.quick-action-btn {
    background: var(--primary-color, #206bc4);
    color: white;
    border: none;
    padding: 0.75rem 1.5rem;
    border-radius: 12px;
    font-weight: 600;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    transition: all 0.3s ease;
    box-shadow: 0 4px 12px rgba(32, 107, 196, 0.3);
}

.quick-action-btn:hover {
    background: var(--primary-hover, #1a5490);
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(32, 107, 196, 0.4);
    color: white;
    text-decoration: none;
}

/* Dashboard Grid */
.dashboard-grid {
    padding: 2.5rem;
    max-width: 1400px;
    margin: 0 auto;
    display: grid;
    grid-template-columns: 1fr 1fr 1fr;
    gap: 2rem;
    grid-template-areas: 
        "overview overview overview"
        "balance transactions cards"
        "stats stats profile"
        "activity activity activity";
}

/* Account Overview Section */
.account-overview {
    grid-area: overview;
    background: linear-gradient(135deg, var(--primary-color, #206bc4) 0%, var(--primary-dark, #1a5490) 100%);
    color: white;
    padding: 2.5rem;
    border-radius: 20px;
    box-shadow: 0 10px 30px rgba(32, 107, 196, 0.3);
    position: relative;
    overflow: hidden;
}

.account-overview::before {
    content: '';
    position: absolute;
    top: -50%;
    right: -50%;
    width: 200%;
    height: 200%;
    background: radial-gradient(circle, rgba(255, 255, 255, 0.1) 0%, transparent 70%);
    pointer-events: none;
}

.overview-content {
    position: relative;
    z-index: 2;
}

.account-balance {
    font-size: 3.5rem;
    font-weight: 800;
    margin-bottom: 0.5rem;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.account-info {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 1.5rem;
}

.account-details h3 {
    font-size: 1.2rem;
    margin-bottom: 0.5rem;
    opacity: 0.9;
}

.account-number {
    font-family: 'Monaco', 'Menlo', monospace;
    font-size: 1rem;
    opacity: 0.8;
}

/* Card Components */
.dashboard-card {
    background: white;
    border-radius: 16px;
    padding: 2rem;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    border: 1px solid var(--border-color, #e5e7eb);
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.dashboard-card:hover {
    transform: translateY(-4px);
    box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);
}

.dashboard-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, var(--primary-color, #206bc4), var(--accent-color, #10b981));
}

.card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1.5rem;
}

.card-title {
    font-size: 1.25rem;
    font-weight: 700;
    color: var(--text-primary, #1f2937);
}

.card-icon {
    width: 48px;
    height: 48px;
    background: linear-gradient(135deg, var(--primary-color, #206bc4), var(--primary-light, #4a8bc2));
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.5rem;
}

/* Balance Card */
.balance-card {
    grid-area: balance;
}

.balance-amount {
    font-size: 2.5rem;
    font-weight: 800;
    color: var(--primary-color, #206bc4);
    margin-bottom: 1rem;
}

.balance-change {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.9rem;
}

.balance-change.positive {
    color: var(--success-color, #10b981);
}

.balance-change.negative {
    color: var(--danger-color, #ef4444);
}

/* Transaction Card */
.transactions-card {
    grid-area: transactions;
}

.transaction-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem 0;
    border-bottom: 1px solid var(--border-light, #f3f4f6);
}

.transaction-item:last-child {
    border-bottom: none;
}

.transaction-info {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.transaction-icon {
    width: 40px;
    height: 40px;
    border-radius: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.2rem;
    color: white;
}

.transaction-icon.credit {
    background: linear-gradient(135deg, var(--success-color, #10b981), #059669);
}

.transaction-icon.debit {
    background: linear-gradient(135deg, var(--danger-color, #ef4444), #dc2626);
}

.transaction-details h4 {
    font-size: 0.95rem;
    font-weight: 600;
    color: var(--text-primary, #1f2937);
    margin-bottom: 0.25rem;
}

.transaction-details p {
    font-size: 0.85rem;
    color: var(--text-muted, #9ca3af);
}

.transaction-amount {
    font-weight: 700;
    font-size: 1rem;
}

.transaction-amount.credit {
    color: var(--success-color, #10b981);
}

.transaction-amount.debit {
    color: var(--danger-color, #ef4444);
}

/* Virtual Cards */
.cards-card {
    grid-area: cards;
}

.virtual-card-display {
    background: linear-gradient(135deg, var(--primary-color, #206bc4), var(--primary-dark, #1a5490));
    border-radius: 16px;
    padding: 1.5rem;
    color: white;
    margin-bottom: 1rem;
    position: relative;
    overflow: hidden;
}

.virtual-card-display::before {
    content: '';
    position: absolute;
    top: -50%;
    right: -30%;
    width: 100%;
    height: 200%;
    background: radial-gradient(circle, rgba(255, 255, 255, 0.1) 0%, transparent 70%);
}

.card-number {
    font-family: 'Monaco', 'Menlo', monospace;
    font-size: 1.1rem;
    letter-spacing: 2px;
    margin-bottom: 1rem;
}

.card-details {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.card-holder {
    font-size: 0.9rem;
    opacity: 0.9;
}

.card-expiry {
    font-size: 0.9rem;
    opacity: 0.9;
}

/* Statistics */
.stats-card {
    grid-area: stats;
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1.5rem;
}

.stat-item {
    text-align: center;
    padding: 1.5rem;
    background: var(--background-light, #f8fafc);
    border-radius: 12px;
    border: 1px solid var(--border-light, #f3f4f6);
}

.stat-value {
    font-size: 2rem;
    font-weight: 800;
    margin-bottom: 0.5rem;
}

.stat-value.credit {
    color: var(--success-color, #10b981);
}

.stat-value.debit {
    color: var(--danger-color, #ef4444);
}

.stat-label {
    font-size: 0.9rem;
    color: var(--text-muted, #9ca3af);
    font-weight: 600;
}

/* Profile Card */
.profile-card {
    grid-area: profile;
}

.profile-info {
    display: flex;
    align-items: center;
    gap: 1rem;
    margin-bottom: 1.5rem;
}

.profile-avatar {
    width: 60px;
    height: 60px;
    background: linear-gradient(135deg, var(--primary-color, #206bc4), var(--accent-color, #10b981));
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.5rem;
    font-weight: 700;
}

.profile-details h3 {
    font-size: 1.2rem;
    font-weight: 700;
    color: var(--text-primary, #1f2937);
    margin-bottom: 0.25rem;
}

.profile-details p {
    color: var(--text-muted, #9ca3af);
    font-size: 0.9rem;
}

.profile-stats {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1rem;
}

.profile-stat {
    text-align: center;
    padding: 1rem;
    background: var(--background-light, #f8fafc);
    border-radius: 8px;
}

.profile-stat-value {
    font-size: 1.2rem;
    font-weight: 700;
    color: var(--primary-color, #206bc4);
    margin-bottom: 0.25rem;
}

.profile-stat-label {
    font-size: 0.8rem;
    color: var(--text-muted, #9ca3af);
}

/* Activity Feed */
.activity-card {
    grid-area: activity;
}

.activity-item {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1rem 0;
    border-bottom: 1px solid var(--border-light, #f3f4f6);
}

.activity-item:last-child {
    border-bottom: none;
}

.activity-icon {
    width: 36px;
    height: 36px;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1rem;
    color: white;
    background: var(--primary-color, #206bc4);
}

.activity-content {
    flex: 1;
}

.activity-title {
    font-size: 0.95rem;
    font-weight: 600;
    color: var(--text-primary, #1f2937);
    margin-bottom: 0.25rem;
}

.activity-description {
    font-size: 0.85rem;
    color: var(--text-muted, #9ca3af);
}

.activity-time {
    font-size: 0.8rem;
    color: var(--text-muted, #9ca3af);
    white-space: nowrap;
}

/* Responsive Design */
@media (max-width: 1200px) {
    .dashboard-grid {
        grid-template-columns: 1fr 1fr;
        grid-template-areas: 
            "overview overview"
            "balance transactions"
            "cards stats"
            "profile profile"
            "activity activity";
    }
}

@media (max-width: 768px) {
    .main-content {
        margin-left: 0;
    }
    
    .banking-sidebar {
        transform: translateX(-100%);
        transition: transform 0.3s ease;
    }
    
    .banking-sidebar.open {
        transform: translateX(0);
    }
    
    .dashboard-grid {
        grid-template-columns: 1fr;
        grid-template-areas: 
            "overview"
            "balance"
            "transactions"
            "cards"
            "stats"
            "profile"
            "activity";
        padding: 1.5rem;
    }
    
    .dashboard-header {
        padding: 1.5rem;
    }
    
    .header-content {
        flex-direction: column;
        gap: 1rem;
        align-items: flex-start;
    }
    
    .welcome-section h1 {
        font-size: 2rem;
    }
}

/* Loading States */
.loading {
    opacity: 0.6;
    pointer-events: none;
}

.skeleton {
    background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
    background-size: 200% 100%;
    animation: loading 1.5s infinite;
}

@keyframes loading {
    0% {
        background-position: 200% 0;
    }
    100% {
        background-position: -200% 0;
    }
}

/* Status Badges */
.status-badge {
    padding: 0.25rem 0.75rem;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.status-badge.active {
    background: rgba(16, 185, 129, 0.1);
    color: var(--success-color, #10b981);
}

.status-badge.pending {
    background: rgba(245, 158, 11, 0.1);
    color: var(--warning-color, #f59e0b);
}

.status-badge.suspended {
    background: rgba(239, 68, 68, 0.1);
    color: var(--danger-color, #ef4444);
}

/* Animations */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.dashboard-card {
    animation: fadeInUp 0.6s ease-out;
}

.dashboard-card:nth-child(1) { animation-delay: 0.1s; }
.dashboard-card:nth-child(2) { animation-delay: 0.2s; }
.dashboard-card:nth-child(3) { animation-delay: 0.3s; }
.dashboard-card:nth-child(4) { animation-delay: 0.4s; }
.dashboard-card:nth-child(5) { animation-delay: 0.5s; }
.dashboard-card:nth-child(6) { animation-delay: 0.6s; }
