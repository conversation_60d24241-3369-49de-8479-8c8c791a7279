<?php
/**
 * Test Dashboard - Simple version to debug issues
 */

// Enable error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<!DOCTYPE html><html><head><title>Test Dashboard</title></head><body>";
echo "<h1>Dashboard Test</h1>";

try {
    echo "<p>✅ PHP is working</p>";
    
    // Test config include
    echo "<p>Testing config include...</p>";
    require_once '../config/config.php';
    echo "<p>✅ Config loaded</p>";
    
    // Test authentication
    echo "<p>Testing authentication...</p>";
    requireLogin();
    echo "<p>✅ Authentication passed</p>";
    
    // Test database
    echo "<p>Testing database...</p>";
    $db = getDB();
    echo "<p>✅ Database connected</p>";
    
    // Test user data
    $user_id = $_SESSION['user_id'];
    echo "<p>User ID: $user_id</p>";
    
    $user_result = $db->query("SELECT * FROM accounts WHERE id = ?", [$user_id]);
    if ($user_result) {
        $user = $user_result->fetch_assoc();
        echo "<p>✅ User data loaded: " . htmlspecialchars($user['first_name'] . ' ' . $user['last_name']) . "</p>";
        echo "<p>Balance: $" . number_format($user['balance'], 2) . "</p>";
    } else {
        echo "<p>❌ Failed to load user data</p>";
    }
    
    echo "<h2>✅ All tests passed!</h2>";
    echo "<p><a href='index.php'>Try Main Dashboard</a></p>";
    
} catch (Exception $e) {
    echo "<h2>❌ Error Found!</h2>";
    echo "<p><strong>Error:</strong> " . htmlspecialchars($e->getMessage()) . "</p>";
    echo "<p><strong>File:</strong> " . $e->getFile() . "</p>";
    echo "<p><strong>Line:</strong> " . $e->getLine() . "</p>";
    echo "<pre>" . htmlspecialchars($e->getTraceAsString()) . "</pre>";
}

echo "</body></html>";
?>
