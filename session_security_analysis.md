# Session Management Security Analysis & Solutions

## 🚨 Critical Security Issue Identified

### Issue 1: Session Switching to Super Admin Instead of Logout

**Problem**: User dashboard switches to super admin session instead of logging out after inactivity or browser history clearing.

**Root Cause Analysis**:
1. **Multiple Session Types**: The system has 3 different session types:
   - Regular user sessions (`$_SESSION['user_id']`, `$_SESSION['username']`)
   - Admin sessions (`$_SESSION['is_admin'] = true`, `$_SESSION['is_admin_session'] = true`)
   - Super admin sessions (`$_SESSION['super_admin_id']`, `$_SESSION['super_admin_login_time']`)

2. **Session Validation Logic Flaw**:
   ```php
   // From config/config.php lines 97-110
   function isLoggedIn() {
       if (!isset($_SESSION['user_id']) || !isset($_SESSION['username'])) {
           return false;
       }
       // For regular users, ensure they don't have admin session flag
       if (!isset($_SESSION['is_admin_session'])) {
           return true;
       }
       // If admin session flag is set, this is an admin session, not a regular user
       return false;
   }
   ```

3. **Session Timeout Handling**:
   ```php
   // From config/config.php lines 162-176
   function checkSessionTimeout() {
       if (isset($_SESSION['last_activity'])) {
           if (time() - $_SESSION['last_activity'] > SESSION_TIMEOUT) {
               $isAdmin = isset($_SESSION['is_admin_session']);
               session_destroy();
               if ($isAdmin) {
                   header('Location: ' . url('admin/login.php?timeout=1'));
               } else {
                   header('Location: ' . url('login.php?timeout=1'));
               }
               exit();
           }
       }
       $_SESSION['last_activity'] = time();
   }
   ```

**Security Vulnerabilities**:
1. **Session Fixation**: Same browser can maintain multiple session types
2. **Privilege Escalation**: User session can inherit admin privileges
3. **Session Hijacking**: Incomplete session destruction allows session reuse
4. **Authentication Bypass**: Weak session validation logic

## 🔧 Solutions

### Solution 1: Implement Strict Session Isolation

**Create Enhanced Session Manager**:

```php
// config/EnhancedSessionManager.php
class EnhancedSessionManager {
    private static $instance = null;
    private $sessionPrefix = '';
    
    public static function getInstance($sessionType = 'user') {
        if (self::$instance === null) {
            self::$instance = new self();
        }
        self::$instance->setSessionType($sessionType);
        return self::$instance;
    }
    
    private function setSessionType($type) {
        $this->sessionPrefix = $type . '_';
        session_name('BANKING_' . strtoupper($type) . '_SESSION');
    }
    
    public function startSession() {
        if (session_status() === PHP_SESSION_NONE) {
            session_start();
        }
        $this->validateSessionType();
    }
    
    private function validateSessionType() {
        $expectedPrefix = $this->sessionPrefix;
        
        // Check if session belongs to correct type
        if (isset($_SESSION['session_type']) && 
            $_SESSION['session_type'] !== str_replace('_', '', $expectedPrefix)) {
            $this->destroySession('Invalid session type');
            return false;
        }
        
        // Set session type if not set
        if (!isset($_SESSION['session_type'])) {
            $_SESSION['session_type'] = str_replace('_', '', $expectedPrefix);
        }
        
        return true;
    }
    
    public function destroySession($reason = 'Session destroyed') {
        // Clear all session data
        $_SESSION = [];
        
        // Delete session cookie
        if (ini_get('session.use_cookies')) {
            $params = session_get_cookie_params();
            setcookie(session_name(), '', time() - 42000,
                $params['path'], $params['domain'],
                $params['secure'], $params['httponly']
            );
        }
        
        // Destroy session
        session_destroy();
        
        // Log the destruction
        error_log("Session destroyed: {$reason}");
    }
}
```

### Solution 2: Fix User Dashboard Session Validation

**Update user dashboard authentication**:

```php
// user/shared/auth_check.php (new file)
<?php
session_start();

// Strict user session validation
function validateUserSession() {
    // Check basic session data
    if (!isset($_SESSION['user_id']) || !isset($_SESSION['username'])) {
        redirectToLogin('No session data');
        return false;
    }
    
    // Ensure this is NOT an admin session
    if (isset($_SESSION['is_admin']) && $_SESSION['is_admin'] === true) {
        redirectToLogin('Admin session detected in user area');
        return false;
    }
    
    if (isset($_SESSION['is_admin_session']) && $_SESSION['is_admin_session'] === true) {
        redirectToLogin('Admin session flag detected');
        return false;
    }
    
    // Ensure this is NOT a super admin session
    if (isset($_SESSION['super_admin_id'])) {
        redirectToLogin('Super admin session detected in user area');
        return false;
    }
    
    // Check session timeout
    if (isset($_SESSION['last_activity'])) {
        if (time() - $_SESSION['last_activity'] > SESSION_TIMEOUT) {
            redirectToLogin('Session timeout');
            return false;
        }
    }
    
    // Update last activity
    $_SESSION['last_activity'] = time();
    
    return true;
}

function redirectToLogin($reason) {
    // Log the redirect reason
    error_log("User session redirect: {$reason}");
    
    // Clear session completely
    $_SESSION = [];
    session_destroy();
    
    // Redirect to login
    header('Location: ../../auth/login.php?reason=' . urlencode($reason));
    exit();
}

// Validate session
validateUserSession();
?>
```

### Solution 3: Implement Session Type Enforcement

**Update all user dashboard files**:

```php
// Replace session checks in user dashboard files
// Instead of: session_start();
// Use: require_once '../shared/auth_check.php';
```

## 🔧 Transfer System Fixes

### Issue 2: OTP Generation Error - "Unknown column 'purpose'"

**Status**: ✅ **FIXED**
- Added missing `purpose` column to `user_otps` table
- Added missing `is_used` column to `user_otps` table
- OTP generation now works correctly

### Issue 3: Transfer Processing "Insufficient Balance" Error

**Analysis**: The error occurs because:
1. User balance check is working correctly
2. The error message is legitimate - user may actually have insufficient balance
3. Need to verify actual user balance vs transfer amount

**Debug Steps**:
1. Check user's actual balance in database
2. Verify transfer amount and fees calculation
3. Ensure balance check logic is accurate

## 🧪 Testing Plan

### Test 1: Session Isolation
1. Login as regular user
2. Open new tab, login as admin
3. Return to user tab - should remain user session
4. Wait for timeout - should logout, not switch to admin

### Test 2: OTP Generation
1. Initiate transfer requiring OTP
2. Verify OTP is generated and stored in database
3. Check email is sent with OTP code
4. Verify OTP validation works

### Test 3: Transfer Processing
1. Check user balance before transfer
2. Attempt transfer with sufficient balance
3. Verify transfer processes correctly
4. Check balance is debited properly

## 🚀 Implementation Priority

1. **HIGH**: Fix session isolation (security critical)
2. **MEDIUM**: Test OTP generation (functionality critical)
3. **LOW**: Debug transfer balance issues (user experience)

## 📝 Prevention Measures

1. **Session Security**:
   - Implement session type validation
   - Use separate session names for different user types
   - Add session regeneration on privilege changes
   - Implement proper session cleanup

2. **Database Schema**:
   - Ensure all required columns exist
   - Add proper indexes for performance
   - Implement foreign key constraints

3. **Error Handling**:
   - Add comprehensive logging
   - Implement user-friendly error messages
   - Add debugging information for development

4. **Testing**:
   - Add automated session security tests
   - Implement transfer system integration tests
   - Add database schema validation tests
