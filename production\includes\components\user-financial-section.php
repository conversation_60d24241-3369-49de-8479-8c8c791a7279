<?php
/**
 * User Financial Section Component - FULL WIDTH
 * Displays recent transactions in a full-width table
 */

if (!isset($recent_transactions)) {
    $recent_transactions = [];
}
?>

<!-- Financial Information - Full Width Recent Transactions -->
<div class="row row-cards mt-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h3 class="card-title">
                    <i class="fas fa-exchange-alt me-2"></i>
                    Recent Transactions
                </h3>
                <div class="card-actions">
                    <input type="text" class="form-control form-control-sm" placeholder="Search transactions..." 
                           onkeyup="filterTransactions(this.value)" style="width: 200px;">
                </div>
            </div>
            <div class="card-body">
                <?php if (!empty($recent_transactions)): ?>
                <div class="table-responsive">
                    <table class="table table-vcenter transaction-table">
                        <thead>
                            <tr>
                                <th>Date</th>
                                <th>Type</th>
                                <th>Description</th>
                                <th>Amount</th>
                                <th>Status</th>
                                <th>Source</th>
                                <th>Reference</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($recent_transactions as $transaction): ?>
                            <tr>
                                <td>
                                    <div class="text-muted">
                                        <?php echo formatDate($transaction['created_at'], 'M j, Y'); ?>
                                    </div>
                                    <small class="text-muted">
                                        <?php echo formatDate($transaction['created_at'], 'g:i A'); ?>
                                    </small>
                                </td>
                                <td>
                                    <?php if (isset($transaction['transaction_type'])): ?>
                                        <span class="badge bg-<?php echo $transaction['transaction_type'] === 'credit' ? 'success' : 'danger'; ?>-lt">
                                            <?php echo ucfirst($transaction['transaction_type']); ?>
                                        </span>
                                    <?php else: ?>
                                        <span class="badge bg-info-lt">Transfer</span>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <div class="text-truncate" style="max-width: 200px;" title="<?php echo htmlspecialchars($transaction['description'] ?? 'N/A'); ?>">
                                        <?php echo htmlspecialchars($transaction['description'] ?? 'N/A'); ?>
                                    </div>
                                </td>
                                <td>
                                    <span class="text-<?php echo (isset($transaction['transaction_type']) && $transaction['transaction_type'] === 'credit') ? 'success' : 'danger'; ?>">
                                        <?php echo formatCurrency($transaction['amount'], $transaction['currency'] ?? 'USD'); ?>
                                    </span>
                                </td>
                                <td>
                                    <span class="badge bg-<?php 
                                        echo match($transaction['status'] ?? 'completed') {
                                            'completed' => 'success',
                                            'pending' => 'warning',
                                            'failed' => 'danger',
                                            'cancelled' => 'secondary',
                                            default => 'info'
                                        };
                                    ?>-lt">
                                        <?php echo ucfirst($transaction['status'] ?? 'completed'); ?>
                                    </span>
                                </td>
                                <td>
                                    <span class="badge bg-<?php 
                                        echo match($transaction['source'] ?? 'account') {
                                            'account' => 'primary',
                                            'virtual_card' => 'info',
                                            'crypto' => 'warning',
                                            default => 'secondary'
                                        };
                                    ?>-lt">
                                        <?php echo ucfirst(str_replace('_', ' ', $transaction['source'] ?? 'account')); ?>
                                    </span>
                                </td>
                                <td>
                                    <?php if (!empty($transaction['reference_number'])): ?>
                                        <span class="font-monospace text-muted" title="Click to copy" 
                                              onclick="copyToClipboard('<?php echo htmlspecialchars($transaction['reference_number']); ?>', this)">
                                            <?php echo htmlspecialchars($transaction['reference_number']); ?>
                                        </span>
                                    <?php else: ?>
                                        <span class="text-muted">-</span>
                                    <?php endif; ?>
                                </td>
                            </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
                <?php else: ?>
                <div class="text-center text-muted py-4">
                    <i class="fas fa-exchange-alt mb-3" style="font-size: 3rem; opacity: 0.3;"></i>
                    <h4>No Transactions Found</h4>
                    <p>This user has no transaction history yet.</p>
                </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>
