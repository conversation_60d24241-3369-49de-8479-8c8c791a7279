# Database Access Guide - Online Banking System

## Overview

This guide provides comprehensive instructions for accessing and managing the Online Banking System database through various tools and interfaces. Multiple access methods are available to suit different development workflows and preferences.

## Available Database Access Tools

### 1. Web-Based Database Terminal 🌐
**File**: `database_terminal.php`
**Access**: Browser-based interface
**Security**: Localhost-only access (configurable)

#### Features:
- Interactive SQL query execution
- Real-time result display with table formatting
- Database statistics and table information
- Query history and examples
- Syntax highlighting and formatting
- Safety checks for dangerous operations

#### Usage:
1. Open browser and navigate to: `http://localhost/online_banking/database_terminal.php`
2. Enter SQL queries in the text area
3. Click "Execute Query" or use Ctrl+Enter
4. View results in formatted tables
5. Use sidebar for quick table access and statistics

#### Security Features:
- IP restriction (localhost only by default)
- Query logging with timestamps
- Dangerous query warnings
- Read-only mode recommendations

### 2. Command Line Interface (CLI) 💻
**File**: `database_cli.php`
**Access**: Terminal/Command prompt
**Platform**: Cross-platform PHP CLI

#### Features:
- Full SQL query execution
- Interactive and batch modes
- Colored terminal output
- Table structure inspection
- Database statistics
- Automated backups
- User account management

#### Usage:
```bash
# Basic commands
php database_cli.php query "SELECT * FROM accounts LIMIT 5"
php database_cli.php tables
php database_cli.php describe accounts
php database_cli.php stats
php database_cli.php users
php database_cli.php backup

# Interactive mode
php database_cli.php interactive

# Help
php database_cli.php help
```

#### Command Shortcuts:
- `q` = query
- `t` = tables  
- `d` = describe
- `s` = stats
- `b` = backup
- `u` = users
- `i` = interactive
- `h` = help

### 3. MySQL Native Connection Scripts 🔧
**Files**: 
- `database_tools/mysql_connect.bat` (Windows)
- `database_tools/mysql_connect.sh` (Linux/Mac)

#### Features:
- Direct MySQL client access
- Pre-configured connection parameters
- Quick query shortcuts
- Database backup/restore
- Health checks
- Import/export functionality

#### Windows Usage:
```cmd
# Run the batch script
database_tools\mysql_connect.bat

# Or double-click the file in Windows Explorer
```

#### Linux/Mac Usage:
```bash
# Make executable
chmod +x database_tools/mysql_connect.sh

# Run the script
./database_tools/mysql_connect.sh
```

### 4. Existing SQL Export Tools 📦
**Files**: 
- `sql_export_generator.php` (CLI)
- `sql_export_interface.php` (Web)
- `sql/export_database.bat` (Windows)
- `sql/export_database.sh` (Linux/Mac)

#### Features:
- Full database backups
- Schema-only exports
- Data-only exports
- Custom table selection
- Automated timestamping

## Database Connection Configuration

### Default Settings
```php
DB_HOST = 'localhost'
DB_USERNAME = 'root'
DB_PASSWORD = 'root'
DB_NAME = 'online_banking'
DB_CHARSET = 'utf8mb4'
```

### Environment-Specific Configuration

#### Development (MAMP/XAMPP)
- **Host**: localhost
- **Port**: 3306 (default)
- **Username**: root
- **Password**: root (MAMP) or empty (XAMPP)

#### Production
- Update `config/database.php` with production credentials
- Use environment variables for sensitive data
- Enable SSL connections if available

## Quick Access Methods

### 1. Direct MySQL Command Line
```bash
# Connect to database
mysql -h localhost -u root -p online_banking

# Execute single query
mysql -h localhost -u root -p online_banking -e "SHOW TABLES;"

# Import SQL file
mysql -h localhost -u root -p online_banking < backup.sql
```

### 2. Common SQL Queries

#### User Management
```sql
-- List all users
SELECT id, username, email, status, balance FROM accounts;

-- Count users by status
SELECT status, COUNT(*) as count FROM accounts GROUP BY status;

-- Find specific user
SELECT * FROM accounts WHERE username = 'john_doe';

-- Check user balance
SELECT username, balance, currency FROM accounts WHERE id = 1;
```

#### Transaction Analysis
```sql
-- Recent transactions
SELECT * FROM transfers ORDER BY created_at DESC LIMIT 10;

-- Transaction summary by status
SELECT status, COUNT(*) as count, SUM(amount) as total 
FROM transfers GROUP BY status;

-- Daily transaction volume
SELECT DATE(created_at) as date, COUNT(*) as transactions, 
       SUM(amount) as volume 
FROM transfers 
WHERE created_at >= DATE_SUB(NOW(), INTERVAL 7 DAY)
GROUP BY DATE(created_at);
```

#### System Statistics
```sql
-- Database size
SELECT 
    ROUND(SUM(data_length + index_length) / 1024 / 1024, 2) AS 'Size (MB)'
FROM information_schema.TABLES 
WHERE table_schema = 'online_banking';

-- Table row counts
SELECT table_name, table_rows 
FROM information_schema.TABLES 
WHERE table_schema = 'online_banking';

-- Active sessions
SELECT COUNT(*) as active_users 
FROM accounts 
WHERE last_login >= DATE_SUB(NOW(), INTERVAL 24 HOUR);
```

### 3. Database Maintenance

#### Backup Commands
```bash
# Full backup
mysqldump -u root -p --single-transaction online_banking > backup.sql

# Schema only
mysqldump -u root -p --no-data online_banking > schema.sql

# Specific tables
mysqldump -u root -p online_banking accounts transfers > user_data.sql
```

#### Restore Commands
```bash
# Restore full database
mysql -u root -p online_banking < backup.sql

# Restore specific tables
mysql -u root -p online_banking < user_data.sql
```

## Security Best Practices

### 1. Access Control
- Limit database access to localhost in development
- Use VPN or SSH tunnels for remote access
- Implement IP whitelisting for production
- Regular password rotation

### 2. Query Safety
- Always use prepared statements in application code
- Validate and sanitize user inputs
- Implement query logging and monitoring
- Use read-only database users for reporting

### 3. Data Protection
- Regular automated backups
- Encrypt sensitive data at rest
- Use SSL/TLS for database connections
- Implement audit logging for all changes

### 4. Development Guidelines
- Never commit database credentials to version control
- Use environment variables for configuration
- Test queries on development data first
- Document all schema changes

## Troubleshooting

### Common Connection Issues

#### "Access denied" Error
```bash
# Check MySQL service status
systemctl status mysql  # Linux
brew services list | grep mysql  # macOS

# Reset MySQL password
sudo mysql_secure_installation
```

#### "Database does not exist" Error
```sql
-- Create database if missing
CREATE DATABASE IF NOT EXISTS online_banking 
CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- Import schema
SOURCE database/schema.sql;
```

#### "Table doesn't exist" Error
```bash
# Run all schema creation scripts
mysql -u root -p online_banking < database/schema.sql
mysql -u root -p online_banking < database/create_otp_table.sql
mysql -u root -p online_banking < database/create_super_admin_2fa_table.sql
# ... (run all scripts in database/ directory)
```

### Performance Issues

#### Slow Queries
```sql
-- Enable slow query log
SET GLOBAL slow_query_log = 'ON';
SET GLOBAL long_query_time = 2;

-- Check for missing indexes
SHOW INDEXES FROM accounts;
SHOW INDEXES FROM transfers;

-- Analyze table performance
ANALYZE TABLE accounts;
ANALYZE TABLE transfers;
```

#### Database Size Management
```sql
-- Check table sizes
SELECT 
    table_name,
    ROUND(((data_length + index_length) / 1024 / 1024), 2) AS 'Size (MB)'
FROM information_schema.TABLES 
WHERE table_schema = 'online_banking'
ORDER BY (data_length + index_length) DESC;

-- Clean up old audit logs (example)
DELETE FROM audit_logs WHERE created_at < DATE_SUB(NOW(), INTERVAL 90 DAY);
```

## Advanced Features

### 1. Database Monitoring
- Set up automated health checks
- Monitor connection counts and query performance
- Track database growth and usage patterns
- Implement alerting for critical issues

### 2. Development Workflows
- Use database migrations for schema changes
- Implement database seeding for test data
- Set up automated testing with test databases
- Use database versioning and rollback procedures

### 3. Integration with Development Tools
- Configure IDE database connections
- Set up database debugging and profiling
- Use database documentation generators
- Implement automated schema validation

## Support and Resources

### Documentation Files
- `DATABASE_DOCUMENTATION.md` - Complete schema documentation
- `sql/README.md` - SQL export system documentation
- Individual table creation scripts in `database/` directory

### Log Files
- `logs/query_log.txt` - Query execution history
- `logs/audit.log` - System audit trail
- `logs/error.log` - Database error logs

### Contact Information
For database-related issues or questions:
1. Check the troubleshooting section above
2. Review log files for error details
3. Consult the database documentation
4. Contact the development team with specific error messages

---

*This guide covers all available database access methods for the Online Banking System. Choose the method that best fits your workflow and security requirements.*
