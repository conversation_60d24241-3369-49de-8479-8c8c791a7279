/* Account Management Specific Styles */

/* Account Operation Form */
.account-operation-form {
    background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
    border-radius: 16px;
    padding: 2rem;
    box-shadow: 0 4px 6px rgba(0,0,0,0.05);
}

/* User Selection */
.user-select-enhanced {
    position: relative;
}

.user-select-enhanced .form-select {
    padding-right: 3rem;
}

.user-balance-indicator {
    position: absolute;
    right: 2.5rem;
    top: 50%;
    transform: translateY(-50%);
    background: linear-gradient(135deg, #3b82f6, #1d4ed8);
    color: white;
    padding: 0.25rem 0.5rem;
    border-radius: 6px;
    font-size: 0.75rem;
    font-weight: 600;
    pointer-events: none;
}

/* Operation Type Selection */
.form-selectgroup {
    display: flex;
    gap: 1rem;
    margin-top: 0.5rem;
}

.form-selectgroup-item {
    flex: 1;
}

.form-selectgroup-input {
    display: none;
}

.form-selectgroup-label {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    padding: 1rem;
    border: 2px solid #e5e7eb;
    border-radius: 12px;
    background: white;
    cursor: pointer;
    transition: all 0.3s ease;
    font-weight: 500;
    color: #374151;
}

.form-selectgroup-label:hover {
    border-color: #3b82f6;
    background: #f8fafc;
}

.form-selectgroup-input:checked + .form-selectgroup-label {
    border-color: #3b82f6;
    background: linear-gradient(135deg, #dbeafe, #bfdbfe);
    color: #1e40af;
    box-shadow: 0 4px 12px rgba(59, 130, 246, 0.2);
}

/* Credit/Debit specific styling */
.form-selectgroup-input[value="credit"]:checked + .form-selectgroup-label {
    border-color: #10b981;
    background: linear-gradient(135deg, #dcfce7, #bbf7d0);
    color: #166534;
    box-shadow: 0 4px 12px rgba(16, 185, 129, 0.2);
}

.form-selectgroup-input[value="debit"]:checked + .form-selectgroup-label {
    border-color: #ef4444;
    background: linear-gradient(135deg, #fee2e2, #fecaca);
    color: #991b1b;
    box-shadow: 0 4px 12px rgba(239, 68, 68, 0.2);
}

/* Amount Input */
.amount-input-group {
    position: relative;
}

.amount-input-group .input-group-text {
    background: linear-gradient(135deg, #f1f5f9, #e2e8f0);
    border: 2px solid #e5e7eb;
    border-right: none;
    color: #64748b;
    font-weight: 600;
    font-size: 1.1rem;
}

.amount-input-group .form-control {
    border-left: none;
    font-size: 1.1rem;
    font-weight: 600;
}

.amount-input-group:focus-within .input-group-text {
    border-color: #3b82f6;
}

/* Account Info Panel */
.account-info-panel {
    background: linear-gradient(135deg, #f8fafc 0%, #ffffff 100%);
    border: 2px solid #e2e8f0;
    border-radius: 16px;
    padding: 1.5rem;
    transition: all 0.3s ease;
}

.account-info-panel.loaded {
    border-color: #3b82f6;
    box-shadow: 0 8px 25px rgba(59, 130, 246, 0.1);
}

.account-avatar {
    width: 60px;
    height: 60px;
    border-radius: 16px;
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    font-weight: 700;
    margin-bottom: 1rem;
}

.account-balance {
    font-size: 2rem;
    font-weight: 700;
    background: linear-gradient(135deg, #1e293b, #475569);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    margin-bottom: 0.5rem;
}

.account-details {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1rem;
    margin-top: 1rem;
}

.account-detail-item {
    background: rgba(59, 130, 246, 0.05);
    border-radius: 8px;
    padding: 0.75rem;
}

.account-detail-label {
    font-size: 0.75rem;
    color: #64748b;
    text-transform: uppercase;
    letter-spacing: 0.025em;
    margin-bottom: 0.25rem;
}

.account-detail-value {
    font-weight: 600;
    color: #1e293b;
}

/* Transaction History Table */
.transaction-table {
    background: white;
    border-radius: 16px;
    overflow: hidden;
    box-shadow: 0 4px 6px rgba(0,0,0,0.05);
}

.transaction-table .table {
    margin-bottom: 0;
}

.transaction-table .table th {
    background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
    border-bottom: 2px solid #e2e8f0;
    font-weight: 600;
    color: #475569;
    padding: 1rem;
    font-size: 0.875rem;
    text-transform: uppercase;
    letter-spacing: 0.025em;
}

.transaction-table .table td {
    padding: 1rem;
    vertical-align: middle;
    border-bottom: 1px solid #f1f5f9;
}

.transaction-table .table tbody tr:hover {
    background: #f8fafc;
}

/* Transaction Type Badges */
.transaction-badge {
    padding: 0.375rem 0.75rem;
    border-radius: 8px;
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.025em;
    display: inline-flex;
    align-items: center;
    gap: 0.25rem;
}

.transaction-badge.credit {
    background: linear-gradient(135deg, #dcfce7, #bbf7d0);
    color: #166534;
    border: 1px solid #86efac;
}

.transaction-badge.debit {
    background: linear-gradient(135deg, #fee2e2, #fecaca);
    color: #991b1b;
    border: 1px solid #fca5a5;
}

/* Category Badges */
.category-badge {
    padding: 0.25rem 0.5rem;
    border-radius: 6px;
    font-size: 0.75rem;
    font-weight: 500;
    background: linear-gradient(135deg, #dbeafe, #bfdbfe);
    color: #1e40af;
    border: 1px solid #93c5fd;
}

/* Submit Button */
.submit-btn {
    background: linear-gradient(135deg, #3b82f6, #1d4ed8);
    border: none;
    border-radius: 12px;
    padding: 0.75rem 2rem;
    font-weight: 600;
    color: white;
    transition: all 0.3s ease;
    width: 100%;
}

.submit-btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 8px 25px rgba(59, 130, 246, 0.3);
    color: white;
}

.submit-btn:disabled {
    opacity: 0.6;
    transform: none;
    box-shadow: none;
}

/* Loading States */
.loading-spinner {
    display: inline-block;
    width: 1rem;
    height: 1rem;
    border: 2px solid transparent;
    border-top: 2px solid currentColor;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Success/Error States */
.operation-success {
    animation: successPulse 0.6s ease-out;
}

.operation-error {
    animation: errorShake 0.6s ease-out;
}

@keyframes successPulse {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.02); }
}

@keyframes errorShake {
    0%, 100% { transform: translateX(0); }
    25% { transform: translateX(-5px); }
    75% { transform: translateX(5px); }
}

/* Responsive Design */
@media (max-width: 768px) {
    .account-operation-form {
        padding: 1rem;
    }
    
    .form-selectgroup {
        flex-direction: column;
        gap: 0.75rem;
    }
    
    .account-details {
        grid-template-columns: 1fr;
        gap: 0.75rem;
    }
    
    .account-balance {
        font-size: 1.5rem;
    }
    
    .transaction-table .table th,
    .transaction-table .table td {
        padding: 0.75rem 0.5rem;
        font-size: 0.875rem;
    }
    
    .account-avatar {
        width: 48px;
        height: 48px;
        font-size: 1.25rem;
    }
}

/* Dark Mode Support */
@media (prefers-color-scheme: dark) {
    .account-operation-form {
        background: linear-gradient(135deg, #1f2937 0%, #111827 100%);
        border-color: #374151;
    }
    
    .account-info-panel {
        background: linear-gradient(135deg, #374151 0%, #1f2937 100%);
        border-color: #4b5563;
    }
    
    .account-info-panel.loaded {
        border-color: #3b82f6;
    }
    
    .account-balance {
        background: linear-gradient(135deg, #f9fafb, #e5e7eb);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
    }
    
    .account-detail-item {
        background: rgba(59, 130, 246, 0.1);
    }
    
    .account-detail-label {
        color: #9ca3af;
    }
    
    .account-detail-value {
        color: #e5e7eb;
    }
    
    .transaction-table {
        background: #1f2937;
    }
    
    .transaction-table .table th {
        background: linear-gradient(135deg, #374151 0%, #4b5563 100%);
        color: #e5e7eb;
        border-bottom-color: #4b5563;
    }
    
    .transaction-table .table td {
        color: #e5e7eb;
        border-bottom-color: #374151;
    }
    
    .transaction-table .table tbody tr:hover {
        background: #374151;
    }
}
