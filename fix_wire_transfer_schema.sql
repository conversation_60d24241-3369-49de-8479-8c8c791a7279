-- Fix Wire Transfer Database Schema
-- Add missing columns to transfers table

-- Add updated_at column (critical for admin updates)
ALTER TABLE transfers ADD COLUMN IF NOT EXISTS updated_at TIMESTAMP NULL DEFAULT NULL;

-- Add wire transfer specific columns if they don't exist
ALTE<PERSON> TABLE transfers ADD COLUMN IF NOT EXISTS bank_name VARCHAR(150) DEFAULT NULL;
<PERSON>TER TABLE transfers ADD COLUMN IF NOT EXISTS swift_code VARCHAR(20) DEFAULT NULL;
ALTER TABLE transfers ADD COLUMN IF NOT EXISTS routing_code VARCHAR(50) DEFAULT NULL;
<PERSON>TE<PERSON> TABLE transfers ADD COLUMN IF NOT EXISTS iban VARCHAR(50) DEFAULT NULL;
ALTER TABLE transfers ADD COLUMN IF NOT EXISTS bank_address TEXT DEFAULT NULL;
ALTER TABLE transfers ADD COLUMN IF NOT EXISTS bank_city VARCHAR(100) DEFAULT NULL;
ALTER TABLE transfers ADD COLUMN IF NOT EXISTS bank_country VARCHAR(50) DEFAULT NULL;
<PERSON><PERSON><PERSON> TABLE transfers ADD COLUMN IF NOT EXISTS beneficiary_address TEXT DEFAULT NULL;
ALTER TABLE transfers ADD COLUMN IF NOT EXISTS purpose_of_payment VARCHAR(200) DEFAULT NULL;
ALTER TABLE transfers ADD COLUMN IF NOT EXISTS wire_transfer_data JSON DEFAULT NULL;
ALTER TABLE transfers ADD COLUMN IF NOT EXISTS processing_status ENUM('pending', 'processing', 'completed', 'failed', 'cancelled') DEFAULT 'pending';
ALTER TABLE transfers ADD COLUMN IF NOT EXISTS admin_notes TEXT DEFAULT NULL;
ALTER TABLE transfers ADD COLUMN IF NOT EXISTS billing_codes_verified BOOLEAN DEFAULT FALSE;
ALTER TABLE transfers ADD COLUMN IF NOT EXISTS billing_verification_data JSON DEFAULT NULL;

-- Update existing records to have proper updated_at values
UPDATE transfers SET updated_at = created_at WHERE updated_at IS NULL;

-- Add indexes for better performance
CREATE INDEX IF NOT EXISTS idx_transfers_processing_status ON transfers(processing_status);
CREATE INDEX IF NOT EXISTS idx_transfers_transfer_type ON transfers(transfer_type);
CREATE INDEX IF NOT EXISTS idx_transfers_updated_at ON transfers(updated_at);
