<?php
/**
 * Super Admin Google Authenticator (2FA) Functions
 * Handles 2FA setup, verification, and management for super administrators
 */

// Include config to get Google2FA function
require_once __DIR__ . '/../../config/config.php';

// Include super admin settings to get getSuperAdminSetting function
require_once __DIR__ . '/../../config/super_admin_settings.php';

/**
 * Get super admin 2FA settings
 */
function getSuperAdmin2FASettings($username = 'superadmin') {
    try {
        require_once __DIR__ . '/../../config/database.php';
        $db = getDB();
        
        $sql = "SELECT * FROM super_admin_2fa_settings WHERE super_admin_username = ?";
        $result = $db->query($sql, [$username]);
        
        if ($result && $result->num_rows === 1) {
            return $result->fetch_assoc();
        }
        
        // Create default settings if not found
        $create_sql = "INSERT INTO super_admin_2fa_settings (super_admin_username, google_2fa_enabled, security_code_enabled, auth_method) VALUES (?, 0, 0, 'none')";
        $db->query($create_sql, [$username]);

        // Return default settings
        return [
            'super_admin_username' => $username,
            'google_2fa_enabled' => 0,
            'google_2fa_secret' => null,
            'backup_codes' => null,
            'last_used_timestamp' => null,
            'failed_attempts' => 0,
            'locked_until' => null,
            'security_code_hash' => null,
            'security_code_enabled' => 0,
            'auth_method' => 'none',
            'security_code_created_at' => null,
            'security_code_last_used' => null
        ];
    } catch (Exception $e) {
        error_log("Error getting super admin 2FA settings: " . $e->getMessage());
        return null;
    }
}

/**
 * Check if super admin 2FA is enabled
 */
function isSuperAdmin2FAEnabled($username = 'superadmin') {
    $settings = getSuperAdmin2FASettings($username);
    return $settings && $settings['google_2fa_enabled'] == 1;
}

/**
 * Check if super admin security code is enabled
 */
function isSuperAdminSecurityCodeEnabled($username = 'superadmin') {
    $settings = getSuperAdmin2FASettings($username);
    return $settings && isset($settings['security_code_enabled']) && $settings['security_code_enabled'] == 1;
}

/**
 * Check if any authentication method is enabled
 */
function isSuperAdminAuthEnabled($username = 'superadmin') {
    $settings = getSuperAdmin2FASettings($username);
    if (!$settings) return false;

    return (isset($settings['google_2fa_enabled']) && $settings['google_2fa_enabled'] == 1) ||
           (isset($settings['security_code_enabled']) && $settings['security_code_enabled'] == 1);
}

/**
 * Get active authentication methods for super admin
 */
function getSuperAdminAuthMethods($username = 'superadmin') {
    $settings = getSuperAdmin2FASettings($username);
    if (!$settings) return [];

    $methods = [];

    if (isset($settings['google_2fa_enabled']) && $settings['google_2fa_enabled'] == 1) {
        $methods[] = 'google_2fa';
    }

    if (isset($settings['security_code_enabled']) && $settings['security_code_enabled'] == 1) {
        $methods[] = 'security_code';
    }

    return $methods;
}

/**
 * Get authentication method display name
 */
function getAuthMethodDisplayName($method) {
    switch ($method) {
        case 'google_2fa':
            return 'Google Authenticator';
        case 'security_code':
            return '6-Digit Security Code';
        case 'both':
            return 'Both Methods Available';
        case 'none':
        default:
            return 'No Authentication';
    }
}

/**
 * Check if super admin 2FA is required (from system settings)
 */
function isSuperAdmin2FARequired() {
    try {
        require_once __DIR__ . '/../../config/database.php';
        $db = getDB();

        $sql = "SELECT setting_value FROM super_admin_settings WHERE setting_key = 'require_2fa'";
        $result = $db->query($sql);

        if ($result && $result->num_rows === 1) {
            $row = $result->fetch_assoc();
            return $row['setting_value'] == '1';
        }

        return false;
    } catch (Exception $e) {
        error_log("Error checking if super admin 2FA is required: " . $e->getMessage());
        return false;
    }
}

/**
 * Generate new Google Authenticator secret
 */
function generateSuperAdmin2FASecret() {
    $google2fa = getGoogle2FA();
    return $google2fa->generateSecretKey();
}

/**
 * Generate QR code URL for super admin 2FA setup
 */
function generateSuperAdmin2FAQRCode($secret, $username = 'superadmin') {
    $google2fa = getGoogle2FA();
    $company = 'SecureBank Online - Super Admin';
    $holder = $username;
    
    return $google2fa->getQRCodeUrl($company, $holder, $secret);
}

/**
 * Verify Google Authenticator code
 */
function verifySuperAdmin2FACode($code, $secret, $username = 'superadmin') {
    try {
        $google2fa = getGoogle2FA();
        
        // Check if account is locked
        if (isSuperAdmin2FALocked($username)) {
            logSuperAdmin2FAAction($username, 'verification_attempt_while_locked', 'Attempted verification while account is locked');
            return false;
        }
        
        // Verify the code
        $isValid = $google2fa->verifyKey($secret, $code);
        
        if ($isValid) {
            // Reset failed attempts on successful verification
            resetSuperAdmin2FAFailedAttempts($username);
            
            // Update last used timestamp to prevent replay attacks
            updateSuperAdmin2FALastUsed($username);
            
            logSuperAdmin2FAAction($username, 'verification_success', 'Google Authenticator code verified successfully');
            return true;
        } else {
            // Increment failed attempts
            incrementSuperAdmin2FAFailedAttempts($username);
            
            logSuperAdmin2FAAction($username, 'verification_failed', 'Invalid Google Authenticator code provided');
            return false;
        }
    } catch (Exception $e) {
        error_log("Error verifying super admin 2FA code: " . $e->getMessage());
        logSuperAdmin2FAAction($username, 'verification_error', 'Error during code verification: ' . $e->getMessage());
        return false;
    }
}

/**
 * Check if super admin 2FA account is locked
 */
function isSuperAdmin2FALocked($username = 'superadmin') {
    try {
        require_once __DIR__ . '/../../config/database.php';
        $db = getDB();
        
        $sql = "SELECT locked_until FROM super_admin_2fa_settings WHERE super_admin_username = ?";
        $result = $db->query($sql, [$username]);
        
        if ($result && $result->num_rows === 1) {
            $row = $result->fetch_assoc();
            if ($row['locked_until'] && strtotime($row['locked_until']) > time()) {
                return true;
            }
        }
        
        return false;
    } catch (Exception $e) {
        error_log("Error checking super admin 2FA lock status: " . $e->getMessage());
        return false;
    }
}

/**
 * Increment failed 2FA attempts and lock if necessary
 */
function incrementSuperAdmin2FAFailedAttempts($username = 'superadmin') {
    try {
        require_once __DIR__ . '/../../config/database.php';
        $db = getDB();
        
        // Get max attempts from settings
        $max_attempts = getSuperAdminSetting('super_admin_2fa_max_attempts', 5);
        $lockout_duration = getSuperAdminSetting('super_admin_2fa_lockout_duration', 30);
        
        // Increment failed attempts
        $sql = "UPDATE super_admin_2fa_settings SET failed_attempts = failed_attempts + 1 WHERE super_admin_username = ?";
        $db->query($sql, [$username]);
        
        // Check if we need to lock the account
        $check_sql = "SELECT failed_attempts FROM super_admin_2fa_settings WHERE super_admin_username = ?";
        $result = $db->query($check_sql, [$username]);
        
        if ($result && $result->num_rows === 1) {
            $row = $result->fetch_assoc();
            if ($row['failed_attempts'] >= $max_attempts) {
                // Lock the account
                $lock_until = date('Y-m-d H:i:s', time() + ($lockout_duration * 60));
                $lock_sql = "UPDATE super_admin_2fa_settings SET locked_until = ? WHERE super_admin_username = ?";
                $db->query($lock_sql, [$lock_until, $username]);
                
                logSuperAdmin2FAAction($username, 'account_locked', "Account locked for $lockout_duration minutes after $max_attempts failed attempts");
            }
        }
    } catch (Exception $e) {
        error_log("Error incrementing super admin 2FA failed attempts: " . $e->getMessage());
    }
}

/**
 * Reset failed 2FA attempts
 */
function resetSuperAdmin2FAFailedAttempts($username = 'superadmin') {
    try {
        require_once __DIR__ . '/../../config/database.php';
        $db = getDB();
        
        $sql = "UPDATE super_admin_2fa_settings SET failed_attempts = 0, locked_until = NULL WHERE super_admin_username = ?";
        $db->query($sql, [$username]);
    } catch (Exception $e) {
        error_log("Error resetting super admin 2FA failed attempts: " . $e->getMessage());
    }
}

/**
 * Update last used timestamp for replay attack prevention
 */
function updateSuperAdmin2FALastUsed($username = 'superadmin') {
    try {
        require_once __DIR__ . '/../../config/database.php';
        $db = getDB();
        
        $sql = "UPDATE super_admin_2fa_settings SET last_used_timestamp = ? WHERE super_admin_username = ?";
        $db->query($sql, [time(), $username]);
    } catch (Exception $e) {
        error_log("Error updating super admin 2FA last used timestamp: " . $e->getMessage());
    }
}

/**
 * Generate backup codes for super admin 2FA
 */
function generateSuperAdmin2FABackupCodes($count = 10) {
    $codes = [];
    for ($i = 0; $i < $count; $i++) {
        $codes[] = strtoupper(bin2hex(random_bytes(4))); // 8-character hex codes
    }
    return $codes;
}

/**
 * Save super admin 2FA settings
 */
function saveSuperAdmin2FASettings($username, $secret, $backup_codes = null) {
    try {
        require_once __DIR__ . '/../../config/database.php';
        $db = getDB();
        
        $backup_codes_json = $backup_codes ? json_encode($backup_codes) : null;
        
        $sql = "UPDATE super_admin_2fa_settings SET 
                google_2fa_enabled = 1, 
                google_2fa_secret = ?, 
                backup_codes = ?,
                failed_attempts = 0,
                locked_until = NULL
                WHERE super_admin_username = ?";
        
        $result = $db->query($sql, [$secret, $backup_codes_json, $username]);
        
        if ($result) {
            logSuperAdmin2FAAction($username, '2fa_enabled', 'Google Authenticator enabled for super admin');
            return true;
        }
        
        return false;
    } catch (Exception $e) {
        error_log("Error saving super admin 2FA settings: " . $e->getMessage());
        return false;
    }
}

/**
 * Generate a secure 6-digit security code
 */
function generateSecurityCode() {
    return str_pad(random_int(100000, 999999), 6, '0', STR_PAD_LEFT);
}

/**
 * Hash a security code for secure storage
 */
function hashSecurityCode($code) {
    // Use Argon2ID if available, otherwise fall back to bcrypt
    if (defined('PASSWORD_ARGON2ID')) {
        return password_hash($code, PASSWORD_ARGON2ID, [
            'memory_cost' => 65536,
            'time_cost' => 4,
            'threads' => 3
        ]);
    } else {
        // Fallback to bcrypt with high cost
        return password_hash($code, PASSWORD_BCRYPT, ['cost' => 12]);
    }
}

/**
 * Verify a security code against its hash
 */
function verifySecurityCode($code, $hash) {
    return password_verify($code, $hash);
}

/**
 * Set super admin security code
 */
function setSuperAdminSecurityCode($username, $code) {
    try {
        require_once __DIR__ . '/../../config/database.php';
        $db = getDB();

        // First check if the required columns exist
        $columns_check = $db->query("SHOW COLUMNS FROM super_admin_2fa_settings LIKE 'security_code_hash'");
        if (!$columns_check || $columns_check->num_rows === 0) {
            error_log("Error: security_code_hash column does not exist. Please run the database upgrade first.");
            return false;
        }

        $hash = hashSecurityCode($code);

        // Check if record exists for this user
        $user_check = $db->query("SELECT id FROM super_admin_2fa_settings WHERE super_admin_username = ?", [$username]);
        if (!$user_check || $user_check->num_rows === 0) {
            // Create new record
            $sql = "INSERT INTO super_admin_2fa_settings (
                        super_admin_username,
                        security_code_hash,
                        security_code_enabled,
                        security_code_created_at,
                        auth_method,
                        google_2fa_enabled,
                        failed_attempts
                    ) VALUES (?, ?, 1, NOW(), 'security_code', 0, 0)";
            $result = $db->query($sql, [$username, $hash]);
        } else {
            // Update existing record
            $sql = "UPDATE super_admin_2fa_settings SET
                    security_code_hash = ?,
                    security_code_enabled = 1,
                    security_code_created_at = NOW(),
                    auth_method = CASE
                        WHEN google_2fa_enabled = 1 THEN 'both'
                        ELSE 'security_code'
                    END,
                    failed_attempts = 0,
                    locked_until = NULL
                    WHERE super_admin_username = ?";
            $result = $db->query($sql, [$hash, $username]);
        }

        if ($result) {
            logSuperAdmin2FAAction($username, 'security_code_set', 'Security code configured for super admin');
            return true;
        }

        return false;
    } catch (Exception $e) {
        error_log("Error setting super admin security code: " . $e->getMessage());
        return false;
    }
}

/**
 * Enable super admin Google 2FA (only if already configured)
 */
function enableSuperAdmin2FA($username = 'superadmin') {
    try {
        require_once __DIR__ . '/../../config/database.php';
        $db = getDB();

        // Check if 2FA is already configured (has secret)
        $settings = getSuperAdmin2FASettings($username);
        if (!$settings || !$settings['google_2fa_secret']) {
            return false; // Cannot enable without proper setup
        }

        $sql = "UPDATE super_admin_2fa_settings SET
                google_2fa_enabled = 1,
                auth_method = CASE
                    WHEN security_code_enabled = 1 THEN 'both'
                    ELSE 'google_2fa'
                END,
                failed_attempts = 0,
                locked_until = NULL
                WHERE super_admin_username = ?";

        $result = $db->query($sql, [$username]);

        if ($result) {
            logSuperAdmin2FAAction($username, '2fa_enabled', 'Google Authenticator enabled for super admin');
            return true;
        }

        return false;
    } catch (Exception $e) {
        error_log("Error enabling super admin 2FA: " . $e->getMessage());
        return false;
    }
}

/**
 * Disable super admin Google 2FA
 */
function disableSuperAdmin2FA($username = 'superadmin') {
    try {
        require_once __DIR__ . '/../../config/database.php';
        $db = getDB();

        $sql = "UPDATE super_admin_2fa_settings SET
                google_2fa_enabled = 0,
                auth_method = CASE
                    WHEN security_code_enabled = 1 THEN 'security_code'
                    ELSE 'none'
                END,
                failed_attempts = 0,
                locked_until = NULL
                WHERE super_admin_username = ?";

        $result = $db->query($sql, [$username]);

        if ($result) {
            logSuperAdmin2FAAction($username, '2fa_disabled', 'Google Authenticator disabled for super admin');
            return true;
        }

        return false;
    } catch (Exception $e) {
        error_log("Error disabling super admin 2FA: " . $e->getMessage());
        return false;
    }
}

/**
 * Disable super admin security code
 */
function disableSuperAdminSecurityCode($username = 'superadmin') {
    try {
        require_once __DIR__ . '/../../config/database.php';
        $db = getDB();

        $sql = "UPDATE super_admin_2fa_settings SET
                security_code_enabled = 0,
                security_code_hash = NULL,
                auth_method = CASE
                    WHEN google_2fa_enabled = 1 THEN 'google_2fa'
                    ELSE 'none'
                END,
                failed_attempts = 0,
                locked_until = NULL
                WHERE super_admin_username = ?";

        $result = $db->query($sql, [$username]);

        if ($result) {
            logSuperAdmin2FAAction($username, 'security_code_disabled', 'Security code disabled for super admin');
            return true;
        }

        return false;
    } catch (Exception $e) {
        error_log("Error disabling super admin security code: " . $e->getMessage());
        return false;
    }
}

/**
 * Verify super admin authentication (supports both methods)
 */
function verifySuperAdminAuth($username, $code, $method = 'auto') {
    try {
        $settings = getSuperAdmin2FASettings($username);
        if (!$settings) {
            return false;
        }

        // Check if account is locked
        if (isSuperAdmin2FALocked($username)) {
            logSuperAdmin2FAAction($username, 'auth_attempt_while_locked', 'Authentication attempt while account is locked');
            return false;
        }

        $verified = false;
        $auth_method_used = '';

        // Auto-detect method based on code format
        if ($method === 'auto') {
            if (preg_match('/^\d{6}$/', $code) && strlen($code) === 6) {
                // Try security code first if it's 6 digits
                if ($settings['security_code_enabled'] && $settings['security_code_hash']) {
                    if (verifySecurityCode($code, $settings['security_code_hash'])) {
                        $verified = true;
                        $auth_method_used = 'security_code';

                        // Update last used timestamp
                        $db = getDB();
                        $db->query("UPDATE super_admin_2fa_settings SET security_code_last_used = NOW() WHERE super_admin_username = ?", [$username]);
                    }
                }

                // If security code failed, try Google 2FA
                if (!$verified && $settings['google_2fa_enabled'] && $settings['google_2fa_secret']) {
                    if (verifySuperAdmin2FACode($code, $settings['google_2fa_secret'], $username)) {
                        $verified = true;
                        $auth_method_used = 'google_2fa';
                    }
                }
            }
        } elseif ($method === 'security_code') {
            if ($settings['security_code_enabled'] && $settings['security_code_hash']) {
                $verified = verifySecurityCode($code, $settings['security_code_hash']);
                $auth_method_used = 'security_code';
            }
        } elseif ($method === 'google_2fa') {
            if ($settings['google_2fa_enabled'] && $settings['google_2fa_secret']) {
                $verified = verifySuperAdmin2FACode($code, $settings['google_2fa_secret'], $username);
                $auth_method_used = 'google_2fa';
            }
        }

        if ($verified) {
            // Reset failed attempts on successful verification
            $db = getDB();
            $db->query("UPDATE super_admin_2fa_settings SET failed_attempts = 0, locked_until = NULL WHERE super_admin_username = ?", [$username]);

            logSuperAdmin2FAAction($username, 'auth_success', "Authentication successful using {$auth_method_used}");
            return true;
        } else {
            // Increment failed attempts
            incrementSuperAdmin2FAFailedAttempts($username);
            logSuperAdmin2FAAction($username, 'auth_failed', "Authentication failed for method: {$method}");
            return false;
        }

    } catch (Exception $e) {
        error_log("Error verifying super admin authentication: " . $e->getMessage());
        return false;
    }
}

/**
 * Log super admin 2FA actions for audit trail
 */
function logSuperAdmin2FAAction($username, $action, $details = '') {
    try {
        require_once __DIR__ . '/../../config/database.php';
        $db = getDB();
        
        $sql = "INSERT INTO super_admin_2fa_audit (super_admin_username, action, details, ip_address, user_agent, success) 
                VALUES (?, ?, ?, ?, ?, ?)";
        
        $ip_address = $_SERVER['REMOTE_ADDR'] ?? 'unknown';
        $user_agent = $_SERVER['HTTP_USER_AGENT'] ?? 'unknown';
        $success = strpos($action, 'success') !== false ? 1 : 0;
        
        $db->query($sql, [$username, $action, $details, $ip_address, $user_agent, $success]);
    } catch (Exception $e) {
        error_log("Error logging super admin 2FA action: " . $e->getMessage());
    }
}


