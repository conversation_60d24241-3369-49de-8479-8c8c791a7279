<?php
/**
 * Comprehensive Error Check for Admin Dashboard
 * This will help identify exactly what's causing the blank page
 */

// Start output buffering to catch any output
ob_start();

// Enable all error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
ini_set('log_errors', 1);

// Create a custom error log for this test
$error_log_file = __DIR__ . '/../logs/admin_error_check.log';
ini_set('error_log', $error_log_file);

// Function to log messages
function logMessage($message) {
    global $error_log_file;
    $timestamp = date('Y-m-d H:i:s');
    file_put_contents($error_log_file, "[{$timestamp}] {$message}\n", FILE_APPEND | LOCK_EX);
}

logMessage("=== ADMIN ERROR CHECK STARTED ===");

try {
    logMessage("Step 1: Testing basic PHP functionality");
    echo "Step 1: PHP is working<br>";
    
    logMessage("Step 2: Testing file paths");
    $config_path = __DIR__ . '/../config/config.php';
    $debug_path = __DIR__ . '/../config/debug-system.php';
    
    logMessage("Config path: " . $config_path);
    logMessage("Debug path: " . $debug_path);
    
    if (!file_exists($config_path)) {
        throw new Exception("config.php not found at: " . $config_path);
    }
    
    if (!file_exists($debug_path)) {
        throw new Exception("debug-system.php not found at: " . $debug_path);
    }
    
    echo "Step 2: File paths verified<br>";
    logMessage("Step 2: File paths verified");
    
    logMessage("Step 3: Including debug system");
    require_once $debug_path;
    echo "Step 3: Debug system loaded<br>";
    logMessage("Step 3: Debug system loaded successfully");
    
    logMessage("Step 4: Including config");
    require_once $config_path;
    echo "Step 4: Config loaded<br>";
    logMessage("Step 4: Config loaded successfully");
    
    logMessage("Step 5: Testing database connection");
    $db = getDB();
    echo "Step 5: Database connected<br>";
    logMessage("Step 5: Database connected successfully");
    
    logMessage("Step 6: Testing admin authentication function");
    if (function_exists('requireAdmin')) {
        echo "Step 6: requireAdmin function exists<br>";
        logMessage("Step 6: requireAdmin function exists");
    } else {
        throw new Exception("requireAdmin function not found");
    }
    
    logMessage("Step 7: Testing session");
    if (session_status() === PHP_SESSION_NONE) {
        session_start();
    }
    echo "Step 7: Session handling working<br>";
    logMessage("Step 7: Session handling working");
    
    logMessage("Step 8: Testing admin header include");
    $header_path = __DIR__ . '/includes/admin-header.php';
    if (!file_exists($header_path)) {
        throw new Exception("admin-header.php not found at: " . $header_path);
    }
    
    // Test header include without executing it
    $header_content = file_get_contents($header_path);
    if (strlen($header_content) > 0) {
        echo "Step 8: Admin header file readable (" . strlen($header_content) . " bytes)<br>";
        logMessage("Step 8: Admin header file readable (" . strlen($header_content) . " bytes)");
    } else {
        throw new Exception("Admin header file is empty");
    }
    
    logMessage("Step 9: Testing dynamic CSS");
    if (file_exists(__DIR__ . '/../config/dynamic-css.php')) {
        require_once __DIR__ . '/../config/dynamic-css.php';
        $css = generateDynamicCSS();
        echo "Step 9: Dynamic CSS generated (" . strlen($css) . " chars)<br>";
        logMessage("Step 9: Dynamic CSS generated (" . strlen($css) . " chars)");
    } else {
        echo "Step 9: Dynamic CSS file not found<br>";
        logMessage("Step 9: Dynamic CSS file not found");
    }
    
    echo "<h2>✅ All Basic Tests Passed!</h2>";
    logMessage("=== ALL BASIC TESTS PASSED ===");
    
    echo "<h3>Now testing full admin index simulation...</h3>";
    logMessage("Step 10: Simulating admin index execution");
    
    // Simulate the admin index execution
    $_SESSION['user_id'] = 1; // Simulate logged in admin
    $_SESSION['is_admin'] = true;
    
    // Test the actual admin index logic
    $page_title = 'Admin Dashboard';
    
    // Test database queries from admin index
    $total_users_result = $db->query("SELECT COUNT(*) as count FROM accounts WHERE is_admin = 0 AND deleted_at IS NULL");
    if ($total_users_result) {
        $total_users = $total_users_result->fetch_assoc()['count'];
        echo "Step 10: Database queries working - Found {$total_users} users<br>";
        logMessage("Step 10: Database queries working - Found {$total_users} users");
    } else {
        throw new Exception("Database query failed");
    }
    
    echo "<h2>🎉 FULL SIMULATION SUCCESSFUL!</h2>";
    echo "<p>The admin dashboard should be working. If it's still showing blank, the issue might be:</p>";
    echo "<ul>";
    echo "<li>Authentication redirects</li>";
    echo "<li>Session issues</li>";
    echo "<li>Output buffering problems</li>";
    echo "<li>JavaScript errors preventing display</li>";
    echo "</ul>";
    
    logMessage("=== FULL SIMULATION SUCCESSFUL ===");
    
} catch (Exception $e) {
    echo "<h2>❌ ERROR FOUND!</h2>";
    echo "<p><strong>Error:</strong> " . htmlspecialchars($e->getMessage()) . "</p>";
    echo "<p><strong>File:</strong> " . $e->getFile() . "</p>";
    echo "<p><strong>Line:</strong> " . $e->getLine() . "</p>";
    
    logMessage("ERROR: " . $e->getMessage() . " in " . $e->getFile() . " on line " . $e->getLine());
    
} catch (Error $e) {
    echo "<h2>❌ FATAL ERROR FOUND!</h2>";
    echo "<p><strong>Error:</strong> " . htmlspecialchars($e->getMessage()) . "</p>";
    echo "<p><strong>File:</strong> " . $e->getFile() . "</p>";
    echo "<p><strong>Line:</strong> " . $e->getLine() . "</p>";
    
    logMessage("FATAL ERROR: " . $e->getMessage() . " in " . $e->getFile() . " on line " . $e->getLine());
}

// Get any output that was buffered
$output = ob_get_clean();

// Display the results
echo "<!DOCTYPE html><html><head><title>Admin Error Check Results</title></head><body>";
echo "<h1>🔍 Admin Dashboard Error Check Results</h1>";
echo $output;
echo "<h3>📋 Check the log file for detailed information:</h3>";
echo "<p><code>" . $error_log_file . "</code></p>";

if (file_exists($error_log_file)) {
    echo "<h4>Recent Log Entries:</h4>";
    echo "<pre style='background: #f5f5f5; padding: 10px; border-radius: 4px; max-height: 300px; overflow-y: auto;'>";
    echo htmlspecialchars(file_get_contents($error_log_file));
    echo "</pre>";
}

echo "</body></html>";
?>
