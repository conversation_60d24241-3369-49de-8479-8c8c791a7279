<?php
/**
 * User Account Information Component
 * Displays user's account details and banking information
 */

if (!isset($user) || !is_array($user)) {
    throw new Exception('User data is required for account info component');
}
?>

<!-- Account Information -->
<div class="col-lg-6">
    <div class="card">
        <div class="card-header">
            <h3 class="card-title">
                <i class="fas fa-university me-2"></i>
                Account Information
            </h3>
        </div>
        <div class="card-body account-info-section">
            <dl class="row">
                <dt class="col-5 border-bottom border-light pb-2 mb-3">Account Number:</dt>
                <dd class="col-7 border-bottom border-light pb-2 mb-3">
                    <span class="font-monospace" title="Click to copy">
                        <?php echo htmlspecialchars($user['account_number']); ?>
                    </span>
                </dd>

                <dt class="col-5 border-bottom border-light pb-2 mb-3">Account Type:</dt>
                <dd class="col-7 border-bottom border-light pb-2 mb-3">
                    <?php echo ucfirst($user['account_type']); ?>
                </dd>

                <dt class="col-5 border-bottom border-light pb-2 mb-3">Currency:</dt>
                <dd class="col-7 border-bottom border-light pb-2 mb-3">
                    <?php echo $user['currency'] ?? 'USD'; ?>
                </dd>

                <dt class="col-5 border-bottom border-light pb-2 mb-3">Current Balance:</dt>
                <dd class="col-7 border-bottom border-light pb-2 mb-3">
                    <strong class="text-primary">
                        <?php echo formatCurrency($user['balance'], $user['currency']); ?>
                    </strong>
                </dd>

                <dt class="col-5 border-bottom border-light pb-2 mb-3">Account Status:</dt>
                <dd class="col-7 border-bottom border-light pb-2 mb-3">
                    <span class="badge bg-<?php echo $user['status'] === 'active' ? 'green' : ($user['status'] === 'suspended' ? 'red' : 'gray'); ?>-lt">
                        <?php echo ucfirst($user['status']); ?>
                    </span>
                </dd>

                <dt class="col-5 border-bottom border-light pb-2 mb-3">Joined:</dt>
                <dd class="col-7 border-bottom border-light pb-2 mb-3">
                    <?php echo formatDate($user['created_at'], 'M j, Y g:i A'); ?>
                </dd>

                <dt class="col-5 border-bottom border-light pb-2 mb-3">Last Login:</dt>
                <dd class="col-7 border-bottom border-light pb-2 mb-3">
                    <?php echo $user['last_login'] ? formatDate($user['last_login'], 'M j, Y g:i A') : 'Never'; ?>
                </dd>

                <dt class="col-5 pb-2">Last Updated:</dt>
                <dd class="col-7 pb-2">
                    <?php echo formatDate($user['updated_at'], 'M j, Y g:i A'); ?>
                </dd>
            </dl>
        </div>
    </div>
</div>
