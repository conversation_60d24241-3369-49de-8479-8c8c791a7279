<?php
/**
 * Email Functionality Test Script
 * Tests email <NAME_EMAIL>
 */

// Include required files
require_once 'config/config.php';
require_once 'config/database.php';
require_once 'config/email.php';
require_once 'config/EmailManager.php';

// Set error reporting for debugging
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h1>🏦 Online Banking System - Email Functionality Test</h1>";
echo "<p><strong>Testing email functionality to: <EMAIL></strong></p>";
echo "<hr>";

// Test email address
$test_email = '<EMAIL>';
$test_name = 'Demo User';

// Test 1: Basic email validation
echo "<h2>Test 1: Email Validation</h2>";
if (isValidEmail($test_email)) {
    echo "✅ Email address validation: PASSED<br>";
} else {
    echo "❌ Email address validation: FAILED<br>";
}

// Test 2: Check email configuration
echo "<h2>Test 2: Email Configuration</h2>";
echo "📧 SMTP Host: " . SMTP_HOST . "<br>";
echo "📧 SMTP Port: " . SMTP_PORT . "<br>";
echo "📧 SMTP Username: " . SMTP_USERNAME . "<br>";
echo "📧 From Email: " . FROM_EMAIL . "<br>";
echo "📧 From Name: " . FROM_NAME . "<br>";

// Test 3: Send test OTP email using legacy function
echo "<h2>Test 3: Legacy OTP Email Function</h2>";
$test_otp = generateOTP();
echo "🔢 Generated OTP: <strong>$test_otp</strong><br>";

try {
    $otp_result = sendOTPEmail($test_email, $test_otp, $test_name);
    if ($otp_result) {
        echo "✅ Legacy OTP email function: SUCCESS<br>";
        echo "📧 OTP email sent to $test_email<br>";
    } else {
        echo "❌ Legacy OTP email function: FAILED<br>";
    }
} catch (Exception $e) {
    echo "❌ Legacy OTP email function: ERROR - " . $e->getMessage() . "<br>";
}

// Test 4: Send test email using EmailManager class
echo "<h2>Test 4: EmailManager Class Test</h2>";
try {
    $emailManager = new EmailManager();
    
    // Test OTP email
    $otp_result = $emailManager->sendOTP($test_email, $test_otp, $test_name);
    if ($otp_result['success']) {
        echo "✅ EmailManager OTP: SUCCESS<br>";
    } else {
        echo "❌ EmailManager OTP: FAILED - " . $otp_result['error'] . "<br>";
    }
    
    // Test welcome email
    $welcome_result = $emailManager->sendWelcomeEmail($test_email, $test_name);
    if ($welcome_result['success']) {
        echo "✅ EmailManager Welcome: SUCCESS<br>";
    } else {
        echo "❌ EmailManager Welcome: FAILED - " . $welcome_result['error'] . "<br>";
    }
    
    // Test configuration test
    $config_result = $emailManager->testConfiguration($test_email);
    if ($config_result['success']) {
        echo "✅ EmailManager Configuration Test: SUCCESS<br>";
    } else {
        echo "❌ EmailManager Configuration Test: FAILED - " . $config_result['error'] . "<br>";
    }
    
} catch (Exception $e) {
    echo "❌ EmailManager class: ERROR - " . $e->getMessage() . "<br>";
}

// Test 5: Send custom test email
echo "<h2>Test 5: Custom Test Email</h2>";
$custom_subject = "🏦 Online Banking System - Email Test " . date('Y-m-d H:i:s');
$custom_message = '
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>Email Test</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 0; padding: 20px; background-color: #f5f5f5; }
        .container { max-width: 600px; margin: 0 auto; background-color: white; border-radius: 10px; overflow: hidden; box-shadow: 0 4px 6px rgba(0,0,0,0.1); }
        .header { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 30px; text-align: center; }
        .content { padding: 30px; }
        .success-box { background: #d4edda; border: 1px solid #c3e6cb; color: #155724; padding: 20px; border-radius: 5px; margin: 20px 0; }
        .info-box { background: #e2e3e5; border: 1px solid #d6d8db; color: #383d41; padding: 15px; border-radius: 5px; margin: 15px 0; }
        .footer { background: #f8f9fa; padding: 20px; text-align: center; color: #6c757d; font-size: 14px; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🏦 Online Banking System</h1>
            <h2>Email Functionality Test</h2>
        </div>
        
        <div class="content">
            <div class="success-box">
                <h3>✅ Email System Working!</h3>
                <p>This email confirms that the Online Banking System email functionality is working correctly.</p>
            </div>
            
            <h3>📋 Test Details:</h3>
            <div class="info-box">
                <strong>Test Email:</strong> ' . $test_email . '<br>
                <strong>Test Date:</strong> ' . date('Y-m-d H:i:s') . '<br>
                <strong>Server:</strong> ' . ($_SERVER['SERVER_NAME'] ?? 'localhost') . '<br>
                <strong>PHP Version:</strong> ' . phpversion() . '<br>
                <strong>Test OTP:</strong> ' . $test_otp . '
            </div>
            
            <h3>🎯 What This Means:</h3>
            <ul>
                <li>✅ SMTP configuration is working</li>
                <li>✅ Email templates are rendering correctly</li>
                <li>✅ Database connections are functional</li>
                <li>✅ OTP generation is working</li>
                <li>✅ Email logging is operational</li>
            </ul>
            
            <h3>🔧 Next Steps:</h3>
            <ol>
                <li>Verify this email was received in your inbox</li>
                <li>Check spam/junk folder if not in inbox</li>
                <li>Test user registration and login OTP flow</li>
                <li>Monitor email logs for any issues</li>
            </ol>
        </div>
        
        <div class="footer">
            <p>This is an automated test email from the Online Banking System.</p>
            <p>&copy; ' . date('Y') . ' Online Banking System. All rights reserved.</p>
        </div>
    </div>
</body>
</html>';

try {
    $custom_result = sendEmail($test_email, $custom_subject, $custom_message, true);
    if ($custom_result) {
        echo "✅ Custom test email: SUCCESS<br>";
        echo "📧 Custom email sent to $test_email<br>";
    } else {
        echo "❌ Custom test email: FAILED<br>";
    }
} catch (Exception $e) {
    echo "❌ Custom test email: ERROR - " . $e->getMessage() . "<br>";
}

// Test 6: Check email logs
echo "<h2>Test 6: Email Logs Check</h2>";
$log_files = [
    'logs/email_simulation.log',
    'logs/email_welcome.log',
    'logs/error.log'
];

foreach ($log_files as $log_file) {
    if (file_exists($log_file)) {
        $log_size = filesize($log_file);
        echo "📄 $log_file: EXISTS (Size: " . number_format($log_size) . " bytes)<br>";
        
        // Show last few lines of log
        if ($log_size > 0) {
            $log_content = file_get_contents($log_file);
            $log_lines = explode("\n", $log_content);
            $recent_lines = array_slice($log_lines, -5);
            echo "<pre style='background: #f8f9fa; padding: 10px; border-radius: 5px; font-size: 12px; max-height: 150px; overflow-y: auto;'>";
            echo "Last 5 lines:\n" . implode("\n", $recent_lines);
            echo "</pre>";
        }
    } else {
        echo "📄 $log_file: NOT FOUND<br>";
    }
}

// Summary
echo "<hr>";
echo "<h2>📊 Test Summary</h2>";
echo "<p><strong>Test completed at:</strong> " . date('Y-m-d H:i:s') . "</p>";
echo "<p><strong>Target email:</strong> $test_email</p>";
echo "<p><strong>Generated OTP:</strong> $test_otp</p>";

echo "<div style='background: #e7f3ff; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
echo "<h3>🔍 What to Check Next:</h3>";
echo "<ol>";
echo "<li>Check your email inbox at <strong><EMAIL></strong></li>";
echo "<li>Look for emails with subjects containing 'Online Banking' or 'Email Test'</li>";
echo "<li>Check spam/junk folder if emails are not in inbox</li>";
echo "<li>Verify OTP code: <strong>$test_otp</strong></li>";
echo "<li>Review email logs above for any error messages</li>";
echo "</ol>";
echo "</div>";

echo "<div style='background: #fff3cd; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
echo "<h3>⚠️ Important Notes:</h3>";
echo "<ul>";
echo "<li>This system is running on localhost/MAMP environment</li>";
echo "<li>Email delivery may take a few minutes</li>";
echo "<li>Some email providers may block emails from localhost</li>";
echo "<li>Check email logs for detailed delivery information</li>";
echo "</ul>";
echo "</div>";

?>
