<?php
/**
 * Setup Wire Transfer System
 * Apply database schema and create sample data
 */

require_once 'config/config.php';

try {
    $db = getDB();
    
    echo "🚀 Setting up Wire Transfer System...\n\n";
    
    // 1. Create billing_code_settings table
    echo "1. Creating billing_code_settings table...\n";
    $db->query("CREATE TABLE IF NOT EXISTS billing_code_settings (
        id INT AUTO_INCREMENT PRIMARY KEY,
        setting_key VARCHAR(100) NOT NULL UNIQUE,
        setting_value TEXT NOT NULL,
        setting_description VARCHAR(255) DEFAULT NULL,
        setting_type ENUM('text', 'boolean', 'number', 'json') DEFAULT 'text',
        is_active BOOLEAN DEFAULT TRUE,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        updated_by INT DEFAULT NULL,
        INDEX idx_setting_key (setting_key),
        INDEX idx_is_active (is_active)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci");
    echo "✅ billing_code_settings table created\n\n";
    
    // 2. Create user_billing_codes table
    echo "2. Creating user_billing_codes table...\n";
    $db->query("CREATE TABLE IF NOT EXISTS user_billing_codes (
        id INT AUTO_INCREMENT PRIMARY KEY,
        user_id INT NOT NULL,
        billing_position TINYINT NOT NULL CHECK (billing_position BETWEEN 1 AND 4),
        billing_name VARCHAR(100) NOT NULL,
        billing_code VARCHAR(50) NOT NULL,
        billing_description TEXT DEFAULT NULL,
        is_active BOOLEAN DEFAULT TRUE,
        is_required BOOLEAN DEFAULT TRUE,
        display_order TINYINT DEFAULT 1,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        created_by INT DEFAULT NULL,
        updated_by INT DEFAULT NULL,
        UNIQUE KEY unique_user_position (user_id, billing_position),
        INDEX idx_user_id (user_id),
        INDEX idx_billing_position (billing_position),
        INDEX idx_is_active (is_active),
        FOREIGN KEY (user_id) REFERENCES accounts(id) ON DELETE CASCADE
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci");
    echo "✅ user_billing_codes table created\n\n";
    
    // 3. Create wire_transfer_fields table
    echo "3. Creating wire_transfer_fields table...\n";
    $db->query("CREATE TABLE IF NOT EXISTS wire_transfer_fields (
        id INT AUTO_INCREMENT PRIMARY KEY,
        field_name VARCHAR(100) NOT NULL UNIQUE,
        field_label VARCHAR(150) NOT NULL,
        field_type ENUM('text', 'email', 'number', 'select', 'textarea', 'tel', 'url') DEFAULT 'text',
        field_placeholder VARCHAR(200) DEFAULT NULL,
        field_options JSON DEFAULT NULL,
        is_required BOOLEAN DEFAULT FALSE,
        is_active BOOLEAN DEFAULT TRUE,
        display_order INT DEFAULT 0,
        field_group VARCHAR(50) DEFAULT 'general',
        help_text TEXT DEFAULT NULL,
        validation_rules JSON DEFAULT NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        created_by INT DEFAULT NULL,
        updated_by INT DEFAULT NULL,
        INDEX idx_field_name (field_name),
        INDEX idx_field_group (field_group),
        INDEX idx_is_active (is_active),
        INDEX idx_display_order (display_order)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci");
    echo "✅ wire_transfer_fields table created\n\n";
    
    // 4. Create billing_code_verifications table
    echo "4. Creating billing_code_verifications table...\n";
    $db->query("CREATE TABLE IF NOT EXISTS billing_code_verifications (
        id INT AUTO_INCREMENT PRIMARY KEY,
        user_id INT NOT NULL,
        billing_position TINYINT NOT NULL,
        billing_code_entered VARCHAR(50) NOT NULL,
        billing_code_expected VARCHAR(50) NOT NULL,
        is_verified BOOLEAN DEFAULT FALSE,
        verification_attempt INT DEFAULT 1,
        ip_address VARCHAR(45) DEFAULT NULL,
        user_agent TEXT DEFAULT NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        INDEX idx_user_id (user_id),
        INDEX idx_is_verified (is_verified),
        INDEX idx_created_at (created_at),
        FOREIGN KEY (user_id) REFERENCES accounts(id) ON DELETE CASCADE
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci");
    echo "✅ billing_code_verifications table created\n\n";
    
    // 5. Add wire transfer columns to transfers table
    echo "5. Adding wire transfer columns to transfers table...\n";
    $wire_columns = [
        "ADD COLUMN IF NOT EXISTS swift_code VARCHAR(20) DEFAULT NULL",
        "ADD COLUMN IF NOT EXISTS routing_code VARCHAR(50) DEFAULT NULL", 
        "ADD COLUMN IF NOT EXISTS iban VARCHAR(50) DEFAULT NULL",
        "ADD COLUMN IF NOT EXISTS bank_name VARCHAR(150) DEFAULT NULL",
        "ADD COLUMN IF NOT EXISTS bank_address TEXT DEFAULT NULL",
        "ADD COLUMN IF NOT EXISTS bank_city VARCHAR(100) DEFAULT NULL",
        "ADD COLUMN IF NOT EXISTS bank_country VARCHAR(50) DEFAULT NULL",
        "ADD COLUMN IF NOT EXISTS beneficiary_address TEXT DEFAULT NULL",
        "ADD COLUMN IF NOT EXISTS purpose_of_payment VARCHAR(200) DEFAULT NULL",
        "ADD COLUMN IF NOT EXISTS billing_codes_verified BOOLEAN DEFAULT FALSE",
        "ADD COLUMN IF NOT EXISTS billing_verification_data JSON DEFAULT NULL",
        "ADD COLUMN IF NOT EXISTS wire_transfer_data JSON DEFAULT NULL",
        "ADD COLUMN IF NOT EXISTS admin_notes TEXT DEFAULT NULL",
        "ADD COLUMN IF NOT EXISTS processing_status ENUM('pending', 'processing', 'completed', 'failed', 'cancelled') DEFAULT 'pending'"
    ];
    
    foreach ($wire_columns as $column) {
        try {
            $db->query("ALTER TABLE transfers $column");
        } catch (Exception $e) {
            // Column might already exist
        }
    }
    echo "✅ Wire transfer columns added to transfers table\n\n";
    
    // 6. Insert default wire transfer fields
    echo "6. Inserting default wire transfer fields...\n";
    $default_fields = [
        // Beneficiary Information
        ['beneficiary_account_number', 'Beneficiary Account Number', 'text', 'Enter account number', 1, 1, 1, 'beneficiary', 'The account number of the person receiving the funds'],
        ['beneficiary_account_name', 'Beneficiary Account Name', 'text', 'Enter full name as on account', 1, 1, 2, 'beneficiary', 'Full name of the account holder (must match bank records)'],
        ['beneficiary_address', 'Beneficiary Address', 'textarea', 'Enter complete address', 1, 1, 3, 'beneficiary', 'Complete address of the beneficiary'],
        
        // Bank Information
        ['bank_name', 'Bank Name', 'text', 'Enter bank name', 1, 1, 1, 'bank', 'Full name of the receiving bank'],
        ['bank_address', 'Bank Address', 'textarea', 'Enter bank address', 1, 1, 2, 'bank', 'Complete address of the receiving bank'],
        ['bank_city', 'Bank City', 'text', 'Enter city', 1, 1, 3, 'bank', 'City where the bank is located'],
        ['bank_country', 'Bank Country', 'text', 'Enter country', 1, 1, 4, 'bank', 'Country where the bank is located'],
        ['swift_code', 'SWIFT Code', 'text', 'Enter SWIFT/BIC code', 1, 1, 5, 'bank', 'SWIFT/BIC code for international transfers'],
        ['routing_code', 'Routing Code', 'text', 'Enter routing number', 0, 1, 6, 'bank', 'Bank routing number (if applicable)'],
        ['iban', 'IBAN', 'text', 'Enter IBAN', 0, 1, 7, 'bank', 'International Bank Account Number (if applicable)']
    ];
    
    foreach ($default_fields as $field) {
        try {
            $check = $db->query("SELECT id FROM wire_transfer_fields WHERE field_name = ?", [$field[0]]);
            if ($check->num_rows == 0) {
                $db->query("INSERT INTO wire_transfer_fields (field_name, field_label, field_type, field_placeholder, is_required, is_active, display_order, field_group, help_text) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)", $field);
            }
        } catch (Exception $e) {
            echo "⚠️  Warning inserting field {$field[0]}: " . $e->getMessage() . "\n";
        }
    }
    echo "✅ Default wire transfer fields inserted\n\n";
    
    // 7. Insert default billing code settings
    echo "7. Inserting default billing code settings...\n";
    $default_settings = [
        ['max_billing_attempts', '3', 'Maximum number of billing code verification attempts per transfer', 'number'],
        ['billing_code_timeout', '300', 'Billing code verification timeout in seconds (5 minutes)', 'number'],
        ['billing_verification_enabled', 'true', 'Enable billing code verification system', 'boolean']
    ];
    
    foreach ($default_settings as $setting) {
        try {
            $check = $db->query("SELECT id FROM billing_code_settings WHERE setting_key = ?", [$setting[0]]);
            if ($check->num_rows == 0) {
                $db->query("INSERT INTO billing_code_settings (setting_key, setting_value, setting_description, setting_type) VALUES (?, ?, ?, ?)", $setting);
            }
        } catch (Exception $e) {
            echo "⚠️  Warning inserting setting {$setting[0]}: " . $e->getMessage() . "\n";
        }
    }
    echo "✅ Default billing code settings inserted\n\n";
    
    echo "🎉 Wire Transfer System setup completed successfully!\n\n";
    echo "📋 Next Steps:\n";
    echo "1. Visit admin/billing-code-settings.php to assign billing codes to users\n";
    echo "2. Visit admin/wire-transfer-fields.php to manage form fields\n";
    echo "3. Test wire transfers at user/wire-transfers/\n\n";
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
}
?>
