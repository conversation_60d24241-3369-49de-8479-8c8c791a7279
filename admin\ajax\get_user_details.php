<?php
require_once '../../config/config.php';
requireAdmin();

header('Content-Type: application/json');

try {
    if (!isset($_GET['user_id']) || empty($_GET['user_id'])) {
        throw new Exception("User ID is required");
    }
    
    $user_id = intval($_GET['user_id']);
    $db = getDB();
    
    // Get user information with balance
    $user_query = "SELECT a.*,
                   COALESCE(a.balance, 0) as balance
                   FROM accounts a
                   WHERE a.id = ? AND a.is_admin = 0";
    $user_result = $db->query($user_query, [$user_id]);
    $user = $user_result ? $user_result->fetch_assoc() : null;
    
    if (!$user) {
        throw new Exception("User not found");
    }
    
    // Get user security settings
    $security_query = "SELECT * FROM user_security_settings WHERE user_id = ?";
    $security_result = $db->query($security_query, [$user_id]);
    $security_settings = $security_result ? $security_result->fetch_assoc() : null;
    
    // Get recent transactions (last 10)
    $transactions_query = "SELECT * FROM transactions 
                          WHERE user_id = ? 
                          ORDER BY created_at DESC 
                          LIMIT 10";
    $transactions_result = $db->query($transactions_query, [$user_id]);
    $transactions = [];
    
    if ($transactions_result) {
        while ($row = $transactions_result->fetch_assoc()) {
            $transactions[] = $row;
        }
    }
    
    // Get account status history (last 5 changes)
    $status_history_query = "SELECT ush.*,
                            a.first_name as changed_by_first_name,
                            a.last_name as changed_by_last_name
                            FROM user_status_history ush
                            LEFT JOIN accounts a ON ush.changed_by = a.id
                            WHERE ush.account_id = ?
                            ORDER BY ush.changed_at DESC
                            LIMIT 5";
    $status_history_result = $db->query($status_history_query, [$user_id]);
    $status_history = [];
    
    if ($status_history_result) {
        while ($row = $status_history_result->fetch_assoc()) {
            $status_history[] = $row;
        }
    }
    
    // Get virtual cards count
    $virtual_cards_query = "SELECT COUNT(*) as card_count FROM virtual_cards WHERE account_id = ?";
    $virtual_cards_result = $db->query($virtual_cards_query, [$user_id]);
    $virtual_cards_count = $virtual_cards_result ? $virtual_cards_result->fetch_assoc()['card_count'] : 0;

    // Get crypto wallets count
    $crypto_wallets_query = "SELECT COUNT(*) as wallet_count FROM crypto_wallets WHERE account_id = ?";
    $crypto_wallets_result = $db->query($crypto_wallets_query, [$user_id]);
    $crypto_wallets_count = $crypto_wallets_result ? $crypto_wallets_result->fetch_assoc()['wallet_count'] : 0;
    
    echo json_encode([
        'success' => true,
        'user' => $user,
        'security' => $security_settings,
        'transactions' => $transactions,
        'status_history' => $status_history,
        'virtual_cards_count' => $virtual_cards_count,
        'crypto_wallets_count' => $crypto_wallets_count
    ]);
    
} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'error' => $e->getMessage()
    ]);
}
?>
