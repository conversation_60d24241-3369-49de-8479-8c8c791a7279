<?php
/**
 * Database Structure Analysis for User Dashboard Redesign
 */

require_once __DIR__ . '/database.php';

try {
    $db = getDB();
    
    echo "=== DATABASE STRUCTURE ANALYSIS ===\n\n";
    
    // Get all tables
    echo "1. ALL TABLES IN DATABASE:\n";
    echo str_repeat("-", 50) . "\n";
    $tables_result = $db->query("SHOW TABLES");
    $tables = [];
    while ($row = $tables_result->fetch_array()) {
        $tables[] = $row[0];
        echo "- " . $row[0] . "\n";
    }
    echo "\n";
    
    // Analyze user-related tables
    $user_related_tables = ['accounts', 'account_transactions', 'user_profiles', 'virtual_cards', 'transfers', 'kyc_documents'];
    
    foreach ($user_related_tables as $table) {
        if (in_array($table, $tables)) {
            echo "2. TABLE STRUCTURE: $table\n";
            echo str_repeat("-", 50) . "\n";
            
            $structure = $db->query("DESCRIBE $table");
            while ($row = $structure->fetch_assoc()) {
                echo sprintf("%-20s %-15s %-10s %-10s %-15s %s\n", 
                    $row['Field'], 
                    $row['Type'], 
                    $row['Null'], 
                    $row['Key'], 
                    $row['Default'], 
                    $row['Extra']
                );
            }
            echo "\n";
            
            // Sample data
            echo "Sample data from $table:\n";
            $sample = $db->query("SELECT * FROM $table LIMIT 3");
            if ($sample && $sample->num_rows > 0) {
                $first_row = true;
                while ($row = $sample->fetch_assoc()) {
                    if ($first_row) {
                        echo "Columns: " . implode(", ", array_keys($row)) . "\n";
                        $first_row = false;
                    }
                    echo "Row: " . implode(" | ", array_map(function($v) { 
                        return strlen($v) > 30 ? substr($v, 0, 30) . '...' : $v; 
                    }, array_values($row))) . "\n";
                }
            } else {
                echo "No data found in $table\n";
            }
            echo "\n" . str_repeat("=", 70) . "\n\n";
        }
    }
    
    // Analyze relationships
    echo "3. TABLE RELATIONSHIPS ANALYSIS:\n";
    echo str_repeat("-", 50) . "\n";
    
    // Check foreign keys
    $fk_query = "
        SELECT 
            TABLE_NAME,
            COLUMN_NAME,
            CONSTRAINT_NAME,
            REFERENCED_TABLE_NAME,
            REFERENCED_COLUMN_NAME
        FROM INFORMATION_SCHEMA.KEY_COLUMN_USAGE
        WHERE REFERENCED_TABLE_SCHEMA = 'online_banking'
        AND TABLE_NAME IN ('" . implode("','", $user_related_tables) . "')
    ";
    
    $fk_result = $db->query($fk_query);
    if ($fk_result && $fk_result->num_rows > 0) {
        while ($row = $fk_result->fetch_assoc()) {
            echo sprintf("%s.%s -> %s.%s\n", 
                $row['TABLE_NAME'], 
                $row['COLUMN_NAME'],
                $row['REFERENCED_TABLE_NAME'], 
                $row['REFERENCED_COLUMN_NAME']
            );
        }
    } else {
        echo "No foreign key relationships found or using implicit relationships\n";
    }
    
    echo "\n";
    
    // Analyze user data comprehensively
    echo "4. COMPREHENSIVE USER DATA ANALYSIS:\n";
    echo str_repeat("-", 50) . "\n";
    
    // Get a sample user and all their related data
    $user_result = $db->query("SELECT * FROM accounts WHERE is_admin = 0 LIMIT 1");
    if ($user_result && $user_result->num_rows > 0) {
        $user = $user_result->fetch_assoc();
        $user_id = $user['id'];
        
        echo "Sample User ID: $user_id\n";
        echo "User Data Available:\n";
        
        // Account information
        echo "\nACCOUNT INFO:\n";
        foreach ($user as $key => $value) {
            echo "- $key: " . (strlen($value) > 50 ? substr($value, 0, 50) . '...' : $value) . "\n";
        }
        
        // Transactions
        echo "\nTRANSACTIONS:\n";
        $trans_result = $db->query("SELECT COUNT(*) as count, SUM(amount) as total FROM account_transactions WHERE account_id = $user_id");
        if ($trans_result) {
            $trans_data = $trans_result->fetch_assoc();
            echo "- Total Transactions: " . $trans_data['count'] . "\n";
            echo "- Total Amount: $" . number_format($trans_data['total'], 2) . "\n";
        }
        
        // Recent transactions
        $recent_trans = $db->query("SELECT * FROM account_transactions WHERE account_id = $user_id ORDER BY created_at DESC LIMIT 5");
        if ($recent_trans && $recent_trans->num_rows > 0) {
            echo "- Recent Transactions:\n";
            while ($trans = $recent_trans->fetch_assoc()) {
                echo "  * " . $trans['transaction_type'] . ": $" . $trans['amount'] . " (" . $trans['status'] . ")\n";
            }
        }
        
        // Virtual cards
        echo "\nVIRTUAL CARDS:\n";
        $cards_result = $db->query("SELECT COUNT(*) as count FROM virtual_cards WHERE user_id = $user_id");
        if ($cards_result) {
            $cards_data = $cards_result->fetch_assoc();
            echo "- Total Virtual Cards: " . $cards_data['count'] . "\n";
        }
        
        // Check for user profiles table
        if (in_array('user_profiles', $tables)) {
            echo "\nUSER PROFILE:\n";
            $profile_result = $db->query("SELECT * FROM user_profiles WHERE user_id = $user_id LIMIT 1");
            if ($profile_result && $profile_result->num_rows > 0) {
                $profile = $profile_result->fetch_assoc();
                foreach ($profile as $key => $value) {
                    echo "- $key: " . (strlen($value) > 50 ? substr($value, 0, 50) . '...' : $value) . "\n";
                }
            } else {
                echo "- No profile data found\n";
            }
        }
        
        // Check for KYC documents
        if (in_array('kyc_documents', $tables)) {
            echo "\nKYC STATUS:\n";
            $kyc_result = $db->query("SELECT COUNT(*) as count, status FROM kyc_documents WHERE user_id = $user_id GROUP BY status");
            if ($kyc_result && $kyc_result->num_rows > 0) {
                while ($kyc = $kyc_result->fetch_assoc()) {
                    echo "- " . $kyc['status'] . ": " . $kyc['count'] . " documents\n";
                }
            } else {
                echo "- No KYC documents found\n";
            }
        }
    }
    
    echo "\n" . str_repeat("=", 70) . "\n";
    echo "DATABASE ANALYSIS COMPLETE\n";
    
} catch (Exception $e) {
    echo "Error: " . $e->getMessage() . "\n";
}
?>
